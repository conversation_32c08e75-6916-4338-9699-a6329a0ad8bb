Config = {
  snatch_ball = 50, -- % that you succeed snatching the ball
  allow_dunking = true, -- should people be able to dunk?
  dunk_percent = 40, -- % that you succeed dunking
  easy_mode = true, -- easier to hit the ball, this should be set to true otherwise it is *really* hard to hit the ball

  Courts = {
    {
      --blip = vector3(-1282.99, -1528.75, 4.31),
      start = vector3(-1283.5114746094,-1528.6459960938,4.3135824203491),
      ball = vector3(-1275.13, -1530.67, 3.4),

      home = {
        vector3(-1272.0, -1517.52, 6.3),
        vector3(-1272.0, -1517.52, 6.0),
      },

      guest = {
        vector3(-1278.22, -1544.04, 6.3),
        vector3(-1278.22, -1544.04, 6.0),
      },

      dunking = {
        home = vector4(-1272.25, -1518.37, 6.2, 344.46),
        guest = vector4(-1277.96, -1542.67, 6.2, 166.81),
      },

      queue = {
        minimum = 1, -- minimum users per team for a match to start
        timer = 30, -- how long (in seconds) until the match starts, after "minimum" users are in queue per team
        game = 300, -- how long (in seconds) a game lasts
      },
    },

    -- Prison
    {
      start = vector3(1682.891, 2499.297, 45.565),
      ball  = vector3(1680.162, 2513.179, 45.565),

      home = {
        vector3(1688.444, 2522.347, 48.20403),
        vector3(1688.444, 2522.347, 47.78479)
      },

      guest = {
        vector3(1672.312, 2503.596, 48.20403),
        vector3(1672.312, 2503.596, 47.78479)
      },

      dunking = {
        home = vector4(1687.738, 2521.489, 48.04685, 316.977),
        guest = vector4(1672.856, 2504.245, 48.04685, 136.977),
      },

      queue = {
        minimum = 1, -- minimum users per team for a match to start
        timer = 30, -- how long (in seconds) until the match starts, after "minimum" users are in queue per team
        game = 300, -- how long (in seconds) a game lasts
      },
    },

    -- Grove St (community center)
    {
      start = vector3(-92.370, -1823.120, 26.942),
      ball  = vector3(-91.025, -1811.288, 26.942),

      home = {
        vector3(-95.97953, -1817.159, 29.2952),
        vector3(-95.97953, -1817.159, 28.74795)
      },

      guest = {
        vector3(-86.05516, -1805.309, 29.2952),
        vector3(-86.05516, -1805.309, 28.74795)
      },

      dunking = {
        home = vector4(-95.14654, -1816.143, 28.84795, 139.511),
        guest = vector4(-87.07366, -1806.522, 28.84795, 319.511),
      },

      queue = {
        minimum = 1, -- minimum users per team for a match to start
        timer = 30, -- how long (in seconds) until the match starts, after "minimum" users are in queue per team
        game = 300, -- how long (in seconds) a game lasts
      },
    },
  },
}

Strings = {
  ["blip"] = "Basketball",
  ["home"] = "Home",
  ["guest"] = "Guest",
  ["join_team"] = "Press ~INPUT_CONTEXT~ to %s team \"~b~%s~s~\" (%s in team)\nPress ~INPUT_DETONATE~ to %s team \"~r~%s~s~\" (%s in team)\nPress ~INPUT_VEH_HEADLIGHT~ to reset court",
  ["join"] = "join",
  ["leave"] = "leave",

  ["game_progress"] = "There's a game in progress.\nIt ends in: %s %s and %s %s",

  ["waiting"] = "Waiting",
  ["cancelled"] = "The game got cancelled due to there being no players in one team.",

  ["starting"] = "\nGame starts in %s %s and %s %s",
  ["minute"] = "minute",
  ["minutes"] = "minutes",
  ["second"] = "second",
  ["seconds"] = "seconds",

  ["left_queue"] = "You left the queue since you walked away",

  ["steal_ball"] = "Press [~b~E~s~] to snatch the ball",
  ["pickup_ball"] = "Press [~b~E~s~] to pick up the ball",
  ["ball_info"] = "Press ~INPUT_VEH_DUCK~ to drop the ball\nPress ~INPUT_ATTACK~ to shoot the ball\nPress ~INPUT_DETONATE~ to dunk",

  ["goal"] = "Good shooting! You scored a point for your team.",
  ["better_luck"] = "You didn't get the ball",
  ["better_luck_dunk"] = "You didn't dunk - better luck next time",

  ["you_lost"] = "Your team <b>lost</b>. Better luck next time!",
  ["you_won"] = "Your team <b>won</b>!",
  ["tie"] = "Your team <b>tied</b> with the other team.",
}
