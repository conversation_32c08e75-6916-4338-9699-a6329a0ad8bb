local using_megaphone = false

local range_default = 20.0
local range_overrides = {
  [`polmav`] = 25.0,
  [`polbuz`] = 25.0,
   -- Staff cars for events/zancudo
  [`dinghy5`] = 35.0,
  [`akula`] = 35.0,
  [`annihilator2`] = 35.0,
  [`conada2`] = 35.0,
  [`hunter`] = 35.0,
  [`valkyrie`] = 35.0,
  [`valkyrie2`] = 35.0,
  [`savage`] = 35.0,
}
local vehicle_megaphone = {
  [`polmav`] = true,
  [`polbuz`] = true,
   -- Staff cars for events/zancudo
  [`dinghy5`] = true,
  [`akula`] = true,
  [`annihilator2`] = true,
  [`conada2`] = true,
  [`hunter`] = true,
  [`valkyrie`] = true,
  [`valkyrie2`] = true,
  [`savage`] = true,
}

exports('StartMegaphone', function()
  local veh = GetVehiclePedIsIn(PlayerPedId())

  if not veh or (GetVehicleClass(veh) ~= 18 and not vehicle_megaphone[GetEntityModel(veh)]) then
    return
  end

  local seat = exports.blrp_vehicles:GetPedVehicleSeat(PlayerPedId())
  if seat ~= -1 and seat ~= 0 then return end -- driver and passenger only

  if exports.blrp_core:me().isHandcuffed() or exports.blrp_core:me().isZipTied() then
    return
  end

  if using_megaphone then return end

  using_megaphone = true
  TriggerServerEvent('megaphone:applySubmix', true)

  local range = range_overrides[GetEntityModel(veh)] or range_default

  exports["pma-voice"]:overrideProximityRange(range, true)

  -- Sounds
  Citizen.CreateThread(function()
    local coords = GetEntityCoords(PlayerPedId())
    TriggerServerEvent('vrp:server:broadcastSpatializedSound', 'CB_RADIO_SFX', 'Start_Squelch', coords, 8)
  end)
end)

exports('StopMegaphone', function()
  if using_megaphone then
    using_megaphone = false
    TriggerServerEvent('megaphone:applySubmix', false)
    exports["pma-voice"]:clearProximityOverride()
    -- Sounds
    Citizen.CreateThread(function()
      local coords = GetEntityCoords(PlayerPedId())
      TriggerServerEvent('vrp:server:broadcastSpatializedSound', 'CB_RADIO_SFX', 'End_Squelch', coords, 8)
    end)
  end
end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    if using_megaphone then
      SetControlNormal(0, 249, 1.0)
      SetControlNormal(1, 249, 1.0)
      SetControlNormal(2, 249, 1.0)
    end
  end
end)

local data = {
  [GetHashKey('default')] = 0,
  [GetHashKey('freq_low')] = 0.0,
  [GetHashKey('freq_hi')] = 10000.0,
  [GetHashKey('rm_mod_freq')] = 300.0,
  [GetHashKey('rm_mix')] = 0.2,
  [GetHashKey('fudge')] = 0.0,
  [GetHashKey('o_freq_lo')] = 200.0,
  [GetHashKey('o_freq_hi')] = 5000.0,
}

local filter

CreateThread(function()
  filter = CreateAudioSubmix("Megaphone")
  SetAudioSubmixEffectRadioFx(filter, 0)

  for hash, value in pairs(data) do
    SetAudioSubmixEffectParamInt(filter, 0, hash, 1)
  end

  AddAudioSubmixOutput(filter, 0)
end)

RegisterNetEvent('megaphone:updateSubmixStatus', function(state, source)
  if state then
    MumbleSetSubmixForServerId(source, filter)
  else
    MumbleSetSubmixForServerId(source, -1)
  end
end)
