pRadio = { }
P.bindInstance('radio', pRadio)

-- Track when players leave channels to detect temporary switches
local radioChannelHistory = {}

-- Clean up old history entries every 5 minutes
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(300000) -- 5 minutes

    local currentTime = os.time()
    for source, channels in pairs(radioChannelHistory) do
      for channel, leaveTime in pairs(channels) do
        -- Remove entries older than 2 minutes
        if currentTime - leaveTime > 120 then
          radioChannelHistory[source][channel] = nil
        end
      end

      -- Clean up empty player entries
      if next(radioChannelHistory[source]) == nil then
        radioChannelHistory[source] = nil
      end
    end
  end
end)

-- Monitors player current radio for faction dispatch
AddStateBagChangeHandler('radioChannel', nil, function(bagName, key, value, reserved, replicated)
	if not string.match(bagName, 'player:') or not value or not tonumber(value) then
		return
	end

	local player = string.gsub(bagName, 'player:', '')
	player = tonumber(player)

	if value <= 0 then
		value = nil
	end

  local character = exports.blrp_core:character(player)

  if not character then
    return
  end

	character.set('radio', value)
end)

playerDropped = {}

AddEventHandler("playerDropped", function()
  playerDropped[source] = true
  -- Clean up radio channel history
  radioChannelHistory[source] = nil
end)

AddStateBagChangeHandler('radioChannels', nil, function(bagName, key, value, reserved, replicated)
	if not string.match(bagName, 'player:') then
		return
	end

	local player = string.gsub(bagName, 'player:', '')
	player = tonumber(player)

  Citizen.Wait(500)

  if playerDropped[player] then
    return
  end

  local character = exports.blrp_core:character(player)

  if not character then
    return
  end

  character.setUserData('radio_channels', value, true, true)

end)

function math.round(num, numDecimalPlaces)
  return tonumber(string.format("%." .. (numDecimalPlaces or 0) .. "f", num))
end

AddEventHandler('core:server:registerSelectedPlayer', function(player)
  local character = exports.blrp_core:character(player)

  if not character then
    return
  end

  local radioChannels = character.getUserData('radio_channels', false, true) -- {"10008":true,"10026":true,"11":true,"12":true,"2":true}

  if not radioChannels or type(radioChannels) ~= "table" then
    return
  end

  Citizen.Wait(15000)

  for channel, _ in pairs(radioChannels) do
    if type(channel) == "string" or type(channel) == "number" then
      channel = math.round(channel, 2)
      TriggerEvent('blrp_radio:server:tryJoinChannel', character.source, channel)
    end
  end
end)


function getNextRadio(source, currentRadio)
	if not currentRadio then
		currentRadio = 0
	end

	voiceData[source] = voiceData[source] or defaultTable(source)

	local tkeys = {}

	for k in pairs(voiceData[source].radios) do table.insert(tkeys, k) end

	table.sort(tkeys)

	if #tkeys == 0 then
		return 0
	end

	local nextRadio = tkeys[1] or 0

	for _, scanRadio in ipairs(tkeys) do
		if scanRadio > currentRadio then
			nextRadio = scanRadio
			break
		end
	end

	return nextRadio
end

function addPlayerToRadio(source, radioChannel, skip_state_set)
	radioChannel = tonumber(radioChannel)

	if not radioChannel or radioChannel < 0 then
		return
	end

	logger.verbose('Added %s to radio %s', source, radioChannel)

	radioData[radioChannel] = radioData[radioChannel] or {}

	for subscriber_source, _ in pairs(radioData[radioChannel]) do
		TriggerClientEvent('pma-voice:addPlayerToRadio', subscriber_source, source, radioChannel)
	end

	voiceData[source] = voiceData[source] or defaultTable(source)
	voiceData[source].radios[radioChannel] = true
	radioData[radioChannel][source] = false

  if not skip_state_set then
	  voiceData[source].radio = radioChannel
	  Player(source).state.radioChannel = radioChannel
  end

  -- Smart police channel join alert system
  if radioChannel >= 12 and radioChannel <= 100 then
    local character = exports.blrp_core:character(source)

    -- Don't alert for FIB or Dispatch
    if character and not (character.hasGroup('FIB') or character.hasGroup('Dispatch')) then
      Citizen.CreateThread(function()
        -- Wait 3 seconds to ensure they're actually staying on the channel
        Citizen.Wait(3000)

        -- Check if they're still on this channel after the delay
        local currentChannel = Player(source).state.radioChannel
        if currentChannel == radioChannel then
          -- Check if they were on this channel recently (within last 30 seconds)
          local wasRecentlyOnChannel = false
          if radioChannelHistory[source] and radioChannelHistory[source][radioChannel] then
            local timeSinceLeft = os.time() - radioChannelHistory[source][radioChannel]
            wasRecentlyOnChannel = timeSinceLeft < 30
          end

          -- Only alert if this is a genuine new join (not a quick return)
          if not wasRecentlyOnChannel then
            -- Play sound for all existing subscribers on the channel
            for subscriber_source, _ in pairs(radioData[radioChannel]) do
              if subscriber_source ~= source then -- Don't play for the joining player
                TriggerClientEvent('InteractSound_CL:PlayOnOne', subscriber_source, 'alert_polce_joined_channel', 0.1)
              end
            end
          end
        end
      end)
    end
  end

	Player(source).state.radioChannels = voiceData[source].radios

	TriggerClientEvent('pma-voice:syncRadioData', source, radioChannel, radioData[radioChannel])
end
exports('AddPlayerToRadio', addPlayerToRadio)

RegisterNetEvent('pma-voice:addPlayerToRadio', function(radioChannel)
	addPlayerToRadio(source, radioChannel)
end)

function removePlayerFromRadio(source, radioChannel, skip_state_set)
	if not radioChannel then
		return
	end

	if not radioData[radioChannel] or radioData[radioChannel][source] == nil then
		return
	end

	logger.verbose('Removed %s from radio %s', source, radioChannel)

	-- Track when player leaves police channels for history
	if radioChannel >= 12 and radioChannel <= 100 then
		radioChannelHistory[source] = radioChannelHistory[source] or {}
		radioChannelHistory[source][radioChannel] = os.time()
	end

	radioData[radioChannel] = radioData[radioChannel] or {}

	for subscriber_source, _ in pairs(radioData[radioChannel]) do
		TriggerClientEvent('pma-voice:removePlayerFromRadio', subscriber_source, source, radioChannel)
	end

	radioData[radioChannel][source] = nil
	voiceData[source] = voiceData[source] or defaultTable(source)
	voiceData[source].radios[radioChannel] = nil

  if not skip_state_set then
	  voiceData[source].radio = getNextRadio(source)
    Player(source).state.radioChannel = voiceData[source].radio
  end

	Player(source).state.radioChannels = voiceData[source].radios
end
exports('RemovePlayerFromRadio', removePlayerFromRadio)

RegisterNetEvent('pma-voice:removePlayerFromRadio', function(radioChannel)
	removePlayerFromRadio(source, radioChannel)
end)

function cyclePlayerRadio()
	voiceData[source] = voiceData[source] or defaultTable(source)

	local currentRadio = voiceData[source].radio

	if currentRadio ~= 0 and radioData[currentRadio][source] then
		exports.blrp_core:character(source).notify('You cannot cycle channels while transmitting')
		TriggerClientEvent('pma-voice:client:playBlockSound', source)

		return
	end

	local nextRadio = getNextRadio(source, voiceData[source].radio)

	if nextRadio == voiceData[source].radio then
		return
	end

	voiceData[source].radio = nextRadio

	Player(source).state.radioChannel = nextRadio

	logger.verbose('Cycle %s radio to channel %s', source, nextRadio)
end
RegisterNetEvent('pma-voice:cyclePlayerRadio', cyclePlayerRadio)

function setTalkingOnRadio(talking, coords)
  local player = source

	voiceData[player] = voiceData[player] or defaultTable(player)

	local plyVoice = voiceData[player]
	local radioTbl = radioData[plyVoice.radio]

	if radioTbl then
		radioTbl[player] = talking
		logger.verbose('Set %s to talking: %s on radio %s', player, talking, plyVoice.radio)

		for subscriber_source, _ in pairs(radioTbl) do
			if subscriber_source ~= player then
				TriggerClientEvent('pma-voice:setTalkingOnRadio', subscriber_source, player, talking, plyVoice.radio, coords)
				logger.verbose('Sync %s to let them know %s is %s', subscriber_source, player, talking and 'talking' or 'not talking')
			end
		end

    local radio_numeric = tonumber(plyVoice.radio)

    -- Play global talk alert for global channels (1-4, 7) when starting to talk
    if talking and (
      (radio_numeric >= 1 and radio_numeric <= 4) or -- DOC global, LEO global, EMS global, Tow global
      radio_numeric == 7 -- DOJ Global
    ) then
      local character = exports.blrp_core:character(player)

      -- Don't alert for FIB or Dispatch
      if character and not (character.hasGroup('FIB') or character.hasGroup('Dispatch')) then
        Citizen.CreateThread(function()
          Citizen.Wait(500) -- Wait 0.5 seconds before playing the alert
          TriggerClientEvent('InteractSound_CL:PlayOnOne', player, 'alert_police_talking_global', 0.5)
        end)
      end
    end

    if
      ( -- LEO, EMS, DOC, Liaison channels
        radio_numeric >= 10 and
        radio_numeric <= 100
      ) or
      ( -- DOC global, LEO global, EMS global, Tow global
        radio_numeric >= 1 and
        radio_numeric <= 4
      ) or
      radio_numeric == 7 -- DOJ Global
    then
      local character = exports.blrp_core:character(player)

      local callsign = character.get('callsign')

      if not callsign or callsign == '' then
        callsign = '#' .. character.get('id')
      end

      local event = 'PTTStart'

      if not talking then
        event = 'PTTEnd'
      end

      pcall(function()
        exports.blrp_api:SendWebsocketEvent('private-radio', event, {
          callsign = callsign,
          id = character.get('id'),
          channel = plyVoice.radio,
          dispatcher = character.hasGroup('Dispatch'),
        })
      end)
    end
	end
end
RegisterNetEvent('pma-voice:setTalkingOnRadio', setTalkingOnRadio)

-- Use proxy for security
pRadio.forceTalkOnChannel = function(channel)
  local player = source

  voiceData[player] = voiceData[player] or defaultTable(player)

  local currentRadio = voiceData[player].radio

  if currentRadio ~= 0 and radioData[currentRadio][player] then
    exports.blrp_core:character(player).notify('You are transmitting and could not change channels automatically')
    TriggerClientEvent('pma-voice:client:playBlockSound', player)
    return
  end

  voiceData[player].radio = channel

  Player(player).state.radioChannel = channel

  logger.verbose('Forced %s radio to channel %s', player, channel)
end


RegisterNetEvent('pma-voice:radioClick', function(radioChannel, state)
  local src = source
  for ply, _ in pairs(radioData[radioChannel] or {}) do
    TriggerClientEvent('pma-voice:radioClick', ply, src, state)
  end
end)
