var ls=Object.defineProperty;var ss=(t,e,n)=>e in t?ls(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var Ue=(t,e,n)=>(ss(t,typeof e!="symbol"?e+"":e,n),n);(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const i of l)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(l){const i={};return l.integrity&&(i.integrity=l.integrity),l.referrerPolicy&&(i.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?i.credentials="include":l.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(l){if(l.ep)return;l.ep=!0;const i=n(l);fetch(l.href,i)}})();function me(){}const Yt=t=>t;function Re(t,e){for(const n in e)t[n]=e[n];return t}function Nl(t){return t()}function Fn(){return Object.create(null)}function Le(t){t.forEach(Nl)}function Lt(t){return typeof t=="function"}function ce(t,e){return t!=t?e==e:t!==e||t&&typeof t=="object"||typeof t=="function"}function is(t){return Object.keys(t).length===0}function Zt(t,...e){if(t==null){for(const r of e)r(void 0);return me}const n=t.subscribe(...e);return n.unsubscribe?()=>n.unsubscribe():n}function pt(t){let e;return Zt(t,n=>e=n)(),e}function fe(t,e,n){t.$$.on_destroy.push(Zt(e,n))}function $e(t,e,n,r){if(t){const l=Pl(t,e,n,r);return t[0](l)}}function Pl(t,e,n,r){return t[1]&&r?Re(n.ctx.slice(),t[1](r(e))):n.ctx}function xe(t,e,n,r){if(t[2]&&r){const l=t[2](r(n));if(e.dirty===void 0)return l;if(typeof l=="object"){const i=[],s=Math.max(e.dirty.length,l.length);for(let a=0;a<s;a+=1)i[a]=e.dirty[a]|l[a];return i}return e.dirty|l}return e.dirty}function et(t,e,n,r,l,i){if(l){const s=Pl(e,n,r,i);t.p(s,l)}}function tt(t){if(t.ctx.length>32){const e=[],n=t.ctx.length/32;for(let r=0;r<n;r++)e[r]=-1;return e}return-1}function bt(t){const e={};for(const n in t)n[0]!=="$"&&(e[n]=t[n]);return e}function Gn(t){return t??""}function os(t,e,n){return t.set(n),e}function as(t){const e=typeof t=="string"&&t.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return e?[parseFloat(e[1]),e[2]||"px"]:[t,"px"]}const Ul=typeof window<"u";let Xt=Ul?()=>window.performance.now():()=>Date.now(),Tn=Ul?t=>requestAnimationFrame(t):me;const dt=new Set;function Wl(t){dt.forEach(e=>{e.c(t)||(dt.delete(e),e.f())}),dt.size!==0&&Tn(Wl)}function Jt(t){let e;return dt.size===0&&Tn(Wl),{promise:new Promise(n=>{dt.add(e={c:t,f:n})}),abort(){dt.delete(e)}}}const us=typeof window<"u"?window:typeof globalThis<"u"?globalThis:global;function L(t,e){t.appendChild(e)}function Dl(t){if(!t)return document;const e=t.getRootNode?t.getRootNode():t.ownerDocument;return e&&e.host?e:t.ownerDocument}function cs(t){const e=G("style");return e.textContent="/* empty */",fs(Dl(t),e),e.sheet}function fs(t,e){return L(t.head||t,e),e.sheet}function D(t,e,n){t.insertBefore(e,n||null)}function W(t){t.parentNode&&t.parentNode.removeChild(t)}function Se(t,e){for(let n=0;n<t.length;n+=1)t[n]&&t[n].d(e)}function G(t){return document.createElement(t)}function oe(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function Ee(t){return document.createTextNode(t)}function H(){return Ee(" ")}function pe(){return Ee("")}function ge(t,e,n,r){return t.addEventListener(e,n,r),()=>t.removeEventListener(e,n,r)}function h(t,e,n){n==null?t.removeAttribute(e):t.getAttribute(e)!==n&&t.setAttribute(e,n)}function Ol(t){return t===""?null:+t}function ds(t){return Array.from(t.childNodes)}function ze(t,e){e=""+e,t.data!==e&&(t.data=e)}function _t(t,e){t.value=e??""}function Y(t,e,n,r){n==null?t.style.removeProperty(e):t.style.setProperty(e,n,r?"important":"")}let Nt;function hs(){if(Nt===void 0){Nt=!1;try{typeof window<"u"&&window.parent&&window.parent.document}catch{Nt=!0}}return Nt}function Mn(t,e){getComputedStyle(t).position==="static"&&(t.style.position="relative");const r=G("iframe");r.setAttribute("style","display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; border: 0; opacity: 0; pointer-events: none; z-index: -1;"),r.setAttribute("aria-hidden","true"),r.tabIndex=-1;const l=hs();let i;return l?(r.src="data:text/html,<script>onresize=function(){parent.postMessage(0,'*')}<\/script>",i=ge(window,"message",s=>{s.source===r.contentWindow&&e()})):(r.src="about:blank",r.onload=()=>{i=ge(r.contentWindow,"resize",e),e()}),L(t,r),()=>{(l||i&&r.contentWindow)&&i(),W(r)}}function be(t,e,n){t.classList.toggle(e,!!n)}function Fl(t,e,{bubbles:n=!1,cancelable:r=!1}={}){return new CustomEvent(t,{detail:e,bubbles:n,cancelable:r})}const Ot=new Map;let Ft=0;function gs(t){let e=5381,n=t.length;for(;n--;)e=(e<<5)-e^t.charCodeAt(n);return e>>>0}function ms(t,e){const n={stylesheet:cs(e),rules:{}};return Ot.set(t,n),n}function Gt(t,e,n,r,l,i,s,a=0){const o=16.666/r;let u=`{
`;for(let y=0;y<=1;y+=o){const S=e+(n-e)*i(y);u+=y*100+`%{${s(S,1-S)}}
`}const f=u+`100% {${s(n,1-n)}}
}`,c=`__svelte_${gs(f)}_${a}`,d=Dl(t),{stylesheet:g,rules:p}=Ot.get(d)||ms(d,t);p[c]||(p[c]=!0,g.insertRule(`@keyframes ${c} ${f}`,g.cssRules.length));const _=t.style.animation||"";return t.style.animation=`${_?`${_}, `:""}${c} ${r}ms linear ${l}ms 1 both`,Ft+=1,c}function jt(t,e){const n=(t.style.animation||"").split(", "),r=n.filter(e?i=>i.indexOf(e)<0:i=>i.indexOf("__svelte")===-1),l=n.length-r.length;l&&(t.style.animation=r.join(", "),Ft-=l,Ft||ps())}function ps(){Tn(()=>{Ft||(Ot.forEach(t=>{const{ownerNode:e}=t.stylesheet;e&&W(e)}),Ot.clear())})}let Tt;function Et(t){Tt=t}function Ln(){if(!Tt)throw new Error("Function called outside component initialization");return Tt}function zt(t){Ln().$$.on_mount.push(t)}function bs(t){Ln().$$.on_destroy.push(t)}function Qt(){const t=Ln();return(e,n,{cancelable:r=!1}={})=>{const l=t.$$.callbacks[e];if(l){const i=Fl(e,n,{cancelable:r});return l.slice().forEach(s=>{s.call(t,i)}),!i.defaultPrevented}return!0}}function Gl(t,e){const n=t.$$.callbacks[e.type];n&&n.slice().forEach(r=>r.call(this,e))}const at=[],Ce=[];let ht=[];const yn=[],_s=Promise.resolve();let kn=!1;function ws(){kn||(kn=!0,_s.then(jl))}function ie(t){ht.push(t)}function Ye(t){yn.push(t)}const nn=new Set;let lt=0;function jl(){if(lt!==0)return;const t=Tt;do{try{for(;lt<at.length;){const e=at[lt];lt++,Et(e),vs(e.$$)}}catch(e){throw at.length=0,lt=0,e}for(Et(null),at.length=0,lt=0;Ce.length;)Ce.pop()();for(let e=0;e<ht.length;e+=1){const n=ht[e];nn.has(n)||(nn.add(n),n())}ht.length=0}while(at.length);for(;yn.length;)yn.pop()();kn=!1,nn.clear(),Et(t)}function vs(t){if(t.fragment!==null){t.update(),Le(t.before_update);const e=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,e),t.after_update.forEach(ie)}}function ys(t){const e=[],n=[];ht.forEach(r=>t.indexOf(r)===-1?e.push(r):n.push(r)),n.forEach(r=>r()),ht=e}let kt;function zn(){return kt||(kt=Promise.resolve(),kt.then(()=>{kt=null})),kt}function Qe(t,e,n){t.dispatchEvent(Fl(`${e?"intro":"outro"}${n}`))}const Dt=new Set;let Oe;function le(){Oe={r:0,c:[],p:Oe}}function se(){Oe.r||Le(Oe.c),Oe=Oe.p}function C(t,e){t&&t.i&&(Dt.delete(t),t.i(e))}function P(t,e,n,r){if(t&&t.o){if(Dt.has(t))return;Dt.add(t),Oe.c.push(()=>{Dt.delete(t),r&&(n&&t.d(1),r())}),t.o(e)}else r&&r()}const Rn={duration:0};function ks(t,e,n){const r={direction:"in"};let l=e(t,n,r),i=!1,s,a,o=0;function u(){s&&jt(t,s)}function f(){const{delay:d=0,duration:g=300,easing:p=Yt,tick:_=me,css:y}=l||Rn;y&&(s=Gt(t,0,1,g,d,p,y,o++)),_(0,1);const S=Xt()+d,I=S+g;a&&a.abort(),i=!0,ie(()=>Qe(t,!0,"start")),a=Jt(M=>{if(i){if(M>=I)return _(1,0),Qe(t,!0,"end"),u(),i=!1;if(M>=S){const K=p((M-S)/g);_(K,1-K)}}return i})}let c=!1;return{start(){c||(c=!0,jt(t),Lt(l)?(l=l(r),zn().then(f)):f())},invalidate(){c=!1},end(){i&&(u(),i=!1)}}}function Ss(t,e,n){const r={direction:"out"};let l=e(t,n,r),i=!0,s;const a=Oe;a.r+=1;let o;function u(){const{delay:f=0,duration:c=300,easing:d=Yt,tick:g=me,css:p}=l||Rn;p&&(s=Gt(t,1,0,c,f,d,p));const _=Xt()+f,y=_+c;ie(()=>Qe(t,!1,"start")),"inert"in t&&(o=t.inert,t.inert=!0),Jt(S=>{if(i){if(S>=y)return g(0,1),Qe(t,!1,"end"),--a.r||Le(a.c),!1;if(S>=_){const I=d((S-_)/c);g(1-I,I)}}return i})}return Lt(l)?zn().then(()=>{l=l(r),u()}):u(),{end(f){f&&"inert"in t&&(t.inert=o),f&&l.tick&&l.tick(1,0),i&&(s&&jt(t,s),i=!1)}}}function X(t,e,n,r){let i=e(t,n,{direction:"both"}),s=r?0:1,a=null,o=null,u=null,f;function c(){u&&jt(t,u)}function d(p,_){const y=p.b-s;return _*=Math.abs(y),{a:s,b:p.b,d:y,duration:_,start:p.start,end:p.start+_,group:p.group}}function g(p){const{delay:_=0,duration:y=300,easing:S=Yt,tick:I=me,css:M}=i||Rn,K={start:Xt()+_,b:p};p||(K.group=Oe,Oe.r+=1),"inert"in t&&(p?f!==void 0&&(t.inert=f):(f=t.inert,t.inert=!0)),a||o?o=K:(M&&(c(),u=Gt(t,s,p,y,_,S,M)),p&&I(0,1),a=d(K,y),ie(()=>Qe(t,p,"start")),Jt(E=>{if(o&&E>o.start&&(a=d(o,y),o=null,Qe(t,a.b,"start"),M&&(c(),u=Gt(t,s,a.b,a.duration,0,S,i.css))),a){if(E>=a.end)I(s=a.b,1-s),Qe(t,a.b,"end"),o||(a.b?c():--a.group.r||Le(a.group.c)),a=null;else if(E>=a.start){const b=E-a.start;s=a.a+a.d*S(b/a.duration),I(s,1-s)}}return!!(a||o)}))}return{run(p){Lt(i)?zn().then(()=>{i=i({direction:p?"in":"out"}),g(p)}):g(p)},end(){c(),a=o=null}}}function re(t){return(t==null?void 0:t.length)!==void 0?t:Array.from(t)}function Vt(t,e){const n={},r={},l={$$scope:1};let i=t.length;for(;i--;){const s=t[i],a=e[i];if(a){for(const o in s)o in a||(r[o]=1);for(const o in a)l[o]||(n[o]=a[o],l[o]=1);t[i]=a}else for(const o in s)l[o]=1}for(const s in r)s in n||(n[s]=void 0);return n}function Kt(t){return typeof t=="object"&&t!==null?t:{}}function Ze(t,e,n){const r=t.$$.props[e];r!==void 0&&(t.$$.bound[r]=n,n(t.$$.ctx[r]))}function ee(t){t&&t.c()}function $(t,e,n){const{fragment:r,after_update:l}=t.$$;r&&r.m(e,n),ie(()=>{const i=t.$$.on_mount.map(Nl).filter(Lt);t.$$.on_destroy?t.$$.on_destroy.push(...i):Le(i),t.$$.on_mount=[]}),l.forEach(ie)}function x(t,e){const n=t.$$;n.fragment!==null&&(ys(n.after_update),Le(n.on_destroy),n.fragment&&n.fragment.d(e),n.on_destroy=n.fragment=null,n.ctx=[])}function Is(t,e){t.$$.dirty[0]===-1&&(at.push(t),ws(),t.$$.dirty.fill(0)),t.$$.dirty[e/31|0]|=1<<e%31}function de(t,e,n,r,l,i,s,a=[-1]){const o=Tt;Et(t);const u=t.$$={fragment:null,ctx:[],props:i,update:me,not_equal:l,bound:Fn(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(o?o.$$.context:[])),callbacks:Fn(),dirty:a,skip_bound:!1,root:e.target||o.$$.root};s&&s(u.root);let f=!1;if(u.ctx=n?n(t,e.props||{},(c,d,...g)=>{const p=g.length?g[0]:d;return u.ctx&&l(u.ctx[c],u.ctx[c]=p)&&(!u.skip_bound&&u.bound[c]&&u.bound[c](p),f&&Is(t,c)),d}):[],u.update(),f=!0,Le(u.before_update),u.fragment=r?r(u.ctx):!1,e.target){if(e.hydrate){const c=ds(e.target);u.fragment&&u.fragment.l(c),c.forEach(W)}else u.fragment&&u.fragment.c();e.intro&&C(t.$$.fragment),$(t,e.target,e.anchor),jl()}Et(o)}class he{constructor(){Ue(this,"$$");Ue(this,"$$set")}$destroy(){x(this,1),this.$destroy=me}$on(e,n){if(!Lt(n))return me;const r=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return r.push(n),()=>{const l=r.indexOf(n);l!==-1&&r.splice(l,1)}}$set(e){this.$$set&&!is(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}const Cs="4";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add(Cs);const st=[];function wt(t,e=me){let n;const r=new Set;function l(a){if(ce(t,a)&&(t=a,n)){const o=!st.length;for(const u of r)u[1](),st.push(u,t);if(o){for(let u=0;u<st.length;u+=2)st[u][0](st[u+1]);st.length=0}}}function i(a){l(a(t))}function s(a,o=me){const u=[a,o];return r.add(u),r.size===1&&(n=e(l,i)||me),a(t),()=>{r.delete(u),r.size===0&&n&&(n(),n=null)}}return{set:l,update:i,subscribe:s}}const Nn=wt({fallbackResourceName:"debug",allowEscapeKey:!0}),Es=wt(window.GetParentResourceName?window.GetParentResourceName():pt(Nn).DEBUG_RESOURCE_NAME),Vl=wt(!window.invokeNative),jn=wt(!1);var ke=(t=>(t.visible="ui:visible",t.start="ui:start",t))(ke||{}),$t=(t=>(t.close="ui:close",t.uiLoaded="ui:loaded",t.finish="ui:finish",t))($t||{}),Ne=(t=>(t.down="keydown",t.up="keyup",t.pressed="keypress",t))(Ne||{}),xt=(t=>(t.click="click",t.down="mousedown",t.up="mouseup",t.move="mousemove",t))(xt||{});const en=pt(Vl),As=pt(Es),Sn=[];async function Kl(t,e={}){if(en==!0){const l=await Ms(t,e);return Promise.resolve(l)}const n={method:"post",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify(e)};return await(await fetch(`https://${As}/${t}`,n)).json()}function Bl(t,e){const n=r=>{const{action:l,data:i}=r.data;l===t&&e(i)};zt(()=>window.addEventListener("message",n)),bs(()=>window.removeEventListener("message",n))}async function Ie(t,e,n=0){en&&setTimeout(()=>{const r=new MessageEvent("message",{data:{action:t,data:e}});window.dispatchEvent(r)},n)}async function Ts(t,e){if(en){if(Sn[t]!==void 0){console.log(`%c[DEBUG] %c${t} %cevent already has a debug receiver.`,"color: red; font-weight: bold;","color: green","");return}Sn[t]=e}}async function Ms(t,e){if(!en)return;const n=Sn[t];return n===void 0?(console.log(`[DEBUG] ${t} event does not have a debugger.`),{}):await n(e)}const Ls=""+new URL("confirmation_001.ogg",import.meta.url).href,zs=""+new URL("question_002.ogg",import.meta.url).href,Rs=""+new URL("maximize_006.ogg",import.meta.url).href,Ns=""+new URL("minimize_006.ogg",import.meta.url).href,Ps=""+new URL("drop_003.ogg",import.meta.url).href,Us=""+new URL("drop_004.ogg",import.meta.url).href,Ws=""+new URL("drop_002.ogg",import.meta.url).href,St={win:new Audio(Ls),lose:new Audio(zs),start:new Audio(Rs),finish:new Audio(Ns),primary:new Audio(Ps),secondary:new Audio(Ws),iteration:new Audio(Us)},Ds=()=>{const t=wt({active:!1});return{...t,...{start:n=>{let{type:r,iterations:l,config:i}=n;l=l||1,St.start.volume=.5,St.start.play(),t.update(s=>({active:!0,type:r,iterations:l,config:i}))},finish:(n=!1)=>{St.finish.volume=.5,St.finish.play(),t.update(r=>(Kl($t.finish,n),{active:!1}))},playSound:n=>{const r=St[n];r.volume=.5,r.currentTime=0,r.play()}}}},R=Ds(),Os=[{action:ke.visible,handler:t=>{}},{action:ke.start,handler:t=>{R.start(t)}}];function Fs(){for(const t of Os)Bl(t.action,t.handler)}function Vn(t){let e,n;const r=t[2].default,l=$e(r,t,t[1],null);return{c(){e=G("main"),l&&l.c(),h(e,"class","svelte-1w09ye1")},m(i,s){D(i,e,s),l&&l.m(e,null),n=!0},p(i,s){l&&l.p&&(!n||s&2)&&et(l,r,i,i[1],n?xe(r,i[1],s,null):tt(i[1]),null)},i(i){n||(C(l,i),n=!0)},o(i){P(l,i),n=!1},d(i){i&&W(e),l&&l.d(i)}}}function Gs(t){let e,n,r=t[0]&&Vn(t);return{c(){r&&r.c(),e=pe()},m(l,i){r&&r.m(l,i),D(l,e,i),n=!0},p(l,[i]){l[0]?r?(r.p(l,i),i&1&&C(r,1)):(r=Vn(l),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(le(),P(r,1,1,()=>{r=null}),se())},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d(l)}}}function js(t,e,n){let r,l,i;fe(t,R,o=>n(3,r=o)),fe(t,jn,o=>n(0,l=o)),fe(t,Nn,o=>n(4,i=o));let{$$slots:s={},$$scope:a}=e;return Bl(ke.visible,o=>{os(jn,l=o,l)}),zt(()=>{if(!i.allowEscapeKey)return;const o=u=>{l&&["Escape"].includes(u.code)&&r.active&&R.finish(!1)};return window.addEventListener("keydown",o),()=>window.removeEventListener("keydown",o)}),t.$$set=o=>{"$$scope"in o&&n(1,a=o.$$scope)},[l,a,s]}class Vs extends he{constructor(e){super(),de(this,e,js,Gs,ce,{})}}function In(t){Ie(ke.visible,t)}const Ks=[{label:"Visible",action:()=>In(!0),delay:500}];function Bs(){for(const t of Ks)setTimeout(()=>{t.action()},t.delay||0)}const Hs=[{action:$t.close,handler:()=>{}}];function qs(){for(const t of Hs)Ts(t.action,t.handler)}var ue=(t=>(t.CircleProgress="CircleProgress",t.Progress="Progress",t.KeyCircle="KeyCircle",t.KeySpam="KeySpam",t.NumberSlide="NumberSlide",t.RapidLines="RapidLines",t.CircleShake="CircleShake",t.PathFind="PathFind",t.Untangle="Untangle",t.LightsOut="LightsOut",t.DigitDazzle="DigitDazzle",t.WordWiz="WordWiz",t.CircleSum="CircleSum",t.WaveMatch="WaveMatch",t.MineSweeper="MineSweeper",t.PrintLock="PrintLock",t.HexaPipe="HexaPipe",t))(ue||{});const Pt=[{label:"Visibility",actions:[{label:"True",action:()=>In(!0)},{label:"False",action:()=>In(!1)}]},{label:"CircleProgress",actions:[{label:"Custom Difficulty",action:t=>{const e={difficulty:t};Ie(ke.start,{type:ue.CircleProgress,iterations:2,config:e})},value:50,type:"slider"}]},{label:"Progress",actions:[{label:"Custom Difficulty",action:t=>{const e={difficulty:t};Ie(ke.start,{type:ue.Progress,iterations:2,config:e})},value:50,type:"slider"}]},{label:"KeyCircle",actions:[{label:"Custom Difficulty",action:t=>{const e={difficulty:t,numberOfKeys:3};Ie(ke.start,{type:ue.KeyCircle,iterations:2,config:e})},value:50,type:"slider"}]},{label:"KeySpam",actions:[{label:"Custom Difficulty",action:t=>{const e={difficulty:t};Ie(ke.start,{type:ue.KeySpam,iterations:2,config:e})},value:50,type:"slider"}]},{label:"NumberSlide",actions:[{label:"Custom Difficulty",action:t=>{const e={difficulty:t,numberOfKeys:4};Ie(ke.start,{type:ue.NumberSlide,iterations:2,config:e})},value:50,type:"slider"}]},{label:"RapidLines",actions:[{label:"Custom Difficulty",action:t=>{const e={difficulty:t,numberOfKeys:4};Ie(ke.start,{type:ue.RapidLines,iterations:2,config:e})},value:50,type:"slider"}]},{label:"CircleShake",actions:[{label:"Custom Difficulty",action:t=>{const e={difficulty:t,numberOfKeys:2};Ie(ke.start,{type:ue.CircleShake,iterations:2,config:e})},value:50,type:"slider"}]},{label:"PathFind",actions:[{label:"Duration",action:t=>{const e={duration:t*1e3,numberOfNodes:10};Ie(ke.start,{type:ue.PathFind,iterations:2,config:e})},value:50,type:"slider"}]},{label:"Untangle",actions:[{label:"Duration",action:t=>{const e={duration:t*1e3,numberOfNodes:10};Ie(ke.start,{type:ue.Untangle,iterations:2,config:e})},min:1,value:50,type:"slider"}]},{label:"Lights Out",actions:[{label:"Duration",action:t=>{const e={duration:3e4,level:t};Ie(ke.start,{type:ue.LightsOut,iterations:2,config:e})},value:0,min:0,max:50,type:"slider"}]},{label:"Digit Dazzle",actions:[{label:"Custom Difficulty",action:t=>{const e={duration:2e4,length:t};Ie(ke.start,{type:ue.DigitDazzle,iterations:2,config:e})},value:4,min:1,max:12,type:"slider"},{label:"Input",action:t=>{const e={duration:2e4,code:[t,t]};Ie(ke.start,{type:ue.DigitDazzle,iterations:2,config:e})},value:1234,type:"text"}]},{label:"Word Wiz",actions:[{label:"Custom Difficulty",action:t=>{const e={duration:2e4,length:t};Ie(ke.start,{type:ue.WordWiz,iterations:2,config:e})},value:4,min:1,max:12,type:"slider"}]},{label:"Circle Sum",actions:[{label:"Custom Difficulty",action:t=>{const e={duration:1e4,length:t};Ie(ke.start,{type:ue.CircleSum,iterations:2,config:e})},value:4,min:1,max:12,type:"slider"}]},{label:"Wave Match",actions:[{label:"Custom Difficulty",action:t=>{const e={duration:t};Ie(ke.start,{type:ue.WaveMatch,iterations:2,config:e})},value:2e4,min:1e3,max:12e5,type:"slider"}]},{label:"Mine Sweeper",actions:[{label:"Custom Difficulty",action:t=>{const e={duration:t,grid:7,target:10};Ie(ke.start,{type:ue.MineSweeper,iterations:2,config:e})},value:2e4,min:1e3,max:12e5,type:"slider"}]},{label:"Print Lock",actions:[{label:"Custom Difficulty",action:t=>{const e={duration:6e4,grid:5,target:10};Ie(ke.start,{type:ue.PrintLock,iterations:2,config:e})},value:5,min:3,max:10,type:"slider"}]}],Ys=""+new URL("debug_image.png",import.meta.url).href;function Kn(t,e,n){const r=t.slice();return r[10]=e[n].label,r[11]=e[n].actions,r}function Bn(t,e,n){const r=t.slice();return r[14]=e[n],r[15]=e,r[16]=n,r}function Hn(t){let e,n=re(t[0]),r=[];for(let l=0;l<n.length;l+=1)r[l]=Yn(Kn(t,n,l));return{c(){e=G("ol");for(let l=0;l<r.length;l+=1)r[l].c();h(e,"class","flex flex-col gap-2 bg-primary z-[9999999] max-w-[25vw] h-[80vh] overflow-y-auto px-[0.5vw] py-[0.5vw]")},m(l,i){D(l,e,i);for(let s=0;s<r.length;s+=1)r[s]&&r[s].m(e,null)},p(l,i){if(i&1){n=re(l[0]);let s;for(s=0;s<n.length;s+=1){const a=Kn(l,n,s);r[s]?r[s].p(a,i):(r[s]=Yn(a),r[s].c(),r[s].m(e,null))}for(;s<r.length;s+=1)r[s].d(1);r.length=n.length}},d(l){l&&W(e),Se(r,l)}}}function Zs(t){let e,n=t[14].label+"",r,l,i;function s(){return t[9](t[14])}return{c(){e=G("button"),r=Ee(n),h(e,"class","w-full px-[0.5vw] py-[0.25vw] bg-accent")},m(a,o){D(a,e,o),L(e,r),l||(i=ge(e,"click",s),l=!0)},p(a,o){t=a,o&1&&n!==(n=t[14].label+"")&&ze(r,n)},d(a){a&&W(e),l=!1,i()}}}function Xs(t){let e,n,r=t[14].label+"",l,i,s,a,o,u,f,c,d,g;function p(){t[7].call(s,t[15],t[16])}function _(){return t[8](t[14])}return{c(){e=G("span"),n=G("p"),l=Ee(r),i=H(),s=G("input"),f=H(),c=G("button"),c.textContent="Action",h(s,"type","range"),h(s,"class","w-full"),h(s,"min",a=t[14].min||0),h(s,"max",o=t[14].max||100),h(s,"step",u=t[14].step||1),h(c,"class","px-[0.5vw] py-[0.25vw] w-[5vw] bg-primary border-secondary border-2"),h(e,"class","w-full px-[0.5vw] py-[0.25vw] flex flex-col gap-[0.2vw] bg-accent items-start")},m(y,S){D(y,e,S),L(e,n),L(n,l),L(e,i),L(e,s),_t(s,t[14].value),L(e,f),L(e,c),d||(g=[ge(s,"change",p),ge(s,"input",p),ge(c,"click",_)],d=!0)},p(y,S){t=y,S&1&&r!==(r=t[14].label+"")&&ze(l,r),S&1&&a!==(a=t[14].min||0)&&h(s,"min",a),S&1&&o!==(o=t[14].max||100)&&h(s,"max",o),S&1&&u!==(u=t[14].step||1)&&h(s,"step",u),S&1&&_t(s,t[14].value)},d(y){y&&W(e),d=!1,Le(g)}}}function Js(t){let e,n,r=t[14].label+"",l,i,s,a,o;function u(){t[5].call(s,t[15],t[16])}function f(...c){return t[6](t[14],...c)}return{c(){e=G("span"),n=G("p"),l=Ee(r),i=H(),s=G("input"),h(s,"type","checkbox"),h(s,"class","h-full aspect-square"),h(e,"class","w-full px-[0.5vw] py-[0.25vw] flex flex-row gap-[0.2vw] bg-accent items-center")},m(c,d){D(c,e,d),L(e,n),L(n,l),L(e,i),L(e,s),s.checked=t[14].value,a||(o=[ge(s,"change",u),ge(s,"input",f)],a=!0)},p(c,d){t=c,d&1&&r!==(r=t[14].label+"")&&ze(l,r),d&1&&(s.checked=t[14].value)},d(c){c&&W(e),a=!1,Le(o)}}}function Qs(t){let e,n,r=t[14].label+"",l,i,s,a,o,u,f;function c(){t[3].call(s,t[15],t[16])}function d(){return t[4](t[14])}return{c(){e=G("span"),n=G("p"),l=Ee(r),i=H(),s=G("input"),a=H(),o=G("button"),o.textContent="Apply",h(n,"class",""),h(s,"type","text"),h(s,"class","h-full w-full px-[0.25vw] text-black"),h(o,"class","px-[0.5vw] py-[0.25vw] w-[5vw] bg-primary"),h(e,"class","w-full px-[0.5vw] py-[0.25vw] flex flex-col gap-[0.2vw] bg-accent items-start")},m(g,p){D(g,e,p),L(e,n),L(n,l),L(e,i),L(e,s),_t(s,t[14].value),L(e,a),L(e,o),u||(f=[ge(s,"input",c),ge(o,"click",d)],u=!0)},p(g,p){t=g,p&1&&r!==(r=t[14].label+"")&&ze(l,r),p&1&&s.value!==t[14].value&&_t(s,t[14].value)},d(g){g&&W(e),u=!1,Le(f)}}}function qn(t){let e;function n(i,s){return i[14].type==="text"?Qs:i[14].type==="checkbox"?Js:i[14].type==="slider"?Xs:Zs}let r=n(t),l=r(t);return{c(){e=G("div"),l.c(),h(e,"class","flex flex-row flex-wrap gap-[0.5vw]")},m(i,s){D(i,e,s),l.m(e,null)},p(i,s){r===(r=n(i))&&l?l.p(i,s):(l.d(1),l=r(i),l&&(l.c(),l.m(e,null)))},d(i){i&&W(e),l.d()}}}function Yn(t){let e,n,r=t[10]+"",l,i,s,a=re(t[11]),o=[];for(let u=0;u<a.length;u+=1)o[u]=qn(Bn(t,a,u));return{c(){e=G("li"),n=G("span"),l=Ee(r),i=H();for(let u=0;u<o.length;u+=1)o[u].c();s=H(),h(n,"class","w-full"),h(e,"class","flex flex-col gap-1 border-l-[2px] border-accent px-[0.25vw]")},m(u,f){D(u,e,f),L(e,n),L(n,l),L(e,i);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(e,null);L(e,s)},p(u,f){if(f&1&&r!==(r=u[10]+"")&&ze(l,r),f&1){a=re(u[11]);let c;for(c=0;c<a.length;c+=1){const d=Bn(u,a,c);o[c]?o[c].p(d,f):(o[c]=qn(d),o[c].c(),o[c].m(e,s))}for(;c<o.length;c+=1)o[c].d(1);o.length=a.length}},d(u){u&&W(e),Se(o,u)}}}function $s(t){let e,n,r,l,i,s,a,o=t[1]&&Hn(t);return{c(){e=G("div"),n=G("button"),n.textContent="Debug",r=H(),o&&o.c(),l=H(),i=G("div"),h(n,"class","px-[1vw] py-[0.5vw] w-fit h-fit z-[9999999] bg-accent"),Y(i,"background-image","url("+Ys+")"),h(i,"class","absolute w-screen bg-cover bg-no-repeat bg-center h-screen top-0 left-0 dev-image"),h(e,"class","w-fit h-fit flex flex-col z-[9999999]")},m(u,f){D(u,e,f),L(e,n),L(e,r),o&&o.m(e,null),L(e,l),L(e,i),s||(a=ge(n,"click",t[2]),s=!0)},p(u,[f]){u[1]?o?o.p(u,f):(o=Hn(u),o.c(),o.m(e,l)):o&&(o.d(1),o=null)},i:me,o:me,d(u){u&&W(e),o&&o.d(),s=!1,a()}}}function xs(t,e,n){zt(()=>{Bs(),qs()});let r=!1;const l=()=>n(1,r=!r);function i(d,g){d[g].value=this.value,n(0,Pt)}const s=d=>{d.action(d.value)};function a(d,g){d[g].value=this.checked,n(0,Pt)}const o=(d,g)=>{d.action(d.value)};function u(d,g){d[g].value=Ol(this.value),n(0,Pt)}return[Pt,r,l,i,s,a,o,u,d=>{d.action(d.value)},d=>{d.action()}]}class ei extends he{constructor(e){super(),de(this,e,xs,$s,ce,{})}}function Hl(t){var e,n,r="";if(typeof t=="string"||typeof t=="number")r+=t;else if(typeof t=="object")if(Array.isArray(t)){var l=t.length;for(e=0;e<l;e++)t[e]&&(n=Hl(t[e]))&&(r&&(r+=" "),r+=n)}else for(n in t)t[n]&&(r&&(r+=" "),r+=n);return r}function ti(){for(var t,e,n=0,r="",l=arguments.length;n<l;n++)(t=arguments[n])&&(e=Hl(t))&&(r&&(r+=" "),r+=e);return r}const Pn="-";function ni(t){const e=li(t),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=t;function l(s){const a=s.split(Pn);return a[0]===""&&a.length!==1&&a.shift(),ql(a,e)||ri(s)}function i(s,a){const o=n[s]||[];return a&&r[s]?[...o,...r[s]]:o}return{getClassGroupId:l,getConflictingClassGroupIds:i}}function ql(t,e){var s;if(t.length===0)return e.classGroupId;const n=t[0],r=e.nextPart.get(n),l=r?ql(t.slice(1),r):void 0;if(l)return l;if(e.validators.length===0)return;const i=t.join(Pn);return(s=e.validators.find(({validator:a})=>a(i)))==null?void 0:s.classGroupId}const Zn=/^\[(.+)\]$/;function ri(t){if(Zn.test(t)){const e=Zn.exec(t)[1],n=e==null?void 0:e.substring(0,e.indexOf(":"));if(n)return"arbitrary.."+n}}function li(t){const{theme:e,prefix:n}=t,r={nextPart:new Map,validators:[]};return ii(Object.entries(t.classGroups),n).forEach(([i,s])=>{Cn(s,r,i,e)}),r}function Cn(t,e,n,r){t.forEach(l=>{if(typeof l=="string"){const i=l===""?e:Xn(e,l);i.classGroupId=n;return}if(typeof l=="function"){if(si(l)){Cn(l(r),e,n,r);return}e.validators.push({validator:l,classGroupId:n});return}Object.entries(l).forEach(([i,s])=>{Cn(s,Xn(e,i),n,r)})})}function Xn(t,e){let n=t;return e.split(Pn).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n}function si(t){return t.isThemeGetter}function ii(t,e){return e?t.map(([n,r])=>{const l=r.map(i=>typeof i=="string"?e+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,a])=>[e+s,a])):i);return[n,l]}):t}function oi(t){if(t<1)return{get:()=>{},set:()=>{}};let e=0,n=new Map,r=new Map;function l(i,s){n.set(i,s),e++,e>t&&(e=0,r=n,n=new Map)}return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return l(i,s),s},set(i,s){n.has(i)?n.set(i,s):l(i,s)}}}const Yl="!";function ai(t){const{separator:e,experimentalParseClassName:n}=t,r=e.length===1,l=e[0],i=e.length;function s(a){const o=[];let u=0,f=0,c;for(let y=0;y<a.length;y++){let S=a[y];if(u===0){if(S===l&&(r||a.slice(y,y+i)===e)){o.push(a.slice(f,y)),f=y+i;continue}if(S==="/"){c=y;continue}}S==="["?u++:S==="]"&&u--}const d=o.length===0?a:a.substring(f),g=d.startsWith(Yl),p=g?d.substring(1):d,_=c&&c>f?c-f:void 0;return{modifiers:o,hasImportantModifier:g,baseClassName:p,maybePostfixModifierPosition:_}}return n?function(o){return n({className:o,parseClassName:s})}:s}function ui(t){if(t.length<=1)return t;const e=[];let n=[];return t.forEach(r=>{r[0]==="["?(e.push(...n.sort(),r),n=[]):n.push(r)}),e.push(...n.sort()),e}function ci(t){return{cache:oi(t.cacheSize),parseClassName:ai(t),...ni(t)}}const fi=/\s+/;function di(t,e){const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:l}=e,i=new Set;return t.trim().split(fi).map(s=>{const{modifiers:a,hasImportantModifier:o,baseClassName:u,maybePostfixModifierPosition:f}=n(s);let c=!!f,d=r(c?u.substring(0,f):u);if(!d){if(!c)return{isTailwindClass:!1,originalClassName:s};if(d=r(u),!d)return{isTailwindClass:!1,originalClassName:s};c=!1}const g=ui(a).join(":");return{isTailwindClass:!0,modifierId:o?g+Yl:g,classGroupId:d,originalClassName:s,hasPostfixModifier:c}}).reverse().filter(s=>{if(!s.isTailwindClass)return!0;const{modifierId:a,classGroupId:o,hasPostfixModifier:u}=s,f=a+o;return i.has(f)?!1:(i.add(f),l(o,u).forEach(c=>i.add(a+c)),!0)}).reverse().map(s=>s.originalClassName).join(" ")}function hi(){let t=0,e,n,r="";for(;t<arguments.length;)(e=arguments[t++])&&(n=Zl(e))&&(r&&(r+=" "),r+=n);return r}function Zl(t){if(typeof t=="string")return t;let e,n="";for(let r=0;r<t.length;r++)t[r]&&(e=Zl(t[r]))&&(n&&(n+=" "),n+=e);return n}function gi(t,...e){let n,r,l,i=s;function s(o){const u=e.reduce((f,c)=>c(f),t());return n=ci(u),r=n.cache.get,l=n.cache.set,i=a,a(o)}function a(o){const u=r(o);if(u)return u;const f=di(o,n);return l(o,f),f}return function(){return i(hi.apply(null,arguments))}}function _e(t){const e=n=>n[t]||[];return e.isThemeGetter=!0,e}const Xl=/^\[(?:([a-z-]+):)?(.+)\]$/i,mi=/^\d+\/\d+$/,pi=new Set(["px","full","screen"]),bi=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,_i=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,wi=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,vi=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,yi=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;function Ge(t){return Je(t)||pi.has(t)||mi.test(t)}function He(t){return vt(t,"length",Mi)}function Je(t){return!!t&&!Number.isNaN(Number(t))}function Ut(t){return vt(t,"number",Je)}function It(t){return!!t&&Number.isInteger(Number(t))}function ki(t){return t.endsWith("%")&&Je(t.slice(0,-1))}function ne(t){return Xl.test(t)}function qe(t){return bi.test(t)}const Si=new Set(["length","size","percentage"]);function Ii(t){return vt(t,Si,Jl)}function Ci(t){return vt(t,"position",Jl)}const Ei=new Set(["image","url"]);function Ai(t){return vt(t,Ei,zi)}function Ti(t){return vt(t,"",Li)}function Ct(){return!0}function vt(t,e,n){const r=Xl.exec(t);return r?r[1]?typeof e=="string"?r[1]===e:e.has(r[1]):n(r[2]):!1}function Mi(t){return _i.test(t)&&!wi.test(t)}function Jl(){return!1}function Li(t){return vi.test(t)}function zi(t){return yi.test(t)}function Ri(){const t=_e("colors"),e=_e("spacing"),n=_e("blur"),r=_e("brightness"),l=_e("borderColor"),i=_e("borderRadius"),s=_e("borderSpacing"),a=_e("borderWidth"),o=_e("contrast"),u=_e("grayscale"),f=_e("hueRotate"),c=_e("invert"),d=_e("gap"),g=_e("gradientColorStops"),p=_e("gradientColorStopPositions"),_=_e("inset"),y=_e("margin"),S=_e("opacity"),I=_e("padding"),M=_e("saturate"),K=_e("scale"),E=_e("sepia"),b=_e("skew"),A=_e("space"),U=_e("translate"),m=()=>["auto","contain","none"],w=()=>["auto","hidden","clip","visible","scroll"],z=()=>["auto",ne,e],T=()=>[ne,e],k=()=>["",Ge,He],F=()=>["auto",Je,ne],j=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],N=()=>["solid","dashed","dotted","double","none"],v=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],O=()=>["start","end","center","between","around","evenly","stretch"],q=()=>["","0",ne],B=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Z=()=>[Je,Ut],Q=()=>[Je,ne];return{cacheSize:500,separator:":",theme:{colors:[Ct],spacing:[Ge,He],blur:["none","",qe,ne],brightness:Z(),borderColor:[t],borderRadius:["none","","full",qe,ne],borderSpacing:T(),borderWidth:k(),contrast:Z(),grayscale:q(),hueRotate:Q(),invert:q(),gap:T(),gradientColorStops:[t],gradientColorStopPositions:[ki,He],inset:z(),margin:z(),opacity:Z(),padding:T(),saturate:Z(),scale:Z(),sepia:q(),skew:Q(),space:T(),translate:T()},classGroups:{aspect:[{aspect:["auto","square","video",ne]}],container:["container"],columns:[{columns:[qe]}],"break-after":[{"break-after":B()}],"break-before":[{"break-before":B()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...j(),ne]}],overflow:[{overflow:w()}],"overflow-x":[{"overflow-x":w()}],"overflow-y":[{"overflow-y":w()}],overscroll:[{overscroll:m()}],"overscroll-x":[{"overscroll-x":m()}],"overscroll-y":[{"overscroll-y":m()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[_]}],"inset-x":[{"inset-x":[_]}],"inset-y":[{"inset-y":[_]}],start:[{start:[_]}],end:[{end:[_]}],top:[{top:[_]}],right:[{right:[_]}],bottom:[{bottom:[_]}],left:[{left:[_]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",It,ne]}],basis:[{basis:z()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",ne]}],grow:[{grow:q()}],shrink:[{shrink:q()}],order:[{order:["first","last","none",It,ne]}],"grid-cols":[{"grid-cols":[Ct]}],"col-start-end":[{col:["auto",{span:["full",It,ne]},ne]}],"col-start":[{"col-start":F()}],"col-end":[{"col-end":F()}],"grid-rows":[{"grid-rows":[Ct]}],"row-start-end":[{row:["auto",{span:[It,ne]},ne]}],"row-start":[{"row-start":F()}],"row-end":[{"row-end":F()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",ne]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",ne]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",...O()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...O(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...O(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[I]}],px:[{px:[I]}],py:[{py:[I]}],ps:[{ps:[I]}],pe:[{pe:[I]}],pt:[{pt:[I]}],pr:[{pr:[I]}],pb:[{pb:[I]}],pl:[{pl:[I]}],m:[{m:[y]}],mx:[{mx:[y]}],my:[{my:[y]}],ms:[{ms:[y]}],me:[{me:[y]}],mt:[{mt:[y]}],mr:[{mr:[y]}],mb:[{mb:[y]}],ml:[{ml:[y]}],"space-x":[{"space-x":[A]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[A]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",ne,e]}],"min-w":[{"min-w":[ne,e,"min","max","fit"]}],"max-w":[{"max-w":[ne,e,"none","full","min","max","fit","prose",{screen:[qe]},qe]}],h:[{h:[ne,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[ne,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[ne,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[ne,e,"auto","min","max","fit"]}],"font-size":[{text:["base",qe,He]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ut]}],"font-family":[{font:[Ct]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",ne]}],"line-clamp":[{"line-clamp":["none",Je,Ut]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Ge,ne]}],"list-image":[{"list-image":["none",ne]}],"list-style-type":[{list:["none","disc","decimal",ne]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[S]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[S]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...N(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Ge,He]}],"underline-offset":[{"underline-offset":["auto",Ge,ne]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ne]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ne]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[S]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...j(),Ci]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Ii]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Ai]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[p]}],"gradient-via-pos":[{via:[p]}],"gradient-to-pos":[{to:[p]}],"gradient-from":[{from:[g]}],"gradient-via":[{via:[g]}],"gradient-to":[{to:[g]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[S]}],"border-style":[{border:[...N(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[S]}],"divide-style":[{divide:N()}],"border-color":[{border:[l]}],"border-color-x":[{"border-x":[l]}],"border-color-y":[{"border-y":[l]}],"border-color-t":[{"border-t":[l]}],"border-color-r":[{"border-r":[l]}],"border-color-b":[{"border-b":[l]}],"border-color-l":[{"border-l":[l]}],"divide-color":[{divide:[l]}],"outline-style":[{outline:["",...N()]}],"outline-offset":[{"outline-offset":[Ge,ne]}],"outline-w":[{outline:[Ge,He]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:k()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[S]}],"ring-offset-w":[{"ring-offset":[Ge,He]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",qe,Ti]}],"shadow-color":[{shadow:[Ct]}],opacity:[{opacity:[S]}],"mix-blend":[{"mix-blend":[...v(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":v()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[o]}],"drop-shadow":[{"drop-shadow":["","none",qe,ne]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[c]}],saturate:[{saturate:[M]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[o]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[S]}],"backdrop-saturate":[{"backdrop-saturate":[M]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",ne]}],duration:[{duration:Q()}],ease:[{ease:["linear","in","out","in-out",ne]}],delay:[{delay:Q()}],animate:[{animate:["none","spin","ping","pulse","bounce",ne]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[K]}],"scale-x":[{"scale-x":[K]}],"scale-y":[{"scale-y":[K]}],rotate:[{rotate:[It,ne]}],"translate-x":[{"translate-x":[U]}],"translate-y":[{"translate-y":[U]}],"skew-x":[{"skew-x":[b]}],"skew-y":[{"skew-y":[b]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",ne]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ne]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ne]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[Ge,He,Ut]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}const Ni=gi(Ri),Pi={2:["aa","ab","ad","ae","ag","ah","ai","al","am","an","ar","as","at","aw","ax","ay","ba","be","bi","bo","by","da","de","do","ed","ef","eh","el","em","en","er","es","et","ex","fa","go","ha","he","hi","ho","id","if","in","is","it","jo","ka","la","li","lo","ma","me","mi","mu","my","na","no","nu","od","oe","of","oh","oi","om","on","op","or","os","ow","ox","oy","pa","pe","pi","re","sh","si","so","ta","ti","to","uh","um","un","up","us","ut","we","wo","xi","ye","yo","za"],3:["the","and","for","are","but","not","you","all","any","can","had","her","was","one","our","out","day","get","has","him","his","how","man","new","now","old","see","two","way","who","boy","did","its","let","put","say","she","too","use","cat","dog","end","fun","got","hit","ice","jam","key","lie","mad","net","oil","pen","rat","sun","top","van","win","yes","yet","big","car","cup","egg","fan","hat","job","leg","map","nap","pad","run","sip","tie","urn","vet","wet","zip","box","fix","fox","gym","hug","kid","log","mix","nod","pop","rug","sit","tan","vet","war","zoo"],4:["able","also","area","away","back","ball","band","base","bath","bear","beat","been","belt","best","bill","bird","blow","blue","boat","body","book","born","both","call","calm","camp","card","care","case","cash","city","club","cold","come","core","cost","crop","data","date","deal","deep","desk","diet","dirt","disk","door","draw","drop","easy","edge","else","face","fact","fair","fall","fast","feel","file","find","fire","flat","flow","food","foot","ford","four","free","game","gear","gift","give","good","grand","grey","grow","half","hand","hard","hate","head","hear","help","high","home","hope","hour","idea","iron","join","just","keep","kill","kind","lack","land","late","life","line","list","live","long","look","love","make","mark","mean","meet","more","name","need","news","note","open","pair","park","part","play","post","pull","pure","race","rate","read","real","rest","ring","road","rock","roll","rose","rule","safe","sale","save","seat","sell","send","shop","side","sign","site","size","slow","sofa","some","stop","show","star","step","stop","take","talk","team","tell","test","text","time","type","unit","view","vote","wait","walk","wall","want","warm","wash","way","wear","week","wind","wish","wood","work","yard","year","yell","zone","zone","zero","bank","bell","bowl","chip","cook","dine","duck","fish","flip","fold","gold","hate","hope","idea","join","kill","look","moon","nail","open"],5:["apple","about","beach","begin","black","blame","brave","bring","check","chose","clear","clock","craft","dance","death","depth","drink","earth","enjoy","enter","equal","fetch","fight","focus","force","found","front","globe","grass","great","grind","guide","happy","heart","house","humor","ideal","image","indie","input","judge","knife","learn","lemon","liver","loved","lucky","major","march","match","media","music","never","night","noise","north","ocean","offer","party","peace","plant","play","price","proof","quick","quiet","radio","rainy","reach","ready","relax","right","river","robin","rocky","round","serve","share","short","shore","silly","smart","snowy","speak","speed","spend","staff","start","steal","store","sweet","table","teach","thank","think","touch","train","trust","value","video","visit","water","wheel","white","world","write","wrong","youth","zebra","zoned","happy","stand","smart","close","baker","bring","board","drain","dance","fight","focus","frame","guide","hobby","hotel","ideal","jewel","laugh","lemon","model","mount","nurse","plant","quick","radio","space","spoon","storm","sweet","train","treat","water","wheat","young","zebra","world","alarm","bliss","brave","bliss","crane","drive","enter","enemy","flash","ghost","hotel","index","jolly","media","queen","smile","track","under","vivid","water","zoned","break","build","catch"],6:["abandon","admire","advice","airport","amount","animal","baking","banner","battle","beauty","belong","beyond","bottle","brandy","breeze","bright","broke","burden","candle","career","cattle","change","cherry","circle","climb","cruise","custom","damage","danger","decline","decent","design","driver","drown","effort","entire","exceed","expand","family","famous","finish","forget","friend","gather","gently","guitar","impact","insert","invite","jacket","jungle","knight","letter","lively","magnet","market","nature","navigate","office","outcome","partner","patient","people","period","police","press","price","rescue","result","school","season","safety","sample","search","service","simple","social","stages","storage","studio","sudden","system","target","theory","ticket","unite","victim","wonder","writer","yellow","zephyr","admire","album","appeal","affect","beyond","bubble","candle","cheese","circle","collect","damage","dinner","economy","exceed","forgot","future","gather","garbage","history","jacket","jungle","lender","margin","manage","morning","notice","outlet","plastic","project","public","signal","simple","silent","social","structure","thread","uncover","victory","window","xenon","yellow","zodiac","across","breeze","bricks","capital","common","custom","decade","distant","elegant","figure","freedom","golden","letter","modest","morning","museum","office","outcome","paper","police","produce","remain","rescue","reward","sensor","standard","student","submit","summer","travel","unfold","village","wonder","wallet","waste","worry","yellow"],7:["ability","absolve","amazing","analyze","balance","beyond","capture","carpet","charm","climate","commit","contrast","courage","creature","cultural","dancing","decide","delight","disease","disturb","distant","economy","elegant","endless","engage","enjoyed","evolve","famous","flavor","follower","foreign","genuine","guitar","horizon","improve","inspire","journey","kitchen","library","machine","maturity","medical","morning","narrow","network","outcome","partner","picture","police","positive","practice","predict","process","product","reality","rewards","succeed","surgery","theory","trustee","twinkle","utility","vacation","version","welfare","witness","absence","adviser","amazing","approach","bravery","captain","changing","charming","clarify","complete","connect","constant","counsel","default","delight","decrease","discuss","dynamic","enjoyed","essence","failure","fashion","feature","freedom","friendly","gather","general","happens","imagine","improve","inspire","install","journey","leading","medicine","message","momentum","neither","organize","outreach","package","pleased","progress","reduced","refresh","reliable","respond","simple","society","success","suitable","system","together","uncover","variable","wonderful","advance","attempt","captured","clarity","directly","election","expression","happily","inspire","journey","justified","leisure","majority","obtain","organize","precise","present","reduced","resolve","strength","transfer","training","uncover","useless","valuable","welfare","winning","workout"],8:["abundant","absolute","absolute","academy","activity","adventure","allegory","amazing","analyze","appeared","beginner","believing","brilliant","brought","capable","careful","celebrate","ceremony","charming","complete","conflict","consumer","creature","criteria","delicate","democracy","deriving","diamonds","different","disaster","efficient","elevated","evident","explore","fashioned","feature","freedom","friendly","generation","harmony","important","influence","inspire","internet","justice","lending","literary","material","medicate","narrative","obstacle","operator","partner","reliably","routine","significant","succeed","technical","temporary","training","uncover","understand","variable","village","beautiful","abiding","advisory","apparent","arrival","atmosphere","backdrop","balance","benefits","campaign","complete","creative","dominant","drawn","element","familiar","glorious","hospitable","inclusive","inspire","journey","keynote","limiting","management","network","opinion","partner","persistent","positive","powerful","premium","process","reliable","relevant","response","sensory","sensible","silently","solution","strategy","succeed","uncover","united","valuable","wilderness","workshop","workout","zealous","ambition","compose","dangerous","emphasis","frequent","informal","magnetic","obstacle","optimal","prominent","radiation","recovery","remarkable","reviewed","sensitive","socialize","suffering","talented","unveiled","utilize","vitality","worldwide","wonderful","awesome","brilliant","calibrate","improve","instruct","mastered","priority","radiation","revealed"]},Un=Math.PI,Ui=Math.cos,Wi=Math.sin,Di=Math.sqrt,Jn=Math.pow,Oi=Math.atan2,Wn=Math.floor,tn=Math.random,Fi=JSON.parse,Gi=JSON.stringify;async function ve(t){return new Promise(e=>setTimeout(e,t))}function ji(t,e,n,r){const l=r-e,i=n-t;return Oi(l,i)*180/Un}function it(t){return t>180?t-360:t}function Vi(t){return t*(Un/180)}function Ke(t,e){return tn()*(e-t)+t}function Ki(t,e,n,r){return Di(Jn(n-t,2)+Jn(r-e,2))}function Bt(...t){return Ni(ti(t))}function we(t){let[e,n]=[null,null];return Array.isArray(t)?(e=t[0],n=t[1]):(e=t,n=t),Wn(e+tn()*(n-e))}function Bi(t){return Array.from({length:t},()=>Wn(tn()*10))}function Hi(t){const e=Pi[t],n=Wn(tn()*e.length);return e[n]}function Qn(t){return Fi(Gi(t))}function $n(t,e,n,r){let l=(r-90)*Un/180;return{x:t+n*Ui(l),y:e+n*Wi(l)}}function xn(t){return Object.prototype.toString.call(t)==="[object Date]"}function Ql(t){return t<.5?4*t*t*t:.5*Math.pow(2*t-2,3)+1}function qi(t){const e=t-1;return e*e*e+1}function En(t,e){if(t===e||t!==t)return()=>t;const n=typeof t;if(n!==typeof e||Array.isArray(t)!==Array.isArray(e))throw new Error("Cannot interpolate values of different type");if(Array.isArray(t)){const r=e.map((l,i)=>En(t[i],l));return l=>r.map(i=>i(l))}if(n==="object"){if(!t||!e)throw new Error("Object cannot be null");if(xn(t)&&xn(e)){t=t.getTime(),e=e.getTime();const i=e-t;return s=>new Date(t+s*i)}const r=Object.keys(e),l={};return r.forEach(i=>{l[i]=En(t[i],e[i])}),i=>{const s={};return r.forEach(a=>{s[a]=l[a](i)}),s}}if(n==="number"){const r=e-t;return l=>t+l*r}throw new Error(`Cannot interpolate ${n} values`)}function Me(t,e={}){const n=wt(t);let r,l=t;function i(s,a){if(t==null)return n.set(t=s),Promise.resolve();l=s;let o=r,u=!1,{delay:f=0,duration:c=400,easing:d=Yt,interpolate:g=En}=Re(Re({},e),a);if(c===0)return o&&(o.abort(),o=null),n.set(t=l),Promise.resolve();const p=Xt()+f;let _;return r=Jt(y=>{if(y<p)return!0;u||(_=g(t,s),typeof c=="function"&&(c=c(t,s)),u=!0),o&&(o.abort(),o=null);const S=y-p;return S>c?(n.set(t=s),!1):(n.set(t=_(d(S/c))),!0)}),r.promise}return{set:i,update:(s,a)=>i(s(l,t),a),subscribe:n.subscribe}}function Ht(t,{delay:e=0,duration:n=400,easing:r=Ql,amount:l=5,opacity:i=0}={}){const s=getComputedStyle(t),a=+s.opacity,o=s.filter==="none"?"":s.filter,u=a*(1-i),[f,c]=as(l);return{delay:e,duration:n,easing:r,css:(d,g)=>`opacity: ${a-u*g}; filter: ${o} blur(${g*f}${c});`}}function te(t,{delay:e=0,duration:n=400,easing:r=qi,start:l=0,opacity:i=0}={}){const s=getComputedStyle(t),a=+s.opacity,o=s.transform==="none"?"":s.transform,u=1-l,f=a*(1-i);return{delay:e,duration:n,easing:r,css:(c,d)=>`
			transform: ${o} scale(${1-u*d});
			opacity: ${a-f*d}
		`}}function ct(t,{delay:e=0,speed:n,duration:r,easing:l=Ql}={}){let i=t.getTotalLength();const s=getComputedStyle(t);return s.strokeLinecap!=="butt"&&(i+=parseInt(s.strokeWidth)),r===void 0?n===void 0?r=800:r=i/n:typeof r=="function"&&(r=r(i)),{delay:e,duration:r,easing:l,css:(a,o)=>`
			stroke-dasharray: ${i};
			stroke-dashoffset: ${o*i};
		`}}const gt={DURATION:{MIN:500,MAX:3e3},SIZE:{MIN:2,MAX:40},FALLBACK_DIFFICULTY:50},rn={DURATION:{MIN:250,MAX:1500},FALLBACK_NUM_KEYS:3,FALLBACK_DIFFICULTY:50},er={DURATION:{MIN:750,MAX:5e3},FALLBACK_DIFFICULTY:50},Xe={DURATION:{MIN:500,MAX:7500},ZONE_SIZE:15,FALLBACK_NUM_KEYS:3,FALLBACK_DIFFICULTY:50},ut={DURATION:{MIN:500,MAX:3e3},ZONE:{MIN:5,MAX:40},ZONE_FROM_RIGHT:95,FALLBACK_DIFFICULTY:50,FALLBACK_NUM_LINES:3},ln={DURATION:{MIN:1e3,MAX:1e4},SIZE:{MIN:2,MAX:15},FALLBACK_DIFFICULTY:50,FALLBACK_NUM_KEYS:3},sn={SIZE:{MIN:2,MAX:8},DEFAULT_LENGTH:4},on={SIZE:{MIN:2,MAX:8},DEFAULT_LENGTH:4},nt={Primary:"E",Secondary:"Q",Space:" ",Numbers:["1","2","3","4"],PrimarySet:["W","A","S","D"],SecondarySet:["I","J","K","L"]},Ve={DEFAULT_WAVE:{speed:1,amplitude:1,wavelength:1,segmentLength:1,lineWidth:10,timeModifier:1},MIN_WAVE:{speed:.1,amplitude:0,wavelength:0,segmentLength:.1,lineWidth:10,timeModifier:.1},MAX_WAVE:{speed:5,amplitude:100,wavelength:10,segmentLength:10,lineWidth:30,timeModifier:1},STEP_WAVE:{speed:.1,amplitude:.1,wavelength:.1,segmentLength:.1,lineWidth:.1,timeModifier:.01},MATCH_THRESHOLD:97.5},Yi={MISTAKES:3};function We(t){const e=nt[t];return e[Math.floor(Math.random()*e.length)]}function Te(t,e){function n(){window.removeEventListener(t,e)}return window.addEventListener(t,e),{removeListener:n}}function an(t){const e=t.slice(),n=e[1].target;return e[19]=n.size,e[20]=n.rotation,e}function tr(t){let e,n,r,l=t[1].key+"",i,s,a,o,u,f,c=t[1]&&nr(an(t));return{c(){e=G("div"),n=G("div"),r=G("p"),i=Ee(l),a=H(),o=oe("svg"),c&&c.c(),h(r,"class","text-shadow absolute font-bold text-[2vw]"),h(n,"style",t[7]),h(n,"class","absolute primary-shadow grid place-items-center primary-bg rounded-full"),h(o,"style",t[6]),h(o,"version","1.1"),h(o,"class","z-0 absolute overflow-visible"),h(o,"xmlns","http://www.w3.org/2000/svg"),h(e,"style",t[6]),h(e,"class","grid place-items-center primary-shadow default-game-position rounded-full")},m(d,g){D(d,e,g),L(e,n),L(n,r),L(r,i),L(e,a),L(e,o),c&&c.m(o,null),f=!0},p(d,g){(!f||g&2)&&l!==(l=d[1].key+"")&&ze(i,l),d[1]?c?c.p(an(d),g):(c=nr(an(d)),c.c(),c.m(o,null)):c&&(c.d(1),c=null)},i(d){f||(d&&ie(()=>{f&&(s||(s=X(r,te,{duration:100},!0)),s.run(1))}),d&&ie(()=>{f&&(u||(u=X(e,te,{},!0)),u.run(1))}),f=!0)},o(d){d&&(s||(s=X(r,te,{duration:100},!1)),s.run(0)),d&&(u||(u=X(e,te,{},!1)),u.run(0)),f=!1},d(d){d&&W(e),d&&s&&s.end(),c&&c.d(),d&&u&&u.end()}}}function nr(t){let e,n,r,l,i;return{c(){e=oe("circle"),n=oe("circle"),l=oe("circle"),Y(e,"stroke-width",ft*.1+"vw"),h(e,"class","absolute fill-none primary-stroke"),h(e,"cx","50%"),h(e,"cy","50%"),h(e,"r",ft*.95+"vw"),Y(n,"transform","rotate("+(-90+t[20])+"deg)"),h(n,"class","absolute radial stroke-tertiary origin-center target-segment svelte-1s27jpb"),h(n,"stroke-dasharray",t[5]+"vw"),h(n,"stroke-dashoffset",r=t[5]*((100-t[19])/100)+"vw"),h(n,"stroke-width",rr+"vw"),h(n,"fill-opacity","0"),h(n,"cx","50%"),h(n,"cy","50%"),h(n,"r",ft*.9+"vw"),Y(l,"transform","rotate("+(-90+t[3]/100*360)+"deg)"),h(l,"class",i="absolute origin-center default-colour-transition "+(t[2]==="success"?"glow-success stroke-success":t[2]==="fail"?"glow-error stroke-error":"stroke-accent glow-accent")),h(l,"stroke-dasharray",t[5]+"vw"),h(l,"stroke-dashoffset",t[5]*((100-$l)/100)+"vw"),h(l,"stroke-width",rr+"vw"),h(l,"fill-opacity","0"),h(l,"cx","50%"),h(l,"cy","50%"),h(l,"r",ft*.9+"vw")},m(s,a){D(s,e,a),D(s,n,a),D(s,l,a)},p(s,a){a&2&&Y(n,"transform","rotate("+(-90+s[20])+"deg)"),a&2&&r!==(r=s[5]*((100-s[19])/100)+"vw")&&h(n,"stroke-dashoffset",r),a&8&&Y(l,"transform","rotate("+(-90+s[3]/100*360)+"deg)"),a&4&&i!==(i="absolute origin-center default-colour-transition "+(s[2]==="success"?"glow-success stroke-success":s[2]==="fail"?"glow-error stroke-error":"stroke-accent glow-accent"))&&h(l,"class",i)},d(s){s&&(W(e),W(n),W(l))}}}function Zi(t){let e,n=t[0]&&tr(t);return{c(){n&&n.c(),e=pe()},m(r,l){n&&n.m(r,l),D(r,e,l)},p(r,[l]){r[0]?n?(n.p(r,l),l&1&&C(n,1)):(n=tr(r),n.c(),C(n,1),n.m(e.parentNode,e)):n&&(le(),P(n,1,1,()=>{n=null}),se())},i(r){C(n)},o(r){P(n)},d(r){r&&W(e),n&&n.d(r)}}}const $l=2,rr=1,ft=4;function Xi(t,e,n){let r,l;fe(t,R,b=>n(10,r=b));const i=Me(0);fe(t,i,b=>n(3,l=b));const s=ft*2,a=2*Math.PI*ft,o=`
        width: ${s}vw;
        height: ${s}vw;
    `,u=`
        width: ${s/2}vw;
        height: ${s/2}vw;
    `;let f=!1,c=null,d=null,g,p=[];function _(){p.forEach(b=>b()),p=[]}R.subscribe(b=>{let A=b.active&&b.type===ue.CircleProgress&&!c;A?(n(0,f=!0),_(),M()):f&&!A&&(n(0,f=!1),n(1,c=null),n(2,d=null),_(),y())});function y(){g==null||g.removeListener(),g=null}async function S(){if(!f)return;const b=c.duration;return i.set(100,{duration:b}),new Promise((A,U)=>{let m=setTimeout(()=>{A(!1)},b);p.push(()=>{m&&clearTimeout(m),i.set(l,{duration:0}),A(!1)}),g=Te(Ne.pressed,w=>{const z=w.key.toUpperCase();if(nt.Numbers.includes(z))if(clearTimeout(m),i.set(l,{duration:0}),z===c.key){const T=l/100*360,k=c.target.rotation,F=c.target.size*3.6,j=$l*3.6;if(T>k-j&&T<F+k)A(!0);else{A(!1);return}}else A(!1)})})}async function I(b,A){if(!f)return;y(),i.set(0,{duration:0});let{difficulty:U}=A;U=(U||gt.FALLBACK_DIFFICULTY)>=100?99:U<=0?5:U,n(1,c={target:E(U),duration:K(U),key:We("Numbers")}),n(2,d=null),await ve(500);const m=await S();n(2,d=m?"success":"fail");const w=m&&b<=1;m&&w?R.playSound("win"):!w&&m?R.playSound("primary"):R.playSound("lose");let z=setTimeout(()=>{if(f)if(m&&b>0)if(b--,b>0)I(b,A);else{R.finish(!0),n(1,c=null);return}else{R.finish(!1),n(1,c=null);return}},500);p.push(async()=>{z&&clearTimeout(z),n(2,d=null)})}function M(){if(!r.active||c)return;const{iterations:b,config:A}=r;I(b,A)}function K(b){const{MIN:A,MAX:U}=gt.DURATION;let m=A+(U-A)*((100-b)/100);const w=m*.2,z=Math.random()*w;return m+=z,m}function E(b){b=b>=100?99:b<=0?5:b;const{MAX:A}=gt.SIZE,U=A-b/100*A;let m=90+Math.random()*120;return U*3.6+m>360&&(m-=U*3.6+m-360),{size:U,rotation:m}}return[f,c,d,l,i,a,o,u]}class Ji extends he{constructor(e){super(),de(this,e,Xi,Zi,ce,{})}}function un(t){const e=t.slice(),n=e[1].target;return e[15]=n.size,e[16]=n.progress,e}function lr(t){let e,n,r,l=t[1].key+"",i,s,a,o,u,f,c,d,g=t[1]&&sr(un(t));return{c(){e=G("div"),n=G("div"),r=G("p"),i=Ee(l),a=H(),o=G("div"),f=H(),g&&g.c(),h(r,"class","text-shadow absolute font-bold text-[2vw]"),h(n,"class","h-[2.5vw] aspect-square absolute grid place-items-center center-y primary-shadow primary-bg -translate-x-[130%]"),Y(o,"left",t[3]+"%"),Y(o,"width",xl+"vw"),h(o,"class",u="h-[1vw] center-y z-[10] absolute origin-center default-colour-transition "+(t[2]==="success"?"glow-success bg-success":t[2]==="fail"?"glow-error bg-error":"bg-accent glow-accent")),h(e,"class","primary-shadow default-game-position w-[20vw] h-[0.5vw] primary-bg")},m(p,_){D(p,e,_),L(e,n),L(n,r),L(r,i),L(e,a),L(e,o),L(e,f),g&&g.m(e,null),d=!0},p(p,_){(!d||_&2)&&l!==(l=p[1].key+"")&&ze(i,l),(!d||_&8)&&Y(o,"left",p[3]+"%"),(!d||_&4&&u!==(u="h-[1vw] center-y z-[10] absolute origin-center default-colour-transition "+(p[2]==="success"?"glow-success bg-success":p[2]==="fail"?"glow-error bg-error":"bg-accent glow-accent")))&&h(o,"class",u),p[1]?g?g.p(un(p),_):(g=sr(un(p)),g.c(),g.m(e,null)):g&&(g.d(1),g=null)},i(p){d||(p&&ie(()=>{d&&(s||(s=X(r,te,{duration:100},!0)),s.run(1))}),p&&ie(()=>{d&&(c||(c=X(e,te,{},!0)),c.run(1))}),d=!0)},o(p){p&&(s||(s=X(r,te,{duration:100},!1)),s.run(0)),p&&(c||(c=X(e,te,{},!1)),c.run(0)),d=!1},d(p){p&&W(e),p&&s&&s.end(),g&&g.d(),p&&c&&c.end()}}}function sr(t){let e;return{c(){e=G("div"),Y(e,"left",t[16]+"%"),Y(e,"width",t[15]+"%"),h(e,"class","h-[1vw] center-y absolute origin-center bg-tertiary z-0 target-segment svelte-1s27jpb")},m(n,r){D(n,e,r)},p(n,r){r&2&&Y(e,"left",n[16]+"%"),r&2&&Y(e,"width",n[15]+"%")},d(n){n&&W(e)}}}function Qi(t){let e,n=t[0]&&lr(t);return{c(){n&&n.c(),e=pe()},m(r,l){n&&n.m(r,l),D(r,e,l)},p(r,[l]){r[0]?n?(n.p(r,l),l&1&&C(n,1)):(n=lr(r),n.c(),C(n,1),n.m(e.parentNode,e)):n&&(le(),P(n,1,1,()=>{n=null}),se())},i(r){C(n)},o(r){P(n)},d(r){r&&W(e),n&&n.d(r)}}}const xl=.5;function $i(t,e,n){let r,l;fe(t,R,I=>n(7,r=I));const i=Me(0);fe(t,i,I=>n(3,l=I));let s=!1,a=null,o=null,u,f=[];function c(){f.forEach(I=>I()),f=[]}R.subscribe(I=>{let M=I.active&&I.type===ue.Progress&&!a;M?(c(),n(0,s=!0),_()):s&&!M&&(n(0,s=!1),n(1,a=null),d(),c())});function d(){u==null||u.removeListener(),u=null}async function g(){if(!s)return;const I=a.duration;return i.set(100,{duration:I}),new Promise((M,K)=>{let E=setTimeout(()=>{M(!1)},I);f.push(()=>{E&&clearTimeout(E),M(!1)}),u=Te(Ne.pressed,b=>{const A=b.key.toUpperCase();if(nt.Numbers.includes(A))if(clearTimeout(E),i.set(l,{duration:0}),A===a.key){const U=a.target.progress,m=a.target.size,w=l;if(w>U-xl&&w<m+U)M(!0);else{M(!1);return}}else M(!1)})})}async function p(I,M){if(!s)return;d(),i.set(0,{duration:0});let{difficulty:K}=M;if(K=(K||gt.FALLBACK_DIFFICULTY)>=100?99:K<=0?5:K,n(1,a={target:S(K),duration:y(K),key:We("Numbers")}),await ve(500),!a)return;const E=await g();if(!a)return;n(2,o=E?"success":"fail");const b=E&&I<=1;E&&b?R.playSound("win"):!b&&E?R.playSound("primary"):R.playSound("lose");let A=setTimeout(()=>{if(s)if(n(2,o=null),E&&I>0)if(I--,I>0)p(I,M);else{R.finish(!0),n(1,a=null);return}else{R.finish(!1),n(1,a=null);return}},500);f.push(()=>{A&&clearTimeout(A),n(2,o=null)})}function _(){if(!r.active||a)return;const{iterations:I,config:M}=r;p(I,M)}function y(I){const{MIN:M,MAX:K}=gt.DURATION;let E=M+(K-M)*((100-I)/100);const b=E*.2,A=Math.random()*b;return E+=A,E}function S(I){I=I>=100?99:I<=0?5:I;const{MAX:M}=gt.SIZE,K=M-I/100*M,E=30,b=100-K,A=Math.random()*(b-E)+E;return{size:K,progress:A}}return[s,a,o,l,i]}class xi extends he{constructor(e){super(),de(this,e,$i,Qi,ce,{})}}function ir(t,e,n){const r=t.slice();return r[21]=e[n],r[23]=n,r}function cn(t){const e=t.slice(),n=e[1];return e[19]=n.stages,e[20]=n.currentStage,e}function fn(t){const e=t.slice(),n=e[1];return e[19]=n.stages,e}function or(t,e,n){const r=t.slice();return r[24]=e[n],r}function ar(t){let e,n,r,l,i,s,a,o=t[1]&&ur(t),u=t[1]&&fr(fn(t)),f=t[1]&&dr(cn(t));return{c(){e=G("div"),o&&o.c(),n=H(),r=oe("svg"),l=oe("circle"),u&&u.c(),i=H(),f&&f.c(),Y(l,"stroke-width",Mt*.1+"vw"),h(l,"class","absolute fill-none primary-stroke"),h(l,"cx","50%"),h(l,"cy","50%"),h(l,"r",Mt*.95+"vw"),h(r,"style",t[6]),h(r,"version","1.1"),h(r,"class","z-0 absolute overflow-visible -rotate-90"),h(r,"xmlns","http://www.w3.org/2000/svg"),h(e,"style",t[6]),h(e,"class","grid place-items-center primary-shadow default-game-position rounded-full w-fit h-fit")},m(c,d){D(c,e,d),o&&o.m(e,null),L(e,n),L(e,r),L(r,l),u&&u.m(r,null),L(e,i),f&&f.m(e,null),a=!0},p(c,d){c[1]?o?(o.p(c,d),d&2&&C(o,1)):(o=ur(c),o.c(),C(o,1),o.m(e,n)):o&&(le(),P(o,1,1,()=>{o=null}),se()),c[1]?u?u.p(fn(c),d):(u=fr(fn(c)),u.c(),u.m(r,null)):u&&(u.d(1),u=null),c[1]?f?f.p(cn(c),d):(f=dr(cn(c)),f.c(),f.m(e,null)):f&&(f.d(1),f=null)},i(c){a||(C(o),c&&ie(()=>{a&&(s||(s=X(e,te,{},!0)),s.run(1))}),a=!0)},o(c){P(o),c&&(s||(s=X(e,te,{},!1)),s.run(0)),a=!1},d(c){c&&W(e),o&&o.d(),u&&u.d(),f&&f.d(),c&&s&&s.end()}}}function ur(t){var a;let e,n,r,l=re((a=t[1])==null?void 0:a.keys),i=[];for(let o=0;o<l.length;o+=1)i[o]=cr(or(t,l,o));const s=o=>P(i[o],1,1,()=>{i[o]=null});return{c(){e=G("div");for(let o=0;o<i.length;o+=1)i[o].c();h(e,"class","flex flex-row items-center justify-center absolute")},m(o,u){D(o,e,u);for(let f=0;f<i.length;f+=1)i[f]&&i[f].m(e,null);r=!0},p(o,u){var f;if(u&130){l=re((f=o[1])==null?void 0:f.keys);let c;for(c=0;c<l.length;c+=1){const d=or(o,l,c);i[c]?(i[c].p(d,u),C(i[c],1)):(i[c]=cr(d),i[c].c(),C(i[c],1),i[c].m(e,null))}for(le(),c=l.length;c<i.length;c+=1)s(c);se()}},i(o){if(!r){for(let u=0;u<l.length;u+=1)C(i[u]);o&&ie(()=>{r&&(n||(n=X(e,te,{},!0)),n.run(1))}),r=!0}},o(o){i=i.filter(Boolean);for(let u=0;u<i.length;u+=1)P(i[u]);o&&(n||(n=X(e,te,{},!1)),n.run(0)),r=!1},d(o){o&&W(e),Se(i,o),o&&n&&n.end()}}}function cr(t){let e,n,r=t[24]+"",l,i,s,a;return{c(){e=G("div"),n=G("p"),l=Ee(r),s=H(),h(n,"class","text-shadow absolute font-bold text-[2vw]"),h(e,"style",t[7]),h(e,"class","grid place-items-center primary-shadow primary-bg")},m(o,u){D(o,e,u),L(e,n),L(n,l),L(e,s),a=!0},p(o,u){(!a||u&2)&&r!==(r=o[24]+"")&&ze(l,r)},i(o){a||(o&&ie(()=>{a&&(i||(i=X(n,te,{duration:100},!0)),i.run(1))}),a=!0)},o(o){o&&(i||(i=X(n,te,{duration:100},!1)),i.run(0)),a=!1},d(o){o&&W(e),o&&i&&i.end()}}}function fr(t){let e,n;return{c(){e=oe("circle"),Y(e,"transform","rotate("+(1/t[19]*360-90)+"deg)"),h(e,"class","absolute stroke-tertiary origin-center"),h(e,"stroke-dasharray",t[5]+"vw"),h(e,"stroke-dashoffset",n=t[5]*((100-t[3])/100)+"vw"),h(e,"stroke-width",to+"vw"),h(e,"fill-opacity","0"),h(e,"cx","50%"),h(e,"cy","50%"),h(e,"r",Mt*.95+"vw")},m(r,l){D(r,e,l)},p(r,l){l&2&&Y(e,"transform","rotate("+(1/r[19]*360-90)+"deg)"),l&8&&n!==(n=r[5]*((100-r[3])/100)+"vw")&&h(e,"stroke-dashoffset",n)},d(r){r&&W(e)}}}function dr(t){let e,n=re({length:t[19]}),r=[];for(let l=0;l<n.length;l+=1)r[l]=hr(ir(t,n,l));return{c(){for(let l=0;l<r.length;l+=1)r[l].c();e=pe()},m(l,i){for(let s=0;s<r.length;s+=1)r[s]&&r[s].m(l,i);D(l,e,i)},p(l,i){if(i&70){n=re({length:l[19]});let s;for(s=0;s<n.length;s+=1){const a=ir(l,n,s);r[s]?r[s].p(a,i):(r[s]=hr(a),r[s].c(),r[s].m(e.parentNode,e))}for(;s<r.length;s+=1)r[s].d(1);r.length=n.length}},d(l){l&&W(e),Se(r,l)}}}function hr(t){let e,n,r,l,i;return{c(){e=G("div"),n=G("div"),l=H(),h(n,"class",r="absolute w-[0.5vw] h-[1vw] default-colour-transition -translate-y-1/4 center-x "+(t[2]=="fail"?"bg-error glow-error":t[20]===t[23]||t[20]===t[19]&&t[23]===0?"bg-accent glow-accent":t[20]>=t[23]&&t[23]!==0||t[2]==="success"?"bg-success glow-success":"bg-tertiary primary-shadow")),h(e,"style",i="transform: rotate("+((t[23]+1)/t[19]*360-90)+"deg);"+t[6]),h(e,"class","origin-center absolute")},m(s,a){D(s,e,a),L(e,n),L(e,l)},p(s,a){a&6&&r!==(r="absolute w-[0.5vw] h-[1vw] default-colour-transition -translate-y-1/4 center-x "+(s[2]=="fail"?"bg-error glow-error":s[20]===s[23]||s[20]===s[19]&&s[23]===0?"bg-accent glow-accent":s[20]>=s[23]&&s[23]!==0||s[2]==="success"?"bg-success glow-success":"bg-tertiary primary-shadow"))&&h(n,"class",r),a&2&&i!==(i="transform: rotate("+((s[23]+1)/s[19]*360-90)+"deg);"+s[6])&&h(e,"style",i)},d(s){s&&W(e)}}}function eo(t){let e,n,r=t[0]&&ar(t);return{c(){r&&r.c(),e=pe()},m(l,i){r&&r.m(l,i),D(l,e,i),n=!0},p(l,[i]){l[0]?r?(r.p(l,i),i&1&&C(r,1)):(r=ar(l),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(le(),P(r,1,1,()=>{r=null}),se())},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d(l)}}}const to=.5,Mt=5;function no(t,e,n){let r,l;fe(t,R,b=>n(11,r=b));const i=Me(0);fe(t,i,b=>n(3,l=b));let s={};const a=Mt*2,o=2*Math.PI*Mt,u=`
        width: ${a}vw;
        height: ${a}vw;
    `,f=`
        width: ${a/4}vw;
        height: ${a/4}vw;
    `;let c=!1,d=null,g=null,p={Down:null,Up:null},_=[];function y(){_.forEach(b=>b()),_=[]}R.subscribe(b=>{let A=b.active&&b.type===ue.KeyCircle&&!d;A?(y(),n(0,c=!0),K()):c&&!A&&(n(0,c=!1),n(1,d=null),n(2,g=null),S(),y())});function S(){var b,A;(b=p.Down)==null||b.removeListener(),(A=p.Up)==null||A.removeListener(),p={Down:null,Up:null},s={}}async function I(){if(!c)return;const b=d.duration,{currentStage:A,stages:U,keys:m}=d,w=Math.floor(A/U*100);return i.set(w,{duration:b}),new Promise((z,T)=>{let k=setTimeout(()=>{z(!1)},b);_.push(()=>{k&&clearTimeout(k),z(!1)}),S(),p.Down=Te(Ne.down,F=>{if(!c||(s[F.key.toUpperCase()]=!0,g))return;const j=Object.keys(s);if(j.length===2){clearTimeout(k),S();const N=j.includes(m[0]),v=j.includes(m[1]);if(i.set(w,{duration:0}),N&&v){if(n(1,d.currentStage++,d),d.currentStage>d.stages){z(!0);return}else R.playSound("primary");n(1,d.keys=[We("PrimarySet"),We("SecondarySet")],d),z(I())}else{z(!1);return}}}),p.Up=Te(Ne.up,F=>{delete s[F.key.toUpperCase()]})})}async function M(b,A){if(!c)return;S(),i.set(0,{duration:0});let{difficulty:U,numberOfKeys:m}=A;if(U=(U||rn.FALLBACK_DIFFICULTY)>=100?99:U<=0?5:U,m=m||rn.FALLBACK_NUM_KEYS,n(1,d={stages:m,currentStage:1,duration:E(U),keys:[We("PrimarySet"),We("SecondarySet")]}),n(2,g=null),await ve(500),!d)return;const w=await I();if(!d)return;n(2,g=w?"success":"fail");const z=w&&b<=1;w&&z?R.playSound("win"):!z&&w?R.playSound("iteration"):R.playSound("lose");let T=setTimeout(()=>{if(c)if(w&&b>0)if(b--,b>0)M(b,A);else{R.finish(!0),n(1,d=null);return}else{R.finish(!1),n(1,d=null);return}},500);_.push(()=>{T&&clearTimeout(T),n(2,g=null)})}function K(){if(!r.active||d)return;const{iterations:b,config:A}=r;M(b,A)}function E(b){const{MIN:A,MAX:U}=rn.DURATION;let m=A+(U-A)*((100-b)/100);const w=m*.2,z=Math.random()*w;return m+=z,m}return[c,d,g,l,i,o,u,f]}class ro extends he{constructor(e){super(),de(this,e,no,eo,ce,{})}}function dn(t){const e=t.slice(),n=e[1];return e[18]=n.size,e}function gr(t){let e,n,r,l,i,s,a,o=t[1]&&mr(t),u=t[1]&&pr(dn(t));return{c(){e=G("div"),n=G("div"),o&&o.c(),r=H(),l=oe("svg"),i=oe("circle"),u&&u.c(),s=oe("circle"),h(n,"class","absolute grid place-items-center z-10"),Y(i,"stroke-width",At*.1+"vw z-0"),h(i,"class","absolute primary-fill"),h(i,"cx","50%"),h(i,"cy","50%"),h(i,"r",At+"vw"),Y(s,"transform","rotate(-90deg)"),h(s,"class","absolute radial stroke-tertiary origin-center target-segment primary-shadow"),h(s,"stroke-dasharray",t[5]+"vw"),h(s,"stroke-dashoffset",a=t[5]*(-(100-t[3])/100)+"vw"),h(s,"stroke-width",so+"vw"),h(s,"fill-opacity","0"),h(s,"cx","50%"),h(s,"cy","50%"),h(s,"r",At*.95+"vw"),h(l,"style",t[6]),h(l,"version","1.1"),h(l,"class","absolute overflow-visible"),h(l,"xmlns","http://www.w3.org/2000/svg"),h(e,"style",t[6]),h(e,"class","grid place-items-center primary-shadow default-game-position rounded-full")},m(f,c){D(f,e,c),L(e,n),o&&o.m(n,null),L(e,r),L(e,l),L(l,i),u&&u.m(l,null),L(l,s)},p(f,c){f[1]?o?(o.p(f,c),c&2&&C(o,1)):(o=mr(f),o.c(),C(o,1),o.m(n,null)):o&&(le(),P(o,1,1,()=>{o=null}),se()),f[1]?u?u.p(dn(f),c):(u=pr(dn(f)),u.c(),u.m(l,s)):u&&(u.d(1),u=null),c&8&&a!==(a=f[5]*(-(100-f[3])/100)+"vw")&&h(s,"stroke-dashoffset",a)},d(f){f&&W(e),o&&o.d(),u&&u.d()}}}function mr(t){let e,n=t[1].key+"",r,l,i,s;return{c(){e=G("p"),r=Ee(n),h(e,"class",l="text-shadow absolute font-bold text-[2vw] "+(!t[2]&&"animate-scale")+" svelte-zhs4mv")},m(a,o){D(a,e,o),L(e,r),s=!0},p(a,o){(!s||o&2)&&n!==(n=a[1].key+"")&&ze(r,n),(!s||o&4&&l!==(l="text-shadow absolute font-bold text-[2vw] "+(!a[2]&&"animate-scale")+" svelte-zhs4mv"))&&h(e,"class",l)},i(a){s||(a&&ie(()=>{s&&(i||(i=X(e,te,{duration:100},!0)),i.run(1))}),s=!0)},o(a){a&&(i||(i=X(e,te,{duration:100},!1)),i.run(0)),s=!1},d(a){a&&W(e),a&&i&&i.end()}}}function pr(t){let e,n,r;return{c(){e=oe("circle"),h(e,"class",n="absolute grid place-items-center default-colour-transition z-10 rounded-full "+(t[2]==="success"?"glow-success fill-success":t[2]==="fail"?"glow-error fill-error":"fill-accent glow-accent")),h(e,"cx","50%"),h(e,"cy","50%"),h(e,"r",r=t[18]/2+"%")},m(l,i){D(l,e,i)},p(l,i){i&4&&n!==(n="absolute grid place-items-center default-colour-transition z-10 rounded-full "+(l[2]==="success"?"glow-success fill-success":l[2]==="fail"?"glow-error fill-error":"fill-accent glow-accent"))&&h(e,"class",n),i&2&&r!==(r=l[18]/2+"%")&&h(e,"r",r)},d(l){l&&W(e)}}}function lo(t){let e,n=t[0]&&gr(t);return{c(){n&&n.c(),e=pe()},m(r,l){n&&n.m(r,l),D(r,e,l)},p(r,[l]){r[0]?n?n.p(r,l):(n=gr(r),n.c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},i:me,o:me,d(r){r&&W(e),n&&n.d(r)}}}const so=.5,At=4;function io(t,e,n){let r,l;fe(t,R,E=>n(10,r=E));const i=Me(0);fe(t,i,E=>n(3,l=E));const s=At*2,a=2*Math.PI*At,o=`
        width: ${s}vw;
        height: ${s}vw;
    `;let u=!1,f=null,c=null,d,g,p=[];function _(){p.forEach(E=>E()),p=[]}R.subscribe(E=>{let b=E.active&&E.type===ue.KeySpam&&!f;b?(_(),n(0,u=!0),M()):u&&!b&&(n(0,u=!1),n(1,f=null),n(2,c=null),y(),_())});function y(){d==null||d.removeListener(),d=null,g==null||g.removeListener(),g=null}async function S(){if(!u)return;const E=f.duration;return i.set(100,{duration:E}),new Promise((b,A)=>{let U=setInterval(()=>{u&&(n(1,f.size-=.1,f),f.size<=0&&n(1,f.size=0,f))},1),m=setTimeout(()=>{u&&(clearTimeout(m),clearInterval(U),b(!1))},E);p.push(()=>{m&&clearTimeout(m),U&&clearInterval(U),b(!1)});let w=!1;d=Te(Ne.pressed,z=>{if(!u||w)return;w=!0;const T=z.key.toUpperCase();if(nt.PrimarySet.includes(T)&&T===f.key){R.playSound("primary");let{size:k}=f;n(1,f.size=k+10,f),f.size>=100&&(n(1,f.size=100,f),clearTimeout(m),clearInterval(U),i.set(l,{duration:0}),b(!0))}}),g=Te(Ne.up,z=>{u&&(w=!1)})})}async function I(E,b){if(!u)return;y(),i.set(0,{duration:0});let{difficulty:A}=b;if(A=(A||er.FALLBACK_DIFFICULTY)>=100?99:A<=0?5:A,n(1,f={duration:K(A),key:We("PrimarySet"),size:0}),n(2,c=null),await ve(500),!f)return;const U=await S();if(y(),!f)return;n(2,c=U?"success":"fail");const m=U&&E<=1;U&&m?R.playSound("win"):!m&&U?R.playSound("iteration"):R.playSound("lose");let w=setTimeout(()=>{if(u)if(U&&E>0)if(E--,E>0)I(E,b);else{R.finish(!0),n(1,f=null);return}else{R.finish(!1),n(1,f=null);return}},500);p.push(()=>{w&&clearTimeout(w),n(2,c=null)})}function M(){if(!r.active||f)return;const{iterations:E,config:b}=r;I(E,b)}function K(E){const{MIN:b,MAX:A}=er.DURATION;let U=b+(A-b)*((100-E)/100);const m=U*.2,w=Math.random()*m;return U+=w,U}return[u,f,c,l,i,a,o]}class oo extends he{constructor(e){super(),de(this,e,io,lo,ce,{})}}function br(t){let e,n,r=(t[5]<t[6]?t[4]:t[0])+"",l,i,s,a;return{c(){e=G("div"),n=G("p"),l=Ee(r),h(n,"class",i="absolute font-bold text-[2vw] default-colour-transition "+(t[1]==="success"?"text-success glow-success ":t[1]==="fail"?"text-error glow-error":t[3]?"text-accent glow-accent":"text-foreground text-shadow ")),Y(e,"left",t[5]+"%"),h(e,"class","grid place-items-center absolute")},m(o,u){D(o,e,u),L(e,n),L(n,l),a=!0},p(o,u){(!a||u&49)&&r!==(r=(o[5]<o[6]?o[4]:o[0])+"")&&ze(l,r),(!a||u&10&&i!==(i="absolute font-bold text-[2vw] default-colour-transition "+(o[1]==="success"?"text-success glow-success ":o[1]==="fail"?"text-error glow-error":o[3]?"text-accent glow-accent":"text-foreground text-shadow ")))&&h(n,"class",i),(!a||u&32)&&Y(e,"left",o[5]+"%")},i(o){a||(o&&ie(()=>{a&&(s||(s=X(e,te,{duration:250},!0)),s.run(1))}),a=!0)},o(o){o&&(s||(s=X(e,te,{duration:250},!1)),s.run(0)),a=!1},d(o){o&&W(e),o&&s&&s.end()}}}function ao(t){let e,n=t[5]>0&&t[5]<100&&br(t);return{c(){n&&n.c(),e=pe()},m(r,l){n&&n.m(r,l),D(r,e,l)},p(r,[l]){r[5]>0&&r[5]<100?n?(n.p(r,l),l&32&&C(n,1)):(n=br(r),n.c(),C(n,1),n.m(e.parentNode,e)):n&&(le(),P(n,1,1,()=>{n=null}),se())},i(r){C(n)},o(r){P(n)},d(r){r&&W(e),n&&n.d(r)}}}function uo(t,e,n){let r,l=me,i=()=>(l(),l=Zt(o,p=>n(5,r=p)),o);t.$$.on_destroy.push(()=>l());let{key:s=null}=e,{state:a=null}=e,{left:o=null}=e;i();let{active:u=null}=e,f="1",c=null;function d(){clearInterval(c),c=null}const g=50-Xe.ZONE_SIZE*1.5;return zt(()=>{n(4,f=We("Numbers")),c=setInterval(()=>{(r>g||a)&&d();let p=We("Numbers");for(;p===f;)p=We("Numbers");n(4,f=p)},150)}),t.$$set=p=>{"key"in p&&n(0,s=p.key),"state"in p&&n(1,a=p.state),"left"in p&&i(n(2,o=p.left)),"active"in p&&n(3,u=p.active)},[s,a,o,u,f,r,g]}class co extends he{constructor(e){super(),de(this,e,uo,ao,ce,{key:0,state:1,left:2,active:3})}}function _r(t,e,n){const r=t.slice();return r[14]=e[n],r[16]=n,r}function wr(t){let e,n,r,l,i,s=t[2]&&vr(t);return{c(){e=G("div"),n=G("div"),r=H(),s&&s.c(),Y(n,"width",Xe.ZONE_SIZE+"%"),h(n,"class","bg-tertiary primary-shadow h-[2.5vw]"),h(e,"class","primary-shadow default-game-position w-[20vw] h-[0.5vw] primary-bg flex items-center justify-center")},m(a,o){D(a,e,o),L(e,n),L(e,r),s&&s.m(e,null),i=!0},p(a,o){a[2]?s?(s.p(a,o),o&4&&C(s,1)):(s=vr(a),s.c(),C(s,1),s.m(e,null)):s&&(le(),P(s,1,1,()=>{s=null}),se())},i(a){i||(C(s),a&&ie(()=>{i&&(l||(l=X(e,te,{},!0)),l.run(1))}),i=!0)},o(a){P(s),a&&(l||(l=X(e,te,{},!1)),l.run(0)),i=!1},d(a){a&&W(e),s&&s.d(),a&&l&&l.end()}}}function vr(t){let e,n,r=re(t[2].keys),l=[];for(let s=0;s<r.length;s+=1)l[s]=yr(_r(t,r,s));const i=s=>P(l[s],1,1,()=>{l[s]=null});return{c(){for(let s=0;s<l.length;s+=1)l[s].c();e=pe()},m(s,a){for(let o=0;o<l.length;o+=1)l[o]&&l[o].m(s,a);D(s,e,a),n=!0},p(s,a){if(a&5){r=re(s[2].keys);let o;for(o=0;o<r.length;o+=1){const u=_r(s,r,o);l[o]?(l[o].p(u,a),C(l[o],1)):(l[o]=yr(u),l[o].c(),C(l[o],1),l[o].m(e.parentNode,e))}for(le(),o=r.length;o<l.length;o+=1)i(o);se()}},i(s){if(!n){for(let a=0;a<r.length;a+=1)C(l[a]);n=!0}},o(s){l=l.filter(Boolean);for(let a=0;a<l.length;a+=1)P(l[a]);n=!1},d(s){s&&W(e),Se(l,s)}}}function yr(t){let e,n;const r=[t[14],{active:t[0]===t[16]}];let l={};for(let i=0;i<r.length;i+=1)l=Re(l,r[i]);return e=new co({props:l}),{c(){ee(e.$$.fragment)},m(i,s){$(e,i,s),n=!0},p(i,s){const a=s&5?Vt(r,[s&4&&Kt(i[14]),s&1&&{active:i[0]===i[16]}]):{};e.$set(a)},i(i){n||(C(e.$$.fragment,i),n=!0)},o(i){P(e.$$.fragment,i),n=!1},d(i){x(e,i)}}}function fo(t){let e,n,r=t[1]&&wr(t);return{c(){r&&r.c(),e=pe()},m(l,i){r&&r.m(l,i),D(l,e,i),n=!0},p(l,[i]){l[1]?r?(r.p(l,i),i&2&&C(r,1)):(r=wr(l),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(le(),P(r,1,1,()=>{r=null}),se())},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d(l)}}}function ho(t,e,n){let r;fe(t,R,S=>n(6,r=S));let l=null,i=!1,s=null,a=null,o,u=[];function f(){u.forEach(S=>S()),u=[]}R.subscribe(S=>{let I=S.active&&S.type===ue.NumberSlide&&!s;I?(f(),n(1,i=!0),p()):i&&!I&&(n(1,i=!1),n(2,s=null),a=null,c(),f())});function c(){o==null||o.removeListener(),o=null}async function d(){if(i)return n(0,l=null),new Promise((S,I)=>{const{keys:M}=s,{ZONE_SIZE:K}=Xe;let E=[];function b(A){E.forEach(U=>U()),E=[],M.forEach((U,m)=>{let{left:w}=U;w.set(pt(w),{duration:0}),n(2,s.keys[m].state=A?"success":"fail",s)}),S(A)}u.push(()=>{E==null||E.forEach(A=>A()),S(!1)}),M.forEach((A,U)=>{let m=s.keys[U];E.push(A.left.subscribe(w=>{w>=50-K/2&&w<=50+K/2?n(0,l=U):w>50+K/2&&m.state!=="success"&&(m.state="fail",n(2,s.keys[U]=m,s),b(!1))}))}),o=Te(Ne.pressed,A=>{const U=A.key.toUpperCase();if(!nt.Numbers.includes(U))return;const m=s.keys[l];if(m||b(!1),U===m.key){m.state="success",n(2,s.keys[l]=m,s);const w=s.keys.every(z=>z.state==="success");R.playSound("primary"),w&&b(!0)}else b(!1)})})}async function g(S,I){if(!i)return;c();let{difficulty:M,numberOfKeys:K}=I;M=(M||Xe.FALLBACK_DIFFICULTY)>=100?99:M<=0?5:M,K=K||Xe.FALLBACK_NUM_KEYS;const E=_(M),b=y(K,E);if(n(2,s={duration:E,keys:b}),a=null,n(0,l=null),await ve(500),!s)return;const A=await d();if(!s)return;c(),a=A?"success":"fail";const U=A&&S<=1;A&&U?R.playSound("win"):!U&&A?R.playSound("iteration"):R.playSound("lose");let m=setTimeout(()=>{if(i)if(A&&S>0)if(S--,S>0)g(S,I);else{R.finish(!0),n(2,s=null);return}else{R.finish(!1),n(2,s=null);return}},500);u.push(()=>{m&&clearTimeout(m),a=null})}function p(){if(!r.active||s)return;const{iterations:S,config:I}=r;g(S,I)}function _(S){const{MIN:I,MAX:M}=Xe.DURATION;let K=I+(M-I)*((100-S)/100);const E=K*.2,b=Math.random()*E;return K+=b,K}function y(S,I){const M=Xe.ZONE_SIZE,K=I*(M/100),E=[];for(let b=0;b<S;b++){const A={key:We("Numbers"),left:Me(0,{duration:0}),state:null};setTimeout(()=>{a||A.left.set(100,{duration:I})},K*b),E.push(A)}return E}return[l,i,s]}class go extends he{constructor(e){super(),de(this,e,ho,fo,ce,{})}}function kr(t){let e,n,r,l,i;return{c(){e=G("div"),Y(e,"left",t[2]+"%"),Y(e,"width",po+"vw"),h(e,"class",n="grid place-items-center z-10 absolute h-[1vw] default-colour-transition "+(t[0]==="success"?"bg-success glow-success":t[0]==="fail"?"bg-error glow-error":t[0]==="active"?"bg-accent glow-accent":"bg-tertiary primary-shadow"))},m(s,a){D(s,e,a),i=!0},p(s,a){(!i||a&4)&&Y(e,"left",s[2]+"%"),(!i||a&1&&n!==(n="grid place-items-center z-10 absolute h-[1vw] default-colour-transition "+(s[0]==="success"?"bg-success glow-success":s[0]==="fail"?"bg-error glow-error":s[0]==="active"?"bg-accent glow-accent":"bg-tertiary primary-shadow")))&&h(e,"class",n)},i(s){i||(s&&ie(()=>{i&&(l&&l.end(1),r=ks(e,te,{duration:250}),r.start())}),i=!0)},o(s){r&&r.invalidate(),s&&(l=Ss(e,te,{duration:250,delay:250})),i=!1},d(s){s&&W(e),s&&l&&l.end()}}}function mo(t){let e,n=t[2]>0&&t[2]<100&&kr(t);return{c(){n&&n.c(),e=pe()},m(r,l){n&&n.m(r,l),D(r,e,l)},p(r,[l]){r[2]>0&&r[2]<100?n?(n.p(r,l),l&4&&C(n,1)):(n=kr(r),n.c(),C(n,1),n.m(e.parentNode,e)):n&&(le(),P(n,1,1,()=>{n=null}),se())},i(r){C(n)},o(r){P(n)},d(r){r&&W(e),n&&n.d(r)}}}const po=.5;function bo(t,e,n){let r,l=me,i=()=>(l(),l=Zt(a,o=>n(2,r=o)),a);t.$$.on_destroy.push(()=>l());let{state:s=null}=e,{left:a=null}=e;return i(),t.$$set=o=>{"state"in o&&n(0,s=o.state),"left"in o&&i(n(1,a=o.left))},[s,a,r]}class _o extends he{constructor(e){super(),de(this,e,bo,mo,ce,{state:0,left:1})}}function Sr(t,e,n){const r=t.slice();return r[14]=e[n],r[16]=n,r}function Ir(t){let e,n,r,l,i,s,a,o=t[0]&&Cr(t);return{c(){e=G("div"),n=G("div"),r=G("p"),r.textContent=`${nt.Primary}`,i=H(),o&&o.c(),h(r,"class","text-shadow absolute font-bold text-[2vw]"),h(n,"class","h-[2.5vw] aspect-square absolute grid place-items-center center-y primary-shadow primary-bg -translate-x-[130%]"),h(e,"class","primary-shadow default-game-position w-[20vw] h-[0.5vw] primary-bg flex items-center")},m(u,f){D(u,e,f),L(e,n),L(n,r),L(e,i),o&&o.m(e,null),a=!0},p(u,f){u[0]?o?(o.p(u,f),f&1&&C(o,1)):(o=Cr(u),o.c(),C(o,1),o.m(e,null)):o&&(le(),P(o,1,1,()=>{o=null}),se())},i(u){a||(u&&ie(()=>{a&&(l||(l=X(r,te,{duration:100},!0)),l.run(1))}),C(o),u&&ie(()=>{a&&(s||(s=X(e,te,{},!0)),s.run(1))}),a=!0)},o(u){u&&(l||(l=X(r,te,{duration:100},!1)),l.run(0)),P(o),u&&(s||(s=X(e,te,{},!1)),s.run(0)),a=!1},d(u){u&&W(e),u&&l&&l.end(),o&&o.d(),u&&s&&s.end()}}}function Cr(t){let e,n,r,l,i=re(t[0].lines),s=[];for(let o=0;o<i.length;o+=1)s[o]=Er(Sr(t,i,o));const a=o=>P(s[o],1,1,()=>{s[o]=null});return{c(){e=G("div"),n=H();for(let o=0;o<s.length;o+=1)s[o].c();r=pe(),Y(e,"width",t[0].zone+"%"),Y(e,"left",ut.ZONE_FROM_RIGHT+"%"),h(e,"class","bg-tertiary primary-shadow h-[1vw] z-0 absolute -translate-x-full")},m(o,u){D(o,e,u),D(o,n,u);for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(o,u);D(o,r,u),l=!0},p(o,u){if((!l||u&1)&&Y(e,"width",o[0].zone+"%"),u&1){i=re(o[0].lines);let f;for(f=0;f<i.length;f+=1){const c=Sr(o,i,f);s[f]?(s[f].p(c,u),C(s[f],1)):(s[f]=Er(c),s[f].c(),C(s[f],1),s[f].m(r.parentNode,r))}for(le(),f=i.length;f<s.length;f+=1)a(f);se()}},i(o){if(!l){for(let u=0;u<i.length;u+=1)C(s[u]);l=!0}},o(o){s=s.filter(Boolean);for(let u=0;u<s.length;u+=1)P(s[u]);l=!1},d(o){o&&(W(e),W(n),W(r)),Se(s,o)}}}function Er(t){let e,n;const r=[t[14]];let l={};for(let i=0;i<r.length;i+=1)l=Re(l,r[i]);return e=new _o({props:l}),{c(){ee(e.$$.fragment)},m(i,s){$(e,i,s),n=!0},p(i,s){const a=s&1?Vt(r,[Kt(i[14])]):{};e.$set(a)},i(i){n||(C(e.$$.fragment,i),n=!0)},o(i){P(e.$$.fragment,i),n=!1},d(i){x(e,i)}}}function wo(t){let e,n,r=t[1]&&Ir(t);return{c(){r&&r.c(),e=pe()},m(l,i){r&&r.m(l,i),D(l,e,i),n=!0},p(l,[i]){l[1]?r?(r.p(l,i),i&2&&C(r,1)):(r=Ir(l),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(le(),P(r,1,1,()=>{r=null}),se())},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d(l)}}}function vo(t,e,n){let r;fe(t,R,S=>n(5,r=S));let l=null,i=!1,s=null,a,o=[];function u(){o.forEach(S=>S()),o=[]}R.subscribe(S=>{let I=S.active&&S.type===ue.RapidLines&&!l;I?(u(),n(1,i=!0),g()):i&&!I&&(n(1,i=!1),n(0,l=null),s=null,f(),u())});function f(){a==null||a.removeListener(),a=null}async function c(){if(i)return new Promise((S,I)=>{const{lines:M,zone:K}=l,{ZONE_FROM_RIGHT:E}=ut,b={max:E,min:E-K};let A=[];function U(m){A.forEach(w=>w()),A=[],M.forEach((w,z)=>{let{left:T}=w;T.set(pt(T),{duration:0}),n(0,l.lines[z].state=m?"success":"fail",l)}),S(m)}o.push(()=>{A==null||A.forEach(m=>m()),S(!1)}),M.forEach((m,w)=>{let z=l.lines[w];A.push(m.left.subscribe(T=>{T>b.min&&T<b.max&&z.state!=="success"?z.state="active":(T==100||T>b.max)&&z.state!=="success"&&(z.state="fail",U(!1)),n(0,l.lines[w]=z,l)}))}),a=Te(Ne.pressed,m=>{if(m.key.toUpperCase()!==nt.Primary)return;let z=!1,T=0,k=0;if(M.forEach((F,j)=>{let{left:N,state:v}=F;if(v==="active"){const O=pt(N);O>b.min&&O<b.max&&(z=!0,O>T&&(k=j,T=O))}}),!z)U(!1);else{R.playSound("primary");const F=l.lines[k];F.state="success",n(0,l.lines[k]=F,l),l.lines.every(N=>N.state==="success")&&U(!0)}})})}async function d(S,I){if(!i)return;f();let{difficulty:M,numberOfKeys:K}=I;M=(M||ut.FALLBACK_DIFFICULTY)>=100?99:M<=0?5:M,K=K||ut.FALLBACK_NUM_LINES;const E=_(M),b=y(K,E),A=p(M);if(n(0,l={duration:E,lines:b,zone:A}),s=null,await ve(500),!l)return;const U=await c();if(f(),!l)return;s=U?"success":"fail";const m=U&&S<=1;U&&m?R.playSound("win"):!m&&U?R.playSound("iteration"):R.playSound("lose");let w=setTimeout(()=>{if(i)if(U&&S>0)if(S--,S>0)d(S,I);else{R.finish(!0),n(0,l=null);return}else{R.finish(!1),n(0,l=null);return}},500);o.push(()=>{w&&clearTimeout(w),s=null})}function g(){if(!r.active||l)return;const{iterations:S,config:I}=r;d(S,I)}function p(S){const{MIN:I,MAX:M}=ut.ZONE;let K=I+(M-I)*((100-S)/100);const E=K*.2,b=Math.random()*E;return K+=b,K}function _(S){const{MIN:I,MAX:M}=ut.DURATION;let K=I+(M-I)*((100-S)/100);const E=K*.2,b=Math.random()*E;return K+=b,K}function y(S,I){const M=[];for(let K=0;K<S;K++){const E=Math.random()*100,A=I*(.2+Math.random()*.6)+(E/100+K*10),U={left:Me(0,{duration:0}),state:null};setTimeout(()=>{s||U.left.set(100,{duration:I})},A*K),M.push(U)}return M}return[l,i]}class yo extends he{constructor(e){super(),de(this,e,vo,wo,ce,{})}}function Ar(t,e,n){const r=t.slice();r[25]=e[n],r[30]=n;const l=100/r[22];r[26]=l;const i=r[30]/r[22]*360;r[27]=i;const s=r[30]+1;return r[28]=s,r}function hn(t){const e=t.slice(),n=e[2].stages;e[22]=n;const r=e[2].progress+"%";e[23]=r;const l=e[2].currentStage;return e[24]=l,e}function Tr(t){let e,n,r,l,i,s,a=t[2]&&Mr(hn(t)),o=t[2]&&zr(t);return{c(){e=G("div"),n=G("div"),a&&a.c(),r=H(),l=oe("svg"),o&&o.c(),h(n,"style",t[10]),h(n,"class","absolute primary-shadow grid place-items-center primary-bg rounded-full svelte-5equ90"),be(n,"shake",t[5]),h(l,"style",t[9]),h(l,"version","1.1"),h(l,"class","z-0 absolute overflow-visible"),h(l,"xmlns","http://www.w3.org/2000/svg"),h(e,"style",t[9]),h(e,"class","grid place-items-center origin-center primary-shadow default-game-position rounded-full circle-shake-main")},m(u,f){D(u,e,f),L(e,n),a&&a.m(n,null),L(e,r),L(e,l),o&&o.m(l,null),t[11](e),s=!0},p(u,f){u[2]?a?a.p(hn(u),f):(a=Mr(hn(u)),a.c(),a.m(n,null)):a&&(a.d(1),a=null),(!s||f&32)&&be(n,"shake",u[5]),u[2]?o?o.p(u,f):(o=zr(u),o.c(),o.m(l,null)):o&&(o.d(1),o=null)},i(u){s||(u&&ie(()=>{s&&(i||(i=X(e,te,{},!0)),i.run(1))}),s=!0)},o(u){u&&(i||(i=X(e,te,{},!1)),i.run(0)),s=!1},d(u){u&&W(e),a&&a.d(),o&&o.d(),t[11](null),u&&i&&i.end()}}}function Mr(t){let e,n,r,l,i=re({length:t[22]}),s=[];for(let a=0;a<i.length;a+=1)s[a]=Lr(Ar(t,i,a));return{c(){e=oe("svg");for(let a=0;a<s.length;a+=1)s[a].c();n=H(),r=G("div"),l=G("div"),h(e,"version","1.1"),h(e,"class","z-0 absolute overflow-visible w-full h-full"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(l,"class","bg-accent glow-accent rounded-full aspect-square"),Y(l,"width",t[23]),h(r,"class","absolute aspect-square w-full grid place-items-center rounded-full overflow-hidden z-10")},m(a,o){D(a,e,o);for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(e,null);D(a,n,o),D(a,r,o),L(r,l)},p(a,o){if(o&388){i=re({length:a[22]});let u;for(u=0;u<i.length;u+=1){const f=Ar(a,i,u);s[u]?s[u].p(f,o):(s[u]=Lr(f),s[u].c(),s[u].m(e,null))}for(;u<s.length;u+=1)s[u].d(1);s.length=i.length}o&4&&Y(l,"width",a[23])},d(a){a&&(W(e),W(n),W(r)),Se(s,a)}}}function Lr(t){let e,n,r;return{c(){e=oe("circle"),Y(e,"transform","rotate("+(-90+t[27])+"deg)"),h(e,"class",n="absolute radial "+(t[28]<t[24]?"stroke-tertiary":t[28]==t[24]?"stroke-accent glow-accent ":"primary-stroke")+" origin-center"),h(e,"stroke-dasharray",t[8]+"vw"),h(e,"stroke-dashoffset",r=t[8]*((101-t[26])/100)+"vw"),h(e,"stroke-width",es+"vw"),h(e,"fill-opacity","0"),h(e,"cx","50%"),h(e,"cy","50%"),h(e,"r",t[7]+"vw")},m(l,i){D(l,e,i)},p(l,i){i&4&&Y(e,"transform","rotate("+(-90+l[27])+"deg)"),i&4&&n!==(n="absolute radial "+(l[28]<l[24]?"stroke-tertiary":l[28]==l[24]?"stroke-accent glow-accent ":"primary-stroke")+" origin-center")&&h(e,"class",n),i&4&&r!==(r=l[8]*((101-l[26])/100)+"vw")&&h(e,"stroke-dashoffset",r)},d(l){l&&W(e)}}}function zr(t){let e,n,r;return{c(){e=oe("circle"),n=oe("circle"),Y(e,"stroke-width",mt*.1+"vw"),h(e,"class","absolute fill-none stroke-tertiary"),h(e,"cx","50%"),h(e,"cy","50%"),h(e,"r",mt*.95+"vw"),Y(n,"transform","rotate("+(t[3]-180)+"deg)"),h(n,"class",r="absolute origin-center default-colour-transition "+(t[4]==="success"?"glow-success stroke-success":t[4]==="fail"?"glow-error stroke-error":"stroke-accent glow-accent")),h(n,"stroke-dasharray",t[6]+"vw"),h(n,"stroke-dashoffset",t[6]*((100-So)/100)+"vw"),h(n,"stroke-width",es+"vw"),h(n,"fill-opacity","0"),h(n,"cx","50%"),h(n,"cy","50%"),h(n,"r",mt*.95+"vw")},m(l,i){D(l,e,i),D(l,n,i)},p(l,i){i&8&&Y(n,"transform","rotate("+(l[3]-180)+"deg)"),i&16&&r!==(r="absolute origin-center default-colour-transition "+(l[4]==="success"?"glow-success stroke-success":l[4]==="fail"?"glow-error stroke-error":"stroke-accent glow-accent"))&&h(n,"class",r)},d(l){l&&(W(e),W(n))}}}function ko(t){let e,n=t[1]&&Tr(t);return{c(){n&&n.c(),e=pe()},m(r,l){n&&n.m(r,l),D(r,e,l)},p(r,[l]){r[1]?n?(n.p(r,l),l&2&&C(n,1)):(n=Tr(r),n.c(),C(n,1),n.m(e.parentNode,e)):n&&(le(),P(n,1,1,()=>{n=null}),se())},i(r){C(n)},o(r){P(n)},d(r){r&&W(e),n&&n.d(r)}}}const So=2,es=.5,mt=5;function Io(t,e,n){let r;fe(t,R,m=>n(14,r=m));let l=null;const i=mt*2,s=2*Math.PI*mt,a=mt/1.5,o=2*Math.PI*a,u=`
        width: ${i}vw;
        height: ${i}vw;
    `,f=`
        width: ${i/2}vw;
        height: ${i/2}vw;
    `;let c=!1,d=null,g=0,p=null,_,y=!1,S=[];function I(){S.forEach(m=>m()),S=[]}R.subscribe(m=>{let w=m.active&&m.type===ue.CircleShake&&!d;w?(n(1,c=!0),I(),b()):c&&!w&&(n(1,c=!1),n(2,d=null),n(4,p=null),n(5,y=!1),I(),M())});function M(){_==null||_.removeListener(),_=null}async function K(m){if(!c)return;const w=()=>Math.random()*(150-50)+50;let z=w();return n(5,y=!1),new Promise((T,k)=>{let F=setInterval(()=>{!c||!y||(n(2,d.progress+=1,d),d.progress>=100&&(d.currentStage<d.stages?(n(2,d.currentStage++,d),n(2,d.progress=0,d),n(2,d.target=A(m),d),z=w(),n(5,y=!1),R.playSound("primary")):(n(5,y=!1),clearInterval(F),T(!0))))},z);S.push(async()=>{F&&clearInterval(F),T(!1)});let j=!1;_=Te(xt.move,N=>{const v=l==null?void 0:l.getBoundingClientRect();v||_==null||_.removeListener();const O=d.target,q=N.clientX,B=N.clientY,Z=v.left+v.width/2,Q=v.top+v.height/2;n(3,g=ji(q,B,Z,Q));const V=it(O.rotation),J=it(O.rotation+O.size*3.6);g>V&&g<J?(j=!0,setTimeout(()=>{j&&n(5,y=!0)},500)):(j=!1,n(5,y=!1))})})}async function E(m,w){if(!c)return;M(),n(3,g=0),n(5,y=!1);let{difficulty:z,numberOfKeys:T}=w;z=(z||ln.FALLBACK_DIFFICULTY)>=100?99:z<=0?5:z,T=T||ln.FALLBACK_NUM_KEYS,n(2,d={target:A(z),stages:T,currentStage:1,progress:0}),n(4,p=null),await ve(500);const k=await K(z);n(4,p=k?"success":"fail");const F=k&&m<=1;k&&F?R.playSound("win"):!F&&k?R.playSound("iteration"):R.playSound("lose");let j=setTimeout(()=>{if(c)if(k&&m>0)if(m--,m>0)E(m,w);else{R.finish(!0),n(2,d=null);return}else{R.finish(!1),n(2,d=null);return}},500);S.push(async()=>{j&&clearTimeout(j)})}function b(){if(!r.active||d)return;const{iterations:m,config:w}=r;E(m,w)}function A(m){m=m>=100?99:m<=0?5:m;const{MIN:w,MAX:z}=ln.SIZE,T=z-m/100*(z-w),k=-180,F=180;let j=Math.random()*(F-k)+F;return it(j)>0&&it(j+T*3.6)<0?j=j-T*3.6:it(j)<0&&it(j+T*3.6)>0&&(j=j+T*3.6),{size:T,rotation:j}}function U(m){Ce[m?"unshift":"push"](()=>{l=m,n(0,l)})}return[l,c,d,g,p,y,s,a,o,u,f,U]}class Co extends he{constructor(e){super(),de(this,e,Io,ko,ce,{})}}function Eo(t){let e,n,r,l,i,s,a,o,u,f,c,d,g,p,_;const y=t[12].default,S=$e(y,t,t[11],null);return{c(){e=G("button"),n=G("div"),S&&S.c(),r=H(),l=oe("svg"),i=oe("g"),s=oe("g"),a=oe("g"),o=oe("path"),h(n,"class","w-full h-full grid place-items-center z-10"),h(i,"id","SVGRepo_bgCarrier"),h(i,"stroke-width","0"),h(s,"id","SVGRepo_tracerCarrier"),h(s,"stroke-linecap","round"),h(s,"stroke-linejoin","round"),h(o,"stroke",t[3]),h(o,"stroke-width",u=t[0]||t[1]?"0":t[2]),h(o,"d","M0,92.375l46.188-80h92.378l46.185,80l-46.185,80H46.188L0,92.375z"),h(a,"id","SVGRepo_iconCarrier"),h(l,"width",t[4]),h(l,"height",t[4]),h(l,"version","1.1"),Y(l,"filter",t[5]),h(l,"class",f="origin-center default-all-transition absolute z-0 "+(t[0]?t[7]:t[6])),h(l,"xmlns","http://www.w3.org/2000/svg"),h(l,"xmlns:xlink","http://www.w3.org/1999/xlink"),h(l,"viewBox","0 0 184.751 184.751"),h(l,"xml:space","preserve"),h(e,"class",c=Bt("aspect-square h-full grid place-items-center default-all-transition hover:scale-105 active:scale-100 ",t[9].class)),ie(()=>t[13].call(e))},m(I,M){D(I,e,M),L(e,n),S&&S.m(n,null),L(e,r),L(e,l),L(l,i),L(l,s),L(l,a),L(a,o),d=Mn(e,t[13].bind(e)),g=!0,p||(_=ge(e,"click",t[14]),p=!0)},p(I,[M]){S&&S.p&&(!g||M&2048)&&et(S,y,I,I[11],g?xe(y,I[11],M,null):tt(I[11]),null),(!g||M&8)&&h(o,"stroke",I[3]),(!g||M&7&&u!==(u=I[0]||I[1]?"0":I[2]))&&h(o,"stroke-width",u),(!g||M&16)&&h(l,"width",I[4]),(!g||M&16)&&h(l,"height",I[4]),(!g||M&32)&&Y(l,"filter",I[5]),(!g||M&193&&f!==(f="origin-center default-all-transition absolute z-0 "+(I[0]?I[7]:I[6])))&&h(l,"class",f),(!g||M&512&&c!==(c=Bt("aspect-square h-full grid place-items-center default-all-transition hover:scale-105 active:scale-100 ",I[9].class)))&&h(e,"class",c)},i(I){g||(C(S,I),g=!0)},o(I){P(S,I),g=!1},d(I){I&&W(e),S&&S.d(I),d(),p=!1,_()}}}function Ao(t,e,n){let r,l,i,s,{$$slots:a={},$$scope:o}=e;const u=Qt();let f=0,{active:c=!1}=e,{disabled:d=!1}=e,{strokeWidth:g="1vh"}=e,{variant:p="accent"}=e;const _={accent:"fill-accent",success:"fill-success",error:"fill-error",warning:"fill-warning"},y={accent:"fill-accent/25",success:"fill-success/25",error:"fill-error/25",warning:"fill-warning/25"};function S(){f=this.clientHeight,n(4,f)}const I=()=>u("click");return t.$$set=M=>{n(9,e=Re(Re({},e),bt(M))),"active"in M&&n(0,c=M.active),"disabled"in M&&n(1,d=M.disabled),"strokeWidth"in M&&n(2,g=M.strokeWidth),"variant"in M&&n(10,p=M.variant),"$$scope"in M&&n(11,o=M.$$scope)},t.$$.update=()=>{t.$$.dirty&1024&&n(3,r=`rgb(var(--${p}))`),t.$$.dirty&1024&&n(7,l=_[p]),t.$$.dirty&1024&&n(6,i=y[p]),t.$$.dirty&11&&n(5,s=c?`drop-shadow(0 0 0.5vh ${r})`:d?"":`drop-shadow(0 0 0.05vw ${r})`)},e=bt(e),[c,d,g,r,f,s,i,l,u,e,p,o,a,S,I]}class To extends he{constructor(e){super(),de(this,e,Ao,Eo,ce,{active:0,disabled:1,strokeWidth:2,variant:10})}}function Mo(t){let e;return{c(){e=G("div"),h(e,"class","loader svelte-uhcmgj")},m(n,r){D(n,e,r)},p:me,i:me,o:me,d(n){n&&W(e)}}}class Lo extends he{constructor(e){super(),de(this,e,null,Mo,ce,{})}}function zo(t){let e,n,r,l,i,s,a;return{c(){e=oe("svg"),n=oe("g"),r=oe("g"),l=oe("g"),i=oe("path"),h(n,"id","SVGRepo_bgCarrier"),h(n,"stroke-width","0"),h(r,"id","SVGRepo_tracerCarrier"),h(r,"stroke-linecap","round"),h(r,"stroke-linejoin","round"),h(i,"d","M4 7.5L7 10L11 5"),h(i,"fill","none"),h(i,"class","stroke-tertiary stroke-width-[0.15vh]"),h(l,"id","SVGRepo_iconCarrier"),h(e,"viewBox","0 0 15 15"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg")},m(o,u){D(o,e,u),L(e,n),L(e,r),L(e,l),L(l,i),a=!0},p:me,i(o){a||(ie(()=>{a&&(s||(s=X(i,ct,{},!0)),s.run(1))}),a=!0)},o(o){s||(s=X(i,ct,{},!1)),s.run(0),a=!1},d(o){o&&W(e),o&&s&&s.end()}}}class Ro extends he{constructor(e){super(),de(this,e,null,zo,ce,{})}}function No(t){let e,n,r,l,i,s;return{c(){e=oe("svg"),n=oe("line"),l=oe("line"),h(n,"x1","2.35355"),h(n,"y1","1.64645"),h(n,"x2","13.3536"),h(n,"y2","12.6464"),h(n,"class","stroke-tertiary stroke-[0.15vh]"),h(l,"x1","1.64645"),h(l,"y1","12.6464"),h(l,"x2","12.6464"),h(l,"y2","1.64645"),h(l,"class","stroke-tertiary stroke-[0.15vh]"),h(e,"class","w-[60%] h-[60%]"),h(e,"viewBox","0 0 15 15"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg")},m(a,o){D(a,e,o),L(e,n),L(e,l),s=!0},p:me,i(a){s||(ie(()=>{s&&(r||(r=X(n,ct,{},!0)),r.run(1))}),ie(()=>{s&&(i||(i=X(l,ct,{},!0)),i.run(1))}),s=!0)},o(a){r||(r=X(n,ct,{},!1)),r.run(0),i||(i=X(l,ct,{},!1)),i.run(0),s=!1},d(a){a&&W(e),a&&r&&r.end(),a&&i&&i.end()}}}class Po extends he{constructor(e){super(),de(this,e,null,No,ce,{})}}function Rr(t,e,n){const r=t.slice();return r[9]=e[n],r[11]=n,r}function Nr(t,e,n){const r=t.slice();return r[12]=e[n],r[11]=n,r}function Pr(t){let e,n=re(t[2]),r=[];for(let l=0;l<n.length;l+=1)r[l]=Ur(Nr(t,n,l));return{c(){e=G("span");for(let l=0;l<r.length;l+=1)r[l].c();h(e,"class","flex flex-row items-center uppercase justify-center font-bold text-[3vh] title svelte-1w3dvpw")},m(l,i){D(l,e,i);for(let s=0;s<r.length;s+=1)r[s]&&r[s].m(e,null)},p(l,i){if(i&4){n=re(l[2]);let s;for(s=0;s<n.length;s+=1){const a=Nr(l,n,s);r[s]?r[s].p(a,i):(r[s]=Ur(a),r[s].c(),r[s].m(e,null))}for(;s<r.length;s+=1)r[s].d(1);r.length=n.length}},d(l){l&&W(e),Se(r,l)}}}function Ur(t){let e,n=t[12]+"",r,l;return{c(){e=G("p"),r=Ee(n),l=H(),be(e,"text-accent",t[11]===t[2].length-1)},m(i,s){D(i,e,s),L(e,r),L(e,l)},p(i,s){s&4&&n!==(n=i[12]+"")&&ze(r,n),s&4&&be(e,"text-accent",i[11]===i[2].length-1)},d(i){i&&W(e)}}}function Wr(t){let e,n;return{c(){e=G("p"),n=Ee(t[1]),h(e,"class","text-tertiary/75 font-medium w-full text-[1.5vh]")},m(r,l){D(r,e,l),L(e,n)},p(r,l){l&2&&ze(n,r[1])},d(r){r&&W(e)}}}function Uo(t){let e,n,r,l;return n=new Lo({}),{c(){e=G("div"),ee(n.$$.fragment),h(e,"class","absolute grid place-items-center w-full h-full aspect-square")},m(i,s){D(i,e,s),$(n,e,null),l=!0},i(i){l||(C(n.$$.fragment,i),i&&ie(()=>{l&&(r||(r=X(e,te,{},!0)),r.run(1))}),l=!0)},o(i){P(n.$$.fragment,i),i&&(r||(r=X(e,te,{},!1)),r.run(0)),l=!1},d(i){i&&W(e),x(n),i&&r&&r.end()}}}function Wo(t){let e,n,r;return n=new Po({}),{c(){e=G("div"),ee(n.$$.fragment),h(e,"class","absolute grid place-items-center w-full h-full aspect-square")},m(l,i){D(l,e,i),$(n,e,null),r=!0},i(l){r||(C(n.$$.fragment,l),r=!0)},o(l){P(n.$$.fragment,l),r=!1},d(l){l&&W(e),x(n)}}}function Do(t){let e,n,r;return n=new Ro({}),{c(){e=G("div"),ee(n.$$.fragment),h(e,"class","absolute grid place-items-center w-full h-full aspect-square")},m(l,i){D(l,e,i),$(n,e,null),r=!0},i(l){r||(C(n.$$.fragment,l),r=!0)},o(l){P(n.$$.fragment,l),r=!1},d(l){l&&W(e),x(n)}}}function Oo(t){let e,n,r,l;const i=[Do,Wo,Uo],s=[];function a(o,u){return o[0]==="success"?0:o[0]==="fail"?1:2}return e=a(t),n=s[e]=i[e](t),{c(){n.c(),r=pe()},m(o,u){s[e].m(o,u),D(o,r,u),l=!0},p(o,u){let f=e;e=a(o),e!==f&&(le(),P(s[f],1,1,()=>{s[f]=null}),se(),n=s[e],n||(n=s[e]=i[e](o),n.c()),C(n,1),n.m(r.parentNode,r))},i(o){l||(C(n),l=!0)},o(o){P(n),l=!1},d(o){o&&W(r),s[e].d(o)}}}function Dr(t){let e,n=re({length:t[3]}),r=[];for(let l=0;l<n.length;l+=1)r[l]=Or(Rr(t,n,l));return{c(){e=G("div");for(let l=0;l<r.length;l+=1)r[l].c();h(e,"class","w-full h-[2vh] flex flex-row gap-[1vh] z-10")},m(l,i){D(l,e,i);for(let s=0;s<r.length;s+=1)r[s]&&r[s].m(e,null)},p(l,i){if(i&57){n=re({length:l[3]});let s;for(s=0;s<n.length;s+=1){const a=Rr(l,n,s);r[s]?r[s].p(a,i):(r[s]=Or(a),r[s].c(),r[s].m(e,null))}for(;s<r.length;s+=1)r[s].d(1);r.length=n.length}},d(l){l&&W(e),Se(r,l)}}}function Fo(t){let e;return{c(){e=G("div"),h(e,"class","h-full bg-tertiary w-full"),be(e,"bg-error",t[5]==100||t[0]=="fail"),be(e,"bg-tertiary",t[0]==null),be(e,"glow-error",t[5]==100||t[0]=="fail"),be(e,"bg-success",t[0]=="success"),be(e,"glow-success",t[0]=="success")},m(n,r){D(n,e,r)},p(n,r){r&33&&be(e,"bg-error",n[5]==100||n[0]=="fail"),r&1&&be(e,"bg-tertiary",n[0]==null),r&33&&be(e,"glow-error",n[5]==100||n[0]=="fail"),r&1&&be(e,"bg-success",n[0]=="success"),r&1&&be(e,"glow-success",n[0]=="success")},d(n){n&&W(e)}}}function Go(t){let e;return{c(){e=G("div"),Y(e,"width",t[5]+"%"),h(e,"class","h-full default-colour-transition ease-linear"),be(e,"bg-error",t[5]==100||t[0]=="fail"),be(e,"bg-accent",t[0]==null),be(e,"glow-error",t[5]==100||t[0]=="fail"),be(e,"glow-accent",t[0]==null),be(e,"bg-success",t[0]=="success"),be(e,"glow-success",t[0]=="success")},m(n,r){D(n,e,r)},p(n,r){r&32&&Y(e,"width",n[5]+"%"),r&33&&be(e,"bg-error",n[5]==100||n[0]=="fail"),r&1&&be(e,"bg-accent",n[0]==null),r&33&&be(e,"glow-error",n[5]==100||n[0]=="fail"),r&1&&be(e,"glow-accent",n[0]==null),r&1&&be(e,"bg-success",n[0]=="success"),r&1&&be(e,"glow-success",n[0]=="success")},d(n){n&&W(e)}}}function Or(t){let e,n;function r(s,a){if(s[11]===s[4])return Go;if(s[4]>s[11])return Fo}let l=r(t),i=l&&l(t);return{c(){e=G("div"),i&&i.c(),n=H(),h(e,"class","w-full h-full primary-bg")},m(s,a){D(s,e,a),i&&i.m(e,null),L(e,n)},p(s,a){l===(l=r(s))&&i?i.p(s,a):(i&&i.d(1),i=l&&l(s),i&&(i.c(),i.m(e,n)))},d(s){s&&W(e),i&&i.d()}}}function jo(t){let e,n,r,l,i,s,a,o,u,f,c=t[2]&&Pr(t),d=t[1]&&Wr(t);i=new To({props:{variant:t[0]==="success"?"success":t[0]==="fail"?"error":"accent",$$slots:{default:[Oo]},$$scope:{ctx:t}}});const g=t[7].default,p=$e(g,t,t[8],null);let _=t[3]>0&&Dr(t);return{c(){e=G("div"),n=G("div"),c&&c.c(),r=H(),d&&d.c(),l=H(),ee(i.$$.fragment),s=H(),p&&p.c(),a=H(),_&&_.c(),h(n,"class","w-full h-[4vh] z-10 flex flex-row items-center justify-start gap-[1vh] border-primary"),h(e,"class",o=Gn(Bt("bg-solid center flex flex-col items-center justify-center p-[1vh] gap-[1vh] absolute shadow-box",t[6].class))+" svelte-1w3dvpw")},m(y,S){D(y,e,S),L(e,n),c&&c.m(n,null),L(n,r),d&&d.m(n,null),L(n,l),$(i,n,null),L(e,s),p&&p.m(e,null),L(e,a),_&&_.m(e,null),f=!0},p(y,[S]){y[2]?c?c.p(y,S):(c=Pr(y),c.c(),c.m(n,r)):c&&(c.d(1),c=null),y[1]?d?d.p(y,S):(d=Wr(y),d.c(),d.m(n,l)):d&&(d.d(1),d=null);const I={};S&1&&(I.variant=y[0]==="success"?"success":y[0]==="fail"?"error":"accent"),S&257&&(I.$$scope={dirty:S,ctx:y}),i.$set(I),p&&p.p&&(!f||S&256)&&et(p,g,y,y[8],f?xe(g,y[8],S,null):tt(y[8]),null),y[3]>0?_?_.p(y,S):(_=Dr(y),_.c(),_.m(e,null)):_&&(_.d(1),_=null),(!f||S&64&&o!==(o=Gn(Bt("bg-solid center flex flex-col items-center justify-center p-[1vh] gap-[1vh] absolute shadow-box",y[6].class))+" svelte-1w3dvpw"))&&h(e,"class",o)},i(y){f||(C(i.$$.fragment,y),C(p,y),y&&ie(()=>{f&&(u||(u=X(e,te,{},!0)),u.run(1))}),f=!0)},o(y){P(i.$$.fragment,y),P(p,y),y&&(u||(u=X(e,te,{},!1)),u.run(0)),f=!1},d(y){y&&W(e),c&&c.d(),d&&d.d(),x(i),p&&p.d(y),_&&_.d(),y&&u&&u.end()}}}function Vo(t,e,n){let{$$slots:r={},$$scope:l}=e,{state:i=null}=e,{subtitle:s=null}=e,{title:a=null}=e,{iterations:o=1}=e,{iteration:u=0}=e,{progress:f=0}=e;return t.$$set=c=>{n(6,e=Re(Re({},e),bt(c))),"state"in c&&n(0,i=c.state),"subtitle"in c&&n(1,s=c.subtitle),"title"in c&&n(2,a=c.title),"iterations"in c&&n(3,o=c.iterations),"iteration"in c&&n(4,u=c.iteration),"progress"in c&&n(5,f=c.progress),"$$scope"in c&&n(8,l=c.$$scope)},e=bt(e),[i,s,a,o,u,f,e,r,l]}class Be extends he{constructor(e){super(),de(this,e,Vo,jo,ce,{state:0,subtitle:1,title:2,iterations:3,iteration:4,progress:5})}}function Ko(t){let e,n,r,l,i,s;return{c(){e=G("button"),h(e,"class",n="absolute "+t[7].class+" hover:scale-125 default-colour-transition aspect-square rounded-full z-10 "+(t[5]&&"border-[0.1vh] border-tertiary")+" "+(t[0]=="success"?"bg-success glow-success scale-125":t[0]=="fail"?"bg-error glow-error  scale-125":t[1]?"bg-accent  glow-accent animate-scale":"bg-tertiary active:!scale-95 hover:brightness-125 animate-scale-mini primary-shadow")+" svelte-1tobd32"),Y(e,"left",t[2]+"%"),Y(e,"top",t[3]+"%"),Y(e,"width",t[4]),Y(e,"height",t[4])},m(a,o){D(a,e,o),l=!0,i||(s=[ge(e,"click",t[8]),ge(e,"mousedown",t[9]),ge(e,"mouseup",t[10])],i=!0)},p(a,[o]){(!l||o&163&&n!==(n="absolute "+a[7].class+" hover:scale-125 default-colour-transition aspect-square rounded-full z-10 "+(a[5]&&"border-[0.1vh] border-tertiary")+" "+(a[0]=="success"?"bg-success glow-success scale-125":a[0]=="fail"?"bg-error glow-error  scale-125":a[1]?"bg-accent  glow-accent animate-scale":"bg-tertiary active:!scale-95 hover:brightness-125 animate-scale-mini primary-shadow")+" svelte-1tobd32"))&&h(e,"class",n),(!l||o&4)&&Y(e,"left",a[2]+"%"),(!l||o&8)&&Y(e,"top",a[3]+"%"),(!l||o&16)&&Y(e,"width",a[4]),(!l||o&16)&&Y(e,"height",a[4])},i(a){l||(ie(()=>{l&&(r||(r=X(e,te,{duration:250,delay:250},!0)),r.run(1))}),l=!0)},o(a){r||(r=X(e,te,{duration:250,delay:250},!1)),r.run(0),l=!1},d(a){a&&W(e),a&&r&&r.end(),i=!1,Le(s)}}}function Bo(t,e,n){let{iterationState:r=null}=e,{selected:l=!1}=e,{x:i,y:s}=e,{size:a="2.5vh"}=e,{root:o=!1}=e;const u=Qt(),f=()=>u("click"),c=()=>u("mousedown"),d=()=>u("mouseup");return t.$$set=g=>{n(7,e=Re(Re({},e),bt(g))),"iterationState"in g&&n(0,r=g.iterationState),"selected"in g&&n(1,l=g.selected),"x"in g&&n(2,i=g.x),"y"in g&&n(3,s=g.y),"size"in g&&n(4,a=g.size),"root"in g&&n(5,o=g.root)},e=bt(e),[r,l,i,s,a,o,u,e,f,c,d]}class ts extends he{constructor(e){super(),de(this,e,Bo,Ko,ce,{iterationState:0,selected:1,x:2,y:3,size:4,root:5})}}function Fr(t,e,n){const r=t.slice();r[30]=e[n].x,r[31]=e[n].y,r[32]=e[n].selected,r[35]=n;const l=`${r[35]==0?An:ns}vh`;return r[33]=l,r}function gn(t){const e=t.slice(),n=e[1];return e[29]=n.targets,e}function Gr(t){let e,n;return e=new Be({props:{state:t[2],title:["Path","Find"],subtitle:"Go to the next point closest point.",iterations:t[3],iteration:t[1].currentIteration,progress:t[7]/t[1].duration*100,$$slots:{default:[Ho]},$$scope:{ctx:t}}}),{c(){ee(e.$$.fragment)},m(r,l){$(e,r,l),n=!0},p(r,l){const i={};l[0]&4&&(i.state=r[2]),l[0]&8&&(i.iterations=r[3]),l[0]&2&&(i.iteration=r[1].currentIteration),l[0]&130&&(i.progress=r[7]/r[1].duration*100),l[0]&118|l[1]&32&&(i.$$scope={dirty:l,ctx:r}),e.$set(i)},i(r){n||(C(e.$$.fragment,r),n=!0)},o(r){P(e.$$.fragment,r),n=!1},d(r){x(e,r)}}}function jr(t){let e,n;function r(){return t[11](t[35])}return e=new ts({props:{root:t[35]==0,iterationState:t[2],selected:t[32],x:t[30],y:t[31],size:t[33]}}),e.$on("click",r),{c(){ee(e.$$.fragment)},m(l,i){$(e,l,i),n=!0},p(l,i){t=l;const s={};i[0]&4&&(s.iterationState=t[2]),i[0]&2&&(s.selected=t[32]),i[0]&2&&(s.x=t[30]),i[0]&2&&(s.y=t[31]),e.$set(s)},i(l){n||(C(e.$$.fragment,l),n=!0)},o(l){P(e.$$.fragment,l),n=!1},d(l){x(e,l)}}}function Ho(t){let e,n,r,l,i,s=re(t[29]),a=[];for(let u=0;u<s.length;u+=1)a[u]=jr(Fr(t,s,u));const o=u=>P(a[u],1,1,()=>{a[u]=null});return{c(){e=G("div"),n=G("canvas"),r=H();for(let u=0;u<a.length;u+=1)a[u].c();h(n,"width",t[4]),h(n,"height",t[5]),h(e,"class","w-[60vh] h-[60vh] aspect-square bg-secondary/90 border-[0.15vh] border-tertiary/50"),ie(()=>t[12].call(e))},m(u,f){D(u,e,f),L(e,n),t[10](n),L(e,r);for(let c=0;c<a.length;c+=1)a[c]&&a[c].m(e,null);l=Mn(e,t[12].bind(e)),i=!0},p(u,f){if((!i||f[0]&16)&&h(n,"width",u[4]),(!i||f[0]&32)&&h(n,"height",u[5]),f[0]&518){s=re(u[29]);let c;for(c=0;c<s.length;c+=1){const d=Fr(u,s,c);a[c]?(a[c].p(d,f),C(a[c],1)):(a[c]=jr(d),a[c].c(),C(a[c],1),a[c].m(e,null))}for(le(),c=s.length;c<a.length;c+=1)o(c);se()}},i(u){if(!i){for(let f=0;f<s.length;f+=1)C(a[f]);i=!0}},o(u){a=a.filter(Boolean);for(let f=0;f<a.length;f+=1)P(a[f]);i=!1},d(u){u&&W(e),t[10](null),Se(a,u),l()}}}function qo(t){let e,n,r=t[0]&&Gr(gn(t));return{c(){r&&r.c(),e=pe()},m(l,i){r&&r.m(l,i),D(l,e,i),n=!0},p(l,i){l[0]?r?(r.p(gn(l),i),i[0]&1&&C(r,1)):(r=Gr(gn(l)),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(le(),P(r,1,1,()=>{r=null}),se())},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d(l)}}}const An=2.5,ns=2;function Yo(t,e,n){let r,l;fe(t,R,N=>n(17,r=N));let i=!1,s=null,a=null;const o=Me(0);fe(t,o,N=>n(7,l=N));let u=null,f,c,d,g,p={x:0,y:0},_=!1;const S=`rgba(${getComputedStyle(document.body).getPropertyValue("--tertiary").split(" ").join(",")}, 1)`;let I=[];function M(){I.forEach(N=>N()),I=[]}R.subscribe(N=>{let v=N.active&&N.type===ue.PathFind&&!a;v?(M(),n(0,i=!0),A()):i&&!v&&(n(0,i=!1),n(1,s=null),n(2,a=null),K(),M())});function K(){f==null||f.removeListener(),f=null}async function E(){if(!i)return;_=!1,z();let N=setTimeout(()=>{o.set(s.duration,{duration:s.duration})},500);return I.push(()=>{N&&clearTimeout(N)}),new Promise((v,O)=>{let q=setTimeout(()=>{B(!1)},s.duration+500);I.push(()=>{q&&clearTimeout(q),v(!1)}),f=Te(xt.move,Z=>{if(!i||!g)return;let{targets:Q,activeIndex:V}=s;if(V==Q.length-1){B(!0);return}if(_){B(!1);return}const J=g==null?void 0:g.getBoundingClientRect(),ae=Z.clientX-J.left,ye=Z.clientY-J.top;p.x=ae,p.y=ye});function B(Z){const Q=l;o.set(Q,{duration:0}),clearTimeout(q),v(Z)}})}async function b(N,v){if(!i)return;K(),o.set(0,{duration:0});const O=we(v.duration);if(n(1,s={targets:U(v.numberOfNodes),activeIndex:0,duration:O,currentIteration:u-N}),n(2,a=null),await ve(500),!s)return;const q=await E();if(!s)return;n(2,a=q?"success":"fail");const B=q&&N<=1;q&&B?R.playSound("win"):!B&&q?R.playSound("iteration"):R.playSound("lose"),await ve(500);let Z=setTimeout(()=>{if(!i||!g)return;if(g.getContext("2d").clearRect(0,0,g.width,g.height),q&&N>0)if(N--,N>0)b(N,v);else{R.finish(!0),n(1,s=null);return}else{R.finish(!1),n(1,s=null);return}},1e3);I.push(()=>{Z&&clearTimeout(Z),n(2,a=null)})}function A(){if(!r.active||s)return;const{iterations:N,config:v}=r;n(3,u=N),b(N,v)}function U(N){const v=we(N),O=[];for(let q=0;q<v;q++)O.push({x:Ke(3,97),y:Ke(3,97),selected:q===0});return w(O)}function m(N){const{activeIndex:v}=s;N!==v+1?_=!0:(R.playSound("primary"),n(1,s.activeIndex++,s),n(1,s.targets[N].selected=!0,s))}function w(N){const v=[N[0]],O=N.slice(1);for(let q=0;q<N.length-1;q++){const B=v[q];let Z=0,Q=1/0;for(let V=0;V<O.length;V++){const J=Ki(B.x,B.y,O[V].x,O[V].y);J<Q&&(Q=J,Z=V)}v.push(O[Z]),O.splice(Z,1)}return v}function z(){if(a||!g)return;const N=g.getContext("2d");N.clearRect(0,0,g.width,g.height),T(N),requestAnimationFrame(z)}function T(N){if(a||!g||!i||!s)return;N.beginPath();const{targets:v,activeIndex:O}=s,q=O<v.length-1?O:v.length-1,B=window.innerHeight/100;function Z(ae,ye,Ae=!1){const Pe=(Ae?An:ns)*B/2;return[ae+Pe,ye+Pe]}const Q=v[0];let[V,J]=Z(Q.x/100*c,Q.y/100*d,!0);for(let ae=0;ae<q;ae++){const ye=v[ae],Ae=ye.x/100*c,Pe=ye.y/100*d,De=v[ae+1],Fe=De.x/100*c,yt=De.y/100*d,[Rt,rt]=Z(Ae,Pe),[Dn,On]=Z(Fe,yt);N.moveTo(Rt,rt),N.lineTo(Dn,On),ae===q-1&&(V=Dn,J=On)}O!==v.length-1&&V&&J&&(N.moveTo(V,J),N.lineTo(p.x,p.y)),N.strokeStyle=S,N.lineWidth=B*An/4,N.lineCap="round",N.stroke()}function k(N){Ce[N?"unshift":"push"](()=>{g=N,n(6,g)})}const F=N=>m(N);function j(){c=this.clientWidth,d=this.clientHeight,n(4,c),n(5,d)}return[i,s,a,u,c,d,g,l,o,m,k,F,j]}class Zo extends he{constructor(e){super(),de(this,e,Yo,qo,ce,{},null,[-1,-1])}}const{window:Xo}=us;function Vr(t,e,n){const r=t.slice();return r[34]=e[n].x,r[35]=e[n].y,r[37]=n,r}function mn(t){const e=t.slice(),n=e[1];return e[33]=n.nodes,e}function Kr(t){let e,n;return e=new Be({props:{title:["Un","tangle"],subtitle:"Make sure none of the lines intersect each other.",iterations:t[3],iteration:t[1].currentIteration,progress:t[10]/t[1].duration*100,state:t[2],$$slots:{default:[Jo]},$$scope:{ctx:t}}}),{c(){ee(e.$$.fragment)},m(r,l){$(e,r,l),n=!0},p(r,l){const i={};l[0]&8&&(i.iterations=r[3]),l[0]&2&&(i.iteration=r[1].currentIteration),l[0]&1026&&(i.progress=r[10]/r[1].duration*100),l[0]&4&&(i.state=r[2]),l[0]&502|l[1]&128&&(i.$$scope={dirty:l,ctx:r}),e.$set(i)},i(r){n||(C(e.$$.fragment,r),n=!0)},o(r){P(e.$$.fragment,r),n=!1},d(r){x(e,r)}}}function Br(t){let e,n;function r(){return t[14](t[37])}return e=new ts({props:{root:t[5]==t[37],class:"hover:cursor-grab active:cursor-grabbing",iterationState:t[2],selected:!0,x:t[34],y:t[35]}}),e.$on("mousedown",r),{c(){ee(e.$$.fragment)},m(l,i){$(e,l,i),n=!0},p(l,i){t=l;const s={};i[0]&32&&(s.root=t[5]==t[37]),i[0]&4&&(s.iterationState=t[2]),i[0]&2&&(s.x=t[34]),i[0]&2&&(s.y=t[35]),e.$set(s)},i(l){n||(C(e.$$.fragment,l),n=!0)},o(l){P(e.$$.fragment,l),n=!1},d(l){x(e,l)}}}function Jo(t){let e,n,r,l,i,s=re(t[33]),a=[];for(let u=0;u<s.length;u+=1)a[u]=Br(Vr(t,s,u));const o=u=>P(a[u],1,1,()=>{a[u]=null});return{c(){e=G("div"),n=G("canvas"),r=H();for(let u=0;u<a.length;u+=1)a[u].c();h(n,"width",t[6]),h(n,"height",t[7]),h(e,"class","w-[60vh] h-[60vh] aspect-square bg-secondary/90 border-[0.15vh] border-tertiary/50"),ie(()=>t[15].call(e))},m(u,f){D(u,e,f),L(e,n),t[13](n),L(e,r);for(let c=0;c<a.length;c+=1)a[c]&&a[c].m(e,null);l=Mn(e,t[15].bind(e)),i=!0},p(u,f){if((!i||f[0]&64)&&h(n,"width",u[6]),(!i||f[0]&128)&&h(n,"height",u[7]),f[0]&54){s=re(u[33]);let c;for(c=0;c<s.length;c+=1){const d=Vr(u,s,c);a[c]?(a[c].p(d,f),C(a[c],1)):(a[c]=Br(d),a[c].c(),C(a[c],1),a[c].m(e,null))}for(le(),c=s.length;c<a.length;c+=1)o(c);se()}},i(u){if(!i){for(let f=0;f<s.length;f+=1)C(a[f]);i=!0}},o(u){a=a.filter(Boolean);for(let f=0;f<a.length;f+=1)P(a[f]);i=!1},d(u){u&&W(e),t[13](null),Se(a,u),l()}}}function Qo(t){let e,n,r,l,i=t[0]&&Kr(mn(t));return{c(){i&&i.c(),e=pe()},m(s,a){i&&i.m(s,a),D(s,e,a),n=!0,r||(l=ge(Xo,"mouseup",t[12]),r=!0)},p(s,a){s[0]?i?(i.p(mn(s),a),a[0]&1&&C(i,1)):(i=Kr(mn(s)),i.c(),C(i,1),i.m(e.parentNode,e)):i&&(le(),P(i,1,1,()=>{i=null}),se())},i(s){n||(C(i),n=!0)},o(s){P(i),n=!1},d(s){s&&W(e),i&&i.d(s),r=!1,l()}}}const je=2.5;function $o(t,e){const n=t.x2-t.x1,r=t.y2-t.y1,l=e.x2-e.x1,i=e.y2-e.y1,s=i*n-l*r;if(s===0)return!1;const a=t.x1-e.x1,o=t.y1-e.y1,u=(l*o-i*a)/s,f=(n*o-r*a)/s;return u>0&&u<1&&f>0&&f<1}function xo(t,e,n){let r,l;fe(t,R,B=>n(18,r=B));let i=!1,s=null,a=null;const o=Me(0);fe(t,o,B=>n(10,l=B));let u=null,f,c=!1,d=null,g,p,_,y=!1;const S=window.innerHeight/100;function I(B,Z){const Q=B/100*g,V=Z/100*p,J=je*S/2;return[Q+J,V+J]}const M=getComputedStyle(document.body),K=`rgba(${M.getPropertyValue("--tertiary").split(" ").join(",")}, 1)`,E=`rgba(${M.getPropertyValue("--error").split(" ").join(",")}, 1)`;let b=[];function A(){b.forEach(B=>B()),b=[]}R.subscribe(B=>{let Z=B.active&&B.type===ue.Untangle&&!a;Z?(A(),n(0,i=!0),z()):i&&!Z&&(n(0,i=!1),n(1,s=null),n(2,a=null),U(),A())});function U(){f==null||f.removeListener(),f=null}async function m(){if(!i)return;n(5,d=-1),n(9,y=!1),k();let B=setTimeout(()=>{o.set(s.duration,{duration:s.duration})},500);return b.push(()=>{B&&clearTimeout(B)}),new Promise((Z,Q)=>{let V=setTimeout(()=>{J(!1)},s.duration+500);b.push(()=>{V&&clearTimeout(V),Z(!1)}),f=Te(xt.move,ae=>{if(!i||!_||(y&&J(!0),!c))return;let{nodes:ye}=s;const Ae=_==null?void 0:_.getBoundingClientRect(),Pe=ae.clientX-Ae.left,De=ae.clientY-Ae.top;let Fe=Pe/g*100,yt=De/p*100;const Rt=100-je;Fe=Math.min(Math.max(Fe,je),Rt),yt=Math.min(Math.max(yt,je),Rt);let rt=je/2*S;rt=rt/g*100,ye[d].x=Fe-rt,ye[d].y=yt-rt});function J(ae){const ye=l;o.set(ye,{duration:0}),n(4,c=!1),clearTimeout(V),Z(ae)}})}async function w(B,Z){if(!i)return;U(),o.set(0,{duration:0});const Q=we(Z.duration);if(n(1,s={nodes:T(Z.numberOfNodes),duration:Q,currentIteration:u-B,intersections:0}),n(2,a=null),await ve(500),!s)return;const V=await m();if(!s)return;n(2,a=V?"success":"fail");const J=V&&B<=1;V&&J?R.playSound("win"):!J&&V?R.playSound("iteration"):R.playSound("lose");let ae=setTimeout(()=>{if(!i||!_)return;if(_.getContext("2d").clearRect(0,0,_.width,_.height),V&&B>0)if(B--,B>0)w(B,Z);else{R.finish(!0),n(1,s=null);return}else{R.finish(!1),n(1,s=null);return}},1e3);b.push(()=>{ae&&clearTimeout(ae),n(2,a=null)})}function z(){if(!r.active||a)return;const{iterations:B,config:Z}=r;n(3,u=B),w(B,Z)}function T(B){const Z=we(B),Q=[];for(let V=0;V<Z;V++){const J=100-je;Q.push({x:Ke(je,J),y:Ke(je,J)})}return Q}function k(){if(a||!_)return;_.getContext("2d").clearRect(0,0,_.width,_.height),j(),requestAnimationFrame(k)}function F(B){if(!_)return;n(1,s.intersections=0,s);const Z=_.getContext("2d");Z.lineWidth=S*je/4;for(let Q=0;Q<B.length;Q++){const V=B[Q];let J=!1;for(let ae=0;ae<B.length;ae++){if(Math.abs(Q-ae)<=1||Q===0&&ae===B.length-1||ae===0&&Q===B.length-1)continue;const ye=B[ae];if(J=$o(V,ye),J){n(1,s.intersections++,s);break}}Z.beginPath(),Z.moveTo(V.x1,V.y1),Z.lineTo(V.x2,V.y2),Z.strokeStyle=J?E:K,Z.stroke()}}function j(){if(a||!_)return;const B=s.nodes,Z=[];for(let Q=0;Q<B.length;Q++){const V=B[Q],[J,ae]=I(V.x,V.y),ye=Q===0?B.length-1:Q-1,Ae=B[ye],[Pe,De]=I(Ae.x,Ae.y);Z.push({x1:Pe,y1:De,x2:J,y2:ae,intersecting:!1})}F(Z)}const N=()=>{n(4,c=!1),n(5,d=-1),n(9,y=i&&(s==null?void 0:s.intersections)==0)};function v(B){Ce[B?"unshift":"push"](()=>{_=B,n(8,_)})}const O=B=>{n(4,c=!0),n(5,d=B)};function q(){g=this.clientWidth,p=this.clientHeight,n(6,g),n(7,p)}return[i,s,a,u,c,d,g,p,_,y,l,o,N,v,O,q]}class ea extends he{constructor(e){super(),de(this,e,xo,Qo,ce,{},null,[-1,-1])}}function ta(t){let e,n,r,l,i;const s=t[3].default,a=$e(s,t,t[2],null);return{c(){e=G("button"),a&&a.c(),h(e,"class",n="w-full h-full default-all-transition border scale-110 focus:outline-none "+(t[1]=="success"?"border-success glow-success bg-success/50":t[1]=="fail"?"border-error glow-error bg-error/50":t[0]?"bg-accent/50 glow-accent border-accent":"bg-secondary/90 scale-100 border-secondary"))},m(o,u){D(o,e,u),a&&a.m(e,null),r=!0,l||(i=ge(e,"click",t[4]),l=!0)},p(o,[u]){a&&a.p&&(!r||u&4)&&et(a,s,o,o[2],r?xe(s,o[2],u,null):tt(o[2]),null),(!r||u&3&&n!==(n="w-full h-full default-all-transition border scale-110 focus:outline-none "+(o[1]=="success"?"border-success glow-success bg-success/50":o[1]=="fail"?"border-error glow-error bg-error/50":o[0]?"bg-accent/50 glow-accent border-accent":"bg-secondary/90 scale-100 border-secondary")))&&h(e,"class",n)},i(o){r||(C(a,o),r=!0)},o(o){P(a,o),r=!1},d(o){o&&W(e),a&&a.d(o),l=!1,i()}}}function na(t,e,n){let{$$slots:r={},$$scope:l}=e,{active:i=!1}=e,{state:s=null}=e;function a(o){Gl.call(this,t,o)}return t.$$set=o=>{"active"in o&&n(0,i=o.active),"state"in o&&n(1,s=o.state),"$$scope"in o&&n(2,l=o.$$scope)},[i,s,l,r,a]}class ra extends he{constructor(e){super(),de(this,e,na,ta,ce,{active:0,state:1})}}const la=[[7,12,11,13,17],[10,12,14],[0,2,4,5,7,9,15,17,19,20,22,24],[1,3,5,6,8,9,10,11,13,14,15,16,18,19,21,23],[5,6,8,9,15,19,20,21,23,24],[0,1,2,3,5,6,7,9,10,11,12,14,18,19,20,21,23,24],[10,12,14,15,17,19,21,22,23],[0,1,2,3,5,9,10,14,15,19,20,21,22,23],[7,11,13,15,17,19,21,23],[1,3,5,6,7,8,9,11,12,13,16,18,19,20,21,22],[1,2,3,6,7,8,11,12,13],[0,2,4,5,7,9,10,12,14,15,17,19,21,22,23],[0,1,2,3,4,6,8,10,11,13,14,16,17,18,21,23],[3,7,9,11,13,15,17,21],[11,16,21],[6,16],[0,5,10,15,20,21,22,23,24],[12,16,17,18,20,21,22,23,24],[2,6,8,10,12,14,16,18,22],[0,2,4,10,12,14,20,22,24],[10,14],[1,2,3,4,6,11,12,13,16,21],[1,2,3,5,9,10,14,15,19,21,22,23],[12,13,14,17,18,22],[10,14,15,16,17,18,19,21,24],[0,5,6,10,11,12,15,16,17,18,21,22,23,24],[0,4,5,9,10,11,12,13,14,15,19,20,24],[2,6,7,8,12,17,22],[12,13,14,17,18,19,22,23,24],[6],[12],[0,4,5,6,9,10,12,14,15,18,19,20,24],[0,1,2,3,4,8,12,16,20,21,22,23,24],[3,8,10,12,14,15,19,20,23,24],[2,4,5,9,10,14,16,17,19,21,22,23,24],[3,4,6,8,10,14,15,17,19],[2,6,8,10,14,15,16,17,18,19,20,24],[6,7,8,11,12,13,16,17,18],[0,2,4,6,8,10,12,14,16,18,20,22,24],[1,3,5,10,11,17,18,21,23],[11,13],[0,4,6,8,12,17,22],[0,1,2,5,8,10,11,12,15,18,20,21,22],[0,4,5,6,8,10,11,12,16,21,22,23],[5,6,8,9,10,11,12,13,14,17,21,22,23],[1,2,3,5,7,12,13,14,15,16,17,18,20,22,24],[2,6,7,8,10,11,12,13,14,16,17,18,22],[2,5,6,7,8,9,10,12,16,19,24],[5,9,12,15,19],[0,4,6,8,12,16,18,20,24],[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24]];function Hr(t,e,n){const r=t.slice();return r[19]=e[n],r[21]=n,r}function pn(t){const e=t.slice(),n=e[1];return e[18]=n.items,e}function qr(t){let e,n;return e=new Be({props:{title:["Lights","Out"],subtitle:"Turn off all the lights.",iterations:t[3],iteration:t[1].currentIteration,progress:t[4]/t[1].duration*100,state:t[2],$$slots:{default:[sa]},$$scope:{ctx:t}}}),{c(){ee(e.$$.fragment)},m(r,l){$(e,r,l),n=!0},p(r,l){const i={};l&8&&(i.iterations=r[3]),l&2&&(i.iteration=r[1].currentIteration),l&18&&(i.progress=r[4]/r[1].duration*100),l&4&&(i.state=r[2]),l&4194310&&(i.$$scope={dirty:l,ctx:r}),e.$set(i)},i(r){n||(C(e.$$.fragment,r),n=!0)},o(r){P(e.$$.fragment,r),n=!1},d(r){x(e,r)}}}function Yr(t){let e,n,r,l,i;function s(...o){return t[7](t[21],...o)}function a(...o){return t[8](t[21],...o)}return n=new ra({props:{state:t[2],active:t[19]}}),n.$on("click",s),n.$on("touchstart",a),{c(){e=G("div"),ee(n.$$.fragment),r=H(),h(e,"class","w-full h-full grid place-items-center")},m(o,u){D(o,e,u),$(n,e,null),L(e,r),i=!0},p(o,u){t=o;const f={};u&4&&(f.state=t[2]),u&2&&(f.active=t[19]),n.$set(f)},i(o){i||(C(n.$$.fragment,o),ie(()=>{i&&(l||(l=X(e,te,{delay:t[21]*25},!0)),l.run(1))}),i=!0)},o(o){P(n.$$.fragment,o),l||(l=X(e,te,{delay:t[21]*25},!1)),l.run(0),i=!1},d(o){o&&W(e),x(n),o&&l&&l.end()}}}function sa(t){let e,n,r=re(t[18]),l=[];for(let s=0;s<r.length;s+=1)l[s]=Yr(Hr(t,r,s));const i=s=>P(l[s],1,1,()=>{l[s]=null});return{c(){e=G("div");for(let s=0;s<l.length;s+=1)l[s].c();Y(e,"grid-template-columns","repeat(5, 1fr)"),h(e,"class","w-[60vh] h-[60vh] aspect-square gap-[2vh] grid")},m(s,a){D(s,e,a);for(let o=0;o<l.length;o+=1)l[o]&&l[o].m(e,null);n=!0},p(s,a){if(a&70){r=re(s[18]);let o;for(o=0;o<r.length;o+=1){const u=Hr(s,r,o);l[o]?(l[o].p(u,a),C(l[o],1)):(l[o]=Yr(u),l[o].c(),C(l[o],1),l[o].m(e,null))}for(le(),o=r.length;o<l.length;o+=1)i(o);se()}},i(s){if(!n){for(let a=0;a<r.length;a+=1)C(l[a]);n=!0}},o(s){l=l.filter(Boolean);for(let a=0;a<l.length;a+=1)P(l[a]);n=!1},d(s){s&&W(e),Se(l,s)}}}function ia(t){let e,n,r=t[0]&&qr(pn(t));return{c(){r&&r.c(),e=pe()},m(l,i){r&&r.m(l,i),D(l,e,i),n=!0},p(l,[i]){l[0]?r?(r.p(pn(l),i),i&1&&C(r,1)):(r=qr(pn(l)),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(le(),P(r,1,1,()=>{r=null}),se())},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d(l)}}}function oa(t,e,n){let r,l;fe(t,R,E=>n(11,r=E));let i=!1,s=null,a=null;const o=Me(0);fe(t,o,E=>n(4,l=E));let u=null,f=null,c=[];function d(){c.forEach(E=>E()),c=[]}R.subscribe(E=>{let b=E.active&&E.type===ue.LightsOut&&!a;b?(d(),n(0,i=!0),_()):i&&!b&&(n(0,i=!1),n(1,s=null),n(2,a=null),d())});async function g(){if(!i)return;let E=setTimeout(()=>{o.set(s.duration,{duration:s.duration})},500);return c.push(()=>{E&&clearTimeout(E)}),new Promise((b,A)=>{let U=setTimeout(()=>{m(f(!0))},s.duration+500);c.push(()=>{U&&clearTimeout(U),b(!1)}),f=(w=!1)=>{const z=s==null?void 0:s.items.every(T=>T===!0);if(w)return z;s.items.every(T=>T===!1)&&m(!0)};function m(w){const z=l;o.set(z,{duration:0}),clearTimeout(U),b(w)}})}async function p(E,b){if(!i)return;o.set(0,{duration:0});const A=we(b.duration),U=we(b.level);if(n(1,s={level:U,duration:A,currentIteration:u-E,items:I(U)}),n(2,a=null),await ve(500),!s)return;const m=await g();if(!s)return;n(2,a=m?"success":"fail");const w=m&&E<=1;m&&w?R.playSound("win"):!w&&m?R.playSound("iteration"):R.playSound("lose");let z=setTimeout(()=>{if(i)if(m&&E>0)if(E--,E>0)p(E,b);else{R.finish(!0),n(1,s=null);return}else{R.finish(!1),n(1,s=null);return}},1e3);c.push(()=>{z&&clearTimeout(z),n(2,a=null)})}function _(){if(!r.active||a)return;const{iterations:E,config:b}=r;n(3,u=E),p(E,b)}let y=(E,b)=>{if(!i||f&&f())return;b.preventDefault(),b.stopPropagation(),S(E),R.playSound("primary"),navigator.vibrate&&navigator.vibrate(100);let A=[],U=E%5,m=Math.floor(E/5);U>0&&U<4?(A.push(E-1),A.push(E+1)):U==0?A.push(E+1):U==4&&A.push(E-1),m>0&&m<4?(A.push(E-5),A.push(E+5)):m==0?A.push(E+5):m==4&&A.push(E-5),A.map(w=>{S(w)}),f()};function S(E){if(!i||!s)return;let b=s.items[E];b!==void 0&&n(1,s.items[E]=!b,s)}function I(E){const b=[];for(let A=0;A<25;A++)la[E].indexOf(A)!==-1?b.push(!0):b.push(!1);return b}return[i,s,a,u,l,o,y,(E,b)=>y(E,b),(E,b)=>y(E,b)]}class aa extends he{constructor(e){super(),de(this,e,oa,ia,ce,{})}}function Zr(t,e,n){const r=t.slice();r[22]=e[n],r[25]=n;const l=r[4][r[25]];return r[23]=l,r}function Xr(t){let e,n;return e=new Be({props:{state:t[2],title:["Digit","Dazzle"],subtitle:"Find the correct code.",iterations:t[6],iteration:t[1].currentIteration,progress:t[7]/t[1].duration*100,$$slots:{default:[ua]},$$scope:{ctx:t}}}),{c(){ee(e.$$.fragment)},m(r,l){$(e,r,l),n=!0},p(r,l){const i={};l&4&&(i.state=r[2]),l&64&&(i.iterations=r[6]),l&2&&(i.iteration=r[1].currentIteration),l&130&&(i.progress=r[7]/r[1].duration*100),l&67108924&&(i.$$scope={dirty:l,ctx:r}),e.$set(i)},i(r){n||(C(e.$$.fragment,r),n=!0)},o(r){P(e.$$.fragment,r),n=!1},d(r){x(e,r)}}}function Jr(t){let e,n=t[23].code+"",r,l,i;return{c(){e=G("p"),r=Ee(n)},m(s,a){D(s,e,a),L(e,r),i=!0},p(s,a){(!i||a&16)&&n!==(n=s[23].code+"")&&ze(r,n)},i(s){i||(s&&ie(()=>{i&&(l||(l=X(e,te,{duration:250},!0)),l.run(1))}),i=!0)},o(s){s&&(l||(l=X(e,te,{duration:250},!1)),l.run(0)),i=!1},d(s){s&&W(e),s&&l&&l.end()}}}function Qr(t){let e,n,r,l=t[23].code&&Jr(t);return{c(){e=G("div"),l&&l.c(),n=H(),h(e,"class",r="grid place-items-center w-[10vh] aspect-square "+(t[2]=="fail"?"bg-error/50 glow-error ":t[23].checking?"bg-accent/25 shadow-accent border border-accent":t[23].state==="correct"?"bg-success/25 shadow-success border border-success":t[23].state==="included"?"bg-warning/25 shadow-warning border border-warning":" primary-bg"))},m(i,s){D(i,e,s),l&&l.m(e,null),L(e,n)},p(i,s){i[23].code?l?(l.p(i,s),s&16&&C(l,1)):(l=Jr(i),l.c(),C(l,1),l.m(e,n)):l&&(le(),P(l,1,1,()=>{l=null}),se()),s&20&&r!==(r="grid place-items-center w-[10vh] aspect-square "+(i[2]=="fail"?"bg-error/50 glow-error ":i[23].checking?"bg-accent/25 shadow-accent border border-accent":i[23].state==="correct"?"bg-success/25 shadow-success border border-success":i[23].state==="included"?"bg-warning/25 shadow-warning border border-warning":" primary-bg"))&&h(e,"class",r)},d(i){i&&W(e),l&&l.d()}}}function ua(t){let e,n,r,l,i,s,a,o,u=re({length:t[3]}),f=[];for(let c=0;c<u.length;c+=1)f[c]=Qr(Zr(t,u,c));return{c(){e=G("div"),n=G("div");for(let c=0;c<f.length;c+=1)f[c].c();r=H(),l=G("button"),i=Ee("Crack"),h(n,"class","flex font-bold text-[5vh] gap-[3vh]"),h(l,"class",s="w-full h-[5vh] "+(t[2]=="fail"?"bg-error/50 glow-error":"btn-accent")+" font-bold uppercase default-all-transition"),h(e,"class","w-fit h-fit flex flex-col gap-[1vh]")},m(c,d){D(c,e,d),L(e,n);for(let g=0;g<f.length;g+=1)f[g]&&f[g].m(n,null);L(e,r),L(e,l),L(l,i),a||(o=ge(l,"click",t[9]),a=!0)},p(c,d){if(d&28){u=re({length:c[3]});let g;for(g=0;g<u.length;g+=1){const p=Zr(c,u,g);f[g]?f[g].p(p,d):(f[g]=Qr(p),f[g].c(),f[g].m(n,null))}for(;g<f.length;g+=1)f[g].d(1);f.length=u.length}d&4&&s!==(s="w-full h-[5vh] "+(c[2]=="fail"?"bg-error/50 glow-error":"btn-accent")+" font-bold uppercase default-all-transition")&&h(l,"class",s)},d(c){c&&W(e),Se(f,c),a=!1,o()}}}function ca(t){let e,n,r=t[0]&&Xr(t);return{c(){r&&r.c(),e=pe()},m(l,i){r&&r.m(l,i),D(l,e,i),n=!0},p(l,[i]){l[0]?r?(r.p(l,i),i&1&&C(r,1)):(r=Xr(l),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(le(),P(r,1,1,()=>{r=null}),se())},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d(l)}}}const $r=250;function fa(t,e,n){let r,l;fe(t,R,m=>n(13,r=m));let i=!1,s=null,a=null;const o=Me(0);fe(t,o,m=>n(7,l=m));let u=null,f=[],c=!1,d=null,g=null,p,_=[];function y(){_.forEach(m=>m()),_=[]}R.subscribe(m=>{let w=m.active&&m.type===ue.DigitDazzle&&!a;w?(y(),n(0,i=!0),K()):i&&!w&&(n(0,i=!1),n(1,s=null),n(2,a=null),S(),y())});function S(){p==null||p.removeListener(),p=null}async function I(){if(!i)return;let m=setTimeout(()=>{o.set(s.duration,{duration:s.duration})},500);return _.push(()=>{m&&clearTimeout(m)}),new Promise((w,z)=>{let T=!1,k=setTimeout(()=>{T=!0,!c&&j(!1)},s.duration+500);_.push(()=>{k&&clearTimeout(k),w(!1)});let F=Te(Ne.down,N=>{if(c)return;if(N.key.toUpperCase()==="BACKSPACE"){R.playSound("primary");let O=f.findIndex(q=>q.code===null);O=O===-1?u:O,O=O!==0?O-1:O,n(4,f[O].code=null,f)}});n(5,d=async()=>{await A()?j(!0):T&&j(!1)}),p=Te(Ne.pressed,N=>{if(c)return;const v=N.key.toUpperCase(),O=f.findIndex(q=>q.code===null);v.length===1&&v>="0"&&v<="9"&&O<u&&O!==-1?(R.playSound("primary"),n(4,f[O].code=Number(v),f)):v==="ENTER"&&(R.playSound("primary"),d())});function j(N){if(c)return;const v=l;o.set(v,{duration:0}),F.removeListener(),clearTimeout(k),w(N)}})}async function M(m,w){if(!i)return;n(5,d=null),n(4,f=[]),n(3,u=null),S(),o.set(0,{duration:0});const z=we(w.duration);let T;if(w.length)n(3,u=b(w.length)),T=E(u);else{const N=w.code.length-m,v=w.code[N].toString();n(3,u=v.length),T=Array.from(v).map(Number)}if(n(4,f=Array.from({length:u},()=>({code:null,checking:!1,state:null}))),n(1,s={code:T,duration:z,currentIteration:g-m}),n(2,a=null),await ve(500),!s)return;const k=await I();if(!s)return;n(2,a=k?"success":"fail");const F=k&&m<=1;k&&F?R.playSound("win"):!F&&k?R.playSound("iteration"):R.playSound("lose");let j=setTimeout(()=>{if(i)if(k&&m>0)if(m--,m>0)M(m,w);else{R.finish(!0),n(1,s=null);return}else{R.finish(!1),n(1,s=null);return}},1e3);_.push(()=>{j&&clearTimeout(j),n(2,a=null)})}function K(){if(!r.active||s)return;const{iterations:m,config:w}=r;n(6,g=m),M(m,w)}function E(m){return Bi(m)}function b(m){const w=sn.SIZE.MIN,z=sn.SIZE.MAX;return Array.isArray(m)?(m[0]=m[0]<w?w:m[0],m[1]=m[1]>z?z:m[1],we(m)):m<w||m>z?(m=m<w?w:m,m=m>z?z:m,we(m)):sn.DEFAULT_LENGTH}async function A(){if(!i||!f.every(T=>T.code!==null))return;c=!0;const w=f.map(T=>T.code);let z=!0;for(let T=0;T<w.length;T++){const k=w[T],F=k===s.code[T];n(4,f[T].checking=!0,f),F?n(4,f[T].state="correct",f):(s.code.includes(k)?n(4,f[T].state="included",f):n(4,f[T].state=null,f),z=!1),R.playSound("secondary"),await ve($r),n(4,f[T].checking=!1,f)}return setTimeout(async()=>{if(!z){await ve(500);for(let T=f.length-1;T>=0;T--)n(4,f[T].code=null,f),R.playSound("secondary"),await ve($r/2)}},0),c=!1,z}return[i,s,a,u,f,d,g,l,o,()=>d()]}class da extends he{constructor(e){super(),de(this,e,fa,ca,ce,{})}}function xr(t,e,n){const r=t.slice();r[22]=e[n],r[25]=n;const l=r[4][r[25]];return r[23]=l,r}function el(t){let e,n;return e=new Be({props:{state:t[2],title:["Word","Wiz"],subtitle:"Find the correct word.",iterations:t[6],iteration:t[1].currentIteration,progress:t[7]/t[1].duration*100,$$slots:{default:[ha]},$$scope:{ctx:t}}}),{c(){ee(e.$$.fragment)},m(r,l){$(e,r,l),n=!0},p(r,l){const i={};l&4&&(i.state=r[2]),l&64&&(i.iterations=r[6]),l&2&&(i.iteration=r[1].currentIteration),l&130&&(i.progress=r[7]/r[1].duration*100),l&67108924&&(i.$$scope={dirty:l,ctx:r}),e.$set(i)},i(r){n||(C(e.$$.fragment,r),n=!0)},o(r){P(e.$$.fragment,r),n=!1},d(r){x(e,r)}}}function tl(t){let e,n=t[23].letter+"",r,l,i;return{c(){e=G("p"),r=Ee(n)},m(s,a){D(s,e,a),L(e,r),i=!0},p(s,a){(!i||a&16)&&n!==(n=s[23].letter+"")&&ze(r,n)},i(s){i||(s&&ie(()=>{i&&(l||(l=X(e,te,{duration:250},!0)),l.run(1))}),i=!0)},o(s){s&&(l||(l=X(e,te,{duration:250},!1)),l.run(0)),i=!1},d(s){s&&W(e),s&&l&&l.end()}}}function nl(t){let e,n,r,l=t[23].letter&&tl(t);return{c(){e=G("div"),l&&l.c(),n=H(),h(e,"class",r="grid place-items-center w-[10vh] aspect-square "+(t[2]=="fail"?"bg-error/50 glow-error ":t[23].checking?"bg-accent/25 shadow-accent border border-accent":t[23].state==="correct"?"bg-success/25 shadow-success border border-success":t[23].state==="included"?"bg-warning/25 shadow-warning border border-warning":" primary-bg"))},m(i,s){D(i,e,s),l&&l.m(e,null),L(e,n)},p(i,s){i[23].letter?l?(l.p(i,s),s&16&&C(l,1)):(l=tl(i),l.c(),C(l,1),l.m(e,n)):l&&(le(),P(l,1,1,()=>{l=null}),se()),s&20&&r!==(r="grid place-items-center w-[10vh] aspect-square "+(i[2]=="fail"?"bg-error/50 glow-error ":i[23].checking?"bg-accent/25 shadow-accent border border-accent":i[23].state==="correct"?"bg-success/25 shadow-success border border-success":i[23].state==="included"?"bg-warning/25 shadow-warning border border-warning":" primary-bg"))&&h(e,"class",r)},d(i){i&&W(e),l&&l.d()}}}function ha(t){let e,n,r,l,i,s,a=re({length:t[3]}),o=[];for(let u=0;u<a.length;u+=1)o[u]=nl(xr(t,a,u));return{c(){e=G("div"),n=G("div");for(let u=0;u<o.length;u+=1)o[u].c();r=H(),l=G("button"),i=Ee("Crack"),h(n,"class","flex font-bold text-[5vh] gap-[3vh]"),h(l,"class",s="w-full h-[5vh] "+(t[2]=="fail"?"bg-error/50 glow-error":"btn-accent")+" font-bold uppercase default-all-transition"),h(e,"class","w-fit h-fit flex flex-col gap-[1vh]")},m(u,f){D(u,e,f),L(e,n);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(n,null);L(e,r),L(e,l),L(l,i),t[9](l)},p(u,f){if(f&28){a=re({length:u[3]});let c;for(c=0;c<a.length;c+=1){const d=xr(u,a,c);o[c]?o[c].p(d,f):(o[c]=nl(d),o[c].c(),o[c].m(n,null))}for(;c<o.length;c+=1)o[c].d(1);o.length=a.length}f&4&&s!==(s="w-full h-[5vh] "+(u[2]=="fail"?"bg-error/50 glow-error":"btn-accent")+" font-bold uppercase default-all-transition")&&h(l,"class",s)},d(u){u&&W(e),Se(o,u),t[9](null)}}}function ga(t){let e,n,r=t[0]&&el(t);return{c(){r&&r.c(),e=pe()},m(l,i){r&&r.m(l,i),D(l,e,i),n=!0},p(l,[i]){l[0]?r?(r.p(l,i),i&1&&C(r,1)):(r=el(l),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(le(),P(r,1,1,()=>{r=null}),se())},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d(l)}}}const rl=250;function ma(t,e,n){let r,l;fe(t,R,m=>n(13,r=m));let i=!1,s=null,a=null;const o=Me(0);fe(t,o,m=>n(7,l=m));let u=null,f=[],c=!1,d=null,g=null,p,_=[];function y(){_.forEach(m=>m()),_=[]}R.subscribe(m=>{let w=m.active&&m.type===ue.WordWiz&&!a;w?(y(),n(0,i=!0),K()):i&&!w&&(n(0,i=!1),n(1,s=null),n(2,a=null),S(),y())});function S(){p==null||p.removeListener(),p=null}async function I(){if(!i)return;let m=setTimeout(()=>{o.set(s.duration,{duration:s.duration})},500);return _.push(()=>{m&&clearTimeout(m)}),new Promise((w,z)=>{let T=!1,k=setTimeout(()=>{T=!0,!c&&N(!1)},s.duration+500);_.push(()=>{k&&clearTimeout(k),w(!1)});let F=Te(Ne.down,v=>{if(c)return;if(v.key.toUpperCase()==="BACKSPACE"){R.playSound("primary");let q=f.findIndex(B=>B.letter===null);q=q===-1?u:q,q=q!==0?q-1:q,n(4,f[q].letter=null,f)}});const j=async()=>{await A()?N(!0):T&&N(!1)};p=Te(Ne.pressed,v=>{if(c)return;const O=v.key.toUpperCase(),q=f.findIndex(B=>B.letter===null);O.length===1&&O>="A"&&O<="Z"&&q<u&&q!==-1?(n(4,f[q].letter=O,f),R.playSound("primary")):O==="ENTER"&&(R.playSound("primary"),j())}),d==null||d.addEventListener("click",j);function N(v){if(c)return;const O=l;o.set(O,{duration:0}),F.removeListener(),d==null||d.removeEventListener("click",j),clearTimeout(k),w(v)}})}async function M(m,w){if(!i)return;n(4,f=[]),n(3,u=null),S(),o.set(0,{duration:0});const z=we(w.duration);n(3,u=b(w.length));const T=E(u);if(n(4,f=Array.from({length:u},()=>({letter:null,checking:!1,state:null}))),n(1,s={word:T,duration:z,currentIteration:g-m}),n(2,a=null),await ve(500),!s)return;const k=await I();if(!s)return;n(2,a=k?"success":"fail");const F=k&&m<=1;k&&F?R.playSound("win"):!F&&k?R.playSound("iteration"):R.playSound("lose");let j=setTimeout(()=>{if(i)if(k&&m>0)if(m--,m>0)M(m,w);else{R.finish(!0),n(1,s=null);return}else{R.finish(!1),n(1,s=null);return}},1e3);_.push(()=>{j&&clearTimeout(j),n(2,a=null)})}function K(){if(!r.active||s)return;const{iterations:m,config:w}=r;n(6,g=m),M(m,w)}function E(m){return Hi(m).toUpperCase().split("")}function b(m){const w=on.SIZE.MIN,z=on.SIZE.MAX;return Array.isArray(m)?(m[0]=m[0]<w?w:m[0],m[1]=m[1]>z?z:m[1],we(m)):m<w||m>z?(m=m<w?w:m,m=m>z?z:m,we(m)):on.DEFAULT_LENGTH}async function A(){if(!i||!f.every(T=>T.letter!==null))return;c=!0;const w=f.map(T=>T.letter);let z=!0;for(let T=0;T<w.length;T++){const k=w[T],F=k===s.word[T];n(4,f[T].checking=!0,f),F?n(4,f[T].state="correct",f):(s.word.includes(k)?n(4,f[T].state="included",f):n(4,f[T].state=null,f),z=!1),R.playSound("secondary"),await ve(rl),n(4,f[T].checking=!1,f)}return setTimeout(async()=>{if(!z){await ve(500);for(let T=f.length-1;T>=0;T--)n(4,f[T].letter=null,f),R.playSound("secondary"),await ve(rl/2)}},0),c=!1,z}function U(m){Ce[m?"unshift":"push"](()=>{d=m,n(5,d)})}return[i,s,a,u,f,d,g,l,o,U]}class pa extends he{constructor(e){super(),de(this,e,ma,ga,ce,{})}}const qt={size:80,innerHoleSize:60,min:2,max:20,gap:1,limit:360};function ba(t){let e,n,r,l,i;return{c(){e=oe("path"),h(e,"d",n=`M${t[12](t[6],t[6],t[7],t[2]*t[1]+t[3])},
        A${t[7]},${t[7]},0,${t[5]},1,
        ${t[12](t[6],t[6],t[7],t[2]*(t[1]+1)-t[3])},
        L${t[12](t[6],t[6],t[8],t[2]*(t[1]+1)-t[3]/2)},
        A${t[8]},${t[8]},0,${t[5]},0,
        ${t[12](t[6],t[6],t[8],t[2]*t[1]+t[3])},
        Z`),h(e,"class",r="default-all-transition stroke-[0.1vw] "+(!t[0]&&"hover:fill-accent/75")+" "+(t[0]=="success"?"stroke-success glow-success fill-success/50":t[0]=="fail"?"stroke-error glow-error fill-error/50":t[4]?"fill-accent glow-accent":"fill-accent/50 scale-100 stroke-accent")+" cursor-pointer")},m(s,a){D(s,e,a),l||(i=[ge(e,"mouseenter",t[10]),ge(e,"mouseleave",t[11]),ge(e,"click",t[19])],l=!0)},p(s,[a]){a&494&&n!==(n=`M${s[12](s[6],s[6],s[7],s[2]*s[1]+s[3])},
        A${s[7]},${s[7]},0,${s[5]},1,
        ${s[12](s[6],s[6],s[7],s[2]*(s[1]+1)-s[3])},
        L${s[12](s[6],s[6],s[8],s[2]*(s[1]+1)-s[3]/2)},
        A${s[8]},${s[8]},0,${s[5]},0,
        ${s[12](s[6],s[6],s[8],s[2]*s[1]+s[3])},
        Z`)&&h(e,"d",n),a&17&&r!==(r="default-all-transition stroke-[0.1vw] "+(!s[0]&&"hover:fill-accent/75")+" "+(s[0]=="success"?"stroke-success glow-success fill-success/50":s[0]=="fail"?"stroke-error glow-error fill-error/50":s[4]?"fill-accent glow-accent":"fill-accent/50 scale-100 stroke-accent")+" cursor-pointer")&&h(e,"class",r)},i:me,o:me,d(s){s&&W(e),l=!1,Le(i)}}}function _a(t,e,n){let r,l,i,s,a,o;const u=Qt();let{state:f}=e,{index:c}=e,{containerSize:d}=e,{pieAngle:g}=e,{gap:p}=e,{hoveredIndex:_}=e,{innerHoleSize:y}=e,{radius:S}=e,{active:I}=e;function M(){f||n(13,_=c)}function K(){n(13,_=null)}const E=Math.cos,b=Math.sin;function A(m,w,z,T){const k=Vi(T),F=m+z*E(k),j=w+z*b(k);return`${F.toPrecision(5)},${j.toPrecision(5)}`}const U=()=>u("click",c);return t.$$set=m=>{"state"in m&&n(0,f=m.state),"index"in m&&n(1,c=m.index),"containerSize"in m&&n(14,d=m.containerSize),"pieAngle"in m&&n(2,g=m.pieAngle),"gap"in m&&n(3,p=m.gap),"hoveredIndex"in m&&n(13,_=m.hoveredIndex),"innerHoleSize"in m&&n(15,y=m.innerHoleSize),"radius"in m&&n(16,S=m.radius),"active"in m&&n(4,I=m.active)},t.$$.update=()=>{t.$$.dirty&6&&n(17,r=Math.abs(g*c-g*(c+1))),t.$$.dirty&8194&&n(18,l=_===c),t.$$.dirty&327680&&n(8,i=S+(l?10:0)),t.$$.dirty&32768&&n(7,s=y/2),t.$$.dirty&16384&&n(6,a=d/2),t.$$.dirty&131072&&n(5,o=r>180?1:0)},[f,c,g,p,I,o,a,s,i,u,M,K,A,_,d,y,S,r,l,U]}class wa extends he{constructor(e){super(),de(this,e,_a,ba,ce,{state:0,index:1,containerSize:14,pieAngle:2,gap:3,hoveredIndex:13,innerHoleSize:15,radius:16,active:4})}}function ll(t,e,n){const r=t.slice();return r[30]=e[n],r[32]=n,r}function sl(t){var l;const e=t.slice(),n=(l=e[7])==null?void 0:l.clientWidth;e[26]=n;const r=qt;return e[27]=r.size,e[28]=r.gap,e[29]=r.innerHoleSize,e}function bn(t){var l;const e=t.slice(),n=e[4]/e[1].target*e[8].clientWidth;e[33]=n;const r=e[33]>e[7].clientWidth?(l=e[7])==null?void 0:l.clientWidth:e[33];return e[27]=r,e}function il(t){let e,n;return e=new Be({props:{state:t[2],title:["Circle","Sum"],iterations:t[6],iteration:t[1].currentIteration,progress:t[10]/t[1].duration*100,subtitle:"Find the right combination to match the target.",$$slots:{default:[ya]},$$scope:{ctx:t}}}),{c(){ee(e.$$.fragment)},m(r,l){$(e,r,l),n=!0},p(r,l){const i={};l[0]&4&&(i.state=r[2]),l[0]&64&&(i.iterations=r[6]),l[0]&2&&(i.iteration=r[1].currentIteration),l[0]&1026&&(i.progress=r[10]/r[1].duration*100),l[0]&958|l[1]&8&&(i.$$scope={dirty:l,ctx:r}),e.$set(i)},i(r){n||(C(e.$$.fragment,r),n=!0)},o(r){P(e.$$.fragment,r),n=!1},d(r){x(e,r)}}}function ol(t){let e,n;return{c(){e=G("div"),Y(e,"width",t[27]+"px"),h(e,"class",n="aspect-square rounded-full absolute default-all-transition "+(t[2]=="success"?"border-success glow-success bg-success/50":t[2]=="fail"?"border-error glow-error bg-error/50":t[33]>t[8].clientWidth?"glow-error bg-error/50":"bg-accent glow-accent"))},m(r,l){D(r,e,l)},p(r,l){l[0]&402&&Y(e,"width",r[27]+"px"),l[0]&278&&n!==(n="aspect-square rounded-full absolute default-all-transition "+(r[2]=="success"?"border-success glow-success bg-success/50":r[2]=="fail"?"border-error glow-error bg-error/50":r[33]>r[8].clientWidth?"glow-error bg-error/50":"bg-accent glow-accent"))&&h(e,"class",n)},d(r){r&&W(e)}}}function va(t){let e,n,r,l=re(t[1].toggles),i=[];for(let a=0;a<l.length;a+=1)i[a]=al(ll(t,l,a));const s=a=>P(i[a],1,1,()=>{i[a]=null});return{c(){e=oe("svg");for(let a=0;a<i.length;a+=1)i[a].c();Y(e,"width",t[27]+"%"),Y(e,"rotate",t[3]+"deg"),h(e,"class","absolute z-[100] overflow-visible aspect-square")},m(a,o){D(a,e,o);for(let u=0;u<i.length;u+=1)i[u]&&i[u].m(e,null);t[16](e),r=!0},p(a,o){if(o[0]&4774){l=re(a[1].toggles);let u;for(u=0;u<l.length;u+=1){const f=ll(a,l,u);i[u]?(i[u].p(f,o),C(i[u],1)):(i[u]=al(f),i[u].c(),C(i[u],1),i[u].m(e,null))}for(le(),u=l.length;u<i.length;u+=1)s(u);se()}(!r||o[0]&8)&&Y(e,"rotate",a[3]+"deg")},i(a){if(!r){for(let o=0;o<l.length;o+=1)C(i[o]);ie(()=>{r&&(n||(n=X(e,te,{delay:250},!0)),n.run(1))}),r=!0}},o(a){i=i.filter(Boolean);for(let o=0;o<i.length;o+=1)P(i[o]);n||(n=X(e,te,{delay:250},!1)),n.run(0),r=!1},d(a){a&&W(e),Se(i,a),t[16](null),a&&n&&n.end()}}}function al(t){let e,n,r;function l(a){t[14](a)}function i(...a){return t[15](t[32],...a)}let s={index:t[32],containerSize:t[26],pieAngle:t[9],radius:t[26]/2,gap:t[28],state:t[2],innerHoleSize:t[29]/100*t[26],active:t[30].active};return t[5]!==void 0&&(s.hoveredIndex=t[5]),e=new wa({props:s}),Ce.push(()=>Ze(e,"hoveredIndex",l)),e.$on("click",i),{c(){ee(e.$$.fragment)},m(a,o){$(e,a,o),r=!0},p(a,o){t=a;const u={};o[0]&128&&(u.containerSize=t[26]),o[0]&512&&(u.pieAngle=t[9]),o[0]&128&&(u.radius=t[26]/2),o[0]&4&&(u.state=t[2]),o[0]&128&&(u.innerHoleSize=t[29]/100*t[26]),o[0]&2&&(u.active=t[30].active),!n&&o[0]&32&&(n=!0,u.hoveredIndex=t[5],Ye(()=>n=!1)),e.$set(u)},i(a){r||(C(e.$$.fragment,a),r=!0)},o(a){P(e.$$.fragment,a),r=!1},d(a){x(e,a)}}}function ya(t){let e,n,r,l,i,s=t[4]!==null&&t[8]&&ol(bn(t)),a=qt&&va(sl(t));return{c(){e=G("div"),n=G("div"),s&&s.c(),l=H(),a&&a.c(),h(n,"class","w-1/3 h-1/3 grid place-items-center overflow-hidden aspect-square bg-secondary/90 border-[0.15vh] border-tertiary/50 rounded-full"),h(e,"class","w-[60vh] h-[60vh] grid place-items-center aspect-square rounded-full overflow-hidden")},m(o,u){D(o,e,u),L(e,n),s&&s.m(n,null),t[13](n),L(e,l),a&&a.m(e,null),i=!0},p(o,u){o[4]!==null&&o[8]?s?s.p(bn(o),u):(s=ol(bn(o)),s.c(),s.m(n,null)):s&&(s.d(1),s=null),qt&&a.p(sl(o),u)},i(o){i||(ie(()=>{i&&(r||(r=X(n,te,{delay:500},!0)),r.run(1))}),C(a),i=!0)},o(o){r||(r=X(n,te,{delay:500},!1)),r.run(0),P(a),i=!1},d(o){o&&W(e),s&&s.d(),t[13](null),o&&r&&r.end(),a&&a.d()}}}function ka(t){let e,n,r=t[0]&&il(t);return{c(){r&&r.c(),e=pe()},m(l,i){r&&r.m(l,i),D(l,e,i),n=!0},p(l,i){l[0]?r?(r.p(l,i),i[0]&1&&C(r,1)):(r=il(l),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(le(),P(r,1,1,()=>{r=null}),se())},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d(l)}}}function Sa(t){const e=[];for(let n=0;n<t;n++)e.push({value:Math.floor(Math.random()*100),active:!1});return e}function Ia(t,e,n){let r,l;fe(t,R,k=>n(19,r=k));let i=!1,s=null,a=null;const o=Me(0);fe(t,o,k=>n(10,l=k));let u=0,f=0,c=null,d=null,g=null,p=null,_=null,y=[];function S(){y.forEach(k=>k()),y=[]}R.subscribe(k=>{let F=k.active&&k.type===ue.CircleSum&&!a;F?(S(),n(0,i=!0),K()):i&&!F&&(n(0,i=!1),n(1,s=null),n(2,a=null),S())});async function I(){if(!i)return;let k=setTimeout(()=>{o.set(s.duration,{duration:s.duration})},500);return y.push(()=>{k&&clearTimeout(k)}),new Promise((F,j)=>{let N=setTimeout(()=>{v(!1)},s.duration+500);y.push(()=>{N&&clearTimeout(N),F(!1)}),_=()=>{f===s.target&&v(!0)};function v(O){const q=l;o.set(q,{duration:0}),_=null,clearTimeout(N),F(O)}})}async function M(k,F){if(!i)return;o.set(0,{duration:0}),n(3,u=0),n(4,f=0);const j=we(F.length),N=Sa(j),v=E(N);if(n(1,s={duration:we(F.duration),target:v,toggles:N,currentIteration:d-k}),n(2,a=null),A(),await ve(500),!s)return;const O=await I();if(!s)return;n(2,a=O?"success":"fail");let q=setTimeout(()=>{if(i)if(O&&k>0)if(k--,k>0)M(k,F);else{R.finish(!0),n(1,s=null);return}else{R.finish(!1),n(1,s=null);return}},1e3);y.push(()=>{q&&clearTimeout(q),n(2,a=null)})}function K(){if(!r.active||s)return;const{iterations:k,config:F}=r;n(6,d=k),M(k,F)}function E(k){if(k.length===0)return 0;let F=0;const j=new Set,v=k.length-1,O=Ke(0,v);for(let q=0;q<O;q++){let B=Math.floor(Ke(0,v));if(j.has(B)){q--;continue}F+=k[B].value,j.add(B)}return F}let b=null;function A(){let k,F,j,N;function v(B){k=1e3/B,j=window.performance.now(),q()}const O=s.currentIteration;async function q(){!i||a||O===s.currentIteration&&(requestAnimationFrame(q),F=window.performance.now(),N=F-j,N>k&&(j=F-N%k,n(3,u+=.5)))}v(60)}function U(k){a||(n(1,s.toggles[k].active=!s.toggles[k].active,s),s.toggles[k].active?n(4,f+=s.toggles[k].value):n(4,f-=s.toggles[k].value),f===s.target&&_())}function m(k){Ce[k?"unshift":"push"](()=>{p=k,n(8,p)})}function w(k){c=k,n(5,c)}const z=(k,F)=>U(k);function T(k){Ce[k?"unshift":"push"](()=>{g=k,n(7,g)})}return t.$$.update=()=>{if(t.$$.dirty[0]&3&&i&&s){const{limit:k,min:F,max:j}=qt,N=s.toggles.length;n(9,b=k/(N<F?F:N>j?j:N))}},[i,s,a,u,f,c,d,g,p,b,l,o,U,m,w,z,T]}class Ca extends he{constructor(e){super(),de(this,e,Ia,ka,ce,{},null,[-1,-1])}}const rs=Math.PI,Ea=rs*2,Aa=rs/2,ul=Math.sin;class Ta{constructor(e,n){Ue(this,"defaultWaveOptions",{speed:10,amplitude:50,wavelength:50,segmentLength:10,lineWidth:2,strokeStyle:"rgba(255, 0, 0, 1)",timeModifier:1});Ue(this,"wave",{...this.defaultWaveOptions});Ue(this,"ctx");Ue(this,"width");Ue(this,"height");Ue(this,"devicePixelRatio");Ue(this,"waveLeft");Ue(this,"waveWidth");Ue(this,"time",0);this.ctx=e.getContext("2d"),this.updateCanvasSize(e),this.updateWaveOptions(n)}updateCanvasSize(e){this.width=e.width,this.height=e.height,this.devicePixelRatio=window.devicePixelRatio||1,e.width=this.width*this.devicePixelRatio,e.height=this.height*this.devicePixelRatio,this.ctx.scale(this.devicePixelRatio,this.devicePixelRatio)}updateWaveOptions(e){e?this.wave={...this.defaultWaveOptions,...e}:this.wave={...this.defaultWaveOptions},this.waveLeft=this.width*.025,this.waveWidth=this.width*.95}clear(){this.ctx.clearRect(0,0,this.width,this.height)}update(e){this.time-=.1,e=e===void 0?this.time:e;const n=this.wave.timeModifier;this.drawSine(e*n,this.wave)}ease(e,n){return n*(ul(e*Ea-Aa)+1)*.5}drawSine(e,n){const{amplitude:r,wavelength:l,lineWidth:i,strokeStyle:s}=n;let{segmentLength:a}=n;a=a||.1;let o=e,u=0,f=this.wave.amplitude;const c=this.height/2;this.ctx.lineWidth=i,this.ctx.strokeStyle=s,this.ctx.lineCap="round",this.ctx.lineJoin="round",this.ctx.beginPath(),this.ctx.moveTo(0,c),this.ctx.lineTo(this.waveLeft,c);for(let d=0;d<this.waveWidth;d+=a)o=e*this.wave.speed+(-c+d)/l,u=ul(o),f=this.ease(d/this.waveWidth,r*1.5),this.ctx.lineTo(d+this.waveLeft,f*u+c);this.ctx.lineTo(this.width,c),this.ctx.stroke()}render(){this.clear(),this.update()}destroy(){this.ctx.clearRect(0,0,this.width,this.height),this.ctx.closePath(),this.ctx.stroke()}}function Ma(t){let e;return{c(){e=G("canvas"),e.innerHTML="",h(e,"class","absolute")},m(n,r){D(n,e,r),t[11](e)},p:me,i:me,o:me,d(n){n&&W(e),t[11](null)}}}function La(t,e,n){let{height:r=null}=e,{width:l=null}=e,{speed:i,amplitude:s,wavelength:a,segmentLength:o,lineWidth:u,timeModifier:f}=Ve.DEFAULT_WAVE,{speed:c=i,amplitude:d=s,wavelength:g=a,segmentLength:p=o,lineWidth:_=u,timeModifier:y=f}=e,{strokeStyle:S=null}=e,I=null,M=null;zt(()=>{if(!M){console.error("Canvas element not found");return}return n(0,M.width=l*(window.devicePixelRatio||1),M),n(0,M.height=r*(window.devicePixelRatio||1),M),n(0,M.style.width=`${l}px`,M),n(0,M.style.height=`${r}px`,M),n(10,I=new Ta(M,{speed:c,amplitude:d,wavelength:g,segmentLength:p,lineWidth:_,strokeStyle:S,timeModifier:y})),K(),()=>{console.log("destroying"),I.destroy()}});function K(){let b,A,U,m;function w(T){b=1e3/T,U=window.performance.now(),z()}function z(){requestAnimationFrame(z),A=window.performance.now(),m=A-U,m>b&&(U=A-m%b,I.render())}w(60)}function E(b){Ce[b?"unshift":"push"](()=>{M=b,n(0,M)})}return t.$$set=b=>{"height"in b&&n(1,r=b.height),"width"in b&&n(2,l=b.width),"speed"in b&&n(3,c=b.speed),"amplitude"in b&&n(4,d=b.amplitude),"wavelength"in b&&n(5,g=b.wavelength),"segmentLength"in b&&n(6,p=b.segmentLength),"lineWidth"in b&&n(7,_=b.lineWidth),"timeModifier"in b&&n(8,y=b.timeModifier),"strokeStyle"in b&&n(9,S=b.strokeStyle)},t.$$.update=()=>{t.$$.dirty&2041&&M&&I&&I.updateWaveOptions({speed:c,amplitude:d,wavelength:g,segmentLength:p,lineWidth:_,strokeStyle:S,timeModifier:y})},[M,r,l,c,d,g,p,_,y,S,I,E]}class cl extends he{constructor(e){super(),de(this,e,La,Ma,ce,{height:1,width:2,speed:3,amplitude:4,wavelength:5,segmentLength:6,lineWidth:7,timeModifier:8,strokeStyle:9})}}const za=t=>({}),fl=t=>({}),Ra=t=>({}),dl=t=>({});function Na(t){let e,n,r,l,i,s,a,o;const u=t[7].before,f=$e(u,t,t[6],dl),c=t[7].after,d=$e(c,t,t[6],fl);return{c(){e=G("div"),f&&f.c(),n=H(),r=G("div"),l=G("input"),i=H(),d&&d.c(),h(l,"class","w-full h-full bg-transparent styled-slider slider-progress svelte-gq68ge"),h(l,"type","range"),h(l,"id","slider"),Y(l,"--value",t[0]),Y(l,"--min",t[1]),Y(l,"--max",t[2]),Y(l,"--step",t[3]),h(l,"min",t[1]),h(l,"max",t[2]),h(l,"step",t[3]),l.disabled=t[4],h(r,"class","w-full grid place-items-center h-full btn px-[0.5vh]"),h(e,"class","flex items-center justify-center w-full h-[3vh] gap-[0.5vh] primary-bg")},m(g,p){D(g,e,p),f&&f.m(e,null),L(e,n),L(e,r),L(r,l),_t(l,t[0]),L(e,i),d&&d.m(e,null),s=!0,a||(o=[ge(l,"change",t[8]),ge(l,"input",t[8]),ge(l,"input",t[5]),ge(l,"mousedown",t[9]),ge(l,"mouseup",t[10])],a=!0)},p(g,[p]){f&&f.p&&(!s||p&64)&&et(f,u,g,g[6],s?xe(u,g[6],p,Ra):tt(g[6]),dl),(!s||p&1)&&Y(l,"--value",g[0]),(!s||p&2)&&Y(l,"--min",g[1]),(!s||p&4)&&Y(l,"--max",g[2]),(!s||p&8)&&Y(l,"--step",g[3]),(!s||p&2)&&h(l,"min",g[1]),(!s||p&4)&&h(l,"max",g[2]),(!s||p&8)&&h(l,"step",g[3]),(!s||p&16)&&(l.disabled=g[4]),p&1&&_t(l,g[0]),d&&d.p&&(!s||p&64)&&et(d,c,g,g[6],s?xe(c,g[6],p,za):tt(g[6]),fl)},i(g){s||(C(f,g),C(d,g),s=!0)},o(g){P(f,g),P(d,g),s=!1},d(g){g&&W(e),f&&f.d(g),d&&d.d(g),a=!1,Le(o)}}}function Pa(t,e,n){let{$$slots:r={},$$scope:l}=e;const i=Qt();let{min:s}=e,{max:a}=e,{step:o}=e,{value:u}=e,{disabled:f=!1}=e,c=u;function d(){c!==u&&(c=u,i("change",u))}function g(){u=Ol(this.value),n(0,u)}const p=()=>R.playSound("primary"),_=()=>R.playSound("secondary");return t.$$set=y=>{"min"in y&&n(1,s=y.min),"max"in y&&n(2,a=y.max),"step"in y&&n(3,o=y.step),"value"in y&&n(0,u=y.value),"disabled"in y&&n(4,f=y.disabled),"$$scope"in y&&n(6,l=y.$$scope)},[u,s,a,o,f,d,l,r,g,p,_]}class ot extends he{constructor(e){super(),de(this,e,Pa,Na,ce,{min:1,max:2,step:3,value:0,disabled:4})}}function _n(t){const e=t.slice(),n=Ve.MAX_WAVE;e[26]=n;const r=Ve.MIN_WAVE;e[27]=r;const l=Ve.STEP_WAVE;e[28]=l;const i=Ve.MATCH_THRESHOLD;return e[29]=i,e}function wn(t){const e=t.slice(),n=window.innerHeight*.0015;e[30]=n;const r=e[4].clientHeight-e[30];e[31]=r;const l=e[4].clientWidth-e[30];return e[32]=l,e}function hl(t){let e,n;return e=new Be({props:{state:t[2],title:["Wave","Match"],subtitle:"Change the parameters to match the wave.",iterations:t[3],iteration:t[1].currentIteration,progress:t[5]/t[1].duration*100,$$slots:{default:[Ua]},$$scope:{ctx:t}}}),{c(){ee(e.$$.fragment)},m(r,l){$(e,r,l),n=!0},p(r,l){const i={};l[0]&4&&(i.state=r[2]),l[0]&8&&(i.iterations=r[3]),l[0]&2&&(i.iteration=r[1].currentIteration),l[0]&34&&(i.progress=r[5]/r[1].duration*100),l[0]&22|l[1]&4&&(i.$$scope={dirty:l,ctx:r}),e.$set(i)},i(r){n||(C(e.$$.fragment,r),n=!0)},o(r){P(e.$$.fragment,r),n=!1},d(r){x(e,r)}}}function gl(t){let e,n,r,l,i,s;const a=[{strokeStyle:t[6]},t[1].targetWave,{height:t[31]},{width:t[32]}];let o={};for(let c=0;c<a.length;c+=1)o=Re(o,a[c]);n=new cl({props:o});const u=[{strokeStyle:t[7]},t[1].userWave,{height:t[31]},{width:t[32]}];let f={};for(let c=0;c<u.length;c+=1)f=Re(f,u[c]);return l=new cl({props:f}),{c(){e=G("div"),ee(n.$$.fragment),r=H(),ee(l.$$.fragment),h(e,"class","w-full h-full grid place-items-center")},m(c,d){D(c,e,d),$(n,e,null),L(e,r),$(l,e,null),s=!0},p(c,d){const g=d[0]&82?Vt(a,[d[0]&64&&{strokeStyle:c[6]},d[0]&2&&Kt(c[1].targetWave),d[0]&16&&{height:c[31]},d[0]&16&&{width:c[32]}]):{};n.$set(g);const p=d[0]&146?Vt(u,[d[0]&128&&{strokeStyle:c[7]},d[0]&2&&Kt(c[1].userWave),d[0]&16&&{height:c[31]},d[0]&16&&{width:c[32]}]):{};l.$set(p)},i(c){s||(C(n.$$.fragment,c),C(l.$$.fragment,c),c&&ie(()=>{s&&(i||(i=X(e,Ht,{delay:500},!0)),i.run(1))}),s=!0)},o(c){P(n.$$.fragment,c),P(l.$$.fragment,c),c&&(i||(i=X(e,Ht,{delay:500},!1)),i.run(0)),s=!1},d(c){c&&W(e),x(n),x(l),c&&i&&i.end()}}}function ml(t){let e,n,r,l,i,s,a,o,u,f,c,d,g,p,_,y,S,I,M,K,E,b,A,U,m,w;function z(V){t[11](V)}let T={disabled:!!t[2],min:t[27].speed,max:t[26].speed,step:t[28].speed};t[1].userWave.speed!==void 0&&(T.value=t[1].userWave.speed),u=new ot({props:T}),Ce.push(()=>Ze(u,"value",z)),u.$on("change",t[9]);function k(V){t[12](V)}let F={disabled:!!t[2],min:t[27].amplitude,max:t[26].amplitude,step:t[28].amplitude};t[1].userWave.amplitude!==void 0&&(F.value=t[1].userWave.amplitude),d=new ot({props:F}),Ce.push(()=>Ze(d,"value",k)),d.$on("change",t[9]);function j(V){t[13](V)}let N={disabled:!!t[2],min:t[27].wavelength,max:t[26].wavelength,step:t[28].wavelength};t[1].userWave.wavelength!==void 0&&(N.value=t[1].userWave.wavelength),_=new ot({props:N}),Ce.push(()=>Ze(_,"value",j)),_.$on("change",t[9]);function v(V){t[14](V)}let O={disabled:!!t[2],min:t[27].segmentLength,max:t[26].segmentLength,step:t[28].segmentLength};t[1].userWave.segmentLength!==void 0&&(O.value=t[1].userWave.segmentLength),I=new ot({props:O}),Ce.push(()=>Ze(I,"value",v)),I.$on("change",t[9]);function q(V){t[15](V)}let B={disabled:!!t[2],min:t[27].lineWidth,max:t[26].lineWidth,step:t[28].lineWidth};t[1].userWave.lineWidth!==void 0&&(B.value=t[1].userWave.lineWidth),E=new ot({props:B}),Ce.push(()=>Ze(E,"value",q)),E.$on("change",t[9]);function Z(V){t[16](V)}let Q={disabled:!!t[2],min:t[27].timeModifier,max:t[26].timeModifier,step:t[28].timeModifier};return t[1].userWave.timeModifier!==void 0&&(Q.value=t[1].userWave.timeModifier),U=new ot({props:Q}),Ce.push(()=>Ze(U,"value",Z)),U.$on("change",t[9]),{c(){e=G("div"),n=G("div"),r=G("div"),i=H(),s=G("div"),a=H(),o=G("div"),ee(u.$$.fragment),c=H(),ee(d.$$.fragment),p=H(),ee(_.$$.fragment),S=H(),ee(I.$$.fragment),K=H(),ee(E.$$.fragment),A=H(),ee(U.$$.fragment),h(r,"class",l="h-full default-all-transition "+(t[2]=="success"?"border-success glow-success bg-success/50":t[2]=="fail"?"border-error glow-error bg-error/50":"bg-accent glow-accent")),Y(r,"width",t[1].match/t[29]*100+"%"),h(s,"class","h-full bg-success default-all-transition absolute"),h(n,"class","w-full h-[2vh] grid place-items-center primary-bg"),h(o,"class","w-full h-full grid grid-cols-3 items-center justify-between gap-[0.5vh]"),h(e,"class","w-full h-[20vh] px-[5vh] flex flex-col items-center justify-center gap-[2vh]")},m(V,J){D(V,e,J),L(e,n),L(n,r),L(n,i),L(n,s),L(e,a),L(e,o),$(u,o,null),L(o,c),$(d,o,null),L(o,p),$(_,o,null),L(o,S),$(I,o,null),L(o,K),$(E,o,null),L(o,A),$(U,o,null),w=!0},p(V,J){(!w||J[0]&4&&l!==(l="h-full default-all-transition "+(V[2]=="success"?"border-success glow-success bg-success/50":V[2]=="fail"?"border-error glow-error bg-error/50":"bg-accent glow-accent")))&&h(r,"class",l),(!w||J[0]&2)&&Y(r,"width",V[1].match/V[29]*100+"%");const ae={};J[0]&4&&(ae.disabled=!!V[2]),!f&&J[0]&2&&(f=!0,ae.value=V[1].userWave.speed,Ye(()=>f=!1)),u.$set(ae);const ye={};J[0]&4&&(ye.disabled=!!V[2]),!g&&J[0]&2&&(g=!0,ye.value=V[1].userWave.amplitude,Ye(()=>g=!1)),d.$set(ye);const Ae={};J[0]&4&&(Ae.disabled=!!V[2]),!y&&J[0]&2&&(y=!0,Ae.value=V[1].userWave.wavelength,Ye(()=>y=!1)),_.$set(Ae);const Pe={};J[0]&4&&(Pe.disabled=!!V[2]),!M&&J[0]&2&&(M=!0,Pe.value=V[1].userWave.segmentLength,Ye(()=>M=!1)),I.$set(Pe);const De={};J[0]&4&&(De.disabled=!!V[2]),!b&&J[0]&2&&(b=!0,De.value=V[1].userWave.lineWidth,Ye(()=>b=!1)),E.$set(De);const Fe={};J[0]&4&&(Fe.disabled=!!V[2]),!m&&J[0]&2&&(m=!0,Fe.value=V[1].userWave.timeModifier,Ye(()=>m=!1)),U.$set(Fe)},i(V){w||(C(u.$$.fragment,V),C(d.$$.fragment,V),C(_.$$.fragment,V),C(I.$$.fragment,V),C(E.$$.fragment,V),C(U.$$.fragment,V),w=!0)},o(V){P(u.$$.fragment,V),P(d.$$.fragment,V),P(_.$$.fragment,V),P(I.$$.fragment,V),P(E.$$.fragment,V),P(U.$$.fragment,V),w=!1},d(V){V&&W(e),x(u),x(d),x(_),x(I),x(E),x(U)}}}function Ua(t){var a;let e,n,r,l,i=t[4]&&!t[2]&&gl(wn(t)),s=((a=t[1])==null?void 0:a.userWave)&&ml(_n(t));return{c(){e=G("div"),n=G("div"),i&&i.c(),r=H(),s&&s.c(),h(n,"class","w-full h-[40vh] bg-secondary/90 border-[0.15vh] border-tertiary/50"),h(e,"class","w-[80vh] h-[60vh] flex flex-col items-center gap-[5vh]")},m(o,u){D(o,e,u),L(e,n),i&&i.m(n,null),t[10](n),L(e,r),s&&s.m(e,null),l=!0},p(o,u){var f;o[4]&&!o[2]?i?(i.p(wn(o),u),u[0]&20&&C(i,1)):(i=gl(wn(o)),i.c(),C(i,1),i.m(n,null)):i&&(le(),P(i,1,1,()=>{i=null}),se()),(f=o[1])!=null&&f.userWave?s?(s.p(_n(o),u),u[0]&2&&C(s,1)):(s=ml(_n(o)),s.c(),C(s,1),s.m(e,null)):s&&(le(),P(s,1,1,()=>{s=null}),se())},i(o){l||(C(i),C(s),l=!0)},o(o){P(i),P(s),l=!1},d(o){o&&W(e),i&&i.d(),t[10](null),s&&s.d()}}}function Wa(t){let e,n,r=t[0]&&hl(t);return{c(){r&&r.c(),e=pe()},m(l,i){r&&r.m(l,i),D(l,e,i),n=!0},p(l,i){l[0]?r?(r.p(l,i),i[0]&1&&C(r,1)):(r=hl(l),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(le(),P(r,1,1,()=>{r=null}),se())},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d(l)}}}function Da(t,e,n){let r,l;fe(t,R,k=>n(19,r=k));const i=getComputedStyle(document.body),s=`rgba(${i.getPropertyValue("--foreground").match(/\d+/g).join(",")}, 0.5)`,a=`rgba(${i.getPropertyValue("--accent").match(/\d+/g).join(",")}, 0.5)`;let o=!1,u=null,f=null;const c=Me(0);fe(t,c,k=>n(5,l=k));let d=null,g=null,p=[];function _(){p.forEach(k=>k()),p=[]}R.subscribe(k=>{let F=k.active&&k.type===ue.WaveMatch&&!f;F?(_(),n(0,o=!0),M()):o&&!F&&(n(0,o=!1),n(1,u=null),n(2,f=null),_())});let y=null;async function S(){if(!o)return;let k=setTimeout(()=>{c.set(u.duration,{duration:u.duration})},500);return p.push(()=>{k&&clearTimeout(k)}),new Promise((F,j)=>{let N=setTimeout(()=>{v(!1)},u.duration+500);p.push(()=>{N&&clearTimeout(N),F(!1)}),y=()=>{u.match>=Ve.MATCH_THRESHOLD&&v(!0)};function v(O){const q=l;c.set(q,{duration:0}),clearTimeout(N),F(O)}})}async function I(k,F){if(!o||(c.set(0,{duration:0}),y=null,n(1,u={duration:we(F.duration),currentIteration:d-k,userWave:Qn(Ve.DEFAULT_WAVE),targetWave:K(),match:0}),E(),n(2,f=null),await ve(500),!u))return;const j=await S();if(!u)return;n(2,f=j?"success":"fail");const N=j&&k<=1;j&&N?R.playSound("win"):!N&&j?R.playSound("iteration"):R.playSound("lose");let v=setTimeout(()=>{if(o)if(j&&k>0)if(k--,k>0)I(k,F);else{R.finish(!0),n(1,u=null);return}else{R.finish(!1),n(1,u=null);return}},1e3);p.push(()=>{v&&clearTimeout(v),n(2,f=null)})}function M(){if(!r.active||u)return;const{iterations:k,config:F}=r;n(3,d=k),I(k,F)}function K(){const{DEFAULT_WAVE:k,MIN_WAVE:F,MAX_WAVE:j,STEP_WAVE:N}=Ve;let v=Qn(k);for(const O in v){const q=Math.random()*(j[O]-F[O])+F[O];v[O]=Math.round(q/N[O])*N[O]}return v}function E(){if(f)return;if(!(u!=null&&u.userWave)||!(u!=null&&u.targetWave))return 0;const k=u.userWave,F=u.targetWave;let j=0;const N=Object.keys(k),v=N.length,{MIN_WAVE:O,MAX_WAVE:q}=Ve;N.forEach(B=>{const Z=k[B],Q=F[B],V=Number(O[B]),J=Number(q[B]);if(J!==V){const ae=(Number(Z)-V)/(J-V),ye=(Number(Q)-V)/(J-V),Ae=1-Math.abs(ae-ye);j+=Ae}}),n(1,u.match=Math.round(j/v*100),u),y&&y()}function b(k){Ce[k?"unshift":"push"](()=>{g=k,n(4,g)})}function A(k){t.$$.not_equal(u.userWave.speed,k)&&(u.userWave.speed=k,n(1,u))}function U(k){t.$$.not_equal(u.userWave.amplitude,k)&&(u.userWave.amplitude=k,n(1,u))}function m(k){t.$$.not_equal(u.userWave.wavelength,k)&&(u.userWave.wavelength=k,n(1,u))}function w(k){t.$$.not_equal(u.userWave.segmentLength,k)&&(u.userWave.segmentLength=k,n(1,u))}function z(k){t.$$.not_equal(u.userWave.lineWidth,k)&&(u.userWave.lineWidth=k,n(1,u))}function T(k){t.$$.not_equal(u.userWave.timeModifier,k)&&(u.userWave.timeModifier=k,n(1,u))}return[o,u,f,d,g,l,s,a,c,E,b,A,U,m,w,z,T]}class Oa extends he{constructor(e){super(),de(this,e,Da,Wa,ce,{},null,[-1,-1])}}function Fa(t){let e,n,r,l,i;const s=t[5].default,a=$e(s,t,t[4],null);return{c(){e=G("button"),a&&a.c(),h(e,"class",n="w-full h-full default-all-transition border scale-105 focus:outline-none "+(t[1]?t[3]?"animate-pulse bg-error/50 border-error":"animate-pulse bg-accent/50 border-accent":t[0]=="success"?"border-success glow-success bg-success/50":t[0]=="fail"?"border-error glow-error bg-error/50":t[2]=="success"?"border-success glow-success bg-success/50":t[2]=="fail"?"border-error glow-error bg-error/50":"bg-accent/50 scale-100 border-accent glow-accent"))},m(o,u){D(o,e,u),a&&a.m(e,null),r=!0,l||(i=ge(e,"click",t[6]),l=!0)},p(o,[u]){a&&a.p&&(!r||u&16)&&et(a,s,o,o[4],r?xe(s,o[4],u,null):tt(o[4]),null),(!r||u&15&&n!==(n="w-full h-full default-all-transition border scale-105 focus:outline-none "+(o[1]?o[3]?"animate-pulse bg-error/50 border-error":"animate-pulse bg-accent/50 border-accent":o[0]=="success"?"border-success glow-success bg-success/50":o[0]=="fail"?"border-error glow-error bg-error/50":o[2]=="success"?"border-success glow-success bg-success/50":o[2]=="fail"?"border-error glow-error bg-error/50":"bg-accent/50 scale-100 border-accent glow-accent")))&&h(e,"class",n)},i(o){r||(C(a,o),r=!0)},o(o){P(a,o),r=!1},d(o){o&&W(e),a&&a.d(o),l=!1,i()}}}function Ga(t,e,n){let{$$slots:r={},$$scope:l}=e,{state:i=null}=e,{preview:s=!1}=e,{bombState:a}=e,{mine:o}=e;function u(f){Gl.call(this,t,f)}return t.$$set=f=>{"state"in f&&n(0,i=f.state),"preview"in f&&n(1,s=f.preview),"bombState"in f&&n(2,a=f.bombState),"mine"in f&&n(3,o=f.mine),"$$scope"in f&&n(4,l=f.$$scope)},[i,s,a,o,l,r,u]}class ja extends he{constructor(e){super(),de(this,e,Ga,Fa,ce,{state:0,preview:1,bombState:2,mine:3})}}function pl(t,e,n){const r=t.slice();return r[20]=e[n],r[22]=n,r}function bl(t,e,n){const r=t.slice();return r[23]=e[n],r[25]=n,r}function vn(t){const e=t.slice(),n=e[1];return e[19]=n.grid,e}function _l(t){let e,n;return e=new Be({props:{state:t[3],title:["Mine","Sweeper"],subtitle:"Remember the mines and clear them all.",iterations:t[4],iteration:t[1].currentIteration,progress:t[5]/t[1].duration*100,$$slots:{default:[Va]},$$scope:{ctx:t}}}),{c(){ee(e.$$.fragment)},m(r,l){$(e,r,l),n=!0},p(r,l){const i={};l&8&&(i.state=r[3]),l&16&&(i.iterations=r[4]),l&2&&(i.iteration=r[1].currentIteration),l&34&&(i.progress=r[5]/r[1].duration*100),l&67108878&&(i.$$scope={dirty:l,ctx:r}),e.$set(i)},i(r){n||(C(e.$$.fragment,r),n=!0)},o(r){P(e.$$.fragment,r),n=!1},d(r){x(e,r)}}}function wl(t){let e,n,r=re(t[19]),l=[];for(let s=0;s<r.length;s+=1)l[s]=yl(pl(t,r,s));const i=s=>P(l[s],1,1,()=>{l[s]=null});return{c(){for(let s=0;s<l.length;s+=1)l[s].c();e=pe()},m(s,a){for(let o=0;o<l.length;o+=1)l[o]&&l[o].m(s,a);D(s,e,a),n=!0},p(s,a){if(a&142){r=re(s[19]);let o;for(o=0;o<r.length;o+=1){const u=pl(s,r,o);l[o]?(l[o].p(u,a),C(l[o],1)):(l[o]=yl(u),l[o].c(),C(l[o],1),l[o].m(e.parentNode,e))}for(le(),o=r.length;o<l.length;o+=1)i(o);se()}},i(s){if(!n){for(let a=0;a<r.length;a+=1)C(l[a]);n=!0}},o(s){l=l.filter(Boolean);for(let a=0;a<l.length;a+=1)P(l[a]);n=!1},d(s){s&&W(e),Se(l,s)}}}function vl(t){let e,n,r,l,i;function s(){return t[8](t[22],t[25])}return n=new ja({props:{state:t[3],mine:t[23].mine,bombState:t[23].state,preview:t[2]}}),n.$on("click",s),{c(){e=G("div"),ee(n.$$.fragment),r=H(),h(e,"class","w-full h-full grid place-items-center")},m(a,o){D(a,e,o),$(n,e,null),L(e,r),i=!0},p(a,o){t=a;const u={};o&8&&(u.state=t[3]),o&2&&(u.mine=t[23].mine),o&2&&(u.bombState=t[23].state),o&4&&(u.preview=t[2]),n.$set(u)},i(a){i||(C(n.$$.fragment,a),ie(()=>{i&&(l||(l=X(e,te,{delay:(t[25]+t[22])*25},!0)),l.run(1))}),i=!0)},o(a){P(n.$$.fragment,a),l||(l=X(e,te,{delay:(t[25]+t[22])*25},!1)),l.run(0),i=!1},d(a){a&&W(e),x(n),a&&l&&l.end()}}}function yl(t){let e,n,r=re(t[20]),l=[];for(let s=0;s<r.length;s+=1)l[s]=vl(bl(t,r,s));const i=s=>P(l[s],1,1,()=>{l[s]=null});return{c(){for(let s=0;s<l.length;s+=1)l[s].c();e=pe()},m(s,a){for(let o=0;o<l.length;o+=1)l[o]&&l[o].m(s,a);D(s,e,a),n=!0},p(s,a){if(a&142){r=re(s[20]);let o;for(o=0;o<r.length;o+=1){const u=bl(s,r,o);l[o]?(l[o].p(u,a),C(l[o],1)):(l[o]=vl(u),l[o].c(),C(l[o],1),l[o].m(e.parentNode,e))}for(le(),o=r.length;o<l.length;o+=1)i(o);se()}},i(s){if(!n){for(let a=0;a<r.length;a+=1)C(l[a]);n=!0}},o(s){l=l.filter(Boolean);for(let a=0;a<l.length;a+=1)P(l[a]);n=!1},d(s){s&&W(e),Se(l,s)}}}function Va(t){let e,n,r=t[1]&&wl(vn(t));return{c(){var l;e=G("div"),r&&r.c(),Y(e,"grid-template-columns","repeat("+((l=t[1])==null?void 0:l.grid.length)+", 1fr)"),h(e,"class","w-[60vh] h-[60vh] aspect-square grid-cols-5 gap-[2vh] grid")},m(l,i){D(l,e,i),r&&r.m(e,null),n=!0},p(l,i){var s;l[1]?r?(r.p(vn(l),i),i&2&&C(r,1)):(r=wl(vn(l)),r.c(),C(r,1),r.m(e,null)):r&&(le(),P(r,1,1,()=>{r=null}),se()),(!n||i&2)&&Y(e,"grid-template-columns","repeat("+((s=l[1])==null?void 0:s.grid.length)+", 1fr)")},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d()}}}function Ka(t){let e,n,r=t[0]&&_l(t);return{c(){r&&r.c(),e=pe()},m(l,i){r&&r.m(l,i),D(l,e,i),n=!0},p(l,[i]){l[0]?r?(r.p(l,i),i&1&&C(r,1)):(r=_l(l),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(le(),P(r,1,1,()=>{r=null}),se())},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d(l)}}}function Ba(t,e){const n=[];for(let l=0;l<t;l++){const i=[];for(let s=0;s<t;s++)i.push({mine:!1,state:null});n.push(i)}let r=0;for(;r<e;){const l=Math.floor(Math.random()*t),i=Math.floor(Math.random()*t);n[l][i].mine||(n[l][i].mine=!0,r++)}return n}function Ha(t,e,n){let r,l;fe(t,R,b=>n(14,r=b));let i=!1,s=null,a=!1,o=null;const u=Me(0);fe(t,u,b=>n(5,l=b));let f=0,c=0,d=0,g=null,p=null,_=[];function y(){_.forEach(b=>b()),_=[]}R.subscribe(b=>{let A=b.active&&b.type===ue.MineSweeper&&!o;A?(y(),n(0,i=!0),M()):i&&!A&&(n(0,i=!1),n(1,s=null),n(3,o=null),y())});async function S(){if(!i)return;f=0,c=0;let b=setTimeout(()=>{u.set(s.duration,{duration:s.duration})},500);return _.push(()=>{b&&clearTimeout(b)}),new Promise((A,U)=>{let m=setTimeout(()=>{w(!1)},s.duration+500);_.push(()=>{m&&clearTimeout(m),A(!1)}),p=()=>{c===d?w(!0):f>=Yi.MISTAKES&&w(!1)};function w(z){const T=l;u.set(T,{duration:0}),p=null,clearTimeout(m),A(z)}})}async function I(b,A){if(!i)return;f=0,c=0,u.set(0,{duration:0});const U=we(A.duration),m=we(A.grid);d=we(A.target);const w=Ba(m,d);if(n(1,s={grid:w,duration:U,currentIteration:g-b}),n(3,o=null),await ve(500),!s||(n(2,a=!0),!await new Promise((j,N)=>{let v=setTimeout(()=>{j(!0),i&&n(2,a=!1)},A.previewDuration||5e3);_.push(()=>{v&&clearTimeout(v),j(!1)})}))||!s)return;const T=await S();if(!s)return;n(3,o=T?"success":"fail");const k=T&&b<=1;T&&k?R.playSound("win"):!k&&T?R.playSound("iteration"):R.playSound("lose");let F=setTimeout(()=>{if(i)if(T&&b>0)if(b--,b>0)I(b,A);else{R.finish(!0),n(1,s=null);return}else{R.finish(!1),n(1,s=null);return}},1e3);_.push(()=>{F&&clearTimeout(F),n(3,o=null)})}function M(){if(!r.active||s)return;const{iterations:b,config:A}=r;n(4,g=b),I(b,A)}function K(b,A){if(a||o)return;const U=s.grid[b][A];U.state=U.mine?"success":"fail",U.state==="success"?c++:f++,R.playSound("primary"),n(1,s.grid[b][A]=U,s),p&&p()}return[i,s,a,o,g,l,u,K,(b,A)=>K(b,A)]}class qa extends he{constructor(e){super(),de(this,e,Ha,Ka,ce,{})}}function kl(t){const e=t[1];t[23]=e.prints,t[24]=e.sections;const n=t[24].length;t[25]=n;const r=Qa(t[2]);t[26]=r}function Sl(t,e,n){const r=t.slice();r[27]=e[n],r[32]=n;const l=r[27].locked;r[28]=l;const i=r[28]&&!r[2]?"":"hover:scale-x-105 active:scale-x-100";r[29]=i;const s=r[28]?"bg-foreground ":"bg-accent hover:scale-105 active:scale-100 glow-accent";return r[30]=s,r}function Il(t,e,n){const r=t.slice();return r[27]=e[n],r[32]=n,r}function Cl(t,e,n){const r=t.slice();return r[34]=e[n],r}function El(t){let e,n;return e=new Be({props:{state:t[2],title:["Print","Lock"],subtitle:"Sift through the prints and find the matches.",iterations:t[3],iteration:t[1].currentIteration,progress:t[5]/t[1].duration*100,$$slots:{default:[Ya]},$$scope:{ctx:t}}}),{c(){ee(e.$$.fragment)},m(r,l){$(e,r,l),n=!0},p(r,l){const i={};l[0]&4&&(i.state=r[2]),l[0]&8&&(i.iterations=r[3]),l[0]&2&&(i.iteration=r[1].currentIteration),l[0]&34&&(i.progress=r[5]/r[1].duration*100),l[0]&22|l[1]&64&&(i.$$scope={dirty:l,ctx:r}),e.$set(i)},i(r){n||(C(e.$$.fragment,r),n=!0)},o(r){P(e.$$.fragment,r),n=!1},d(r){x(e,r)}}}function Al(t){let e,n,r,l;return{c(){e=oe("path"),h(e,"d",n=t[34]),h(e,"fill","none"),h(e,"mask",r="url(#mask-"+t[32]+"_"+t[27].print+")"),h(e,"class",l="stroke-tertiary overflow-visible default-colour-transition "+t[26]),h(e,"stroke-width",Ja)},m(i,s){D(i,e,s)},p(i,s){s[0]&2&&n!==(n=i[34])&&h(e,"d",n),s[0]&2&&r!==(r="url(#mask-"+i[32]+"_"+i[27].print+")")&&h(e,"mask",r),s[0]&4&&l!==(l="stroke-tertiary overflow-visible default-colour-transition "+i[26])&&h(e,"class",l)},d(i){i&&W(e)}}}function Tl(t){let e,n,r,l,i,s,a,o,u=re(t[23][t[27].print]),f=[];for(let c=0;c<u.length;c+=1)f[c]=Al(Cl(t,u,c));return{c(){e=oe("svg"),n=oe("mask"),r=oe("rect");for(let c=0;c<f.length;c+=1)f[c].c();h(r,"x","0"),h(r,"y",l=t[7]/t[25]*t[32]),h(r,"width",t[7]),h(r,"height",i=t[7]/t[25]),h(r,"fill","white"),h(n,"id",s="mask-"+t[32]+"_"+t[27].print),h(e,"class","w-full aspect-square absolute overflow-visible"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"viewBox","0 0 "+t[7]+" "+t[7])},m(c,d){D(c,e,d),L(e,n),L(n,r);for(let g=0;g<f.length;g+=1)f[g]&&f[g].m(e,null);o=!0},p(c,d){if((!o||d[0]&2&&l!==(l=c[7]/c[25]*c[32]))&&h(r,"y",l),(!o||d[0]&2&&i!==(i=c[7]/c[25]))&&h(r,"height",i),(!o||d[0]&2&&s!==(s="mask-"+c[32]+"_"+c[27].print))&&h(n,"id",s),d[0]&6){u=re(c[23][c[27].print]);let g;for(g=0;g<u.length;g+=1){const p=Cl(c,u,g);f[g]?f[g].p(p,d):(f[g]=Al(p),f[g].c(),f[g].m(e,null))}for(;g<f.length;g+=1)f[g].d(1);f.length=u.length}},i(c){o||(c&&ie(()=>{o&&(a||(a=X(e,Ht,{duration:250},!0)),a.run(1))}),o=!0)},o(c){c&&(a||(a=X(e,Ht,{duration:250},!1)),a.run(0)),o=!1},d(c){c&&W(e),Se(f,c),c&&a&&a.end()}}}function Ml(t){let e=t[27].print,n,r=Tl(t);return{c(){r.c(),n=pe()},m(l,i){r.m(l,i),D(l,n,i)},p(l,i){i[0]&2&&ce(e,e=l[27].print)?(le(),P(r,1,1,me),se(),r=Tl(l),r.c(),C(r,1),r.m(n.parentNode,n)):r.p(l,i)},d(l){l&&W(n),r.d(l)}}}function Ll(t){let e,n,r,l,i,s,a,o,u,f,c,d;function g(){return t[10](t[32])}function p(){return t[11](t[32])}return{c(){e=G("div"),n=G("button"),l=H(),i=G("div"),a=H(),o=G("button"),f=H(),h(n,"class",r="w-[4vh] h-[80%] default-all-transition "+t[29]+" "+t[30]),h(i,"class",s="w-full h-full border-x-[0.2vh] "+(t[32]===0?"border-b-[0.1vh] border-t-[0.2vh]":t[32]===t[25]-1?"border-t-[0.1vh] border-b-[0.2vh]":" border-y-[0.1vh]")+" "+(t[28]?"border-foreground ":"border-accent")),h(o,"class",u="w-[4vh] h-[80%] default-all-transition "+t[29]+" "+t[30]),h(e,"class","w-full h-full flex items-center justify-center z-10")},m(_,y){D(_,e,y),L(e,n),L(e,l),L(e,i),L(e,a),L(e,o),L(e,f),c||(d=[ge(n,"click",g),ge(o,"click",p)],c=!0)},p(_,y){t=_,y[0]&6&&r!==(r="w-[4vh] h-[80%] default-all-transition "+t[29]+" "+t[30])&&h(n,"class",r),y[0]&2&&s!==(s="w-full h-full border-x-[0.2vh] "+(t[32]===0?"border-b-[0.1vh] border-t-[0.2vh]":t[32]===t[25]-1?"border-t-[0.1vh] border-b-[0.2vh]":" border-y-[0.1vh]")+" "+(t[28]?"border-foreground ":"border-accent"))&&h(i,"class",s),y[0]&6&&u!==(u="w-[4vh] h-[80%] default-all-transition "+t[29]+" "+t[30])&&h(o,"class",u)},d(_){_&&W(e),c=!1,Le(d)}}}function Ya(t){kl(t);let e,n,r,l,i,s,a,o,u=re(t[24]),f=[];for(let g=0;g<u.length;g+=1)f[g]=Ml(Il(t,u,g));let c=re(t[24]),d=[];for(let g=0;g<c.length;g+=1)d[g]=Ll(Sl(t,c,g));return{c(){e=G("div"),n=G("div"),r=G("div"),i=H();for(let g=0;g<f.length;g+=1)f[g].c();s=H(),a=G("div"),o=H();for(let g=0;g<d.length;g+=1)d[g].c();h(r,"class",l="w-[20%] aspect-square absolute rounded-full border "+t[26]),h(a,"class","w-[101%] h-[101%] bg-secondary/90 absolute -z-10"),h(n,"class","w-[85%] aspect-square grid place-items-center absolute"),Y(e,"width","calc("+t[7]+"px + 7.5vh)"),Y(e,"height",t[7]+"px"),h(e,"class","flex flex-col items-center justify-center")},m(g,p){D(g,e,p),L(e,n),L(n,r),L(n,i);for(let _=0;_<f.length;_+=1)f[_]&&f[_].m(n,null);L(n,s),L(n,a),t[9](n),L(e,o);for(let _=0;_<d.length;_+=1)d[_]&&d[_].m(e,null)},p(g,p){if(kl(g),p[0]&4&&l!==(l="w-[20%] aspect-square absolute rounded-full border "+g[26])&&h(r,"class",l),p[0]&134){u=re(g[24]);let _;for(_=0;_<u.length;_+=1){const y=Il(g,u,_);f[_]?f[_].p(y,p):(f[_]=Ml(y),f[_].c(),f[_].m(n,s))}for(;_<f.length;_+=1)f[_].d(1);f.length=u.length}if(p[0]&262){c=re(g[24]);let _;for(_=0;_<c.length;_+=1){const y=Sl(g,c,_);d[_]?d[_].p(y,p):(d[_]=Ll(y),d[_].c(),d[_].m(e,null))}for(;_<d.length;_+=1)d[_].d(1);d.length=c.length}},d(g){g&&W(e),Se(f,g),t[9](null),Se(d,g)}}}function Za(t){let e,n,r=t[0]&&El(t);return{c(){r&&r.c(),e=pe()},m(l,i){r&&r.m(l,i),D(l,e,i),n=!0},p(l,i){l[0]?r?(r.p(l,i),i[0]&1&&C(r,1)):(r=El(l),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(le(),P(r,1,1,()=>{r=null}),se())},i(l){n||(C(r),n=!0)},o(l){P(r),n=!1},d(l){l&&W(e),r&&r.d(l)}}}const Xa=67.5,zl=100,Ja=5,Wt=5;function Qa(t){switch(t){case"success":return"bg-success/50 glow-success border-success";case"fail":return"bg-error/50 glow-error border-error";default:return"bg-accent/50 glow-accent border-accent"}}function $a(t,e,n){let r,l;fe(t,R,w=>n(14,r=w));let i=!1,s=null,a=null;const o=Me(0);fe(t,o,w=>n(5,l=w));let u=null,f=null;const d=Xa*window.innerHeight/100*.85;let g=null,p=[];function _(){p.forEach(w=>w()),p=[]}R.subscribe(w=>{let z=w.active&&w.type===ue.PrintLock&&!a;z?(_(),n(0,i=!0),I()):i&&!z&&(n(0,i=!1),n(1,s=null),n(2,a=null),_())});async function y(){if(!i)return;let w=setTimeout(()=>{o.set(s.duration,{duration:s.duration})},500);return p.push(()=>{w&&clearTimeout(w)}),new Promise((z,T)=>{let k=setTimeout(()=>{F(!1)},s.duration+500);p.push(()=>{k&&clearTimeout(k),z(!1)}),g=async()=>{await ve(250);const j=s.sections.find(v=>v.locked);s.sections.every(v=>v.print===(j==null?void 0:j.print))&&F(!0)};function F(j){const N=l;o.set(N,{duration:0}),g=null,clearTimeout(k),z(j)}})}async function S(w,z){if(!i)return;o.set(0,{duration:0});const T=we(z.target),k=E(T),F=we(z.grid),j=we([0,F-1]);if(n(1,s={prints:k,sections:Array.from({length:F},(q,B)=>({print:we([0,F-1]),locked:B===j})),duration:we(z.duration),currentIteration:u-w}),n(2,a=null),await ve(500),!s)return;const N=await y();if(!s)return;n(2,a=N?"success":"fail");const v=N&&w<=1;N&&v?R.playSound("win"):!v&&N?R.playSound("iteration"):R.playSound("lose");let O=setTimeout(()=>{if(i)if(N&&w>0)if(w--,w>0)S(w,z);else{R.finish(!0),n(1,s=null);return}else{R.finish(!1),n(1,s=null);return}},1e3);p.push(()=>{O&&clearTimeout(O),n(2,a=null)})}function I(){if(!r.active||s)return;const{iterations:w,config:z}=r;n(3,u=w),S(w,z)}function M(w,z,T,k,F){let j=$n(w,z,T,F),N=$n(w,z,T,k),v=F-k<=180?"0":"1";return["M",j.x,j.y,"A",T,T,0,v,0,N.x,N.y].join(" ")}function K(){const w=d,z=d,T=w/2,k=z/2,F=Math.min(w,z),j=w*.15,N=F/2,v=[],O=we([zl/2,zl]),q=N-j,B=Math.floor(q/Wt),Z=Math.min(O,B);for(let Q=0;Q<Z;Q++){let V=j+(Q+1)*Wt;V+=Ke(-Wt/2,Wt/2),V=Math.max(j,Math.min(V,N));let J=Ke(0,360),ae=J+Ke(30,90);v.push(M(T,k,V,J,ae))}return v}function E(w){const z=[];for(let T=0;T<w;T++){const k=K();z.push(k)}return z}function b(w,z){if(a||!g)return;const{sections:T}=s,{prints:k}=s,F=T[w];if(F.locked){R.playSound("secondary");return}let j=F.print+z;j<0?j=k.length-1:j>=k.length&&(j=0),R.playSound("primary"),F.print=j,n(1,s.sections[w]=F,s),g&&g()}function A(w){Ce[w?"unshift":"push"](()=>{f=w,n(4,f)})}return[i,s,a,u,f,l,o,d,b,A,w=>b(w,-1),w=>b(w,1)]}class xa extends he{constructor(e){super(),de(this,e,$a,Za,ce,{},null,[-1,-1])}}function eu(t){let e,n,r,l,i,s,a,o,u,f,c,d,g,p,_,y,S,I,M,K,E,b,A,U,m,w,z,T,k,F,j,N;return e=new Ji({}),r=new xi({}),i=new ro({}),a=new oo({}),u=new go({}),c=new yo({}),g=new Co({}),_=new Zo({}),S=new ea({}),M=new aa({}),E=new da({}),A=new pa({}),m=new Ca({}),z=new Oa({}),k=new qa({}),j=new xa({}),{c(){ee(e.$$.fragment),n=H(),ee(r.$$.fragment),l=H(),ee(i.$$.fragment),s=H(),ee(a.$$.fragment),o=H(),ee(u.$$.fragment),f=H(),ee(c.$$.fragment),d=H(),ee(g.$$.fragment),p=H(),ee(_.$$.fragment),y=H(),ee(S.$$.fragment),I=H(),ee(M.$$.fragment),K=H(),ee(E.$$.fragment),b=H(),ee(A.$$.fragment),U=H(),ee(m.$$.fragment),w=H(),ee(z.$$.fragment),T=H(),ee(k.$$.fragment),F=H(),ee(j.$$.fragment)},m(v,O){$(e,v,O),D(v,n,O),$(r,v,O),D(v,l,O),$(i,v,O),D(v,s,O),$(a,v,O),D(v,o,O),$(u,v,O),D(v,f,O),$(c,v,O),D(v,d,O),$(g,v,O),D(v,p,O),$(_,v,O),D(v,y,O),$(S,v,O),D(v,I,O),$(M,v,O),D(v,K,O),$(E,v,O),D(v,b,O),$(A,v,O),D(v,U,O),$(m,v,O),D(v,w,O),$(z,v,O),D(v,T,O),$(k,v,O),D(v,F,O),$(j,v,O),N=!0},i(v){N||(C(e.$$.fragment,v),C(r.$$.fragment,v),C(i.$$.fragment,v),C(a.$$.fragment,v),C(u.$$.fragment,v),C(c.$$.fragment,v),C(g.$$.fragment,v),C(_.$$.fragment,v),C(S.$$.fragment,v),C(M.$$.fragment,v),C(E.$$.fragment,v),C(A.$$.fragment,v),C(m.$$.fragment,v),C(z.$$.fragment,v),C(k.$$.fragment,v),C(j.$$.fragment,v),N=!0)},o(v){P(e.$$.fragment,v),P(r.$$.fragment,v),P(i.$$.fragment,v),P(a.$$.fragment,v),P(u.$$.fragment,v),P(c.$$.fragment,v),P(g.$$.fragment,v),P(_.$$.fragment,v),P(S.$$.fragment,v),P(M.$$.fragment,v),P(E.$$.fragment,v),P(A.$$.fragment,v),P(m.$$.fragment,v),P(z.$$.fragment,v),P(k.$$.fragment,v),P(j.$$.fragment,v),N=!1},d(v){v&&(W(n),W(l),W(s),W(o),W(f),W(d),W(p),W(y),W(I),W(K),W(b),W(U),W(w),W(T),W(F)),x(e,v),x(r,v),x(i,v),x(a,v),x(u,v),x(c,v),x(g,v),x(_,v),x(S,v),x(M,v),x(E,v),x(A,v),x(m,v),x(z,v),x(k,v),x(j,v)}}}function Rl(t){let e,n;return e=new ei({}),{c(){ee(e.$$.fragment)},m(r,l){$(e,r,l),n=!0},i(r){n||(C(e.$$.fragment,r),n=!0)},o(r){P(e.$$.fragment,r),n=!1},d(r){x(e,r)}}}function tu(t){let e,n,r,l;e=new Vs({props:{$$slots:{default:[eu]},$$scope:{ctx:t}}});let i=t[0]&&Rl();return{c(){ee(e.$$.fragment),n=H(),i&&i.c(),r=pe()},m(s,a){$(e,s,a),D(s,n,a),i&&i.m(s,a),D(s,r,a),l=!0},p(s,[a]){const o={};a&2&&(o.$$scope={dirty:a,ctx:s}),e.$set(o),s[0]?i?a&1&&C(i,1):(i=Rl(),i.c(),C(i,1),i.m(r.parentNode,r)):i&&(le(),P(i,1,1,()=>{i=null}),se())},i(s){l||(C(e.$$.fragment,s),C(i),l=!0)},o(s){P(e.$$.fragment,s),P(i),l=!1},d(s){s&&(W(n),W(r)),x(e,s),i&&i.d(s)}}}function nu(t,e,n){let r;return fe(t,Vl,l=>n(0,r=l)),Nn.set({fallbackResourceName:"debug",allowEscapeKey:!0}),Fs(),Kl($t.uiLoaded),[r]}class ru extends he{constructor(e){super(),de(this,e,nu,tu,ce,{})}}new ru({target:document.getElementById("app")});
