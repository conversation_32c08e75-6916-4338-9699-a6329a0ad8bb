function prepareClientHouse(house_data)
  local data = {}

  for _, key in pairs({
    'id',
    'address',
    'door_location',
    'garage_location',
    'mail_location',
    'owner_character_id',
    'coowner_character_id',
    'is_locked',
    'is_listed',
    'owner_price',
    'market_price',
    'owner',
  }) do
    data[key] = house_data[key]
  end

  return data
end

P.bindInstance('postoffice', {
  getHouses = function()
    local houses = exports.blrp_core:GetHouses()

    local houses_client = {}

    for _, house_data in pairs(houses) do
      houses_client[house_data.id] = prepareClientHouse(house_data)
    end

    return houses_client
  end
})

Citizen.CreateThread(function()
  while true do
    -- Move mail around the system internally
    MySQL.Async.execute("UPDATE mail_sent SET system_location = 'vinewood' WHERE sent_time < DATE_SUB(NOW(), interval @interval minute) AND sent = true AND delivered = false AND system_location != 'vinewood'", {
      ['interval'] = postoffice_config.shipment_hold_time
    })

    -- Auto deliver after mail has been stagnant for long enough
    MySQL.Async.fetchAll("SELECT * FROM mail_sent WHERE sent_time < DATE_SUB(NOW(), interval @interval minute) AND sent = true AND delivered = false AND system_location = 'vinewood'", {
      ['interval'] = postoffice_config.shipment_stagnate_time
    }, function(result)
      for _, mail in ipairs(result) do
        TriggerEvent('blrp_postoffice:server:tryDeliverMail', false, mail.id)
      end
    end)

    Citizen.Wait(1000 * 60 * 5)
  end
end)

RegisterServerEvent('blrp_postoffice:server:sendMail')
AddEventHandler('blrp_postoffice:server:sendMail', function(location)
  TriggerClientEvent("blrp_postoffice:client:showInterface", source, location)
end)

AddEventHandler('core:server:registerSelectedPlayer', function(source, character)
  SetTimeout(100, function()
    for _, location in ipairs(postoffice_config.locations) do
      if not location.hide_prompt then
        exports.blrp_core:character(source).drawInteract(location)
      end
    end
  end)
end)

-- Main handler for depositing items to be mailed
RegisterNetEvent('blrp_postoffice:server:openDepositBox', function(data)
  local destination = data.destination
  local mail_type = postoffice_config.mail_types[data.mail_type]

  local char = exports.blrp_core:character(source)

  local sender_name = data.sender_name

  if sender_name == "" or sender_name == nil then
    sender_name = "(Anonymous)"
  end

  local owner_character_id = destination.owner_character_id
  local co_owner_character_id = destination.coowner_character_id or -1

  local owner_characters = MySQL.query.await('SELECT * FROM characters WHERE id = ? or id = ?', {
    owner_character_id, co_owner_character_id
  })

  local owner_phone = nil
  local co_owner_phone = nil

  for _, owner_character in pairs(owner_characters) do
    if owner_character.id == owner_character_id then
      owner_phone = owner_character.phone
    end

    if owner_character.id == co_owner_character_id then
      co_owner_phone = owner_character.phone
    end
  end

  -- Insert the base data (mail type, destination address/name/location, sender name) and retrieve the record #
  MySQL.Async.insert(
    'INSERT INTO mail_sent (mail_type, recipient_char_id, recipient_name, recipient_address, recipient_phone, recipient2_char_id, recipient2_phone, sender_identifier, sender_char_id, sender_name, house_id, x, y, z, system_location, checkout_time, sent_time)' ..
    'VALUES (@mail_type, @recipient_char_id, @recipient_name, @recipient_address, @recipient_phone, @recipient2_char_id, @recipient2_phone, @sender_identifier, @sender_char_id, @sender_name, @house_id, @x, @y, @z, @system_location, NOW(), NOW())',
    {
      ['mail_type'] = mail_type.type,
      ['recipient_char_id'] = owner_character_id,
      ['recipient_name'] = data.recipient_name,
      ['recipient_address'] = destination.address,
      ['recipient_phone'] = owner_phone,
      ['recipient2_char_id'] = co_owner_character_id,
      ['recipient2_phone'] = co_owner_phone,
      ['sender_identifier'] = char.get('identifier'),
      ['sender_char_id'] = char.get('id'),
      ['sender_name'] = sender_name,
      ['house_id'] = destination.id,
      ['x'] = destination.mail_location.x,
      ['y'] = destination.mail_location.y,
      ['z'] = destination.mail_location.z,
      ['system_location'] = data.system_location
    },
    function(mail_id)
      local cost = get_cost(mail_type, destination.mail_location, data.system_location)
      local mailChestName = 'mail' .. mail_id .. 'contents'

      -- Open the mail chest and allow player to move items back and forth
      -- Callback is called when the chest closes
      char.openChest(mailChestName, mail_type.chest_weight, function()
        local had_prohib_items = false
        local has_items = false
        local white_items = {}

        -- Check contents of mail chest. Remove and return any blacklisted items
        exports.blrp_core:chest(mailChestName).fetchContents(function(items)
            if not items then items = {} end

            for item_name, item_data in pairs(items) do
              local push_whitelist = true

              for _, prohib_item_name in ipairs(postoffice_config.prohibited_items) do
                if string.find(item_name, prohib_item_name) ~= nil then
                  had_prohib_items = true
                  push_whitelist = false

                  char.log("MAIL", "Rejected " .. item_name .. " (" .. item_data.amount .. ")")
                  char.give(item_name, item_data.amount, item_data.meta)
                end
              end

              if push_whitelist then
                white_items[item_name] = item_data
              end
            end

            -- Check to see if the player actually put items in after factoring out blacklisted items. If not, give them an error
            for item_name, item_data in pairs(white_items) do
              if item_data.amount > 0 then
                has_items = true
              end
            end
        end)

        if had_prohib_items then
          char.notify("The postal service rejected some of your items and returned them to you")
          exports.blrp_core:chest(mailChestName).setContents(white_items)
        end

        if has_items then
          -- Prompt player to confirm the address and cost of the outgoing parcel
          if not char.request("Send " .. mail_type.type .. " to " .. destination.address .. " for $" .. cost .. "?") then
            -- Player didn't accept the prompt to send mail, return items
            char.log("MAIL", "(" .. char.get('identifier') .. ") prompt timeout")
            returnItems(mail_id, mailChestName, char)
            return
          end

          if not char.tryPayment(cost) then
            -- Not enough money, return items
            char.log("MAIL", "(" .. char.get('identifier') .. ") insufficient funds to send mail")
            returnItems(mail_id, mailChestName, char)
            return
          end

          -- Log action, notify of success
          char.log("MAIL", "(" .. char.get('identifier') .. ") Sending mail (DB ID: " .. mail_id .. ") (char) " .. char.get('id') .. " -> (recip char 1) " .. destination.owner_character_id .. ", (recip char 2) " .. tostring(destination.coowner_character_id) .. " Cost: $" .. cost .. ", Contents (json): " .. json.encode(white_items))
          char.notify("Your parcel was accepted by the postal service")

          -- Update record so that the system can move it to the depot
          MySQL.Async.execute('UPDATE mail_sent SET sent = true WHERE id = @id', { ['id'] = mail_id })
        else
          -- Player didn't deposit any items to send
          char.notify("You must deposit some items to send")
          MySQL.Async.execute('DELETE FROM mail_sent WHERE id = @id LIMIT 1', { ['id'] = mail_id })
        end
      end, {
        whitelisted_items = mail_type.whitelisted_items
      })
    end
  )
end)

RegisterServerEvent('blrp_postoffice:server:tryDeliverMail')
AddEventHandler('blrp_postoffice:server:tryDeliverMail', function(source, mail_id)
  -- Grab record from the DB so we know the data is our own
  MySQL.Async.fetchAll("SELECT * FROM mail_sent WHERE id = @id", { ['id'] = mail_id }, function(result)
    if #result > 0 then
      local mail = result[1]

      -- If mail has already been delivered (is possible because it can be checked out by more than one player), discontinue processing
      if not mail.delivered then
        MySQL.Async.execute("UPDATE mail_sent SET delivered = true, system_location = 'recipient' WHERE id = @id LIMIT 1", { ['id'] = mail_id })

        local houseMailboxName = 'h' .. mail.house_id .. 'mailBox'

        -- Add parcel (with metadata) to house mailbox
        exports.blrp_core:chest(houseMailboxName).fetchContents(function(items)
            if not items then items = {} end

            items['gopostal_' .. mail.mail_type .. ':meta:' .. mail.id] = {
              amount = 1,
              meta = {
                mail_data = mail,
                mail_address = mail.recipient_address,
                mail_to = mail.recipient_name,
                mail_from = mail.sender_name,
                sent_by_char_id = mail.sender_char_id
              }
            }

            exports.blrp_core:chest(houseMailboxName).setContents(items)
        end)

        if source then
          exports.blrp_core:character(source).log("MAIL", "Delivered player-bound mail. ID: " .. mail_id)
        end

        -- Send text message advising of delivered package
				TriggerEvent('gcPhone:sendMessage_Anonymous', "252-5118", mail.recipient_phone, "Your GoPostal package has been delivered to " .. mail.recipient_address .. ". This is an automated message.")

        if mail.recipient2_phone then
          TriggerEvent('gcPhone:sendMessage_Anonymous', "252-5118", mail.recipient2_phone, "Your GoPostal package has been delivered to " .. mail.recipient_address .. ". This is an automated message.")
        end
      end
    end
  end)
end)

RegisterServerEvent('blrp_postoffice:server:tryOpenMail')
AddEventHandler('blrp_postoffice:server:tryOpenMail', function(source, mail_metaname)
  local matched_name = false
  local char = exports.blrp_core:character(source)

  -- Check to see if the mail item actually has metadata
  for type, __ in pairs(postoffice_config.mail_types) do
    if string.match(mail_metaname, 'gopostal_' .. type .. ':meta:') then
      matched_name = true
    end
  end

  if not matched_name then
    char.notify("You can't open mail that isn't addressed to you")
    return
  end

  -- Grab the item from the player's inventory so we can read the metadata
  local meta = char.hasGetItemMeta(mail_metaname)

  if not meta then
    return
  end

  if char.get('id') ~= meta.mail_data.recipient_char_id and char.get('id') ~= meta.mail_data.recipient2_char_id then
    char.notify("You can't open mail that isn't addressed to you")
    return
  end

  local mailChestName = 'mail' .. meta.mail_data.id .. 'contents'
  local allowTransaction = true

  -- Checks to see if a player is trying to cheat by transferring items between one of their characters and another
  if tostring(meta.mail_data.sender_identifier) == tostring(char.get('identifier')) and tostring(meta.mail_data.recipient_char_id) ~= tostring(meta.mail_data.sender_char_id) and tostring(meta.mail_data.recipient2_char_id) ~= tostring(meta.mail_data.sender_char_id) then
    allowTransaction = false
  end

  if allowTransaction then
    -- All checks passed. Remove the mail item from the player's inventory and add the contents
    exports.blrp_core:chest(mailChestName).fetchContents(function(items)
      if not items then items = {} end

      char.hideInventory()
      char.take(mail_metaname, 1, false)
      char.log("MAIL", "Player opened mail, sent_mail DB ID: " .. meta.mail_data.id .. ", Contents (json): " .. json.encode(items))

      for item_name, item_data in pairs(items) do
        char.give(item_name, item_data.amount, item_data.meta)
      end

      char.give('gopostal_' .. meta.mail_data.mail_type .. '_open:meta:' .. meta.mail_data.id, 1, meta)

      exports.blrp_core:chest(mailChestName).setContents({})
    end)
  else
    -- Player is cheating. Log the action, remove the mail item from the player's inventory, then delete all the items they were trying to send
    exports.blrp_core:chest(mailChestName).fetchContents(function(items)
      if not items then items = {} end
      char.log("MAILFRAUD", "Mail fraud: [ID] " .. char.get('identifier') .. " Attempted to exchange items between two characters with same account identifier via mail system. MailID: " .. mail_item.meta.mail_data.id .. ", Contents (json): " .. json.encode(items))

      char.hideInventory()
      char.take(mail_metaname, 1, false)

      exports.blrp_core:chest(mailChestName).setContents({})
    end)
  end
end)

function returnItems(mail_id, chestName, char)
  char.notify("Your deposited items have been returned")
  exports.blrp_core:chest(chestName).fetchContents(function(items)
      char.log("MAIL", "Returned deposited items: " .. json.encode(items))

      for item_name, item_data in pairs(items) do
        char.give(item_name, item_data.amount)
      end

      exports.blrp_core:chest(chestName).setContents({})
  end)

  MySQL.Async.execute('DELETE FROM mail_sent WHERE id = @id LIMIT 1', { ['id'] = mail_id })
end

function get_cost(mail_type, destination, origin_name)
    local office_location = nil

    for _, location in ipairs(postoffice_config.locations) do
      if location.args == origin_name then
        office_location = location.coords
      end
    end

    if office_location == nil then
      return 10000
    end

    local dx = office_location.x - destination.x
    local dy = office_location.y - destination.y

    local distance = math.ceil(math.sqrt(dx * dx + dy * dy))

    return math.ceil(distance * mail_type.cost_per_meter)
end
