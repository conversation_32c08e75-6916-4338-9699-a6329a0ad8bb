<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVarGlobal>
    <Kits>
        <Item>
            <kitName>6932_polcoach_modkit</kitName>
            <id value="6932"/>
            <kitType>MKT_SPECIAL</kitType>
            <visibleMods>
                <!-- Police Equipments -->
                <Item>
                    <modelName>polcoach_fbar</modelName>
                    <modShopLabel>POL_GRILL</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_CHASSIS</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>null</modelName>
                    <modShopLabel>POL_PERFORMANCE</modShopLabel>
                    <linkedModels/>
                    <turnOffBones/>
                    <type>VMT_SPOILER</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>polcoach_null</modelName>
                    <modShopLabel>POL_GRILLREM</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_y</Item>
                    </turnOffBones>
                    <type>VMT_CHASSIS</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
                <Item>
                    <modelName>polcoach_null</modelName>
                    <modShopLabel>POL_BADGEREM</modShopLabel>
                    <linkedModels/>
                    <turnOffBones>
                        <Item>misc_z</Item>
                    </turnOffBones>
                    <type>VMT_CHASSIS</type>
                    <bone>chassis</bone>
                    <collisionBone>chassis</collisionBone>
                    <cameraPos>VMCP_DEFAULT</cameraPos>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <turnOffExtra value="false"/>
                    <disableBonnetCamera value="false"/>
                    <allowBonnetSlide value="true"/>
                </Item>
            </visibleMods>
            <linkMods>
                <Item>
                    <modelName>polcoach_badge</modelName>
                    <bone>chassis</bone>
                    <turnOffExtra value="false"/>
                </Item>
            </linkMods>
            <statMods>
                <Item>
                    <identifier/>
                    <modifier value="25"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="50"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="75"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="125"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ENGINE</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="150"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="200"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="250"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_BRAKES</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="25"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="50"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="75"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.000000"/>
                    <weight value="5"/>
                    <type>VMT_GEARBOX</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="6"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="12"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="18"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="24"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="30"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_SUSPENSION</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="20"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="40"/>
                    <audioApply value="1.000000"/>
                    <weight value="10"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="60"/>
                    <audioApply value="1.000000"/>
                    <weight value="20"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="80"/>
                    <audioApply value="1.000000"/>
                    <weight value="30"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier/>
                    <modifier value="100"/>
                    <audioApply value="1.000000"/>
                    <weight value="40"/>
                    <type>VMT_ARMOUR</type>
                </Item>
                <Item>
                    <identifier>HORN_TRUCK</identifier>
                    <modifier value="1766676233"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_COP</identifier>
                    <modifier value="2904189469"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_CLOWN</identifier>
                    <modifier value="2543206147"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_1</identifier>
                    <modifier value="1732399718"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_2</identifier>
                    <modifier value="2046162893"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_3</identifier>
                    <modifier value="2194999691"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_4</identifier>
                    <modifier value="2508304100"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_MUSICAL_5</identifier>
                    <modifier value="3707223535"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HORN_SAD_TROMBONE</identifier>
                    <modifier value="632950117"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
                    <modifier value="3628534289"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
                    <modifier value="3892554122"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
                    <modifier value="4112892878"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
                    <modifier value="116877169"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
                    <modifier value="2684983719"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
                    <modifier value="2982690084"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
                    <modifier value="3203290992"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
                    <modifier value="771284519"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
                    <modifier value="2586621229"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
                    <modifier value="283386134"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
                    <modifier value="3884502400"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
                    <modifier value="265723083"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
                    <modifier value="1746883687"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
                    <modifier value="1919870950"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
                    <modifier value="1085277077"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_1</identifier>
                    <modifier value="444549672"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_2</identifier>
                    <modifier value="1603064898"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_3</identifier>
                    <modifier value="240366033"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>HIPSTER_HORN_4</identifier>
                    <modifier value="960137118"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_1</identifier>
                    <modifier value="3572144790"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_2</identifier>
                    <modifier value="3801396714"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_3</identifier>
                    <modifier value="2843657151"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>INDEP_HORN_4</identifier>
                    <modifier value="3341811489"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXE_HORN_1</identifier>
                    <modifier value="3199657341"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXE_HORN_2</identifier>
                    <modifier value="2900378064"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXE_HORN_3</identifier>
                    <modifier value="3956195248"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXORY_HORN_1</identifier>
                    <modifier value="676333254"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXURY_HORN_2</identifier>
                    <modifier value="2099578296"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LUXURY_HORN_3</identifier>
                    <modifier value="1373384483"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_01</identifier>
                    <modifier value="2916775806"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
                    <modifier value="3714706952"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_02</identifier>
                    <modifier value="2611860261"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
                    <modifier value="3206770359"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_1</identifier>
                    <modifier value="310529291"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
                    <modifier value="2965568987"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_2</identifier>
                    <modifier value="55291550"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
                    <modifier value="965054819"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_01</identifier>
                    <modifier value="55862314"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_01_PREVIEW</identifier>
                    <modifier value="2156743178"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_02</identifier>
                    <modifier value="400002352"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_02_PREVIEW</identifier>
                    <modifier value="897484282"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_03</identifier>
                    <modifier value="560832604"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>XM15_HORN_03_PREVIEW</identifier>
                    <modifier value="314232747"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_01</identifier>
                    <modifier value="3851180092"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_01_Preview</identifier>
                    <modifier value="246182814"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_02</identifier>
                    <modifier value="3412861948"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_02_Preview</identifier>
                    <modifier value="1804608241"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_03</identifier>
                    <modifier value="3374260066"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <Item>
                    <identifier>DLC_AW_Airhorn_03_Preview</identifier>
                    <modifier value="2798044638"/>
                    <audioApply value="1.000000"/>
                    <weight value="0"/>
                    <type>VMT_HORN</type>
                </Item>
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
                <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
            </statMods>
            <slotNames>
                <Item>
                    <slot>VMT_WING_L</slot>
                    <name>TOP_MIR</name>
                </Item>
                <Item>
                    <slot>VMT_WING_R</slot>
                    <name>CMOD_SLOU_N</name>
                </Item>
                <Item>
                    <slot>VMT_SKIRT</slot>
                    <name>TOP_SUNST</name>
                </Item>
                <Item>
                    <slot>VMT_CHASSIS</slot>
                    <name>TOP_CAGE</name>
                </Item>
                <Item>
                    <slot>VMT_ICE</slot>
                    <name>RAMBAR</name>
                </Item>
            </slotNames>
            <liveryNames/>
        </Item>
    </Kits>
    <Sirens>
        <Item>
            <id value="132"/>
            <name>PolCoach_Sirens</name>
            <timeMultiplier value="1.00000000"/>
            <lightFalloffMax value="80.00000000"/>
            <lightFalloffExponent value="55.00000000"/>
            <lightInnerConeAngle value="2.00000000"/>
            <lightOuterConeAngle value="70.00000000"/>
            <lightOffset value="0.00000000"/>
            <textureName>VehicleLight_sirenlight</textureName>
            <sequencerBpm value="500"/>
            <leftHeadLight>
                <sequencer value="0"/>
            </leftHeadLight>
            <rightHeadLight>
                <sequencer value="0"/>
            </rightHeadLight>
            <leftTailLight>
                <sequencer value="1431655765"/>
            </leftTailLight>
            <rightTailLight>
                <sequencer value="2863311530"/>
            </rightTailLight>
            <leftHeadLightMultiples value="1"/>
            <rightHeadLightMultiples value="1"/>
            <leftTailLightMultiples value="1"/>
            <rightTailLightMultiples value="1"/>
            <useRealLights value="true"/>
            <sirens>
                <!--Siren 1 : Red-->
                <!--Siren 11 : Amber -->
                <!--Siren 12 : Amber -->
                <!--Siren 13 : Blue -->
                <!--Siren 14 : Blue -->
                <!--Siren 15 : Blue-->
                <!--Siren 16 : Blue-->
                <!--Siren 17 : Red -->
                <!--Siren 18 : Blue -->
                <!--Siren 19 : Red -->
                <!--Siren 20 : Blue -->
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="33556490"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="0.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0000"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="2"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!--Siren 2 : Red-->
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2902465536"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="0.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0000"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!--Siren 3 : Red-->
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="8402442"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="0.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0000"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!--Siren 4 : Red-->
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2889900800"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="0.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0000"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!--Siren 5 : Blue-->
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2146698"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="0.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!--Siren 6 : Blue-->
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2886828224"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="0.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!--Siren 7 : Blue-->
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="721002"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="0.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!--Siren 8 : Blue-->
                    <rotation>
                        <delta value="-0.01000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2886074400"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="0.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!--Siren 9 : Red-->
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="33603754"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="0.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0000"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!--Siren 10 : red -->
                    <rotation>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="3.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2902581248"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="0.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0000"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="8597674"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFD700"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="2890275840"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFD700"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="28852909"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF0000FF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="2887254528"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF000AFF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF0000FF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="2840943274"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF0000FF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="1454024021"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0000"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0000"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="1431655765"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0000"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <rotation>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="0.00000000"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="2863311530"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFF0000FF"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
            </sirens>
        </Item>
    </Sirens>
</CVehicleModelInfoVarGlobal>