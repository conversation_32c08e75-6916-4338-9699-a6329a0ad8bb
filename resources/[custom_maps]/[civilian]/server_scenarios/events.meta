<?xml version="1.0" encoding="UTF-8"?>
<CEventDataManager>
	<eventResponseTaskData>
		<Item type="CEventDataResponseTaskGunAimedAt">
			<Name>RESPONSE_TASK_GUN_AIMED_AT</Name>
		</Item>
		<Item type="CEventDataResponseTaskTurnToFace">
			<Name>RESPONSE_TASK_TURN_TO_FACE</Name>
			<Face value="1"/>
		</Item>
		<Item type="CEventDataResponsePoliceTaskWanted">
			<Name>RESPONSE_POLICE_TASK_WANTED</Name>
		</Item>
		<Item type="CEventDataResponseSwatTaskWanted">
			<Name>RESPONSE_SWAT_TASK_WANTED</Name>
		</Item>
		<Item type="CEventDataResponseTaskCrouch">
			<Name>RESPONSE_TASK_CROUCH</Name>
			<TimeToCrouch value="3.000000"/>
		</Item>
		<Item type="CEventDataResponseTaskCower">
			<Name>RESPONSE_TASK_COWER</Name>
		</Item>
		<Item type="CEventDataResponseTaskWalkRoundFire">
			<Name>RESPONSE_TASK_WALK_ROUND_FIRE</Name>
		</Item>
		<Item type="CEventDataResponseTaskHandsUp">
			<Name>RESPONSE_TASK_HANDS_UP</Name>
		</Item>
		<Item type="CEventDataResponseTaskLeaveCarAndFlee">
			<Name>RESPONSE_TASK_LEAVE_CAR_AND_FLEE</Name>
		</Item>
		<Item type="CEventDataResponseTaskCombat">
			<Name>RESPONSE_TASK_COMBAT</Name>
		</Item>
		<Item type="CEventDataResponseTaskThreat">
			<Name>RESPONSE_TASK_THREAT</Name>
		</Item>
		<Item type="CEventDataResponseTaskFlee">
			<Name>RESPONSE_TASK_FLEE</Name>
			<Flee value="1"/>
		</Item>
		<Item type="CEventDataResponseTaskScenarioFlee">
			<Name>RESPONSE_TASK_SCENARIO_FLEE</Name>
			<Flee value="1"/>
		</Item>
		<Item type="CEventDataResponseTaskFlyAway">
			<Name>RESPONSE_TASK_FLY_AWAY</Name>
			<Flee value="1"/>
		</Item>
		<Item type="CEventDataResponseTaskWalkRoundEntity">
			<Name>RESPONSE_TASK_WALK_ROUND_ENTITY</Name>
		</Item>
		<Item type="CEventDataResponseTaskHeadTrack">
			<Name>RESPONSE_TASK_HEAD_TRACK</Name>
		</Item>
		<Item type="CEventDataResponseTaskShockingEventGoto">
			<Name>RESPONSE_TASK_SHOCKING_EVENT_GOTO</Name>
		</Item>
		<Item type="CEventDataResponseTaskShockingEventHurryAway">
			<Name>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</Name>
		</Item>
		<Item type="CEventDataResponseTaskShockingEventWatch">
			<Name>RESPONSE_TASK_SHOCKING_EVENT_WATCH</Name>
		</Item>
		<Item type="CEventDataResponseTaskEvasiveStep">
			<Name>RESPONSE_TASK_EVASIVE_STEP</Name>
		</Item>
		<Item type="CEventDataResponseTaskShockingPoliceInvestigate">
			<Name>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</Name>
		</Item>
		<Item type="CEventDataResponseTaskEscapeBlast">
			<Name>RESPONSE_TASK_ESCAPE_BLAST</Name>
		</Item>
		<Item type="CEventDataResponseTaskAgitated">
			<Name>RESPONSE_TASK_AGITATED</Name>
		</Item>
		<Item type="CEventDataResponseTaskExplosion">
			<Name>RESPONSE_TASK_EXPLOSION</Name>
		</Item>
		<Item type="CEventDataResponseTaskDuckAndCover">
			<Name>RESPONSE_TASK_DUCK_AND_COVER</Name>
		</Item>
		<Item type="CEventDataResponseTaskShockingEventReactToAircraft">
			<Name>RESPONSE_TASK_SHOCKING_EVENT_REACT_TO_AIRCRAFT</Name>
		</Item>
		<Item type="CEventDataResponseTaskShockingEventReact">
			<Name>RESPONSE_TASK_SHOCKING_EVENT_REACT</Name>
		</Item>
		<Item type="CEventDataResponseTaskShockingEventBackAway">
			<Name>RESPONSE_TASK_SHOCKING_EVENT_BACK_AWAY</Name>
		</Item>
		<Item type="CEventDataResponseTaskShockingEventStopAndStare">
			<Name>RESPONSE_TASK_SHOCKING_EVENT_STOP_AND_STARE</Name>
		</Item>
		<Item type="CEventDataResponseTaskSharkAttack">
			<Name>RESPONSE_TASK_SHARK_ATTACK</Name>
		</Item>
		<Item type="CEventDataResponseTaskExhaustedFlee">
			<Name>RESPONSE_TASK_EXHAUSTED_FLEE</Name>
		</Item>
		<Item type="CEventDataResponseTaskGrowlAndFlee">
			<Name>RESPONSE_TASK_GROWL_AND_FLEE</Name>
		</Item>
		<Item type="CEventDataResponseTaskWalkAway">
			<Name>RESPONSE_TASK_WALK_AWAY</Name>
		</Item>
		<Item type="CEventDataResponseAggressiveRubberneck">
			<Name>RESPONSE_TASK_AGGRESSIVE_RUBBERNECK</Name>
		</Item>
    <Item type="CEventDataResponseTaskShockingNiceCar">
      <Name>RESPONSE_TASK_SHOCKING_NICE_CAR</Name>
    </Item>
    <Item type="CEventDataResponseFriendlyNearMiss">
      <Name>RESPONSE_TASK_FRIENDLY_NEAR_MISS</Name>
    </Item>
    <Item type="CEventDataResponseFriendlyAimedAt">
      <Name>RESPONSE_TASK_FRIENDLY_AIMED_AT</Name>
    </Item>
    <Item type="CEventDataResponseDeferToScenarioPointFlags">
      <Name>RESPONSE_TASK_DEFER_TO_SCENARIO_POINT_FLAGS</Name>
    </Item>
     <Item type="CEventDataResponseTaskShockingEventThreatResponse">
      <Name>RESPONSE_TASK_SHOCKING_EVENT_THREAT_RESPONSE</Name>
    </Item>
    <Item type="CEventDataResponsePlayerDeath">
      <Name>RESPONSE_TASK_PLAYER_DEATH</Name>
    </Item>
	</eventResponseTaskData>
	<eventDecisionMakerResponseData>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_EXPLOSION</Name>
			<Event>EVENT_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXPLOSION</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
          		<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInComplexScenario</EventResponseDecisionFlags>
				</Item>
          		<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
			</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COP_RESPONSE_EXPLOSION</Name>
			<Event>EVENT_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_EXPLOSION</Name>
			<Event>EVENT_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXPLOSION</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
          		<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInComplexScenario ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
          		<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>TURN_TO_FACE_RESPONSE_MUGGING</Name>
			<Event>EVENT_SHOCKING_MUGGING</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>INVESTIGATE_RESPONSE_MUGGING</Name>
			<Event>EVENT_SHOCKING_MUGGING</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_MUGGING</Name>
			<Event>EVENT_SHOCKING_MUGGING</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOT_FIRED</Name>
			<Event>EVENT_SHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli InvalidIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOT_FIRED</Name>
			<Event>EVENT_SHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FIREMAN_RESPONSE_SHOT_FIRED</Name>
			<Event>EVENT_SHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>MEDIC_RESPONSE_SHOT_FIRED</Name>
			<Event>EVENT_SHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_SHOT_FIRED</Name>
			<Event>EVENT_SHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FISH_RESPONSE_FLEE</Name>
			<Event>EVENT_ENCROACHING_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SCENARIO_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
					<EventResponseFleeFlags>DisableCover</EventResponseFleeFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SHARK_ATTACK_ENCROACHING</Name>
			<Event>EVENT_ENCROACHING_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHARK_ATTACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario InvalidIfThereAreAttackingSharks InvalidIfSourceIsAnAnimal</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SHARK_ATTACK_HATE</Name>
			<Event>EVENT_ACQUAINTANCE_PED_HATE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHARK_ATTACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario InvalidIfThereAreAttackingSharks InvalidIfSourceIsAnAnimal</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_ENCROACHMENT</Name>
			<Event>EVENT_ENCROACHING_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>WALK_AWAY_RESPONSE_ENCROACHMENT</Name>
			<Event>EVENT_ENCROACHING_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_WALK_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GULL_RESPONSE_FLEE</Name>
			<Event>EVENT_ENCROACHING_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GULL_RESPONSE_EXPLOSION</Name>
			<Event>EVENT_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GULL_RESPONSE_GUNSHOT</Name>
			<Event>EVENT_SHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>HEN_RESPONSE_FLEE</Name>
			<Event>EVENT_ENCROACHING_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SCENARIO_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
					<EventResponseFleeFlags>DisableCover</EventResponseFleeFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>RAT_RESPONSE_FLEE</Name>
			<Event>EVENT_ENCROACHING_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SCENARIO_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
					<EventResponseFleeFlags>DisableCover</EventResponseFleeFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_DAMAGE</Name>
			<Event>EVENT_DAMAGE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_FOOT_STEP_HEARD</Name>
			<Event>EVENT_FOOT_STEP_HEARD</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_EXPLOSION</Name>
			<Event>EVENT_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_SHOCKING_EXPLOSION</Name>
			<Event>EVENT_SHOCKING_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_CAR_CRASH</Name>
			<Event>EVENT_SHOCKING_CAR_CRASH</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DOG_RESPONSE_CAR_CRASH</Name>
			<Event>EVENT_SHOCKING_CAR_CRASH</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DOG_RESPONSE_INJURED_PED</Name>
			<Event>EVENT_SHOCKING_INJURED_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSource ValidOnlyIfFriendlyWithTarget InvalidIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DOG_RESPONSE_SEEN_PED_KILLED</Name>
			<Event>EVENT_SHOCKING_SEEN_PED_KILLED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSource ValidOnlyIfFriendlyWithTarget InvalidIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_SEEN_PED_RUN_OVER</Name>
			<Event>EVENT_SHOCKING_PED_RUN_OVER</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_INJURED_PED</Name>
			<Event>EVENT_SHOCKING_INJURED_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_HELICOPTER</Name>
			<Event>EVENT_SHOCKING_HELICOPTER_OVERHEAD</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_PLANE</Name>
			<Event>EVENT_SHOCKING_PLANE_FLY_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_SHOT_FIRED</Name>
			<Event>EVENT_SHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_SHOT_FIRED_WHIZZED_BY</Name>
			<Event>EVENT_SHOT_FIRED_WHIZZED_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
			<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Name>
			<Event>EVENT_SHOT_FIRED_BULLET_IMPACT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COMBAT_RESPONSE_FOOT_STEP_HEARD</Name>
			<Event>EVENT_FOOT_STEP_HEARD</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_COMBAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GROWL_RESPONSE_SHOT_FIRED</Name>
			<Event>EVENT_SHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_GROWL_AND_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GROWL_RESPONSE_SHOT_FIRED_WHIZZED_BY</Name>
			<Event>EVENT_SHOT_FIRED_WHIZZED_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_GROWL_AND_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GROWL_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Name>
			<Event>EVENT_SHOT_FIRED_BULLET_IMPACT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_GROWL_AND_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COMBAT_RESPONSE_ENCROACHMENT</Name>
			<Event>EVENT_ENCROACHING_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_COMBAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GROWL_RESPONSE_HORN</Name>
			<Event>EVENT_SHOCKING_HORN_SOUNDED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_GROWL_AND_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_DAMAGE</Name>
			<Event>EVENT_DAMAGE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_CAR_CRASH</Name>
			<Event>EVENT_SHOCKING_CAR_CRASH</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_HORN</Name>
			<Event>EVENT_SHOCKING_HORN_SOUNDED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_INJURED_PED</Name>
			<Event>EVENT_SHOCKING_INJURED_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_HELICOPTER</Name>
			<Event>EVENT_SHOCKING_HELICOPTER_OVERHEAD</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_PLANE</Name>
			<Event>EVENT_SHOCKING_PLANE_FLY_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
 			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_SHOCKING_EXPLOSION</Name>
			<Event>EVENT_SHOCKING_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_SEEN_PED_RUN_OVER</Name>
			<Event>EVENT_SHOCKING_PED_RUN_OVER</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_EXPLOSION</Name>
			<Event>EVENT_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_SHOT_FIRED</Name>
			<Event>EVENT_SHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_SHOT_FIRED_WHIZZED_BY</Name>
			<Event>EVENT_SHOT_FIRED_WHIZZED_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Name>
			<Event>EVENT_SHOT_FIRED_BULLET_IMPACT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>EXHAUSTED_FLEE_RESPONSE_SHOCKING_MELEE_ACTION</Name>
			<Event>EVENT_SHOCKING_SEEN_MELEE_ACTION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EXHAUSTED_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="4.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>HORSE_FLEE_RESPONSE_DAMAGE</Name>
			<Event>EVENT_DAMAGE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidIfHasARider NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>HORSE_FLEE_RESPONSE_EXPLOSION</Name>
			<Event>EVENT_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidIfHasARider NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>HORSE_FLEE_RESPONSE_SHOT_FIRED</Name>
			<Event>EVENT_SHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidIfHasARider NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>HORSE_FLEE_RESPONSE_SHOT_FIRED_WHIZZED_BY</Name>
			<Event>EVENT_SHOT_FIRED_WHIZZED_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidIfHasARider NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>HORSE_FLEE_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Name>
			<Event>EVENT_SHOT_FIRED_BULLET_IMPACT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidIfHasARider NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>HORSE_FLEE_RESPONSE_SHOCKING_MELEE_ACTION</Name>
			<Event>EVENT_SHOCKING_SEEN_MELEE_ACTION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="4.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidIfHasARider NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_HATE</Name>
			<Event>EVENT_ACQUAINTANCE_PED_HATE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_WANTED</Name>
			<Event>EVENT_ACQUAINTANCE_PED_WANTED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_COMBAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_INJURED_CRY_FOR_HELP</Name>
			<Event>EVENT_INJURED_CRY_FOR_HELP</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_COMBAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_CRIME_CRY_FOR_HELP</Name>
			<Event>EVENT_CRIME_CRY_FOR_HELP</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInHeli</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar</EventResponseDecisionFlags>
				</Item>	
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="2500.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar</EventResponseDecisionFlags>
				</Item>					
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="2500.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidOnlyIfSourceIsNotThreatening</EventResponseDecisionFlags>
				</Item>				
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="2500.000000"/>
					<EventResponseDecisionFlags>ValidOnlyIfSourceIsThreatening</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_CRIME_CRY_FOR_HELP</Name>
			<Event>EVENT_CRIME_CRY_FOR_HELP</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COP_RESPONSE_SHOCKING_CAR_ALARM</Name>
			<Event>EVENT_SHOCKING_CAR_ALARM</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidInHeli</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COP_RESPONSE_PROPERTY_DAMAGE</Name>
			<Event>EVENT_SHOCKING_PROPERTY_DAMAGE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="144.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidInCar</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="144.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COP_RESPONSE_CRIME_CRY_FOR_HELP</Name>
			<Event>EVENT_CRIME_CRY_FOR_HELP</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_COMBAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COP_RESPONSE_WANTED</Name>
			<Event>EVENT_ACQUAINTANCE_PED_WANTED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_POLICE_TASK_WANTED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SWAT_RESPONSE_WANTED</Name>
			<Event>EVENT_ACQUAINTANCE_PED_WANTED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_SWAT_TASK_WANTED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SWAT_RESPONSE_DAMAGE</Name>
			<Event>EVENT_DAMAGE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_SWAT_TASK_WANTED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SWAT_RESPONSE_SHOT_FIRED</Name>
			<Event>EVENT_SHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_SWAT_TASK_WANTED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SWAT_RESPONSE_SHOT_FIRED_WHIZZED_BY</Name>
			<Event>EVENT_SHOT_FIRED_WHIZZED_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_SWAT_TASK_WANTED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SWAT_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Name>
			<Event>EVENT_SHOT_FIRED_BULLET_IMPACT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_SWAT_TASK_WANTED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SWAT_RESPONSE_GUN_AIMED_AT</Name>
			<Event>EVENT_GUN_AIMED_AT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_SWAT_TASK_WANTED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SWAT_RESPONSE_SHOUT_TARGET_POSITION</Name>
			<Event>EVENT_SHOUT_TARGET_POSITION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_SWAT_TASK_WANTED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SWAT_RESPONSE_MELEE</Name>
			<Event>EVENT_MELEE_ACTION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_SWAT_TASK_WANTED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SWAT_RESPONSE_VEHICLE_DAMAGE_WEAPON</Name>
			<Event>EVENT_VEHICLE_DAMAGE_WEAPON</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_SWAT_TASK_WANTED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SWAT_RESPONSE_DRAGGED_OUT_CAR</Name>
			<Event>EVENT_DRAGGED_OUT_CAR</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_SWAT_TASK_WANTED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SWAT_RESPONSE_PED_ENTERED_MY_VEHICLE</Name>
			<Event>EVENT_PED_ENTERED_MY_VEHICLE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_SWAT_TASK_WANTED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_VEHICLE_DAMAGE_WEAPON</Name>
			<Event>EVENT_VEHICLE_DAMAGE_WEAPON</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_COMBAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_VEHICLE_DAMAGE_WEAPON</Name>
			<Event>EVENT_VEHICLE_DAMAGE_WEAPON</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_VEHICLE_ON_FIRE</Name>
			<Event>EVENT_VEHICLE_ON_FIRE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_LEAVE_CAR_AND_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_GUN_AIMED_AT</Name>
			<Event>EVENT_GUN_AIMED_AT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HANDS_UP</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_GUN_AIMED_AT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_NON_VIOLENT_WEAPON_AIMED_AT</Name>
			<Event>EVENT_SHOCKING_NON_VIOLENT_WEAPON_AIMED_AT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>TURN_TO_FACE_RESPONSE_SHOCKING_NON_VIOLENT_WEAPON_AIMED_AT</Name>
			<Event>EVENT_SHOCKING_NON_VIOLENT_WEAPON_AIMED_AT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_GUN_AIMED_AT</Name>
			<Event>EVENT_GUN_AIMED_AT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Name>
			<Event>EVENT_SHOT_FIRED_BULLET_IMPACT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli InvalidIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FIREMAN_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Name>
			<Event>EVENT_SHOT_FIRED_BULLET_IMPACT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>MEDIC_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Name>
			<Event>EVENT_SHOT_FIRED_BULLET_IMPACT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FRIENDLY_RESPONSE_GUN_AIMED_AT</Name>
			<Event>EVENT_FRIENDLY_AIMED_AT</Event>
			<Decision>
        <Item>
          <TaskRef>RESPONSE_TASK_FRIENDLY_AIMED_AT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
        </Item>
			<Item>
				<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
				<Chance_SourcePlayer value="1.000000"/>
				<Chance_SourceFriend value="1.000000"/>
				<Chance_SourceThreat value="1.000000"/>
				<Chance_SourceOther value="1.000000"/>
				<DistanceMinSq value="-1.000000"/>
				<DistanceMaxSq value="-1.000000"/>
				<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
			</Item>
			</Decision>
		</Item> 
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_FRIENDLY_AIMED_AT</Name>
			<Event>EVENT_FRIENDLY_AIMED_AT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>		
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOUT_TARGET_POSITION</Name>
			<Event>EVENT_SHOUT_TARGET_POSITION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_COMBAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_DAMAGE</Name>
			<Event>EVENT_DAMAGE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_COMBAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_DAMAGE</Name>
			<Event>EVENT_DAMAGE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOT_FIRED_WHIZZED_BY</Name>
			<Event>EVENT_SHOT_FIRED_WHIZZED_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli InvalidIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FIREMAN_RESPONSE_SHOT_FIRED_WHIZZED_BY</Name>
			<Event>EVENT_SHOT_FIRED_WHIZZED_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>MEDIC_RESPONSE_SHOT_FIRED_WHIZZED_BY</Name>
			<Event>EVENT_SHOT_FIRED_WHIZZED_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOT_FIRED_WHIZZED_BY</Name>
			<Event>EVENT_SHOT_FIRED_WHIZZED_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
    <Item type="CEventDecisionMakerResponse">
			<Name>FRIENDLY_RESPONSE_TASK_NEAR_MISS</Name>
			<Event>EVENT_FRIENDLY_FIRE_NEAR_MISS</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FRIENDLY_NEAR_MISS</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_FRIENDLY_FIRE_NEAR_MISS</Name>
			<Event>EVENT_FRIENDLY_FIRE_NEAR_MISS</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_MELEE</Name>
			<Event>EVENT_MELEE_ACTION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_MELEE_ACTION</Name>
			<Event>EVENT_MELEE_ACTION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_SHOCKING_MELEE_ACTION</Name>
			<Event>EVENT_SHOCKING_SEEN_MELEE_ACTION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="4.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_POTENTIAL_WALK_INTO_FIRE</Name>
			<Event>EVENT_POTENTIAL_WALK_INTO_FIRE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_WALK_ROUND_FIRE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_EVENT_DRAGGED_OUT_CAR</Name>
			<Event>EVENT_DRAGGED_OUT_CAR</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_EVENT_PED_ENTERED_MY_VEHICLE</Name>
			<Event>EVENT_PED_ENTERED_MY_VEHICLE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_LEAVE_CAR_AND_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar</EventResponseDecisionFlags>					
				</Item>
			</Decision>
		</Item>	
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_POTENTIAL_WALK_INTO_VEHICLE</Name>
			<Event>EVENT_POTENTIAL_WALK_INTO_VEHICLE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_WALK_ROUND_ENTITY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>					
				</Item>
			</Decision>
		</Item>		
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_POTENTIAL_GET_RUN_OVER</Name>
			<Event>EVENT_POTENTIAL_GET_RUN_OVER</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_EVASIVE_STEP</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>					
				</Item>
        <Item>
          <TaskRef>RESPONSE_TASK_FLEE</TaskRef>
          <Chance_SourcePlayer value="1.000000"/>
          <Chance_SourceFriend value="1.000000"/>
          <Chance_SourceThreat value="1.000000"/>
          <Chance_SourceOther value="1.000000"/>
          <DistanceMinSq value="-1.000000"/>
          <DistanceMaxSq value="-1.000000"/>
          <EventResponseDecisionFlags>ValidOnFoot ValidOnlyInComplexScenario</EventResponseDecisionFlags>
        </Item>
			</Decision>
		</Item>		
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_OBJECT_COLLISION</Name>
			<Event>EVENT_OBJECT_COLLISION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_WALK_ROUND_ENTITY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>					
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_CAR_CHASE</Name>
			<Event>EVENT_SHOCKING_CAR_CHASE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="1225.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="1225.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="1225.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_CAR_CRASH</Name>
			<Event>EVENT_SHOCKING_CAR_CRASH</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="49.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.00000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="49.00000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.00000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_CAR_CRASH</Name>
			<Event>EVENT_SHOCKING_CAR_CRASH</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.00000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.00000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="25.00000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.00000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_SHOCKING_CAR_CRASH</Name>
			<Event>EVENT_SHOCKING_CAR_CRASH</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
    <Item type="CEventDecisionMakerResponse">
      <Name>DEFAULT_RESPONSE_SHOCKING_BICYCLE_CRASH</Name>
      <Event>EVENT_SHOCKING_BICYCLE_CRASH</Event>
      <Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.00000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
      </Decision>
    </Item>
    <Item type="CEventDecisionMakerResponse">
      <Name>GANG_RESPONSE_SHOCKING_BICYCLE_CRASH</Name>
      <Event>EVENT_SHOCKING_BICYCLE_CRASH</Event>
      <Decision>
      	<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.00000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="25.00000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.00000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
      </Decision>
    </Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_CAR_ON_CAR</Name>
			<Event>EVENT_SHOCKING_CAR_ON_CAR</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>					
				</Item>
			</Decision>
		</Item>		
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_CAR_PILE_UP</Name>
			<Event>EVENT_SHOCKING_CAR_PILE_UP</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="100.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="100.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_CAR_PILE_UP</Name>
			<Event>EVENT_SHOCKING_CAR_PILE_UP</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="100.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="100.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="100.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_DEAD_BODY</Name>
			<Event>EVENT_SHOCKING_DEAD_BODY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_GOTO</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk InvalidWhenAfraid NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_AGGRESSIVE_RUBBERNECK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>OFFDUTY_EMT_RESPONSE_DEAD_BODY</Name>
			<Event>EVENT_SHOCKING_DEAD_BODY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_PED_KILLED</Name>
			<Event>EVENT_SHOCKING_SEEN_PED_KILLED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.00000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnlyIfFriendlyWithTarget ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.00000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="25.00000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk InvalidIfFriendlyWithTarget ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.00000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk InvalidIfFriendlyWithTarget ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COP_RESPONSE_SHOCKING_DEAD_BODY</Name>
			<Event>EVENT_SHOCKING_DEAD_BODY</Event>
			<Cooldown>
				<Time value="30.0" />
				<MaxDistance value="100.0" />
			</Cooldown>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_DEAD_BODY</Name>
			<Event>EVENT_SHOCKING_DEAD_BODY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000" />
					<Chance_SourceFriend value="1.000000" />
					<Chance_SourceThreat value="1.000000" />
					<Chance_SourceOther value="1.000000" />
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_SHOCKING_DEAD_BODY</Name>
			<Event>EVENT_SHOCKING_DEAD_BODY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_DRIVING_ON_PAVEMENT</Name>
			<Event>EVENT_SHOCKING_DRIVING_ON_PAVEMENT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk</EventResponseDecisionFlags>
				</Item>
 				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="25.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_DRIVING_ON_PAVEMENT</Name>
			<Event>EVENT_SHOCKING_DRIVING_ON_PAVEMENT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_BICYCLE_ON_PAVEMENT</Name>
			<Event>EVENT_SHOCKING_BICYCLE_ON_PAVEMENT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidWhenAfraid ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_BICYCLE_ON_PAVEMENT</Name>
			<Event>EVENT_SHOCKING_BICYCLE_ON_PAVEMENT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_EXPLOSION</Name>
			<Event>EVENT_SHOCKING_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_SHOCKING_EXPLOSION</Name>
			<Event>EVENT_SHOCKING_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_STUDIO_BOMB</Name>
			<Event>EVENT_SHOCKING_STUDIO_BOMB</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidOnlyInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COP_RESPONSE_STUDIO_BOMB</Name>
			<Event>EVENT_SHOCKING_STUDIO_BOMB</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_WANTED</Name>
			<Event>EVENT_ACQUAINTANCE_PED_WANTED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_COMBAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_EXPLOSION</Name>
			<Event>EVENT_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_STUDIO_BOMB</Name>
			<Event>EVENT_SHOCKING_STUDIO_BOMB</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_SHOCKING_INJURED_PED</Name>
			<Event>EVENT_SHOCKING_INJURED_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSource InvalidIfSourceEntityIsOtherEntity InvalidIfTargetDoesNotInfluenceWanted</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSource ValidOnlyIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.500000"/>
					<DistanceMinSq value="25.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSource</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli ValidOnlyIfSource</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli ValidOnlyIfSourceIsInvalid</EventResponseDecisionFlags>
				</Item>
 			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_SHOCKING_PED_RUN_OVER</Name>
			<Event>EVENT_SHOCKING_PED_RUN_OVER</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_SHOCKING_DEAD_BODY</Name>
			<Event>EVENT_SHOCKING_DEAD_BODY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_CRIME_CRY_FOR_HELP</Name>
			<Event>EVENT_CRIME_CRY_FOR_HELP</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_COMBAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_SHOCKING_CAR_CRASH</Name>
			<Event>EVENT_SHOCKING_CAR_CRASH</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidInCar</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="25.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COP_RESPONSE_CAR_CRASH</Name>
			<Event>EVENT_SHOCKING_CAR_CRASH</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_SHOCKING_BICYCLE_CRASH</Name>
			<Event>EVENT_SHOCKING_BICYCLE_CRASH</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidInCar</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="25.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_SHOCKING_MAD_DRIVER</Name>
			<Event>EVENT_SHOCKING_MAD_DRIVER</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.000000"/>
					<EventResponseDecisionFlags>ValidInCar</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="100.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_SHOCKING_MAD_DRIVER_EXTREME</Name>
			<Event>EVENT_SHOCKING_MAD_DRIVER_EXTREME</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.000000"/>
					<EventResponseDecisionFlags>ValidInCar</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="100.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_SHOCKING_MAD_DRIVER_BICYCLE</Name>
			<Event>EVENT_SHOCKING_MAD_DRIVER_BICYCLE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_SHOCKING_PED_KILLED</Name>
			<Event>EVENT_SHOCKING_SEEN_PED_KILLED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidIfTargetDoesNotInfluenceWanted</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>SECURITY_RESPONSE_SHOCKING_SEEN_MELEE_ACTION</Name>
			<Event>EVENT_SHOCKING_SEEN_MELEE_ACTION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot </EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COP_RESPONSE_SEEN_PED_KILLED</Name>
			<Event>EVENT_SHOCKING_SEEN_PED_KILLED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidIfTargetDoesNotInfluenceWanted</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_PED_KILLED</Name>
			<Event>EVENT_SHOCKING_SEEN_PED_KILLED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_COMBAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidIfFriendlyWithTarget InvalidIfMissionPedInMP</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>InvalidIfFriendlyWithTarget InvalidIfMissionPedInMP</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_COMBAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle InvalidIfFriendlyWithTarget ValidOnlyIfMissionPedInMP</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_SHOCKING_SEEN_PED_KILLED</Name>
			<Event>EVENT_SHOCKING_SEEN_PED_KILLED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="100.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom InvalidIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="100.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom InvalidIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_FIRE</Name>
			<Event>EVENT_SHOCKING_FIRE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_GUN_FIGHT</Name>
			<Event>EVENT_SHOCKING_GUN_FIGHT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_GUN_FIGHT</Name>
			<Event>EVENT_SHOCKING_GUN_FIGHT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_GUNSHOT_FIRED</Name>
			<Event>EVENT_SHOCKING_GUNSHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FRIENDLY_NEAR_MISS</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>				
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_GUNSHOT_FIRED</Name>
			<Event>EVENT_SHOCKING_GUNSHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_HELICOPTER_OVERHEAD</Name>
			<Event>EVENT_SHOCKING_HELICOPTER_OVERHEAD</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_HELICOPTER_OVERHEAD</Name>
			<Event>EVENT_SHOCKING_HELICOPTER_OVERHEAD</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
    <Item type="CEventDecisionMakerResponse">
      <Name>DEFAULT_RESPONSE_SHOCKING_PARACHUTER_OVERHEAD</Name>
      <Event>EVENT_SHOCKING_PARACHUTER_OVERHEAD</Event>
      <Decision>
        <Item>
          <TaskRef>RESPONSE_TASK_SHOCKING_EVENT_WATCH</TaskRef>
          <Chance_SourcePlayer value="1.000000"/>
          <Chance_SourceFriend value="1.000000"/>
          <Chance_SourceThreat value="1.000000"/>
          <Chance_SourceOther value="1.000000"/>
          <DistanceMinSq value="-1.000000"/>
          <DistanceMaxSq value="-1.000000"/>
          <EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk InvalidForStationaryPeds</EventResponseDecisionFlags>
        </Item>
        <Item>
          <TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
          <Chance_SourcePlayer value="1.000000"/>
          <Chance_SourceFriend value="1.000000"/>
          <Chance_SourceThreat value="1.000000"/>
          <Chance_SourceOther value="1.000000"/>
          <DistanceMinSq value="-1.000000"/>
          <DistanceMaxSq value="-1.000000"/>
          <EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk InvalidForStationaryPeds</EventResponseDecisionFlags>
        </Item>
        <Item>
          <TaskRef>RESPONSE_TASK_FLEE</TaskRef>
          <Chance_SourcePlayer value="1.000000"/>
          <Chance_SourceFriend value="1.000000"/>
          <Chance_SourceThreat value="1.000000"/>
          <Chance_SourceOther value="1.000000"/>
          <DistanceMinSq value="-1.000000"/>
          <DistanceMaxSq value="-1.000000"/>
          <EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
        </Item>
        <Item>
          <TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
          <Chance_SourcePlayer value="1.000000"/>
          <Chance_SourceFriend value="1.000000"/>
          <Chance_SourceThreat value="1.000000"/>
          <Chance_SourceOther value="1.000000"/>
          <DistanceMinSq value="-1.000000"/>
          <DistanceMaxSq value="-1.000000"/>
          <EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
        </Item>
        <Item>
          <TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
          <Chance_SourcePlayer value="1.000000"/>
          <Chance_SourceFriend value="1.000000"/>
          <Chance_SourceThreat value="1.000000"/>
          <Chance_SourceOther value="1.000000"/>
          <DistanceMinSq value="-1.000000"/>
          <DistanceMaxSq value="-1.000000"/>
          <EventResponseDecisionFlags>ValidOnFoot ValidOnlyForStationaryPeds</EventResponseDecisionFlags>
        </Item>
      </Decision>
    </Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_HORN_SOUNDED</Name>
			<Event>EVENT_SHOCKING_HORN_SOUNDED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_ENGINE_REVVED</Name>
			<Event>EVENT_SHOCKING_ENGINE_REVVED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="49.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="49.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="49.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="49.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_SIREN</Name>
			<Event>EVENT_SHOCKING_SIREN</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_CAR_ALARM</Name>
			<Event>EVENT_SHOCKING_CAR_ALARM</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_PROPERTY_DAMAGE</Name>
			<Event>EVENT_SHOCKING_PROPERTY_DAMAGE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="144.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
          			<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="144.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle ValidInCar</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="144.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_PROPERTY_DAMAGE</Name>
			<Event>EVENT_SHOCKING_PROPERTY_DAMAGE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="144.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="144.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle ValidInCar</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="144.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_DANGEROUS_ANIMAL</Name>
			<Event>EVENT_SHOCKING_DANGEROUS_ANIMAL</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="900.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_IN_DANGEROUS_VEHICLE</Name>
			<Event>EVENT_SHOCKING_IN_DANGEROUS_VEHICLE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FLEE_RESPONSE_HORN</Name>
			<Event>EVENT_SHOCKING_HORN_SOUNDED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_INJURED_PED</Name>
			<Event>EVENT_SHOCKING_INJURED_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfFriendlyWithTarget ValidOnlyIfSource InvalidIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.00000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnlyIfFriendlyWithTarget ValidOnlyIfSource</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidOnlyIfSource InvalidIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsInvalid</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnlyIfSourceIsInvalid</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="25.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle ValidOnFoot ValidOnlyIfSourceIsInvalid</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="25.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidIfFriendlyWithTarget ValidOnlyIfSource</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="25.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnlyIfSource InvalidIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_AGGRESSIVE_RUBBERNECK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar InvalidIfFriendlyWithTarget ValidOnlyIfSource</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSource ValidOnlyIfFriendlyWithTarget ValidOnlyIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidOnlyIfSource ValidOnlyIfFriendlyWithTarget ValidOnlyIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSource ValidOnlyIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle ValidOnlyIfSource ValidOnlyIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="25.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle ValidOnlyIfSource ValidOnlyIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>MEDIC_RESPONSE_INJURED_PED</Name>
			<Event>EVENT_SHOCKING_INJURED_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidOnlyIfSource ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COP_RESPONSE_SHOCKING_INJURED_PED</Name>
			<Event>EVENT_SHOCKING_INJURED_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourcePed InvalidIfSourceEntityIsOtherEntity InvalidIfTargetDoesNotInfluenceWanted</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSource InvalidIfSourcePed InvalidIfSourceEntityIsOtherEntity InvalidIfTargetDoesNotInfluenceWanted</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidOnlyIfSource InvalidIfSourcePed InvalidIfSourceEntityIsOtherEntity InvalidIfTargetDoesNotInfluenceWanted</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSource ValidOnlyIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidOnlyIfSource ValidOnlyIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnlyIfSource</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInHeli ValidOnlyIfSource</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnlyIfSourceIsInvalid</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_INJURED_PED</Name>
			<Event>EVENT_SHOCKING_INJURED_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidOnlyIfSource ValidOnlyIfFriendlyWithTarget InvalidIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidOnlyIfSource InvalidIfFriendlyWithTarget InvalidIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000" />
					<Chance_SourceFriend value="0.000000" />
					<Chance_SourceThreat value="1.000000" />
					<Chance_SourceOther value="1.000000" />
					<DistanceMinSq value="25.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidOnlyIfSource InvalidIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSource ValidOnlyIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle ValidOnlyIfSource ValidOnlyIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000" />
					<Chance_SourceFriend value="0.000000" />
					<Chance_SourceThreat value="1.000000" />
					<Chance_SourceOther value="1.000000" />
					<DistanceMinSq value="25.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidOnlyIfSource ValidOnlyIfSourceEntityIsOtherEntity</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli ValidOnlyIfSource</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli ValidOnlyIfSourceIsInvalid</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_SHOCKING_INJURED_PED</Name>
			<Event>EVENT_SHOCKING_INJURED_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="100.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom InvalidIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="100.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom InvalidIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_MAD_DRIVER</Name>
			<Event>EVENT_SHOCKING_MAD_DRIVER</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_MAD_DRIVER_EXTREME</Name>
			<Event>EVENT_SHOCKING_MAD_DRIVER_EXTREME</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_MAD_DRIVER_BICYCLE</Name>
			<Event>EVENT_SHOCKING_MAD_DRIVER_BICYCLE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidInHeli ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_PED_RUN_OVER</Name>
			<Event>EVENT_SHOCKING_PED_RUN_OVER</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_AGGRESSIVE_RUBBERNECK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_PED_RUN_OVER</Name>
			<Event>EVENT_SHOCKING_PED_RUN_OVER</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COP_RESPONSE_SHOCKING_PED_RUN_OVER</Name>
			<Event>EVENT_SHOCKING_PED_RUN_OVER</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_POLICE_INVESTIGATE</TaskRef>
					<Chance_SourcePlayer value="0.500000"/>
					<Chance_SourceFriend value="0.500000"/>
					<Chance_SourceThreat value="0.500000"/>
					<Chance_SourceOther value="0.500000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_PED_SHOT</Name>
			<Event>EVENT_SHOCKING_PED_SHOT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="400.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="400.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_THREAT_RESPONSE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="400.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_PED_SHOT</Name>
			<Event>EVENT_SHOCKING_PED_SHOT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="400.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_THREAT_RESPONSE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="400.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="400.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_PLANE_FLY_BY</Name>
			<Event>EVENT_SHOCKING_PLANE_FLY_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_PLANE_FLY_BY</Name>
			<Event>EVENT_SHOCKING_PLANE_FLY_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_RUNNING_PED</Name>
			<Event>EVENT_SHOCKING_RUNNING_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_RUNNING_STAMPEDE</Name>
			<Event>EVENT_SHOCKING_RUNNING_STAMPEDE</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_SEEN_CAR_STOLEN</Name>
			<Event>EVENT_SHOCKING_SEEN_CAR_STOLEN</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>  
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk ValidOnlyIfNoScenarioRadius InvalidIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk ValidOnlyIfOutsideScenarioRadius InvalidIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle ValidOnlyIfOutsideScenarioRadius InvalidIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_DEFER_TO_SCENARIO_POINT_FLAGS</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk ValidOnlyIfInsideScenarioRadius ValidOnBicycle InvalidIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk InvalidIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli InvalidIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_SEEN_CAR_STOLEN</Name>
			<Event>EVENT_SHOCKING_SEEN_CAR_STOLEN</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk ValidOnlyIfNoScenarioRadius InvalidIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk ValidOnlyIfOutsideScenarioRadius InvalidIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle ValidOnlyIfOutsideScenarioRadius InvalidIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_DEFER_TO_SCENARIO_POINT_FLAGS</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
				<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk ValidOnlyIfInsideScenarioRadius ValidOnBicycle InvalidIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk InvalidIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli InvalidIfOtherVehicleIsYourVehicle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>MEDIC_RESPONSE_SHOCKING_SEEN_CAR_STOLEN</Name>
			<Event>EVENT_SHOCKING_SEEN_CAR_STOLEN</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FIRE_RESPONSE_SHOCKING_SEEN_CAR_STOLEN</Name>
			<Event>EVENT_SHOCKING_SEEN_CAR_STOLEN</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_SEEN_GANG_FIGHT</Name>
			<Event>EVENT_SHOCKING_SEEN_GANG_FIGHT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_GOTO</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk InvalidWhenAfraid NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_SEEN_MELEE_ACTION</Name>
			<Event>EVENT_SHOCKING_SEEN_MELEE_ACTION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="16.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.00000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="225.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="16.000000"/>
					<DistanceMaxSq value="225.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>AGITATED_RESPONSE_SHOCKING_PED_KNOCKED_INTO</Name>
			<Event>EVENT_SHOCKING_PED_KNOCKED_INTO_BY_PLAYER</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="16.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.00000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="225.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="16.000000"/>
					<DistanceMaxSq value="225.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="16.00000"/>
					<EventResponseDecisionFlags>ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_PED_KNOCKED_INTO</Name>
			<Event>EVENT_SHOCKING_PED_KNOCKED_INTO_BY_PLAYER</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="16.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.00000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="225.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="16.000000"/>
					<DistanceMaxSq value="225.00000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.00000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="16.00000"/>
					<EventResponseDecisionFlags>ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_SEEN_MELEE_ACTION</Name>
			<Event>EVENT_SHOCKING_SEEN_MELEE_ACTION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="16.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="16.000000"/>
					<DistanceMaxSq value="225.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="16.000000"/>
					<DistanceMaxSq value="225.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_SHOCKING_SEEN_MELEE_ACTION</Name>
			<Event>EVENT_SHOCKING_SEEN_MELEE_ACTION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="9.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom ValidOnlyIfTargetIsInvalidOrFriendly</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="9.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInHeli ValidOnBicycle ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom ValidOnlyIfTargetIsInvalidOrFriendly</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="9.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom ValidOnlyIfTargetIsValidAndNotFriendly</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="9.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom ValidOnlyIfTargetIsValidAndNotFriendly</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="9.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="9.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="9.000000"/>
					<EventResponseDecisionFlags>ValidInHeli ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom ValidOnlyIfTargetIsValidAndNotFriendly</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="9.000000"/>
					<EventResponseDecisionFlags>ValidInHeli ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom ValidOnlyIfTargetIsValidAndNotFriendly</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="9.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInHeli ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="9.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInHeli ValidOnlyIfSourceIsAnAnimal ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>COP_RESPONSE_SHOCKING_SEEN_MELEE_ACTION</Name>
			<Event>EVENT_SHOCKING_SEEN_MELEE_ACTION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="25.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="25.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_SEEN_CONFRONTATION</Name>
			<Event>EVENT_SHOCKING_SEEN_CONFRONTATION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk InvalidInAnInterior</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInAnInterior</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_SEEN_CONFRONTATION</Name>
			<Event>EVENT_SHOCKING_SEEN_CONFRONTATION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_TURN_TO_FACE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidInCrosswalk InvalidInAnInterior</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInCrosswalk</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyInAnInterior</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnBicycle ValidInCar ValidInHeli</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_SEEN_NICE_CAR</Name>
			<Event>EVENT_SHOCKING_SEEN_NICE_CAR</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_NICE_CAR</TaskRef>
					<Chance_SourcePlayer value="0.400000"/>
					<Chance_SourceFriend value="0.500000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.400000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_SEEN_VEHICLE_TOWED</Name>
			<Event>EVENT_SHOCKING_SEEN_VEHICLE_TOWED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_LEAVE_CAR_AND_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_SEEN_WEAPON_THREAT</Name>
			<Event>EVENT_SHOCKING_SEEN_WEAPON_THREAT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags></EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_STOP_AND_STARE</TaskRef>
					<Chance_SourcePlayer value="0.500000"/>
					<Chance_SourceFriend value="0.500000"/>
					<Chance_SourceThreat value="0.500000"/>
					<Chance_SourceOther value="0.500000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidOnlyForDrivers ValidForTargetOutsideVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.500000"/>
					<Chance_SourceFriend value="0.500000"/>
					<Chance_SourceThreat value="0.500000"/>
					<Chance_SourceOther value="0.500000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidOnlyForDrivers ValidForTargetOutsideVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnlyForPassengersWithNoDriver</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnlyForPassengersWithDriver ValidForTargetInsideVehicle</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidInCar ValidOnlyForPassengersWithDriver ValidForTargetOutsideVehicle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_SEEN_WEIRD_PED</Name>
			<Event>EVENT_SHOCKING_SEEN_WEIRD_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_SEEN_INSULT</Name>
			<Event>EVENT_SHOCKING_SEEN_INSULT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_VISIBLE_WEAPON</Name>
			<Event>EVENT_SHOCKING_VISIBLE_WEAPON</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_VISIBLE_WEAPON</Name>
			<Event>EVENT_SHOCKING_VISIBLE_WEAPON</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="1.000000" />
					<Chance_SourceFriend value="1.000000" />
					<Chance_SourceThreat value="1.000000" />
					<Chance_SourceOther value="1.000000" />
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GANG_RESPONSE_SHOCKING_SEEN_WEAPON_THREAT</Name>
			<Event>EVENT_SHOCKING_SEEN_WEAPON_THREAT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000" />
					<Chance_SourceFriend value="0.000000" />
					<Chance_SourceThreat value="1.000000" />
					<Chance_SourceOther value="1.000000" />
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_HEAD_TRACK</TaskRef>
					<Chance_SourcePlayer value="0.000000" />
					<Chance_SourceFriend value="1.000000" />
					<Chance_SourceThreat value="0.000000" />
					<Chance_SourceOther value="0.000000" />
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_SHOCKING_SEEN_WEAPON_THREAT</Name>
			<Event>EVENT_SHOCKING_SEEN_WEAPON_THREAT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_AGITATED</Name>
			<Event>EVENT_AGITATED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidInCar ValidOnBicycle</EventResponseDecisionFlags>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_REQUEST_HELP_WITH_CONFRONTATION</Name>
			<Event>EVENT_REQUEST_HELP_WITH_CONFRONTATION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_AGITATED</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle</EventResponseDecisionFlags>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>DEFAULT_RESPONSE_SHOCKING_POTENTIAL_BLAST</Name>
			<Event>EVENT_SHOCKING_POTENTIAL_BLAST</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_THREAT_RESPONSE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags></EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
    <Item type="CEventDecisionMakerResponse">
       <Name>DEFAULT_RESPONSE_PLAYER_DEATH</Name>
       <Event>EVENT_PLAYER_DEATH</Event>
       <Decision>
        <Item>
          <TaskRef>RESPONSE_TASK_PLAYER_DEATH</TaskRef>
          <Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnBicycle ValidInCar</EventResponseDecisionFlags>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
        </Item>
       </Decision>
    </Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>CAT_RESPONSE_ENCROACHMENT</Name>
			<Event>EVENT_ENCROACHING_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="1.500000"/>
					<EventResponseDecisionFlags>ValidOnFoot InvalidIfSourceIsAnAnimal</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="64.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsAnAnimal</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GULL_RESPONSE_SHOT_FIRED_WHIZZED_BY</Name>
			<Event>EVENT_SHOT_FIRED_WHIZZED_BY</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>GULL_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Name>
			<Event>EVENT_SHOT_FIRED_BULLET_IMPACT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_FLY_AWAY</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FISH_RESPONSE_EXPLOSION</Name>
			<Event>EVENT_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SCENARIO_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
					<EventResponseFleeFlags>DisableCover</EventResponseFleeFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FISH_RESPONSE_GUNSHOT</Name>
			<Event>EVENT_SHOT_FIRED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SCENARIO_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
					<EventResponseFleeFlags>DisableCover</EventResponseFleeFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FISH_RESPONSE_SHOCKING_EXPLOSION</Name>
			<Event>EVENT_SHOCKING_EXPLOSION</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SCENARIO_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="0.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
					<EventResponseFleeFlags>DisableCover</EventResponseFleeFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>RABBIT_RESPONSE_FLEE</Name>
			<Event>EVENT_ENCROACHING_PED</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_SCENARIO_FLEE</TaskRef>
					<Chance_SourcePlayer value="1.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="1.000000"/>
					<Chance_SourceOther value="1.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot NotValidInComplexScenario</EventResponseDecisionFlags>
					<EventResponseFleeFlags>DisableCover</EventResponseFleeFlags>
				</Item>
			</Decision>
		</Item>
		<Item type="CEventDecisionMakerResponse">
			<Name>FAMILY_RESPONSE_SHOCKING_PED_SHOT</Name>
			<Event>EVENT_SHOCKING_PED_SHOT</Event>
			<Decision>
				<Item>
					<TaskRef>RESPONSE_TASK_THREAT</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="100.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="100.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom ValidOnlyIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_SHOCKING_EVENT_HURRY_AWAY</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnFoot ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom InvalidIfFriendlyWithTarget</EventResponseDecisionFlags>
				</Item>
				<Item>
					<TaskRef>RESPONSE_TASK_FLEE</TaskRef>
					<Chance_SourcePlayer value="0.000000"/>
					<Chance_SourceFriend value="1.000000"/>
					<Chance_SourceThreat value="0.000000"/>
					<Chance_SourceOther value="0.000000"/>
					<DistanceMinSq value="-1.000000"/>
					<DistanceMaxSq value="-1.000000"/>
					<EventResponseDecisionFlags>ValidOnlyIfSourceIsPlayer ValidOnlyIfRandom</EventResponseDecisionFlags>
				</Item>
			</Decision>
		</Item>
	</eventDecisionMakerResponseData>
	<eventDecisionMaker>
		<Item type="CEventDataDecisionMaker">
			<Name>PLAYER</Name>
			<DecisionMakerParentRef/>
			<EventResponse/>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>COP</Name>
			<DecisionMakerParentRef>BASE</DecisionMakerParentRef>
			<EventResponse>
				<Item>COP_RESPONSE_WANTED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_EXPLOSION</Item>
				<Item>COP_RESPONSE_STUDIO_BOMB</Item>
				<Item>COP_RESPONSE_SHOCKING_INJURED_PED</Item>
 				<Item>COP_RESPONSE_SHOCKING_PED_RUN_OVER</Item>
				<Item>COP_RESPONSE_SHOCKING_DEAD_BODY</Item>
				<Item>COP_RESPONSE_CRIME_CRY_FOR_HELP</Item>
				<Item>COP_RESPONSE_SEEN_PED_KILLED</Item>
				<Item>COP_RESPONSE_SHOCKING_SEEN_MELEE_ACTION</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_WEAPON_THREAT</Item>
				<Item>INVESTIGATE_RESPONSE_MUGGING</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SIREN</Item>
				<Item>COP_RESPONSE_CAR_CRASH</Item>
				<Item>COP_RESPONSE_SHOCKING_CAR_ALARM</Item>
				<Item>AGITATED_RESPONSE_SHOCKING_PED_KNOCKED_INTO</Item>
				<Item>COP_RESPONSE_PROPERTY_DAMAGE</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>FIREMAN</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>DEFAULT_RESPONSE_DAMAGE</Item>
				<Item>FIREMAN_RESPONSE_SHOT_FIRED</Item>
				<Item>FIREMAN_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>FIREMAN_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
				<Item>DEFAULT_RESPONSE_GUN_AIMED_AT</Item>
				<Item>DEFAULT_RESPONSE_HATE</Item>
				<Item>DEFAULT_RESPONSE_VEHICLE_ON_FIRE</Item>
				<Item>DEFAULT_RESPONSE_POTENTIAL_WALK_INTO_FIRE</Item>
				<Item>DEFAULT_RESPONSE_MELEE</Item>
				<Item>DEFAULT_RESPONSE_EVENT_DRAGGED_OUT_CAR</Item>
				<Item>DEFAULT_RESPONSE_EVENT_PED_ENTERED_MY_VEHICLE</Item>
				<Item>DEFAULT_RESPONSE_POTENTIAL_GET_RUN_OVER</Item>
				<Item>DEFAULT_RESPONSE_POTENTIAL_WALK_INTO_VEHICLE</Item>
				<Item>DEFAULT_RESPONSE_OBJECT_COLLISION</Item>
				<Item>DEFAULT_RESPONSE_AGITATED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_MELEE_ACTION</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_PED_KILLED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_WEAPON_THREAT</Item>
				<Item>DEFAULT_RESPONSE_MUGGING</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SIREN</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_CAR_ALARM</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_DANGEROUS_ANIMAL</Item>
				<Item>DEFAULT_RESPONSE_VEHICLE_DAMAGE_WEAPON</Item>
				<Item>FIRE_RESPONSE_SHOCKING_SEEN_CAR_STOLEN</Item>
			</EventResponse> 
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>MEDIC</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>DEFAULT_RESPONSE_DAMAGE</Item>
				<Item>MEDIC_RESPONSE_SHOT_FIRED</Item>
				<Item>MEDIC_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>MEDIC_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
				<Item>DEFAULT_RESPONSE_GUN_AIMED_AT</Item>
				<Item>DEFAULT_RESPONSE_HATE</Item>
				<Item>DEFAULT_RESPONSE_VEHICLE_ON_FIRE</Item>
				<Item>DEFAULT_RESPONSE_POTENTIAL_WALK_INTO_FIRE</Item>
				<Item>DEFAULT_RESPONSE_MELEE</Item>
				<Item>DEFAULT_RESPONSE_EVENT_DRAGGED_OUT_CAR</Item>
				<Item>DEFAULT_RESPONSE_EVENT_PED_ENTERED_MY_VEHICLE</Item>
				<Item>DEFAULT_RESPONSE_POTENTIAL_GET_RUN_OVER</Item>
				<Item>DEFAULT_RESPONSE_POTENTIAL_WALK_INTO_VEHICLE</Item>
				<Item>DEFAULT_RESPONSE_OBJECT_COLLISION</Item>
				<Item>DEFAULT_RESPONSE_AGITATED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_PED_KILLED</Item>
				<Item>MEDIC_RESPONSE_INJURED_PED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_WEAPON_THREAT</Item>
				<Item>DEFAULT_RESPONSE_MUGGING</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SIREN</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_CAR_ALARM</Item>
				<Item>MEDIC_RESPONSE_SHOCKING_SEEN_CAR_STOLEN</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_DANGEROUS_ANIMAL</Item>
				<Item>DEFAULT_RESPONSE_VEHICLE_DAMAGE_WEAPON</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>OFFDUTY_EMT</Name>
			<DecisionMakerParentRef>DEFAULT</DecisionMakerParentRef>
			<EventResponse>
				<Item>FIREMAN_RESPONSE_SHOT_FIRED</Item>
				<Item>FIREMAN_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>FIREMAN_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
				<Item>OFFDUTY_EMT_RESPONSE_DEAD_BODY</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>Security</Name>
			<DecisionMakerParentRef>BASE</DecisionMakerParentRef>
			<EventResponse>
				<Item>SECURITY_RESPONSE_WANTED</Item>
				<Item>SECURITY_RESPONSE_EXPLOSION</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_EXPLOSION</Item>
				<Item>SECURITY_RESPONSE_STUDIO_BOMB</Item>
				<Item>SECURITY_RESPONSE_SHOCKING_INJURED_PED</Item>
				<Item>SECURITY_RESPONSE_SHOCKING_PED_RUN_OVER</Item>
				<Item>SECURITY_RESPONSE_SHOCKING_DEAD_BODY</Item>
				<Item>SECURITY_RESPONSE_CRIME_CRY_FOR_HELP</Item>
				<Item>SECURITY_RESPONSE_SHOCKING_CAR_CRASH</Item>
				<Item>SECURITY_RESPONSE_SHOCKING_BICYCLE_CRASH</Item>
				<Item>SECURITY_RESPONSE_SHOCKING_MAD_DRIVER</Item>
				<Item>SECURITY_RESPONSE_SHOCKING_MAD_DRIVER_EXTREME</Item>
				<Item>SECURITY_RESPONSE_SHOCKING_MAD_DRIVER_BICYCLE</Item>
				<Item>SECURITY_RESPONSE_SHOCKING_PED_KILLED</Item>
				<Item>SECURITY_RESPONSE_SHOCKING_SEEN_MELEE_ACTION</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_WEAPON_THREAT</Item>
				<Item>INVESTIGATE_RESPONSE_MUGGING</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SIREN</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_CAR_ALARM</Item>
				<Item>AGITATED_RESPONSE_SHOCKING_PED_KNOCKED_INTO</Item>
				<Item>COP_RESPONSE_PROPERTY_DAMAGE</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>SWAT</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>SWAT_RESPONSE_WANTED</Item>
				<Item>SWAT_RESPONSE_DAMAGE</Item>
				<Item>SWAT_RESPONSE_SHOT_FIRED</Item>
				<Item>SWAT_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>SWAT_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
				<Item>SWAT_RESPONSE_GUN_AIMED_AT</Item>
				<Item>DEFAULT_RESPONSE_VEHICLE_ON_FIRE</Item>
				<Item>DEFAULT_RESPONSE_POTENTIAL_WALK_INTO_FIRE</Item>
				<Item>SWAT_RESPONSE_SHOUT_TARGET_POSITION</Item>
				<Item>SWAT_RESPONSE_MELEE</Item>
				<Item>SWAT_RESPONSE_VEHICLE_DAMAGE_WEAPON</Item>
				<Item>SWAT_RESPONSE_DRAGGED_OUT_CAR</Item>
				<Item>SWAT_RESPONSE_PED_ENTERED_MY_VEHICLE</Item>
				<Item>DEFAULT_RESPONSE_POTENTIAL_WALK_INTO_VEHICLE</Item>
				<Item>DEFAULT_RESPONSE_OBJECT_COLLISION</Item>
				<Item>COP_RESPONSE_CRIME_CRY_FOR_HELP</Item>
				<Item>COP_RESPONSE_SEEN_PED_KILLED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_WEAPON_THREAT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_DANGEROUS_ANIMAL</Item>
				<Item>INVESTIGATE_RESPONSE_MUGGING</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SIREN</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_CAR_ALARM</Item>
				<Item>AGITATED_RESPONSE_SHOCKING_PED_KNOCKED_INTO</Item>
				<Item>DEFAULT_RESPONSE_HATE</Item>
				<Item>COP_RESPONSE_PROPERTY_DAMAGE</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>EMPTY</Name>
			<DecisionMakerParentRef/>
			<EventResponse/>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>BASE</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>DEFAULT_RESPONSE_DAMAGE</Item>
				<Item>DEFAULT_RESPONSE_EXPLOSION</Item>
				<Item>DEFAULT_RESPONSE_SHOT_FIRED</Item>
				<Item>DEFAULT_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>DEFAULT_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
				<Item>DEFAULT_RESPONSE_GUN_AIMED_AT</Item>
				<Item>DEFAULT_RESPONSE_HATE</Item>
				<Item>DEFAULT_RESPONSE_WANTED</Item>
				<Item>DEFAULT_RESPONSE_VEHICLE_ON_FIRE</Item>
				<Item>DEFAULT_RESPONSE_POTENTIAL_WALK_INTO_FIRE</Item>
				<Item>DEFAULT_RESPONSE_SHOUT_TARGET_POSITION</Item>
				<Item>DEFAULT_RESPONSE_INJURED_CRY_FOR_HELP</Item>
				<Item>DEFAULT_RESPONSE_MELEE</Item>
				<Item>DEFAULT_RESPONSE_VEHICLE_DAMAGE_WEAPON</Item>
				<Item>DEFAULT_RESPONSE_EVENT_DRAGGED_OUT_CAR</Item>
				<Item>DEFAULT_RESPONSE_EVENT_PED_ENTERED_MY_VEHICLE</Item>
				<Item>DEFAULT_RESPONSE_POTENTIAL_GET_RUN_OVER</Item>
				<Item>DEFAULT_RESPONSE_POTENTIAL_WALK_INTO_VEHICLE</Item>
				<Item>DEFAULT_RESPONSE_OBJECT_COLLISION</Item>
				<Item>DEFAULT_RESPONSE_AGITATED</Item>
				<Item>DEFAULT_RESPONSE_REQUEST_HELP_WITH_CONFRONTATION</Item>
				<Item>FRIENDLY_RESPONSE_GUN_AIMED_AT</Item>
				<Item>FRIENDLY_RESPONSE_TASK_NEAR_MISS</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_DANGEROUS_ANIMAL</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SIREN</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_POTENTIAL_BLAST</Item>
        <Item>DEFAULT_RESPONSE_PLAYER_DEATH</Item>
        <Item>COMBAT_RESPONSE_FOOT_STEP_HEARD</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>DEFAULT</Name>
			<DecisionMakerParentRef>BASE</DecisionMakerParentRef>
			<EventResponse>
				<Item>DEFAULT_RESPONSE_SHOCKING_ENGINE_REVVED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_EXPLOSION</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_GUN_FIGHT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_PED_SHOT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_GUNSHOT_FIRED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_FIRE</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_CAR_CHASE</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_CAR_PILE_UP</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_CAR_CRASH</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_BICYCLE_CRASH</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_CAR_ON_CAR</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_MAD_DRIVER</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_MAD_DRIVER_EXTREME</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_MAD_DRIVER_BICYCLE</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_DRIVING_ON_PAVEMENT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_BICYCLE_ON_PAVEMENT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_INJURED_PED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_PED_RUN_OVER</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_GANG_FIGHT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_MELEE_ACTION</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_CONFRONTATION</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_INSULT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_NICE_CAR</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_VEHICLE_TOWED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_WEAPON_THREAT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_WEIRD_PED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_HELICOPTER_OVERHEAD</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_PARACHUTER_OVERHEAD</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_CAR_STOLEN</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_DEAD_BODY</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_PED_KILLED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_PLANE_FLY_BY</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_HORN_SOUNDED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_VISIBLE_WEAPON</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_RUNNING_PED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_RUNNING_STAMPEDE</Item>
				<Item>DEFAULT_RESPONSE_STUDIO_BOMB</Item>
				<Item>DEFAULT_RESPONSE_CRIME_CRY_FOR_HELP</Item>
				<Item>DEFAULT_RESPONSE_MUGGING</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_NON_VIOLENT_WEAPON_AIMED_AT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SIREN</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_IN_DANGEROUS_VEHICLE</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_CAR_ALARM</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_PED_KNOCKED_INTO</Item>
				<Item>DEFAULT_RESPONSE_PROPERTY_DAMAGE</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>GANG</Name>
			<DecisionMakerParentRef>BASE</DecisionMakerParentRef>
			<EventResponse>
				<Item>GANG_RESPONSE_SHOT_FIRED</Item>
				<Item>GANG_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>GANG_RESPONSE_GUN_AIMED_AT</Item>
				<Item>GANG_RESPONSE_SHOCKING_GUN_FIGHT</Item>
				<Item>GANG_RESPONSE_SHOCKING_GUNSHOT_FIRED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_EXPLOSION</Item>
				<Item>GANG_RESPONSE_SHOCKING_PED_SHOT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_FIRE</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_CAR_CHASE</Item>
				<Item>GANG_RESPONSE_SHOCKING_CAR_PILE_UP</Item>
				<Item>GANG_RESPONSE_SHOCKING_CAR_CRASH</Item>
				<Item>GANG_RESPONSE_SHOCKING_BICYCLE_CRASH</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_CAR_ON_CAR</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_MAD_DRIVER</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_MAD_DRIVER_EXTREME</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_MAD_DRIVER_BICYCLE</Item>
				<Item>GANG_RESPONSE_SHOCKING_DRIVING_ON_PAVEMENT</Item>
				<Item>GANG_RESPONSE_SHOCKING_BICYCLE_ON_PAVEMENT</Item>
				<Item>GANG_RESPONSE_SHOCKING_INJURED_PED</Item>
				<Item>GANG_RESPONSE_SHOCKING_PED_RUN_OVER</Item>
				<Item>GANG_RESPONSE_SHOCKING_SEEN_MELEE_ACTION</Item>
				<Item>GANG_RESPONSE_SHOCKING_SEEN_CONFRONTATION</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_INSULT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_NICE_CAR</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SEEN_VEHICLE_TOWED</Item>
				<Item>GANG_RESPONSE_SHOCKING_SEEN_WEAPON_THREAT</Item>
				<Item>GANG_RESPONSE_SHOCKING_HELICOPTER_OVERHEAD</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_PARACHUTER_OVERHEAD</Item>
				<Item>GANG_RESPONSE_SHOCKING_SEEN_CAR_STOLEN</Item>
				<Item>GANG_RESPONSE_SHOCKING_DEAD_BODY</Item>
				<Item>GANG_RESPONSE_SHOCKING_PLANE_FLY_BY</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_HORN_SOUNDED</Item>
				<Item>GANG_RESPONSE_SHOCKING_VISIBLE_WEAPON</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_RUNNING_PED</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_RUNNING_STAMPEDE</Item>
				<Item>GANG_RESPONSE_CRIME_CRY_FOR_HELP</Item>
				<Item>GANG_RESPONSE_SHOCKING_PED_KILLED</Item>
				<Item>TURN_TO_FACE_RESPONSE_MUGGING</Item>
				<Item>TURN_TO_FACE_RESPONSE_SHOCKING_NON_VIOLENT_WEAPON_AIMED_AT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_SIREN</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_CAR_ALARM</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_PED_KNOCKED_INTO</Item>
				<Item>GANG_RESPONSE_PROPERTY_DAMAGE</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>FAMILY</Name>
			<DecisionMakerParentRef>GANG</DecisionMakerParentRef>
			<EventResponse>
				<Item>FAMILY_RESPONSE_DAMAGE</Item>
				<Item>FAMILY_RESPONSE_EXPLOSION</Item>
				<Item>FAMILY_RESPONSE_FRIENDLY_AIMED_AT</Item>
				<Item>FAMILY_RESPONSE_FRIENDLY_FIRE_NEAR_MISS</Item>
				<Item>FAMILY_RESPONSE_MELEE_ACTION</Item>
				<Item>FAMILY_RESPONSE_SHOCKING_DEAD_BODY</Item>
				<Item>FAMILY_RESPONSE_SHOCKING_EXPLOSION</Item>
				<Item>FAMILY_RESPONSE_SHOCKING_INJURED_PED</Item>
				<Item>FAMILY_RESPONSE_SHOCKING_SEEN_MELEE_ACTION</Item>
				<Item>FAMILY_RESPONSE_SHOCKING_SEEN_PED_KILLED</Item>
				<Item>FAMILY_RESPONSE_SHOCKING_SEEN_WEAPON_THREAT</Item>
				<Item>FAMILY_RESPONSE_SHOCKING_PED_SHOT</Item>
				<Item>FAMILY_RESPONSE_SHOT_FIRED</Item>
				<Item>FAMILY_RESPONSE_VEHICLE_DAMAGE_WEAPON</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>GULL</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>GULL_RESPONSE_FLEE</Item>
				<Item>GULL_RESPONSE_EXPLOSION</Item>
				<Item>GULL_RESPONSE_GUNSHOT</Item>
				<Item>GULL_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>GULL_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>HEN</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>FLEE_RESPONSE_DAMAGE</Item>
				<Item>FLEE_RESPONSE_EXPLOSION</Item>
				<Item>FLEE_RESPONSE_SHOT_FIRED</Item>
				<Item>FLEE_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>FLEE_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
				<Item>HEN_RESPONSE_FLEE</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_DANGEROUS_ANIMAL</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>RAT</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>RAT_RESPONSE_FLEE</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>FISH</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>FISH_RESPONSE_FLEE</Item>
				<Item>FISH_RESPONSE_EXPLOSION</Item>
				<Item>FISH_RESPONSE_GUNSHOT</Item>
				<Item>FISH_RESPONSE_SHOCKING_EXPLOSION</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>Shark</Name>
			<EventResponse>
				<Item>SHARK_ATTACK_ENCROACHING</Item>
				<Item>SHARK_ATTACK_HATE</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>HORSE</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>HORSE_FLEE_RESPONSE_DAMAGE</Item>
				<Item>HORSE_FLEE_RESPONSE_EXPLOSION</Item>
				<Item>HORSE_FLEE_RESPONSE_SHOT_FIRED</Item>
				<Item>HORSE_FLEE_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>HORSE_FLEE_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
				<Item>HORSE_FLEE_RESPONSE_SHOCKING_MELEE_ACTION</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_DANGEROUS_ANIMAL</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>DomesticAnimal</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>EXHAUSTED_FLEE_RESPONSE_DAMAGE</Item>
				<Item>EXHAUSTED_FLEE_RESPONSE_EXPLOSION</Item>
				<Item>EXHAUSTED_FLEE_RESPONSE_SHOT_FIRED</Item>
				<Item>EXHAUSTED_FLEE_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>EXHAUSTED_FLEE_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
				<Item>EXHAUSTED_FLEE_RESPONSE_SHOCKING_MELEE_ACTION</Item>
				<Item>EXHAUSTED_FLEE_RESPONSE_HORN</Item>
				<Item>EXHAUSTED_FLEE_RESPONSE_CAR_CRASH</Item>
				<Item>EXHAUSTED_FLEE_RESPONSE_SEEN_PED_RUN_OVER</Item>
				<Item>EXHAUSTED_FLEE_RESPONSE_INJURED_PED</Item>
				<Item>EXHAUSTED_FLEE_RESPONSE_SHOCKING_EXPLOSION</Item>
				<Item>EXHAUSTED_FLEE_RESPONSE_HELICOPTER</Item>
				<Item>EXHAUSTED_FLEE_RESPONSE_PLANE</Item>
				<Item>WALK_AWAY_RESPONSE_ENCROACHMENT</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_DANGEROUS_ANIMAL</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>DOG</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>DEFAULT_RESPONSE_DAMAGE</Item>
				<Item>FLEE_RESPONSE_EXPLOSION</Item>
				<Item>DEFAULT_RESPONSE_SHOT_FIRED</Item>
				<Item>DEFAULT_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>DEFAULT_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
				<Item>DEFAULT_RESPONSE_HATE</Item>
				<Item>DEFAULT_RESPONSE_AGITATED</Item>
				<Item>DEFAULT_RESPONSE_SHOUT_TARGET_POSITION</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_DANGEROUS_ANIMAL</Item>
				<Item>DOG_RESPONSE_CAR_CRASH</Item>
				<Item>DOG_RESPONSE_INJURED_PED</Item>
				<Item>DOG_RESPONSE_SEEN_PED_KILLED</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>WildAnimal</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>FLEE_RESPONSE_DAMAGE</Item>
				<Item>FLEE_RESPONSE_CAR_CRASH</Item>
				<Item>FLEE_RESPONSE_EXPLOSION</Item>
				<Item>FLEE_RESPONSE_SHOT_FIRED</Item>
				<Item>FLEE_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>FLEE_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
				<Item>FLEE_RESPONSE_FOOT_STEP_HEARD</Item>
				<Item>FLEE_RESPONSE_ENCROACHMENT</Item>
				<Item>FLEE_RESPONSE_HORN</Item>
				<Item>FLEE_RESPONSE_SHOCKING_MELEE_ACTION</Item>
				<Item>FLEE_RESPONSE_SEEN_PED_RUN_OVER</Item>
				<Item>FLEE_RESPONSE_INJURED_PED</Item>
				<Item>FLEE_RESPONSE_SHOCKING_EXPLOSION</Item>
				<Item>FLEE_RESPONSE_HELICOPTER</Item>
				<Item>FLEE_RESPONSE_PLANE</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_DANGEROUS_ANIMAL</Item>
			</EventResponse>
		</Item>
    <Item type="CEventDataDecisionMaker">
      <Name>Cougar</Name>
      <DecisionMakerParentRef/>
      <EventResponse>
        <Item>DEFAULT_RESPONSE_DAMAGE</Item>
        <Item>FLEE_RESPONSE_EXPLOSION</Item>
        <Item>FLEE_RESPONSE_SHOT_FIRED</Item>
        <Item>FLEE_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
        <Item>FLEE_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
        <Item>COMBAT_RESPONSE_FOOT_STEP_HEARD</Item>
        <Item>COMBAT_RESPONSE_ENCROACHMENT</Item>
        <Item>FLEE_RESPONSE_HORN</Item>
        <Item>FLEE_RESPONSE_CAR_CRASH</Item>
        <Item>FLEE_RESPONSE_SHOCKING_EXPLOSION</Item>
        <Item>FLEE_RESPONSE_HELICOPTER</Item>
        <Item>FLEE_RESPONSE_PLANE</Item>
        <Item>DEFAULT_RESPONSE_HATE</Item>
      </EventResponse>
    </Item>
		<Item type="CEventDataDecisionMaker">
			<Name>SmallAnimal</Name>
			<DecisionMakerParentRef/>
			<EventResponse>
				<Item>FLEE_RESPONSE_DAMAGE</Item>
				<Item>FLEE_RESPONSE_CAR_CRASH</Item>
				<Item>FLEE_RESPONSE_EXPLOSION</Item>
				<Item>FLEE_RESPONSE_SHOT_FIRED</Item>
				<Item>FLEE_RESPONSE_SHOT_FIRED_WHIZZED_BY</Item>
				<Item>FLEE_RESPONSE_SHOT_FIRED_BULLET_IMPACT</Item>
				<Item>FLEE_RESPONSE_HORN</Item>
				<Item>FLEE_RESPONSE_SHOCKING_MELEE_ACTION</Item>
				<Item>FLEE_RESPONSE_SHOCKING_EXPLOSION</Item>
				<Item>FLEE_RESPONSE_HELICOPTER</Item>
				<Item>FLEE_RESPONSE_PLANE</Item>
				<Item>DEFAULT_RESPONSE_SHOCKING_DANGEROUS_ANIMAL</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>Cat</Name>
			<DecisionMakerParentRef>SmallAnimal</DecisionMakerParentRef>
			<EventResponse>
				<Item>CAT_RESPONSE_ENCROACHMENT</Item>
			</EventResponse>
		</Item>
		<Item type="CEventDataDecisionMaker">
			<Name>Rabbit</Name>
			<DecisionMakerParentRef>SmallAnimal</DecisionMakerParentRef>
			<EventResponse>
				<Item>FLEE_RESPONSE_FOOT_STEP_HEARD</Item>
				<Item>RABBIT_RESPONSE_FLEE</Item>
			</EventResponse>
		</Item>
	</eventDecisionMaker>
</CEventDataManager>
