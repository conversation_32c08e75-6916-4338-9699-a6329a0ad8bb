<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
  <Item>
    <modelName>polsandstorm</modelName>
    <txdName>polsandstorm</txdName>
    <handlingId>PDSANDSTORM</handlingId>
    <gameName>POLSANDSTORM</gameName>
    <vehicleMakeName>VAPID</vehicleMakeName>
    <expressionDictName>null</expressionDictName>
    <expressionName>null</expressionName>
    <animConvRoofDictName>va_sandstorm</animConvRoofDictName>
    <animConvRoofName>sandstorm</animConvRoofName>
    <animConvRoofWindowsAffected />
    <ptfxAssetName>null</ptfxAssetName>
    <audioNameHash>caracara2</audioNameHash>
    <layout>LAYOUT_STD_STRETCH</layout>
    <coverBoundOffsets>MESA_COVER_OFFSET_INFO</coverBoundOffsets>
    <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
    <scenarioLayout />
  <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
  <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
  <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
    <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
    <FirstPersonDriveByIKOffset x="-0.018000" y="-0.043000" z="-0.020000" />
    <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
  <FirstPersonProjectileDriveByIKOffset x="0.080000" y="0.000000" z="-0.060000" />
  <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="0.060000" z="-0.090000" />
  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.058000" />
  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="-0.058000" />
  <FirstPersonDriveByLeftPassengerIKOffset x="-0.030000" y="0.010000" z="-0.076000" />
  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="-0.040000" />
  <FirstPersonDriveByRightRearPassengerIKOffset x="0.015000" y="0.010000" z="-0.076000" />
  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.150000" z="0.010000" />
  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.150000" z="0.010000" />
  <FirstPersonMobilePhoneOffset x="0.150000" y="0.260000" z="0.503000" />
    <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.445000" />
  <FirstPersonMobilePhoneSeatIKOffset>
    <Item>
        <Offset x="0.154000" y="0.076000" z="0.478000" />
        <SeatIndex value="2" />
    </Item>
    <Item>
        <Offset x="0.154000" y="0.076000" z="0.478000" />
        <SeatIndex value="3" />
    </Item>
  </FirstPersonMobilePhoneSeatIKOffset>
    <PovCameraOffset x="0.000000" y="-0.120000" z="0.580000" />
    <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
    <PovPassengerCameraOffset x="0.000000" y="0.010000" z="0.075000" />
    <PovRearPassengerCameraOffset x="0.000000" y="-0.190000" z="0.145000" />
    <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
    <shouldUseCinematicViewMode value="true" />
    <shouldCameraTransitionOnClimbUpDown value="false" />
    <shouldCameraIgnoreExiting value="false" />
    <AllowPretendOccupants value="true" />
    <AllowJoyriding value="true" />
    <AllowSundayDriving value="true" />
    <AllowBodyColorMapping value="true" />
    <wheelScale value="0.269000" />
    <wheelScaleRear value="0.269000" />
    <dirtLevelMin value="0.000000" />
    <dirtLevelMax value="0.850000" />
    <envEffScaleMin value="0.000000" />
    <envEffScaleMax value="1.000000" />
    <envEffScaleMin2 value="0.000000" />
    <envEffScaleMax2 value="1.000000" />
    <damageMapScale value="0.600000" />
    <damageOffsetScale value="1.000000" />
    <diffuseTint value="0xC4000000" />
    <steerWheelMult value="1.000000" />
    <HDTextureDist value="5.000000" />
    <lodDistances content="float_array">
      15.000000
      30.000000
      70.000000
      140.000000
      500.000000
      500.000000
    </lodDistances>
    <minSeatHeight value="0.846" />
    <identicalModelSpawnDistance value="20" />
    <maxNumOfSameColor value="10" />
    <defaultBodyHealth value="1000.000000" />
    <pretendOccupantsScale value="1.000000" />
    <visibleSpawnDistScale value="1.000000" />
    <trackerPathWidth value="2.000000" />
    <weaponForceMult value="0.900000" />
    <frequency value="100" />
    <swankness>SWANKNESS_1</swankness>
    <maxNum value="1" />
    <flags>FLAG_HAS_LIVERY FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS</flags>
    <type>VEHICLE_TYPE_CAR</type>
    <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
    <vehicleClass>VC_EMERGENCY</vehicleClass>
  <dashboardType>VDT_BANSHEE</dashboardType>
    <wheelType>VWT_OFFROAD</wheelType>
    <trailers />
    <additionalTrailers>
        <Item>boattrailer</Item>
      <Item>trailersmall</Item>
      <Item>trailersmall2</Item>
    </additionalTrailers>
    <drivers />
    <extraIncludes />
    <doorsWithCollisionWhenClosed>
      <Item>VEH_EXT_BOOT</Item>
    </doorsWithCollisionWhenClosed>
    <driveableDoors />
    <bumpersNeedToCollideWithMap value="true" />
    <needsRopeTexture value="false" />
    <requiredExtras />
    <rewards />
    <cinematicPartCamera>
      <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
      <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
      <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      <Item>WHEEL_REAR_LEFT_CAMERA</Item>
    </cinematicPartCamera>
    <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
    <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
    <buoyancySphereSizeScale value="1.000000" />
    <pOverrideRagdollThreshold type="NULL" />
    <firstPersonDrivebyData>
      <Item>RANGER_MESA_FRONT_LEFT</Item>
      <Item>VAN_SPEEDO_FRONT_RIGHT</Item>
      <Item>RANGER_CRUSADER_REAR_LEFT</Item>
      <Item>RANGER_CRUSADER_REAR_RIGHT</Item>
    </firstPersonDrivebyData>
  </Item>
  <Item>
        <modelName>nkcoquette</modelName>
        <txdName>nkcoquette</txdName>
        <handlingId>NKCOQUETTE</handlingId>
        <gameName>NKCOQUETTE</gameName>
        <vehicleMakeName>NKCOQUETTE</vehicleMakeName>
        <expressionDictName>null</expressionDictName>
        <expressionName>null</expressionName>
        <animConvRoofDictName>null</animConvRoofDictName>
        <animConvRoofName>null</animConvRoofName>
        <animConvRoofWindowsAffected/>
        <ptfxAssetName>null</ptfxAssetName>
        <audioNameHash>casco</audioNameHash>
        <layout>LAYOUT_LOW</layout>
        <coverBoundOffsets>COQUETTE_COVER_OFFSET_INFO</coverBoundOffsets>
        <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
        <scenarioLayout/>
        <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
        <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
        <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
        <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
        <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.020000"/>
        <FirstPersonDriveByUnarmedIKOffset x="-0.015000" y="-0.075000" z="0.000000"/>
        <FirstPersonProjectileDriveByIKOffset x="0.015000" y="-0.140000" z="-0.030000"/>
        <FirstPersonProjectileDriveByPassengerIKOffset x="-0.170000" y="-0.145000" z="-0.045000"/>
        <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000"/>
        <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000"/>
        <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
        <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.000000"/>
        <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
        <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
        <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.025000" y="-0.145000" z="0.000000"/>
        <FirstPersonMobilePhoneOffset x="0.160000" y="0.130000" z="0.550000"/>
        <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.063000" z="0.443000"/>
        <PovCameraOffset x="0.000000" y="-0.300000" z="0.665000"/>
        <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
        <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
        <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000"/>
        <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
        <shouldUseCinematicViewMode value="true"/>
        <shouldCameraTransitionOnClimbUpDown value="false"/>
        <shouldCameraIgnoreExiting value="false"/>
        <AllowPretendOccupants value="true"/>
        <AllowJoyriding value="true"/>
        <AllowSundayDriving value="true"/>
        <AllowBodyColorMapping value="true"/>
        <wheelScale value="0.276500"/>
        <wheelScaleRear value="0.276500"/>
        <dirtLevelMin value="0.000000"/>
        <dirtLevelMax value="0.300000"/>
        <envEffScaleMin value="0.000000"/>
        <envEffScaleMax value="1.000000"/>
        <envEffScaleMin2 value="0.000000"/>
        <envEffScaleMax2 value="1.000000"/>
        <damageMapScale value="0.600000"/>
        <damageOffsetScale value="1.000000"/>
        <diffuseTint value="0x00FFFFFF"/>
        <steerWheelMult value="1.000000"/>
        <HDTextureDist value="5.000000"/>
        <lodDistances content="float_array">
            15.000000
            30.000000
            70.000000
            140.000000
            500.000000
            500.000000
        </lodDistances>
        <minSeatHeight value="0.814"/>
        <identicalModelSpawnDistance value="20"/>
        <maxNumOfSameColor value="1"/>
        <defaultBodyHealth value="1000.000000"/>
        <pretendOccupantsScale value="1.000000"/>
        <visibleSpawnDistScale value="1.000000"/>
        <trackerPathWidth value="2.000000"/>
        <weaponForceMult value="1.000000"/>
        <frequency value="10"/>
        <swankness>SWANKNESS_5</swankness>
        <maxNum value="5"/>
        <flags>FLAG_HAS_LIVERY FLAG_NO_BOOT FLAG_SPORTS FLAG_EXTRAS_STRONG FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_TAILLIGHT_CORONAS</flags>
        <type>VEHICLE_TYPE_CAR</type>
        <plateType>VPT_BACK_PLATES</plateType>
        <dashboardType>VDT_FEROCI</dashboardType>
        <vehicleClass>VC_EMERGENCY</vehicleClass>
        <wheelType>VWT_SPORT</wheelType>
        <trailers/>
        <additionalTrailers/>
        <drivers/>
        <extraIncludes/>
        <doorsWithCollisionWhenClosed/>
        <driveableDoors/>
        <bumpersNeedToCollideWithMap value="true"/>
        <needsRopeTexture value="false"/>
        <requiredExtras/>
        <rewards/>
        <cinematicPartCamera>
            <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
            <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
            <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
            <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        </cinematicPartCamera>
        <NmBraceOverrideSet/>
        <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
        <buoyancySphereSizeScale value="1.000000"/>
        <pOverrideRagdollThreshold type="NULL"/>
        <firstPersonDrivebyData>
            <Item>LOW_COQUETTE_FRONT_LEFT</Item>
            <Item>LOW_COQUETTE_FRONT_RIGHT</Item>
        </firstPersonDrivebyData>
    </Item>
    <Item>
        <modelName>lssdbri</modelName>
        <txdName>lssdbri</txdName>
        <handlingId>POLBRIGHAM</handlingId>
        <gameName>lssdbri</gameName>
        <vehicleMakeName>DECLASSE</vehicleMakeName>
        <expressionDictName>null</expressionDictName>
        <expressionName>null</expressionName>
        <animConvRoofDictName>null</animConvRoofDictName>
        <animConvRoofName>null</animConvRoofName>
        <animConvRoofWindowsAffected/>
        <ptfxAssetName>null</ptfxAssetName>
        <audioNameHash>blade</audioNameHash>
        <layout>LAYOUT_STANDARD</layout>
        <coverBoundOffsets>EMPEROR_COVER_OFFSET_INFO</coverBoundOffsets>
        <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
        <scenarioLayout/>
        <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
        <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
        <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
        <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
        <FirstPersonDriveByIKOffset x="0.000000" y="-0.045000" z="-0.020000"/>
        <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000"/>
        <FirstPersonProjectileDriveByIKOffset x="0.055000" y="0.000000" z="-0.040000"/>
        <FirstPersonProjectileDriveByPassengerIKOffset x="-0.055000" y="0.000000" z="-0.040000"/>
        <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="-0.020000"/>
        <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="-0.020000"/>
        <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
        <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.045000" z="-0.020000"/>
        <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000"/>
        <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
        <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000"/>
        <FirstPersonMobilePhoneOffset x="0.140000" y="0.223000" z="0.506000"/>
        <FirstPersonPassengerMobilePhoneOffset x="0.146000" y="0.193000" z="0.395000"/>
        <FirstPersonMobilePhoneSeatIKOffset>
            <Item>
                <Offset x="0.136000" y="0.116000" z="0.415000"/>
                <SeatIndex value="2"/>
            </Item>
            <Item>
                <Offset x="0.136000" y="0.116000" z="0.415000"/>
                <SeatIndex value="3"/>
            </Item>
        </FirstPersonMobilePhoneSeatIKOffset>
        <PovCameraOffset x="0.000000" y="-0.200000" z="0.600000"/>
        <PovCameraVerticalAdjustmentForRollCage value="0.000000"/>
        <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.030000"/>
        <PovRearPassengerCameraOffset x="0.000000" y="-0.050000" z="0.030000"/>
        <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
        <shouldUseCinematicViewMode value="true"/>
        <shouldCameraTransitionOnClimbUpDown value="false"/>
        <shouldCameraIgnoreExiting value="false"/>
        <AllowPretendOccupants value="false"/>
        <AllowJoyriding value="false"/>
        <AllowSundayDriving value="false"/>
        <AllowBodyColorMapping value="true"/>
        <wheelScale value="0.219"/>
        <wheelScaleRear value="0.219"/>
        <dirtLevelMin value="0.100000"/>
        <dirtLevelMax value="0.8500000"/>
        <envEffScaleMin value="0.000000"/>
        <envEffScaleMax value="1.000000"/>
        <envEffScaleMin2 value="0.000000"/>
        <envEffScaleMax2 value="1.000000"/>
        <damageMapScale value="0.300000"/>
        <damageOffsetScale value="1.000000"/>
        <diffuseTint value="0x00FFFFFF"/>
        <steerWheelMult value="1.000000"/>
        <HDTextureDist value="5.000000"/>
        <lodDistances content="float_array">
            15.000000
            30.000000
            70.000000
            140.000000
            500.000000
            500.000000
        </lodDistances>
        <minSeatHeight value="0.815"/>
        <identicalModelSpawnDistance value="20"/>
        <maxNumOfSameColor value="10"/>
        <defaultBodyHealth value="1000.000000"/>
        <pretendOccupantsScale value="1.000000"/>
        <visibleSpawnDistScale value="1.000000"/>
        <trackerPathWidth value="2.000000"/>
        <weaponForceMult value="1.000000"/>
        <frequency value="30"/>
        <swankness>SWANKNESS_2</swankness>
        <maxNum value="5"/>
        <flags>FLAG_EXTRAS_STRONG FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_NO_RESPRAY FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_HAS_LIVERY FLAG_RECESSED_TAILLIGHT_CORONAS</flags>
        <type>VEHICLE_TYPE_CAR</type>
        <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
        <dashboardType>VDT_DUKES</dashboardType>
        <vehicleClass>VC_EMERGENCY</vehicleClass>
        <wheelType>VWT_LOWRIDER</wheelType>
        <trailers/>
        <additionalTrailers/>
        <drivers>
            <Item>
                <driverName>S_M_Y_sheriff_01</driverName>
                <npcName/>
            </Item>
            <Item>
                <driverName>S_F_Y_sheriff_01</driverName>
                <npcName/>
            </Item>
        </drivers>
        <extraIncludes/>
        <doorsWithCollisionWhenClosed/>
        <driveableDoors/>
        <bumpersNeedToCollideWithMap value="false"/>
        <needsRopeTexture value="false"/>
        <requiredExtras>EXTRA_5 EXTRA_6</requiredExtras>
        <rewards>
            <Item>REWARD_WEAPON_PUMPSHOTGUN</Item>
            <Item>REWARD_AMMO_PUMPSHOTGUN</Item>
            <Item>REWARD_STAT_WEAPON</Item>
        </rewards>
        <cinematicPartCamera>
            <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
            <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
            <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
            <Item>WHEEL_REAR_LEFT_CAMERA</Item>
        </cinematicPartCamera>
        <NmBraceOverrideSet/>
        <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000"/>
        <buoyancySphereSizeScale value="1.000000"/>
        <pOverrideRagdollThreshold type="NULL"/>
        <firstPersonDrivebyData>
            <Item>STD_EMPEROR_FRONT_LEFT</Item>
            <Item>STD_EMPEROR_FRONT_RIGHT</Item>
            <Item>STD_EMPEROR_REAR_LEFT</Item>
            <Item>STD_EMPEROR_REAR_RIGHT</Item>
        </firstPersonDrivebyData>
    </Item>
  <Item>
    <modelName>poltaxi</modelName>
    <txdName>poltaxi</txdName>
    <handlingId>cvpi</handlingId>
    <gameName>POLTAXI</gameName>
    <vehicleMakeName />
    <expressionDictName>null</expressionDictName>
    <expressionName>null</expressionName>
    <animConvRoofDictName>null</animConvRoofDictName>
    <animConvRoofName>null</animConvRoofName>
    <animConvRoofWindowsAffected />
    <ptfxAssetName>null</ptfxAssetName>
    <audioNameHash>taxi</audioNameHash>
    <layout>LAYOUT_STANDARD</layout>
    <coverBoundOffsets>TAXI_COVER_OFFSET_INFO</coverBoundOffsets>
    <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
    <scenarioLayout />
    <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
    <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
    <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
    <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
    <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
    <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
  <FirstPersonProjectileDriveByIKOffset x="0.030000" y="-0.080000" z="-0.020000" />
  <FirstPersonProjectileDriveByPassengerIKOffset x="0.030000" y="-0.080000" z="-0.020000" />
  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.000000" z="0.000000" />
  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
  <FirstPersonMobilePhoneOffset x="0.150000" y="0.278000" z="0.510000" />
    <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.175000" z="0.445000" />
  <FirstPersonMobilePhoneSeatIKOffset>
    <Item>
        <Offset x="0.136000" y="0.126000" z="0.455000" />
        <SeatIndex value="2" />
    </Item>
    <Item>
        <Offset x="0.136000" y="0.126000" z="0.455000" />
        <SeatIndex value="3" />
    </Item>
  </FirstPersonMobilePhoneSeatIKOffset>

    <PovCameraOffset x="0.000000" y="-0.145000" z="0.650000" />
    <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
    <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
    <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
    <shouldUseCinematicViewMode value="true" />
    <shouldCameraTransitionOnClimbUpDown value="false" />
    <shouldCameraIgnoreExiting value="false" />
    <AllowPretendOccupants value="true" />
    <AllowJoyriding value="false" />
    <AllowSundayDriving value="false" />
    <AllowBodyColorMapping value="true" />
    <wheelScale value="0.235500" />
    <wheelScaleRear value="0.235500" />
    <dirtLevelMin value="0.000000" />
    <dirtLevelMax value="0.850000" />
    <envEffScaleMin value="0.000000" />
    <envEffScaleMax value="1.000000" />
    <envEffScaleMin2 value="0.000000" />
    <envEffScaleMax2 value="1.000000" />
    <damageMapScale value="0.600000" />
    <damageOffsetScale value="1.000000" />
    <diffuseTint value="0xAA0A0A0A" />
    <steerWheelMult value="1.000000" />
    <HDTextureDist value="5.000000" />
    <lodDistances content="float_array">
      12.000000
      25.000000
      55.000000
      110.000000
      500.000000
      500.000000
    </lodDistances>
    <minSeatHeight value="0.839" />
    <identicalModelSpawnDistance value="20" />
    <maxNumOfSameColor value="10" />
    <defaultBodyHealth value="1000.000000" />
    <pretendOccupantsScale value="1.000000" />
    <visibleSpawnDistScale value="1.000000" />
    <trackerPathWidth value="2.000000" />
    <weaponForceMult value="1.000000" />
    <frequency value="100" />
    <swankness>SWANKNESS_1</swankness>
    <maxNum value="5" />
    <flags>FLAG_EXTRAS_STRONG FLAG_HAS_LIVERY FLAG_LAW_ENFORCEMENT FLAG_EXTRAS_REQUIRE FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_HAS_INTERIOR_EXTRAS</flags>
    <type>VEHICLE_TYPE_CAR</type>
    <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
  <dashboardType>VDT_GENTAXI</dashboardType>
    <vehicleClass>VC_EMERGENCY</vehicleClass>
    <wheelType>VWT_SPORT</wheelType>
    <trailers />
    <additionalTrailers />
    <drivers>
      <Item>
        <driverName>A_M_Y_StLat_01</driverName>
        <npcName />
      </Item>
    </drivers>
    <extraIncludes />
    <doorsWithCollisionWhenClosed />
    <driveableDoors />
    <bumpersNeedToCollideWithMap value="true" />
    <needsRopeTexture value="false" />
    <requiredExtras />
    <rewards />
    <cinematicPartCamera>
      <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
      <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
      <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
      <Item>WHEEL_REAR_LEFT_CAMERA</Item>
    </cinematicPartCamera>
    <NmBraceOverrideSet />
    <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
    <buoyancySphereSizeScale value="1.000000" />
    <pOverrideRagdollThreshold type="NULL" />
  <firstPersonDrivebyData>
      <Item>STD_TAXI_FRONT_LEFT</Item>
      <Item>STD_TAXI_FRONT_RIGHT</Item>
    </firstPersonDrivebyData>
  </Item>
    <Item>
      <modelName>hellion2</modelName>
      <txdName>hellion2</txdName>
      <handlingId>HELLION2</handlingId>
      <gameName>HELLION2</gameName>
      <vehicleMakeName>ANNIS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>POLICE3</audioNameHash>
      <layout>LAYOUT_RANGER</layout>
      <coverBoundOffsets>HELLION_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="0.023000" y="-0.050000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.040000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.050000" y="-0.010000" z="0.070000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="0.060000" z="-0.090000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.055000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="-0.055000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.010000" z="-0.040000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="-0.040000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.010000" z="-0.040000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonMobilePhoneOffset x="0.143000" y="0.245000" z="0.493000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.403000" />
      <PovCameraOffset x="0.000000" y="-0.160000" z="0.680000" />
      <PovPassengerCameraOffset x="-0.050000" y="0.040000" z="0.015000" />
      <PovRearPassengerCameraOffset x="-0.050000" y="0.040000" z="0.015000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.280000" />
      <wheelScaleRear value="0.280000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
				100.000000
				200.000000
				300.000000
				400.000000
				500.000000
				500.000000
      </lodDistances>
      <minSeatHeight value="1.006" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_EXTRAS_STRONG FLAG_EXTRAS_ALL FLAG_HAS_LIVERY FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>RANGER_HELLION_FRONT_LEFT</Item>
        <Item>RANGER_HELLION_FRONT_RIGHT</Item>
        <Item>RANGER_DUBSTA_REAR_LEFT</Item>
        <Item>RANGER_DUBSTA_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>polcoquettep</modelName>
      <txdName>polcoquettep</txdName>
      <handlingId>POLCOQUETTE</handlingId>
      <gameName>polcoquettep</gameName>
      <vehicleMakeName>INVERTO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>COQUETTE</audioNameHash>
      <layout>LAYOUT_LOW</layout>
      <coverBoundOffsets>COQUETTE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.015000" y="-0.075000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.015000" y="-0.140000" z="-0.030000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.170000" y="-0.145000" z="-0.045000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.055000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.025000" y="-0.145000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.160000" y="0.130000" z="0.550000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.063000" z="0.443000" />
      <PovCameraOffset x="0.000000" y="-0.300000" z="0.665000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.276500" />
      <wheelScaleRear value="0.276500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0xA2000000" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.814" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="1" />
      <flags>FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_HAS_LIVERY FLAG_EXTRAS_ALL FLAG_NO_BOOT FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_EXTRAS_STRONG FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_TAILLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_FEROCI</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_F_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards>
        <Item>REWARD_WEAPON_FIREEXTINGUISHER</Item>
        <Item>REWARD_AMMO_FIREEXTINGUISHER</Item>
        <Item>REWARD_WEAPON_FLARE</Item>
        <Item>REWARD_AMMO_FLARE</Item>
        <Item>REWARD_WEAPON_PISTOL</Item>
        <Item>REWARD_AMMO_PISTOL</Item>
        <Item>REWARD_WEAPON_STUNGUN</Item>
        <Item>REWARD_AMMO_STUNGUN</Item>
        <Item>REWARD_WEAPON_CARBINERIFLE</Item>
        <Item>REWARD_AMMO_CARBINERIFLE</Item>
        <Item>REWARD_HEALTH_ENTER_VEHICLE</Item>
        <Item>REWARD_STAT_HEALTH</Item>
      </rewards>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_COQUETTE_FRONT_LEFT</Item>
        <Item>LOW_COQUETTE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>poldmntp</modelName>
      <txdName>poldmntp</txdName>
      <handlingId>POLDOMINATOR3</handlingId>
      <gameName>poldmntp</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>dominator</audioNameHash>
      <layout>LAYOUT_LOW_DOMINATOR3</layout>
      <coverBoundOffsets>DOMINATOR3_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="-0.060000" y="-0.120000" z="-0.050000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.030000" y="-0.120000" z="-0.030000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.020000" y="-0.100000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.095000" z="0.610000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.270000" y="0.060000" z="0.495000" />
      <PovCameraOffset x="0.000000" y="-0.340000" z="0.735000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.305000" />
      <wheelScaleRear value="0.305000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.800000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.00000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0xA2000000" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.780" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="5" />
      <flags>FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_HAS_LIVERY FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_SPORTS FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_RECESSED_TAILLIGHT_CORONAS FLAG_REPORT_CRIME_IF_STANDING_ON</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_F_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards>
        <Item>REWARD_WEAPON_FIREEXTINGUISHER</Item>
        <Item>REWARD_AMMO_FIREEXTINGUISHER</Item>
        <Item>REWARD_WEAPON_FLARE</Item>
        <Item>REWARD_AMMO_FLARE</Item>
        <Item>REWARD_WEAPON_PISTOL</Item>
        <Item>REWARD_AMMO_PISTOL</Item>
        <Item>REWARD_WEAPON_STUNGUN</Item>
        <Item>REWARD_AMMO_STUNGUN</Item>
        <Item>REWARD_WEAPON_CARBINERIFLE</Item>
        <Item>REWARD_AMMO_CARBINERIFLE</Item>
        <Item>REWARD_HEALTH_ENTER_VEHICLE</Item>
        <Item>REWARD_STAT_HEALTH</Item>
      </rewards>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_DOMINATOR3_FRONT_LEFT</Item>
        <Item>LOW_DOMINATOR3_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>polgauntletp</modelName>
      <txdName>polgauntletp</txdName>
      <handlingId>POLGAUNTLET</handlingId>
      <gameName>polgauntletp</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>saspolgauntlet</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>GAUNTLET4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.050000" y="-0.060000" z="-0.035000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.050000" y="-0.080000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.140000" z="-0.050000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.140000" z="-0.050000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.060000" z="-0.035000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.010000" y="-0.100000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.145000" y="0.203000" z="0.560000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.216000" y="0.148000" z="0.450000" />
      <PovCameraOffset x="0.000000" y="-0.230000" z="0.700000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.276800" />
      <wheelScaleRear value="0.276800" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0xA2000000" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.844" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="1" />
      <flags>FLAG_HAS_LIVERY FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_AVERAGE_CAR FLAG_CAN_HAVE_NEONS FLAG_EXTRAS_STRONG</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_F_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards>
        <Item>REWARD_WEAPON_FIREEXTINGUISHER</Item>
        <Item>REWARD_AMMO_FIREEXTINGUISHER</Item>
        <Item>REWARD_WEAPON_FLARE</Item>
        <Item>REWARD_AMMO_FLARE</Item>
        <Item>REWARD_WEAPON_PISTOL</Item>
        <Item>REWARD_AMMO_PISTOL</Item>
        <Item>REWARD_WEAPON_STUNGUN</Item>
        <Item>REWARD_AMMO_STUNGUN</Item>
        <Item>REWARD_WEAPON_CARBINERIFLE</Item>
        <Item>REWARD_AMMO_CARBINERIFLE</Item>
        <Item>REWARD_HEALTH_ENTER_VEHICLE</Item>
        <Item>REWARD_STAT_HEALTH</Item>
      </rewards>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_GAUNTLET4_FRONT_LEFT</Item>
        <Item>STD_GAUNTLET4_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
      <numSeatsOverride value="2" />
    </Item>
    <Item>
      <modelName>polnovak</modelName>
      <txdName>polnovak</txdName>
      <handlingId>polnovak</handlingId>
      <gameName>polnovak</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>POLICE3</audioNameHash>
      <layout>LAYOUT_RANGER_NOVAK</layout>
      <coverBoundOffsets>BALLER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.050000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.030000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.030000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.025000" z="-0.035000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.030000" z="-0.050000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="-0.010000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.050000" z="-0.050000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="-0.010000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.530000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.136000" z="0.445000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.136000" y="0.136000" z="0.445000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.165000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.010000" z="0.030000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.304000" />
      <wheelScaleRear value="0.304000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.050000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0xA2000000" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        25.000000
        70.000000
        140.000000
        350.000000
        350.000000
      </lodDistances>
      <minSeatHeight value="0.887" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="1" />
      <flags>FLAG_EXTRAS_STRONG FLAG_EXTRAS_ALL FLAG_HAS_LIVERY FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_PARKING_SENSORS FLAG_RICH_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_F_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes>
        <Item>EXTRA_1</Item>
      </extraIncludes>
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards>
        <Item>REWARD_WEAPON_FIREEXTINGUISHER</Item>
        <Item>REWARD_AMMO_FIREEXTINGUISHER</Item>
        <Item>REWARD_WEAPON_FLARE</Item>
        <Item>REWARD_AMMO_FLARE</Item>
        <Item>REWARD_WEAPON_PISTOL</Item>
        <Item>REWARD_AMMO_PISTOL</Item>
        <Item>REWARD_WEAPON_STUNGUN</Item>
        <Item>REWARD_AMMO_STUNGUN</Item>
        <Item>REWARD_WEAPON_CARBINERIFLE</Item>
        <Item>REWARD_AMMO_CARBINERIFLE</Item>
        <Item>REWARD_HEALTH_ENTER_VEHICLE</Item>
        <Item>REWARD_STAT_HEALTH</Item>
      </rewards>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_BUFFALO_FRONT_LEFT</Item>
        <Item>STD_BUFFALO_FRONT_RIGHT</Item>
        <Item>STD_BALLER3_REAR_LEFT</Item>
        <Item>STD_BALLER3_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>politaligto</modelName>
      <txdName>politaligto</txdName>
      <handlingId>PDSTINGERTT</handlingId>
      <gameName>politaligto</gameName>
      <vehicleMakeName>GROTTI</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>italigto</audioNameHash>
      <layout>LAYOUT_LOW_ITALIGTO</layout>
      <coverBoundOffsets>ITALIGTO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.160000" z="-0.025000" />
      <FirstPersonProjectileDriveByIKOffset x="0.010000" y="-0.140000" z="-0.060000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.020000" y="-0.140000" z="-0.060000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.100000" z="-0.040000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.160000" z="-0.025000" />
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.133000" z="0.526000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.209000" y="0.124000" z="0.401000" />
      <PovCameraOffset x="0.000000" y="-0.285000" z="0.650000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.274500" />
      <wheelScaleRear value="0.274500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.400000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.40000" />
      <damageOffsetScale value="0.40000" />
      <diffuseTint value="0xD1000000" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_5</swankness>
      <maxNum value="4" />
      <flags>FLAG_HAS_LIVERY FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_HAS_INTERIOR_EXTRAS FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_SPORTS FLAG_RICH_CAR FLAG_RECESSED_HEADLIGHT_CORONAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_HIEND</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>LOW_ITALIGTO_FRONT_LEFT</Item>
        <Item>LOW_ITALIGTO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
          <modelName>polstingertt</modelName>
          <txdName>polstingertt</txdName>
          <handlingId>PDSTINGERTT</handlingId>
          <gameName>polstingertt</gameName>
          <vehicleMakeName>GROTTI</vehicleMakeName>
          <expressionDictName>null</expressionDictName>
          <expressionName>null</expressionName>
          <animConvRoofDictName>null</animConvRoofDictName>
          <animConvRoofName>null</animConvRoofName>
          <animConvRoofWindowsAffected />
          <ptfxAssetName>null</ptfxAssetName>
          <audioNameHash>stingertt</audioNameHash>
          <layout>LAYOUT_LOW_ITALIGTO</layout>
          <coverBoundOffsets>ITALIGTO_COVER_OFFSET_INFO</coverBoundOffsets>
          <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
          <scenarioLayout />
          <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
          <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
          <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG_EXTRA_LOW</bonnetCameraName>
          <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
          <FirstPersonDriveByIKOffset x="0.000000" y="-0.100000" z="-0.040000" />
          <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.160000" z="-0.025000" />
    	  <FirstPersonProjectileDriveByIKOffset x="0.010000" y="-0.140000" z="-0.060000" />
    	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.020000" y="-0.140000" z="-0.060000" />
    	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.100000" z="-0.040000" />
    	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.160000" z="-0.025000" />
    	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.133000" z="0.526000" />
          <FirstPersonPassengerMobilePhoneOffset x="0.209000" y="0.124000" z="0.401000" />
          <PovCameraOffset x="0.000000" y="-0.285000" z="0.650000" />
          <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
          <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
          <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
          <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
          <shouldUseCinematicViewMode value="true" />
          <shouldCameraTransitionOnClimbUpDown value="false" />
          <shouldCameraIgnoreExiting value="false" />
          <AllowPretendOccupants value="true" />
          <AllowJoyriding value="true" />
          <AllowSundayDriving value="true" />
          <AllowBodyColorMapping value="true" />
          <wheelScale value="0.292500" />
          <wheelScaleRear value="0.292500" />
          <dirtLevelMin value="0.000000" />
          <dirtLevelMax value="0.400000" />
          <envEffScaleMin value="0.000000" />
          <envEffScaleMax value="1.000000" />
          <envEffScaleMin2 value="0.000000" />
          <envEffScaleMax2 value="1.000000" />
          <damageMapScale value="0.40000" />
          <damageOffsetScale value="0.40000" />
          <diffuseTint value="0xD1000000" />
          <steerWheelMult value="1.000000" />
          <HDTextureDist value="5.000000" />
          <lodDistances content="float_array">
            10.000000
            25.000000
            60.000000
            120.000000
            500.000000
            500.000000
          </lodDistances>
          <identicalModelSpawnDistance value="20" />
          <maxNumOfSameColor value="1" />
          <defaultBodyHealth value="1000.000000" />
          <pretendOccupantsScale value="1.000000" />
          <visibleSpawnDistScale value="1.000000" />
          <trackerPathWidth value="2.000000" />
          <weaponForceMult value="1.000000" />
          <frequency value="30" />
          <swankness>SWANKNESS_5</swankness>
          <maxNum value="4" />
          <flags>FLAG_HAS_LIVERY FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_HAS_INTERIOR_EXTRAS FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_SPORTS FLAG_RICH_CAR FLAG_RECESSED_HEADLIGHT_CORONAS</flags>
          <type>VEHICLE_TYPE_CAR</type>
          <plateType>VPT_BACK_PLATES</plateType>
          <dashboardType>VDT_RACE</dashboardType>
          <vehicleClass>VC_EMERGENCY</vehicleClass>
          <wheelType>VWT_HIEND</wheelType>
          <trailers />
          <additionalTrailers />
          <drivers />
          <extraIncludes />
          <doorsWithCollisionWhenClosed />
          <driveableDoors />
          <bumpersNeedToCollideWithMap value="false" />
          <needsRopeTexture value="false" />
          <requiredExtras />
          <rewards />
          <cinematicPartCamera>
            <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
            <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
            <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
            <Item>WHEEL_REAR_LEFT_CAMERA</Item>
          </cinematicPartCamera>
          <NmBraceOverrideSet />
          <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
          <buoyancySphereSizeScale value="1.000000" />
          <pOverrideRagdollThreshold type="NULL" />
          <firstPersonDrivebyData>
            <Item>LOW_ITALIGTO_FRONT_LEFT</Item>
            <Item>LOW_ITALIGTO_FRONT_RIGHT</Item>
          </firstPersonDrivebyData>
        </Item>
        <Item>
          <modelName>polbrioso3</modelName>
          <txdName>polbrioso3</txdName>
          <handlingId>POLBRIOSO3</handlingId>
          <gameName>polbrioso3</gameName>
          <vehicleMakeName>GROTTI</vehicleMakeName>
          <expressionDictName>null</expressionDictName>
          <expressionName>null</expressionName>
          <animConvRoofDictName>null</animConvRoofDictName>
          <animConvRoofName>null</animConvRoofName>
          <animConvRoofWindowsAffected />
          <ptfxAssetName>veh_sm_car_small</ptfxAssetName>
          <audioNameHash>BRIOSO3</audioNameHash>
          <layout>LAYOUT_STD_ISSI3</layout>
          <coverBoundOffsets>BRIOSO2_COVER_OFFSET_INFO</coverBoundOffsets>
          <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
          <scenarioLayout />
          <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
          <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
          <bonnetCameraName>VEHICLE_BONNET_CAMERA_ISSI3</bonnetCameraName>
          <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
          <FirstPersonDriveByIKOffset x="0.040000" y="-0.075000" z="-0.060000" />
          <FirstPersonDriveByUnarmedIKOffset x="-0.030000" y="-0.140000" z="-0.010000" />
          <FirstPersonProjectileDriveByIKOffset x="0.050000" y="-0.120000" z="-0.055000" />
          <FirstPersonProjectileDriveByPassengerIKOffset x="-0.040000" y="-0.130000" z="-0.055000" />
          <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.065000" z="-0.060000" />
          <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.120000" z="-0.025000" />
          <FirstPersonMobilePhoneOffset x="0.125000" y="0.180000" z="0.525000" />
          <FirstPersonPassengerMobilePhoneOffset x="0.201000" y="0.120000" z="0.423000" />
          <PovCameraOffset x="0.000000" y="-0.185000" z="0.635000" />
          <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
          <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
          <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC_SMALL</vfxInfoName>
          <shouldUseCinematicViewMode value="true" />
          <shouldCameraTransitionOnClimbUpDown value="false" />
          <shouldCameraIgnoreExiting value="false" />
          <AllowPretendOccupants value="true" />
          <AllowJoyriding value="true" />
          <AllowSundayDriving value="true" />
          <AllowBodyColorMapping value="true" />
          <wheelScale value="0.184400" />
          <wheelScaleRear value="0.184400" />
          <dirtLevelMin value="0.000000" />
          <dirtLevelMax value="0.300000" />
          <envEffScaleMin value="0.000000" />
          <envEffScaleMax value="1.000000" />
          <envEffScaleMin2 value="0.000000" />
          <envEffScaleMax2 value="1.000000" />
          <damageMapScale value="0.50000" />
          <damageOffsetScale value="0.50000" />
          <diffuseTint value="0x00FFFFFF" />
          <steerWheelMult value="1.000000" />
          <HDTextureDist value="5.000000" />
          <lodDistances content="float_array">
            10.000000
            25.000000
            60.000000
            120.000000
            500.000000
            500.000000
          </lodDistances>
          <minSeatHeight value="0.881" />
          <identicalModelSpawnDistance value="20" />
          <maxNumOfSameColor value="1" />
          <defaultBodyHealth value="1000.000000" />
          <pretendOccupantsScale value="1.000000" />
          <visibleSpawnDistScale value="1.000000" />
          <trackerPathWidth value="2.000000" />
          <weaponForceMult value="1.000000" />
          <frequency value="30" />
          <swankness>SWANKNESS_5</swankness>
          <maxNum value="5" />
          <flags>FLAG_HAS_LIVERY FLAG_LAW_ENFORCEMENT FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_SPORTS FLAG_SPAWN_ON_TRAILER FLAG_RICH_CAR FLAG_NO_BROKEN_DOWN_SCENARIO FLAG_CAN_HAVE_NEONS FLAG_INCREASE_CAMBER_WITH_SUSPENSION_MOD</flags>
          <type>VEHICLE_TYPE_CAR</type>
          <plateType>VPT_BACK_PLATES</plateType>
          <dashboardType>VDT_PEYOTE</dashboardType>
          <vehicleClass>VC_EMERGENCY</vehicleClass>
          <wheelType>VWT_MUSCLE</wheelType>
          <trailers />
          <additionalTrailers />
          <drivers />
          <extraIncludes />
          <doorsWithCollisionWhenClosed />
          <driveableDoors />
          <bumpersNeedToCollideWithMap value="false" />
          <needsRopeTexture value="false" />
          <requiredExtras />
          <rewards />
          <cinematicPartCamera>
            <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
            <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
            <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
            <Item>WHEEL_REAR_LEFT_CAMERA</Item>
          </cinematicPartCamera>
          <NmBraceOverrideSet />
          <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
          <buoyancySphereSizeScale value="1.000000" />
          <pOverrideRagdollThreshold type="NULL" />
          <firstPersonDrivebyData>
            <Item>STD_BRIOSO2_FRONT_LEFT</Item>
            <Item>STD_BRIOSO2_FRONT_RIGHT</Item>
          </firstPersonDrivebyData>
          <numSeatsOverride value="2" />
        </Item>
  </InitDatas>
  <txdRelationships>
  	<Item>
			<parent>vehicles_coquette_interior</parent>
			<child>nkcoquette</child>
		</Item>
    <Item>
      <parent>vehicles_coquette_interior</parent>
      <child>polcoquettep</child>
    </Item>
    <Item>
      <parent>vehicles_specter_interior</parent>
      <child>poldmntp</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_n_w_interior</parent>
      <child>polgauntletp</child>
    </Item>
    <Item>
      <parent>vehicles_cav_interior</parent>
      <child>hellion2</child>
    </Item>
      <Item>
        <parent>vehicles_fmj_interior</parent>
        <child>polnovak</child>
      </Item>
    <Item>
        <parent>vehicles_itali_w_interior</parent>
        <child>politaligto</child>
    </Item>
    <Item>
      <parent>vehicles_itali_w_interior</parent>
      <child>polstingertt</child>
    </Item>
    <Item>
      <parent>vehicles_poltax_interior</parent>
      <child>poltaxi</child>
    </Item>
    <Item>
      <parent>vehicles_peyote_w_interior</parent>
      <child>polbrioso3</child>
    </Item>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_brigham_interior</child>
    </Item>
    <Item>
      <parent>vehicles_brigham_interior</parent>
      <child>lssdbri</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
