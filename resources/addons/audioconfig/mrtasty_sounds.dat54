<?xml version="1.0" encoding="UTF-8"?>
<Dat54>
 <Version value="31554421" />
 <NameTable>
  <Item>DLC_MRTASTY\MRTASTY</Item>
 </NameTable>
 <Items>
  <Item type="SoundList">
   <Name>dlc_mrtasty_horn_list_icevan</Name>
   <Header>
    <Flags value="0xAAAAAAAA" />
   </Header>
   <UnkShort value="0" />
   <Items>
    <Item>dlc_mrtasty_icevan_horn</Item>
   </Items>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_horn</Name>
   <Header>
    <Flags value="0x0000004" />
    <Volume value="65435" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_horn_oss</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1>mr_tasty_horn</AudioHash1>
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_horn_oss</Item>
    <Item>mr_tasty_horn</Item>
   </AudioTracks>
  </Item>
  <Item type="OnStopSound">
   <Name>dlc_mrtasty_icevan_horn_oss</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_horn_seq</AudioHash0>
   <AudioHash1>dlc_mrtasty_icevan_loudspeaker_switch_off_env</AudioHash1>
   <AudioHash2 />
  </Item>
  <Item type="SequentialSound">
   <Name>dlc_mrtasty_icevan_horn_seq</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA8AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_loudspeaker_switch_on_mt</Item>
    <Item>dlc_mrtasty_icevan_horn_if</Item>
   </AudioTracks>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_set_horn_active_math</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA8AAAA" />
   </Header>
   <AudioHash />
   <UnkData>
    <Item>
     <UnkByte value="4" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>hornactive</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_loudspeaker_switch_on_mt</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_loudspeaker_switch_on_click_env</Item>
    <Item>dlc_mrtasty_icevan_loudspeaker_switch_on_feedback_rnd</Item>
   </AudioTracks>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_loudspeaker_switch_on_click_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="15" />
   <UnkShort1 value="0" />
   <UnkShort2 value="25" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="100" />
   <UnkShort4 value="0" />
   <UnkInt1 value="25" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_resident_doors_click</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_resident_doors_click</Name>
   <Header>
    <Flags value="0x0000803C" />
    <Volume value="65235" />
    <VolumeVariance value="200" />
    <Pitch value="65335" />
    <PitchVariance value="150" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <ContainerName>resident/doors</ContainerName>
   <FileName>hash_485E8A9D</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="RandomizedSound">
   <Name>dlc_mrtasty_icevan_loudspeaker_switch_on_feedback_rnd</Name>
   <Header>
    <Flags value="0x00008304" />
    <Volume value="65235" />
    <PreDelay value="100" />
    <PreDelayVariance value="50" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <UnkByte value="0" />
   <UnkBytes>FF FF</UnkBytes>
   <Items>
    <Item key="dlc_mrtasty_icevan_loudspeaker_switch_on_feedback_env_on_1" value="1" />
    <Item key="dlc_mrtasty_icevan_loudspeaker_switch_on_feedback_env_on_2" value="1" />
    <Item key="dlc_mrtasty_icevan_loudspeaker_switch_on_feedback_env_on_3" value="1" />
    <Item key="null_sound" value="0.15" />
   </Items>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_loudspeaker_switch_on_feedback_env_on_1</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="50" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="325" />
   <UnkShort4 value="0" />
   <UnkInt1 value="80" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>linear_fall</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_on_1</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_on_1</Name>
   <Header>
    <Flags value="0x0000800C" />
    <Volume value="65235" />
    <VolumeVariance value="200" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>on_1</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_loudspeaker_switch_on_feedback_env_on_2</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="50" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="700" />
   <UnkShort4 value="0" />
   <UnkInt1 value="60" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>linear_fall</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_on_2</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_on_2</Name>
   <Header>
    <Flags value="0x0000800C" />
    <Volume value="65235" />
    <VolumeVariance value="200" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>on_2</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_loudspeaker_switch_on_feedback_env_on_3</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="50" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="600" />
   <UnkShort4 value="0" />
   <UnkInt1 value="75" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>linear_fall</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_on_3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_on_3</Name>
   <Header>
    <Flags value="0x0000800C" />
    <Volume value="65235" />
    <VolumeVariance value="200" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>on_3</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_loudspeaker_switch_off_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="15" />
   <UnkShort1 value="0" />
   <UnkShort2 value="20" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="175" />
   <UnkShort4 value="0" />
   <UnkInt1 value="25" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>linear_fall</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_resident_doors_click_off</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_resident_doors_click_off</Name>
   <Header>
    <Flags value="0x0000803C" />
    <Volume value="100" />
    <VolumeVariance value="200" />
    <Pitch value="150" />
    <PitchVariance value="50" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <ContainerName>resident/doors</ContainerName>
   <FileName>hash_EC7F393A</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="IfSound">
   <Name>dlc_mrtasty_icevan_horn_if</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA8AAAA" />
   </Header>
   <AudioHash1>dlc_mrtasty_icevan_tubular</AudioHash1>
   <AudioHash2>dlc_mrtasty_icevan_horn_rnd</AudioHash2>
   <ParameterHash1>game.clock.hours</ParameterHash1>
   <UnkByte value="3" />
   <UnkFloat value="20" />
   <ParameterHash2 />
  </Item>
  <Item type="RandomizedSound">
   <Name>dlc_mrtasty_icevan_horn_rnd</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <UnkByte value="0" />
   <UnkBytes />
   <Items>
    <Item key="dlc_mrtasty_icevan_valkyries" value="1" />
    <Item key="dlc_mrtasty_icevan_sugarplum" value="1" />
    <Item key="dlc_mrtasty_icevan_scarborough" value="1" />
    <Item key="dlc_mrtasty_icevan_greensleeves" value="1" />
    <Item key="dlc_mrtasty_icevan_bumble" value="1" />
    <Item key="dlc_mrtasty_icevan_gta4" value="1" />
   </Items>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_whoopee</Name>
   <Header>
    <Flags value="0x30008010" />
    <Pitch value="400" />
    <Category>vehicles_horns_loud</Category>
    <Unk23 value="30" />
    <Unk24 value="10" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_whoopee_pts</AudioHash>
   <Variables>
    <Item>
     <Name>delaytime</Name>
     <Value value="0.1" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="ParameterTransformSound">
   <Name>dlc_mrtasty_icevan_whoopee_pts</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_whoopee_lp</AudioHash>
   <Items>
    <Item>
     <ParameterHash>sound.forwardspeed</ParameterHash>
     <UnkFloat0 value="-20" />
     <UnkFloat1 value="50" />
     <NestedData>
      <Item>
       <UnkFloat0 value="-1" />
       <UnkInt value="7" />
       <ParameterHash>delaytime</ParameterHash>
       <UnkFloat1 value="0.07" />
       <UnkFloat2 value="0.2" />
       <Vectors>
        0, 0.55
        0.282381, 0.6297369
        0.4744444, 0.3509333
        0.9644445, 0
        0.9966667, 0
       </Vectors>
      </Item>
     </NestedData>
    </Item>
   </Items>
  </Item>
  <Item type="LoopingSound">
   <Name>dlc_mrtasty_icevan_whoopee_lp</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA8AAAA" />
   </Header>
   <UnkShort0 value="-1" />
   <UnkShort1 value="0" />
   <UnkShort2 value="0" />
   <AudioHash>dlc_mrtasty_icevan_whoopee_mt</AudioHash>
   <ParameterHash />
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_whoopee_mt</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_whoopee_lh</Item>
    <Item>dlc_mrtasty_icevan_whoopee_rh</Item>
   </AudioTracks>
  </Item>
  <Item type="SequentialOverlapSound">
   <Name>dlc_mrtasty_icevan_whoopee_lh</Name>
   <Header>
    <Flags value="0x00008005" />
    <Flags2 value="0xA000AAAA" />
    <Volume value="65035" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <UnkShort value="180" />
   <ParameterHash0>delaytime</ParameterHash0>
   <ParameterHash1 />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_chords_g#2b2</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_f#2_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_a#2c#3</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_f#2_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_a#2c#3</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_c#2_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_g#2b2c#3</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_g1_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
   </AudioTracks>
  </Item>
  <Item type="SequentialOverlapSound">
   <Name>dlc_mrtasty_icevan_whoopee_rh</Name>
   <Header>
    <Flags value="0x00008005" />
    <Flags2 value="0xA000AAAA" />
    <Volume value="65335" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <UnkShort value="180" />
   <ParameterHash0 />
   <ParameterHash1 />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_chords_f#a#3</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_f#a#3</Item>
    <Item>dlc_mrtasty_icevan_chords_g#b</Item>
    <Item>dlc_mrtasty_icevan_chords_a#3c#4</Item>
    <Item>dlc_mrtasty_icevan_chords_g#b</Item>
    <Item>dlc_mrtasty_icevan_chords_f#a#3</Item>
    <Item>dlc_mrtasty_icevan_chords_a#3c#4</Item>
    <Item>dlc_mrtasty_icevan_chords_a#2c#3</Item>
    <Item>dlc_mrtasty_icevan_chords_f#a#3</Item>
    <Item>dlc_mrtasty_icevan_chords_f#a#3</Item>
    <Item>dlc_mrtasty_icevan_chords_fg#</Item>
    <Item>dlc_mrtasty_icevan_f3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_f#3_env</Item>
    <Item>dlc_mrtasty_icevan_chords_fg#</Item>
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_tubular</Name>
   <Header>
    <Flags value="0x0000004" />
    <Volume value="600" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_tubular_vb</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_tubular_vb</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_tubular_vb</Name>
   <Header>
    <Flags value="0x00008000" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <AudioHash>dlc_mrtasty_icevan_tubular_pts</AudioHash>
   <Variables>
    <Item>
     <Name>playbackrate</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>playerhealth</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="2" />
    </Item>
    <Item>
     <Name>pitchfactor</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>pitch</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="ParameterTransformSound">
   <Name>dlc_mrtasty_icevan_tubular_pts</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_tubular_midi</AudioHash>
   <Items>
    <Item>
     <ParameterHash>speed</ParameterHash>
     <UnkFloat0 value="0" />
     <UnkFloat1 value="50" />
     <NestedData>
      <Item>
       <UnkFloat0 value="0.25" />
       <UnkInt value="7" />
       <ParameterHash>playbackrate</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="2" />
       <Vectors>
        0, 0.6
        0.5751222, 0.7826
        0.9417889, 0.99126666
        1, 1
       </Vectors>
      </Item>
      <Item>
       <UnkFloat0 value="-1" />
       <UnkInt value="7" />
       <ParameterHash>pitchfactor</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="1" />
       <Vectors>
        0, 1
        0.1098556, 1
        0.8298556, 0.05
        1, 0.05
       </Vectors>
      </Item>
     </NestedData>
    </Item>
   </Items>
  </Item>
  <Item type="AutomationSound">
   <Name>dlc_mrtasty_icevan_tubular_midi</Name>
   <Header>
    <Flags value="0x38000001" />
    <Flags2 value="0xAAA9AAAA" />
    <Unk22 value="10" />
    <Unk23 value="10" />
    <Unk24 value="10" />
   </Header>
   <AudioHash0 />
   <UnkFloat0 value="1.2" />
   <UnkFloat1 value="0" />
   <ParameterHash>playbackrate</ParameterHash>
   <AudioHash1>dlc_mrtasty_icevan_tubular_note_map</AudioHash1>
   <WaveSlotId>dlc_mrtasty/mrtasty</WaveSlotId>
   <UnkHash1>icevan_tubular</UnkHash1>
   <UnkData />
  </Item>
  <Item type="Unknown">
   <Name>dlc_mrtasty_icevan_tubular_note_map</Name>
   <Header>
    <Flags value="0xAAAAAAAA" />
   </Header>
   <UnkData>
    <Item>
     <UnkByte0 value="52" />
     <UnkByte1 value="128" />
     <UnkByte2 value="0" />
     <AudioTrack>dlc_mrtasty_icevan_tubular_note_vb</AudioTrack>
    </Item>
   </UnkData>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_tubular_note_vb</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_tubular_note_calc_pitch</AudioHash>
   <Variables>
    <Item>
     <Name>pitchvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>healthvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_tubular_note_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_tubular_note_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_tubular_note_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_tubular_note_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_tubular_note_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_tubular_note_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_icevan_tubular_note</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_icevan_tubular_note</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="400" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns_loud</Category>
    <Unk18 value="250" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_valkyries</Name>
   <Header>
    <Flags value="0x0000004" />
    <Volume value="600" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_valkyries_vb</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_valkyries_vb</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_valkyries_vb</Name>
   <Header>
    <Flags value="0x00008000" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <AudioHash>dlc_mrtasty_icevan_valkyries_pts</AudioHash>
   <Variables>
    <Item>
     <Name>playbackrate</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>playerhealth</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="2" />
    </Item>
    <Item>
     <Name>pitchfactor</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>pitch</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="ParameterTransformSound">
   <Name>dlc_mrtasty_icevan_valkyries_pts</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_valkyries_midi</AudioHash>
   <Items>
    <Item>
     <ParameterHash>speed</ParameterHash>
     <UnkFloat0 value="0" />
     <UnkFloat1 value="50" />
     <NestedData>
      <Item>
       <UnkFloat0 value="0.25" />
       <UnkInt value="7" />
       <ParameterHash>playbackrate</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="2" />
       <Vectors>
        0, 0.6
        0.5751222, 0.7826
        0.9417889, 0.99126666
        1, 1
       </Vectors>
      </Item>
      <Item>
       <UnkFloat0 value="-1" />
       <UnkInt value="7" />
       <ParameterHash>pitchfactor</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="1" />
       <Vectors>
        0, 1
        0.1098556, 1
        0.8298556, 0.05
        1, 0.05
       </Vectors>
      </Item>
     </NestedData>
    </Item>
   </Items>
  </Item>
  <Item type="AutomationSound">
   <Name>dlc_mrtasty_icevan_valkyries_midi</Name>
   <Header>
    <Flags value="0x38000001" />
    <Flags2 value="0xAAA9AAAA" />
    <Unk22 value="10" />
    <Unk23 value="10" />
    <Unk24 value="10" />
   </Header>
   <AudioHash0 />
   <UnkFloat0 value="1" />
   <UnkFloat1 value="0" />
   <ParameterHash>playbackrate</ParameterHash>
   <AudioHash1>dlc_mrtasty_icevan_valkyries_note_map</AudioHash1>
   <WaveSlotId>dlc_mrtasty/mrtasty</WaveSlotId>
   <UnkHash1>icevan_valkyries</UnkHash1>
   <UnkData />
  </Item>
  <Item type="Unknown">
   <Name>dlc_mrtasty_icevan_valkyries_note_map</Name>
   <Header>
    <Flags value="0xAAAAAAAA" />
   </Header>
   <UnkData>
    <Item>
     <UnkByte0 value="44" />
     <UnkByte1 value="68" />
     <UnkByte2 value="0" />
     <AudioTrack>dlc_mrtasty_icevan_valkyries_note_vb</AudioTrack>
    </Item>
    <Item>
     <UnkByte0 value="69" />
     <UnkByte1 value="128" />
     <UnkByte2 value="0" />
     <AudioTrack>dlc_mrtasty_icevan_valkyries_note_loud_vb</AudioTrack>
    </Item>
   </UnkData>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_valkyries_note_vb</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_valkyries_note_calc_pitch</AudioHash>
   <Variables>
    <Item>
     <Name>pitchvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>healthvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_valkyries_note_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_valkyries_note_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_valkyries_note_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_valkyries_note_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_valkyries_note_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_valkyries_note_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_icevan_valkyries_note</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_icevan_valkyries_note</Name>
   <Header>
    <Flags value="0x0020C014" />
    <Volume value="65235" />
    <Pitch value="65135" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns_loud</Category>
    <Unk18 value="250" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_valkyries_note_loud_vb</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_valkyries_note_loud_calc_pitch</AudioHash>
   <Variables>
    <Item>
     <Name>pitchvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>healthvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_valkyries_note_loud_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_valkyries_note_loud_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_valkyries_note_loud_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_valkyries_note_loud_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_valkyries_note_loud_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_valkyries_note_loud_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_icevan_valkyries_note_loud</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_icevan_valkyries_note_loud</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="64935" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns_loud</Category>
    <Unk18 value="250" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_sugarplum</Name>
   <Header>
    <Flags value="0x0000004" />
    <Volume value="600" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_sugarplum_vb</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_sugarplum_vb</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_sugarplum_vb</Name>
   <Header>
    <Flags value="0x00008000" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <AudioHash>dlc_mrtasty_icevan_sugarplum_pts</AudioHash>
   <Variables>
    <Item>
     <Name>playbackrate</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>playerhealth</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="2" />
    </Item>
    <Item>
     <Name>pitchfactor</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>pitch</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="ParameterTransformSound">
   <Name>dlc_mrtasty_icevan_sugarplum_pts</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_sugarplum_midi</AudioHash>
   <Items>
    <Item>
     <ParameterHash>speed</ParameterHash>
     <UnkFloat0 value="0" />
     <UnkFloat1 value="50" />
     <NestedData>
      <Item>
       <UnkFloat0 value="0.25" />
       <UnkInt value="7" />
       <ParameterHash>playbackrate</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="2" />
       <Vectors>
        0, 0.6
        0.5751222, 0.7826
        0.9417889, 0.99126666
        1, 1
       </Vectors>
      </Item>
      <Item>
       <UnkFloat0 value="-1" />
       <UnkInt value="7" />
       <ParameterHash>pitchfactor</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="1" />
       <Vectors>
        0, 1
        0.1098556, 1
        0.8298556, 0.05
        1, 0.05
       </Vectors>
      </Item>
     </NestedData>
    </Item>
   </Items>
  </Item>
  <Item type="AutomationSound">
   <Name>dlc_mrtasty_icevan_sugarplum_midi</Name>
   <Header>
    <Flags value="0x38000001" />
    <Flags2 value="0xAAA9AAAA" />
    <Unk22 value="10" />
    <Unk23 value="10" />
    <Unk24 value="10" />
   </Header>
   <AudioHash0 />
   <UnkFloat0 value="0.8" />
   <UnkFloat1 value="0" />
   <ParameterHash>playbackrate</ParameterHash>
   <AudioHash1>dlc_mrtasty_icevan_sugarplum_note_map</AudioHash1>
   <WaveSlotId>dlc_mrtasty/mrtasty</WaveSlotId>
   <UnkHash1>icevan_sugarplum</UnkHash1>
   <UnkData />
  </Item>
  <Item type="Unknown">
   <Name>dlc_mrtasty_icevan_sugarplum_note_map</Name>
   <Header>
    <Flags value="0xAAAAAAAA" />
   </Header>
   <UnkData>
    <Item>
     <UnkByte0 value="36" />
     <UnkByte1 value="128" />
     <UnkByte2 value="0" />
     <AudioTrack>dlc_mrtasty_icevan_sugarplum_note_vb</AudioTrack>
    </Item>
   </UnkData>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_sugarplum_note_vb</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_sugarplum_note_calc_pitch</AudioHash>
   <Variables>
    <Item>
     <Name>pitchvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>healthvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_sugarplum_note_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_sugarplum_note_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_sugarplum_note_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_sugarplum_note_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_sugarplum_note_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_sugarplum_note_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_icevan_sugarplum_note</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_icevan_sugarplum_note</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="64335" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns_loud</Category>
    <Unk18 value="250" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_scarborough</Name>
   <Header>
    <Flags value="0x0000004" />
    <Volume value="600" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_scarborough_vb</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_scarborough_vb</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_scarborough_vb</Name>
   <Header>
    <Flags value="0x00008000" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <AudioHash>dlc_mrtasty_icevan_scarborough_pts</AudioHash>
   <Variables>
    <Item>
     <Name>playbackrate</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>playerhealth</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="2" />
    </Item>
    <Item>
     <Name>pitchfactor</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>pitch</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="ParameterTransformSound">
   <Name>dlc_mrtasty_icevan_scarborough_pts</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_scarborough_midi</AudioHash>
   <Items>
    <Item>
     <ParameterHash>speed</ParameterHash>
     <UnkFloat0 value="0" />
     <UnkFloat1 value="50" />
     <NestedData>
      <Item>
       <UnkFloat0 value="0.25" />
       <UnkInt value="7" />
       <ParameterHash>playbackrate</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="2" />
       <Vectors>
        0, 0.6
        0.5751222, 0.7826
        0.9417889, 0.99126666
        1, 1
       </Vectors>
      </Item>
      <Item>
       <UnkFloat0 value="-1" />
       <UnkInt value="7" />
       <ParameterHash>pitchfactor</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="1" />
       <Vectors>
        0, 1
        0.1098556, 1
        0.8298556, 0.05
        1, 0.05
       </Vectors>
      </Item>
     </NestedData>
    </Item>
   </Items>
  </Item>
  <Item type="AutomationSound">
   <Name>dlc_mrtasty_icevan_scarborough_midi</Name>
   <Header>
    <Flags value="0x38000001" />
    <Flags2 value="0xAAA9AAAA" />
    <Unk22 value="10" />
    <Unk23 value="10" />
    <Unk24 value="10" />
   </Header>
   <AudioHash0 />
   <UnkFloat0 value="1" />
   <UnkFloat1 value="0" />
   <ParameterHash>playbackrate</ParameterHash>
   <AudioHash1>dlc_mrtasty_icevan_scarborough_note_map</AudioHash1>
   <WaveSlotId>dlc_mrtasty/mrtasty</WaveSlotId>
   <UnkHash1>icevan_scarborough</UnkHash1>
   <UnkData />
  </Item>
  <Item type="Unknown">
   <Name>dlc_mrtasty_icevan_scarborough_note_map</Name>
   <Header>
    <Flags value="0xAAAAAAAA" />
   </Header>
   <UnkData>
    <Item>
     <UnkByte0 value="36" />
     <UnkByte1 value="128" />
     <UnkByte2 value="0" />
     <AudioTrack>dlc_mrtasty_icevan_scarborough_note_vb</AudioTrack>
    </Item>
   </UnkData>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_scarborough_note_vb</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_scarborough_note_calc_pitch</AudioHash>
   <Variables>
    <Item>
     <Name>pitchvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>healthvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_scarborough_note_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_scarborough_note_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_scarborough_note_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_scarborough_note_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_scarborough_note_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_scarborough_note_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_icevan_scarborough_note</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_icevan_scarborough_note</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="64335" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns_loud</Category>
    <Unk18 value="250" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_greensleeves</Name>
   <Header>
    <Flags value="0x0000004" />
    <Volume value="600" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_greensleeves_vb</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_greensleeves_vb</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_greensleeves_vb</Name>
   <Header>
    <Flags value="0x00008000" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <AudioHash>dlc_mrtasty_icevan_greensleeves_pts</AudioHash>
   <Variables>
    <Item>
     <Name>playbackrate</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>playerhealth</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="2" />
    </Item>
    <Item>
     <Name>pitchfactor</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>pitch</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="ParameterTransformSound">
   <Name>dlc_mrtasty_icevan_greensleeves_pts</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_greensleeves_midi</AudioHash>
   <Items>
    <Item>
     <ParameterHash>speed</ParameterHash>
     <UnkFloat0 value="0" />
     <UnkFloat1 value="50" />
     <NestedData>
      <Item>
       <UnkFloat0 value="0.25" />
       <UnkInt value="7" />
       <ParameterHash>playbackrate</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="2" />
       <Vectors>
        0, 0.6
        0.5751222, 0.7826
        0.9417889, 0.99126666
        1, 1
       </Vectors>
      </Item>
      <Item>
       <UnkFloat0 value="-1" />
       <UnkInt value="7" />
       <ParameterHash>pitchfactor</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="1" />
       <Vectors>
        0, 1
        0.1098556, 1
        0.8298556, 0.05
        1, 0.05
       </Vectors>
      </Item>
     </NestedData>
    </Item>
   </Items>
  </Item>
  <Item type="AutomationSound">
   <Name>dlc_mrtasty_icevan_greensleeves_midi</Name>
   <Header>
    <Flags value="0x38000001" />
    <Flags2 value="0xAAA9AAAA" />
    <Unk22 value="10" />
    <Unk23 value="10" />
    <Unk24 value="10" />
   </Header>
   <AudioHash0 />
   <UnkFloat0 value="1" />
   <UnkFloat1 value="0" />
   <ParameterHash>playbackrate</ParameterHash>
   <AudioHash1>dlc_mrtasty_icevan_greensleeves_note_map</AudioHash1>
   <WaveSlotId>dlc_mrtasty/mrtasty</WaveSlotId>
   <UnkHash1>icevan_greensleeves</UnkHash1>
   <UnkData />
  </Item>
  <Item type="Unknown">
   <Name>dlc_mrtasty_icevan_greensleeves_note_map</Name>
   <Header>
    <Flags value="0xAAAAAAAA" />
   </Header>
   <UnkData>
    <Item>
     <UnkByte0 value="33" />
     <UnkByte1 value="128" />
     <UnkByte2 value="0" />
     <AudioTrack>dlc_mrtasty_icevan_greensleeves_note_vb</AudioTrack>
    </Item>
   </UnkData>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_greensleeves_note_vb</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_greensleeves_note_calc_pitch</AudioHash>
   <Variables>
    <Item>
     <Name>pitchvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>healthvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_greensleeves_note_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_greensleeves_note_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_greensleeves_note_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_greensleeves_note_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_greensleeves_note_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_greensleeves_note_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_icevan_greensleeves_note</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_icevan_greensleeves_note</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="64035" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns_loud</Category>
    <Unk18 value="250" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_bumble</Name>
   <Header>
    <Flags value="0x0000004" />
    <Volume value="600" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_bumble_vb</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_bumble_vb</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_bumble_vb</Name>
   <Header>
    <Flags value="0x00008000" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <AudioHash>dlc_mrtasty_icevan_bumble_pts</AudioHash>
   <Variables>
    <Item>
     <Name>playbackrate</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>playerhealth</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="2" />
    </Item>
    <Item>
     <Name>pitchfactor</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>pitch</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="ParameterTransformSound">
   <Name>dlc_mrtasty_icevan_bumble_pts</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_bumble_midi</AudioHash>
   <Items>
    <Item>
     <ParameterHash>speed</ParameterHash>
     <UnkFloat0 value="0" />
     <UnkFloat1 value="50" />
     <NestedData>
      <Item>
       <UnkFloat0 value="0.25" />
       <UnkInt value="7" />
       <ParameterHash>playbackrate</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="2" />
       <Vectors>
        0, 0.6
        0.5751222, 0.7826
        0.9417889, 0.99126666
        1, 1
       </Vectors>
      </Item>
      <Item>
       <UnkFloat0 value="-1" />
       <UnkInt value="7" />
       <ParameterHash>pitchfactor</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="1" />
       <Vectors>
        0, 1
        0.1098556, 1
        0.8298556, 0.05
        1, 0.05
       </Vectors>
      </Item>
     </NestedData>
    </Item>
   </Items>
  </Item>
  <Item type="AutomationSound">
   <Name>dlc_mrtasty_icevan_bumble_midi</Name>
   <Header>
    <Flags value="0x38000001" />
    <Flags2 value="0xAAA9AAAA" />
    <Unk22 value="10" />
    <Unk23 value="10" />
    <Unk24 value="10" />
   </Header>
   <AudioHash0 />
   <UnkFloat0 value="1.2" />
   <UnkFloat1 value="0" />
   <ParameterHash>playbackrate</ParameterHash>
   <AudioHash1>dlc_mrtasty_icevan_bumble_note_map</AudioHash1>
   <WaveSlotId>dlc_mrtasty/mrtasty</WaveSlotId>
   <UnkHash1>icevan_bumble</UnkHash1>
   <UnkData />
  </Item>
  <Item type="Unknown">
   <Name>dlc_mrtasty_icevan_bumble_note_map</Name>
   <Header>
    <Flags value="0xAAAAAAAA" />
   </Header>
   <UnkData>
    <Item>
     <UnkByte0 value="47" />
     <UnkByte1 value="128" />
     <UnkByte2 value="0" />
     <AudioTrack>dlc_mrtasty_icevan_bumble_note_vb</AudioTrack>
    </Item>
   </UnkData>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_bumble_note_vb</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_bumble_note_calc_pitch</AudioHash>
   <Variables>
    <Item>
     <Name>pitchvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>healthvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_bumble_note_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_bumble_note_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_bumble_note_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_bumble_note_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_bumble_note_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_bumble_note_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_icevan_bumble_note</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_icevan_bumble_note</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="65435" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns_loud</Category>
    <Unk18 value="250" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_gta4</Name>
   <Header>
    <Flags value="0x0000004" />
    <Volume value="600" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_gta4_vb</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_gta4_vb</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_gta4_vb</Name>
   <Header>
    <Flags value="0x00008000" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <AudioHash>dlc_mrtasty_icevan_gta4_pts</AudioHash>
   <Variables>
    <Item>
     <Name>playbackrate</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>playerhealth</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="2" />
    </Item>
    <Item>
     <Name>pitchfactor</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>pitch</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="ParameterTransformSound">
   <Name>dlc_mrtasty_icevan_gta4_pts</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_gta4_midi</AudioHash>
   <Items>
    <Item>
     <ParameterHash>speed</ParameterHash>
     <UnkFloat0 value="0" />
     <UnkFloat1 value="50" />
     <NestedData>
      <Item>
       <UnkFloat0 value="0.25" />
       <UnkInt value="7" />
       <ParameterHash>playbackrate</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="2" />
       <Vectors>
        0, 0.6
        0.5751222, 0.7826
        0.9417889, 0.99126666
        1, 1
       </Vectors>
      </Item>
      <Item>
       <UnkFloat0 value="-1" />
       <UnkInt value="7" />
       <ParameterHash>pitchfactor</ParameterHash>
       <UnkFloat1 value="0" />
       <UnkFloat2 value="1" />
       <Vectors>
        0, 1
        0.1098556, 1
        0.8298556, 0.05
        1, 0.05
       </Vectors>
      </Item>
     </NestedData>
    </Item>
   </Items>
  </Item>
  <Item type="AutomationSound">
   <Name>dlc_mrtasty_icevan_gta4_midi</Name>
   <Header>
    <Flags value="0x38000001" />
    <Flags2 value="0xAAA9AAAA" />
    <Unk22 value="10" />
    <Unk23 value="10" />
    <Unk24 value="10" />
   </Header>
   <AudioHash0 />
   <UnkFloat0 value="0.9" />
   <UnkFloat1 value="0" />
   <ParameterHash>playbackrate</ParameterHash>
   <AudioHash1>dlc_mrtasty_icevan_gta4_note_map</AudioHash1>
   <WaveSlotId>dlc_mrtasty/mrtasty</WaveSlotId>
   <UnkHash1>icevan_gta4</UnkHash1>
   <UnkData />
  </Item>
  <Item type="Unknown">
   <Name>dlc_mrtasty_icevan_gta4_note_map</Name>
   <Header>
    <Flags value="0xAAAAAAAA" />
   </Header>
   <UnkData>
    <Item>
     <UnkByte0 value="24" />
     <UnkByte1 value="128" />
     <UnkByte2 value="0" />
     <AudioTrack>dlc_mrtasty_icevan_gta4_note_vb</AudioTrack>
    </Item>
   </UnkData>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_gta4_note_vb</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_gta4_note_calc_pitch</AudioHash>
   <Variables>
    <Item>
     <Name>pitchvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>healthvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_gta4_note_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_gta4_note_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_gta4_note_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_gta4_note_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_gta4_note_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_gta4_note_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_icevan_gta4_note</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_icevan_gta4_note</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="63135" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns_loud</Category>
    <Unk18 value="250" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_suspension_down</Name>
   <Header>
    <Flags value="0x00208000" />
    <Category>vehicles_suspension</Category>
    <Unk18 value="100" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_suspension_down_mt</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1>suspension_down</AudioHash1>
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_suspension_down_mt</Item>
    <Item>suspension_down</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_suspension_down_mt</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>suspension_down</Item>
    <Item>dlc_mrtasty_icevan_suspension_down_env</Item>
    <Item>dlc_mrtasty_icevan_suspension_down_2_env</Item>
   </AudioTracks>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_suspension_down_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_icevan_suspension_down_rnd</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="RandomizedSound">
   <Name>dlc_mrtasty_icevan_suspension_down_rnd</Name>
   <Header>
    <Flags value="0x0000030C" />
    <Volume value="65035" />
    <VolumeVariance value="350" />
    <PreDelay value="150" />
    <PreDelayVariance value="75" />
   </Header>
   <UnkByte value="0" />
   <UnkBytes>FF FF FF FF FF FF FF FF</UnkBytes>
   <Items>
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_b3" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_b4" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c3" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c4" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d3" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d4" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_e3" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_e4" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f3" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f4" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g3" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g4" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a3" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a4" value="1" />
    <Item key="null_sound" value="2" />
   </Items>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_suspension_down_2_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_icevan_suspension_down_2_rnd</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="RandomizedSound">
   <Name>dlc_mrtasty_icevan_suspension_down_2_rnd</Name>
   <Header>
    <Flags value="0x0000030C" />
    <Volume value="65135" />
    <VolumeVariance value="600" />
    <PreDelay value="125" />
    <PreDelayVariance value="75" />
   </Header>
   <UnkByte value="0" />
   <UnkBytes>FF FF FF FF FF FF</UnkBytes>
   <Items>
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c#3" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c#4" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d#3" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d#4" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f#3" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f#4" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g#3" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g#4" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a#3" value="1" />
    <Item key="dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a#4" value="1" />
    <Item key="null_sound" value="2" />
   </Items>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_reverse_warning</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_reverse_warning_rtol</AudioHash>
   <Variables>
    <Item>
     <Name>pitchvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>healthvar</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
   </Variables>
  </Item>
  <Item type="IfSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_if</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA8AAAA" />
   </Header>
   <AudioHash1 />
   <AudioHash2 />
   <ParameterHash1>hornactive</ParameterHash1>
   <UnkByte value="4" />
   <UnkFloat value="1" />
   <ParameterHash2 />
  </Item>
  <Item type="RetriggeredOverlappedSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_rtol</Name>
   <Header>
    <Flags value="0x1800C000" />
    <DopplerFactor value="10" />
    <Category>vehicles_horns</Category>
    <Unk22 value="10" />
    <Unk23 value="35" />
   </Header>
   <UnkShort0 value="65535" />
   <UnkShort1 value="0" />
   <UnkShort2 value="900" />
   <UnkShort3 value="0" />
   <ParameterHash0 />
   <ParameterHash1 />
   <AudioHash0 />
   <AudioHash1>dlc_mrtasty_icevan_reverse_warning_mt</AudioHash1>
   <AudioHash2 />
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_mt</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_reverse_warning_c4</Item>
    <Item>dlc_mrtasty_icevan_reverse_warning_e4</Item>
    <Item>dlc_mrtasty_icevan_reverse_warning_g4</Item>
    <Item>dlc_mrtasty_icevan_reverse_warning_b4</Item>
    <Item>dlc_mrtasty_icevan_wait</Item>
    <Item>dlc_mrtasty_icevan_reverse_warning_c5</Item>
    <Item>dlc_mrtasty_icevan_reverse_warning_d5</Item>
    <Item>dlc_mrtasty_icevan_reverse_warning_e5</Item>
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_b4</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_reverse_warning_b4_calc_pitch</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_reverse_warning_b4_calc_pitch</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_b4_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_reverse_warning_b4_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_b4_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_b4_env_wrap</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_b4_env_wrap</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_c4</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_reverse_warning_c4_calc_pitch</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_reverse_warning_c4_calc_pitch</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_c4_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_reverse_warning_c4_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_c4_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_c4_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_c4_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_c5</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_reverse_warning_c5_calc_pitch</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_reverse_warning_c5_calc_pitch</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_c5_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_reverse_warning_c5_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_c5_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_c5_env_wrap</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_c5_env_wrap</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_d5</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_reverse_warning_d5_calc_pitch</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_reverse_warning_d5_calc_pitch</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_d5_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_reverse_warning_d5_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_d5_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_d5_env_wrap</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_d5_env_wrap</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_e4</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_reverse_warning_e4_calc_pitch</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_reverse_warning_e4_calc_pitch</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_e4_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_reverse_warning_e4_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_e4_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_e4_env_wrap</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_e4_env_wrap</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_e5</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_reverse_warning_e5_calc_pitch</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_reverse_warning_e5_calc_pitch</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_e5_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_reverse_warning_e5_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_e5_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_e5_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_e5_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_g4</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_reverse_warning_g4_calc_pitch</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_reverse_warning_g4_calc_pitch</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_g4_calc_pitch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_reverse_warning_g4_apply_var</AudioHash>
   <UnkData>
    <Item>
     <UnkByte value="1" />
     <UnkInt0 value="**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-772796878" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="-855175714" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>healthvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="12" />
     <UnkInt0 value="-**********" />
     <UnkInt1 value="0" />
     <UnkInt2 value="**********" />
     <UnkInt3 value="0" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" />
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" />
     <UnkInt2 value="0" />
     <UnkInt3 value="-855175714" />
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitchvar</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_reverse_warning_g4_apply_var</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_g4_env_wrap</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitchvar" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_g4_env_wrap</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_wait</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0 />
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item />
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_envelopes_silence</Name>
   <Header>
    <Flags value="0x00000004" />
    <Volume value="55535" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_a#3_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_a#3_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_rest_env</Name>
   <Header>
    <Flags value="0x00000004" />
    <Volume value="55535" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_a#3_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_a#3_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="VariableBlockSound">
   <Name>dlc_mrtasty_icevan_vb</Name>
   <Header>
    <Flags value="0x00208004" />
    <Volume value="400" />
    <Category>vehicles_horns_loud</Category>
    <Unk18 value="150" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_sequence</AudioHash>
   <Variables>
    <Item>
     <Name>pitch</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>minpitch</Name>
     <Value value="-0.17" />
     <UnkFloat value="0" />
     <Flags value="1" /><!--1-->
    </Item>
    <Item>
     <Name>maxpitch</Name>
     <Value value="0.17" />
     <UnkFloat value="0" />
     <Flags value="1" /><!--1-->
    </Item>
    <Item>
     <Name>delaytime</Name>
     <Value value="0.25" />
     <UnkFloat value="0" />
     <Flags value="1" /><!--1-->
    </Item>
    <Item>
     <Name>variation</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="1" /><!--1-->
    </Item>
    <Item>
     <Name>temposcaling</Name>
     <Value value="1" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>delay</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="0" />
    </Item>
    <Item>
     <Name>direction</Name>
     <Value value="0" />
     <UnkFloat value="0" />
     <Flags value="1" /><!--1-->
    </Item>
   </Variables>
  </Item>
  <Item type="RetriggeredOverlappedSound">
   <Name>dlc_mrtasty_icevan_sequence</Name>
   <Header>
    <Flags value="0x1800C005" />
    <Flags2 value="0xAAA9AAAA" />
    <Volume value="600" />
    <DopplerFactor value="10" />
    <Category>vehicles_horns_loud</Category>
    <Unk22 value="10" />
    <Unk23 value="35" />
   </Header>
   <UnkShort0 value="65535" />
   <UnkShort1 value="0" />
   <UnkShort2 value="320" /><!--???-->
   <UnkShort3 value="0" />
   <ParameterHash0 />
   <ParameterHash1>delay</ParameterHash1>
   <AudioHash0 />
   <AudioHash1>dlc_mrtasty_icevan_sequence_seq</AudioHash1>
   <AudioHash2 />
  </Item>
  <Item type="SequentialSound">
   <Name>dlc_mrtasty_icevan_sequence_seq</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA8AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_sequence_pitch_rand</Item>
    <Item>dlc_mrtasty_icevan_sequence_switch</Item>
   </AudioTracks>
  </Item>
  <Item type="MathOperationSound">
   <Name>dlc_mrtasty_icevan_sequence_pitch_rand</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA8AAAA" />
   </Header>
   <AudioHash />
   <UnkData>
    <Item>
     <UnkByte value="12" /><!--rand(minpitch, maxpitch) = pitch-->
     <UnkInt0 value="0" />
     <UnkInt1 value="**********" /><!--minpitch-->
     <UnkInt2 value="0" />
     <UnkInt3 value="**********" /><!--maxpitch-->
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>pitch</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="2" /><!--delaytime * temposcaling = delay-->
     <UnkInt0 value="0" />
     <UnkInt1 value="-637081528" /><!--delaytime-->
     <UnkInt2 value="0" />
     <UnkInt3 value="-**********" /><!--temposcaling-->
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>delay</ParameterHash1>
    </Item>
    <Item>
     <UnkByte value="0" /><!--0.05 + delay = delay-->
     <UnkInt0 value="**********" /><!--0.05-->
     <UnkInt1 value="0" />
     <UnkInt2 value="0" />
     <UnkInt3 value="**********" /><!--delay-->
     <UnkInt4 value="0" />
     <ParameterHash0 />
     <ParameterHash1>delay</ParameterHash1>
    </Item>
   </UnkData>
  </Item>
  <Item type="SwitchSound">
   <Name>dlc_mrtasty_icevan_sequence_switch</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA9AAAA" />
   </Header>
   <ParameterHash>variation</ParameterHash>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_sugarplum</Item>
    <Item>dlc_mrtasty_icevan_greensleeves_harmonised</Item>
    <Item>dlc_mrtasty_icevan_scarb_harmonised</Item>
    <Item>dlc_mrtasty_icevan_bumble</Item>
    <Item>dlc_mrtasty_icevan_gta4</Item>
    <Item>dlc_mrtasty_icevan_valkyries</Item>
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_greensleeves_harmonised</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_greensleeves_harmonised_mt</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitch" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_greensleeves_harmonised_mt</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_greensleeves_harmonised_mt</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_greensleeves_chords</Item>
    <Item>dlc_mrtasty_icevan_greensleeves_seq</Item>
   </AudioTracks>
  </Item>
  <Item type="SequentialOverlapSound">
   <Name>dlc_mrtasty_icevan_greensleeves_chords</Name>
   <Header>
    <Flags value="0x00008005" />
    <Flags2 value="0xA000AAAA" />
    <Volume value="65035" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <UnkShort value="0" />
   <ParameterHash0>delaytime</ParameterHash0>
   <ParameterHash1>direction</ParameterHash1>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_a2_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_c3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_g3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_gb</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_gb</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_e3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_ge4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_ge4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_a2_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_f3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_ac4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_ac4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_e3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_g#b</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_g#b</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_e3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_g#b</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_a2_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_c3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_g3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_gb</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_gb</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_e3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_ge4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_ge4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_f3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_ac4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_ac4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_e3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_g#b</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_chords_g#b</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_a2_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_a2_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_ce4</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
   </AudioTracks>
  </Item>
  <Item type="SequentialOverlapSound">
   <Name>dlc_mrtasty_icevan_greensleeves_seq</Name>
   <Header>
    <Flags value="0x00008001" />
    <Flags2 value="0xA000AAAA" />
    <Category>vehicles_horns_loud</Category>
   </Header>
   <UnkShort value="0" />
   <ParameterHash0>delaytime</ParameterHash0>
   <ParameterHash1>direction</ParameterHash1>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_c4_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_d4_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_e4_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_f4_env</Item>
    <Item>dlc_mrtasty_icevan_e4_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_d4_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_b3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_g3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
    <Item>dlc_mrtasty_icevan_b3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_c4_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_g#3_env</Item>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_b3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_g#3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_e3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_c4_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_d4_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_e4_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_f4_env</Item>
    <Item>dlc_mrtasty_icevan_e4_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_d4_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_b3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_g3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
    <Item>dlc_mrtasty_icevan_b3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_c4_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_b3_env</Item>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_g#3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_f#3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_g#3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
    <Item>dlc_mrtasty_icevan_envelopes_silence</Item>
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_scarb_harmonised</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_scarb_harmonised_mt</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables>
    <Item key="pitch" value="1" />
   </Variables>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_scarb_harmonised_mt</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_scarb_harmonised_mt</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_scarb</Item>
    <Item>dlc_mrtasty_icevan_scarb_harmonised_accomp</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_ac</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
    <Item>dlc_mrtasty_icevan_c3_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_ce</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_c3_env</Item>
    <Item>dlc_mrtasty_icevan_e3_env</Item>
    <Item>dlc_mrtasty_icevan_g4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_ce4</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_e4_env</Item>
    <Item>dlc_mrtasty_icevan_c4_env</Item>
    <Item>dlc_mrtasty_icevan_e3_env</Item>
    <Item>dlc_mrtasty_icevan_c4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_cf</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_c3_env</Item>
    <Item>dlc_mrtasty_icevan_f4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_cg4</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_g4_env</Item>
    <Item>dlc_mrtasty_icevan_c4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_eg</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_e3_env</Item>
    <Item>dlc_mrtasty_icevan_g3_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_eg4</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_e4_env</Item>
    <Item>dlc_mrtasty_icevan_g4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_fa</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_f3_env</Item>
    <Item>dlc_mrtasty_icevan_a4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_a#3c#4</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_c#4_env</Item>
    <Item>dlc_mrtasty_icevan_a#3_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_g#2b2</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA0AAAA" />
    <Pitch value="64335" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_g#3_env</Item>
    <Item>dlc_mrtasty_icevan_b3_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_a#2c#3</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_c#3_env</Item>
    <Item>dlc_mrtasty_icevan_a#2_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_g#2b2c#3</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA0AAAA" />
    <Pitch value="64335" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_g#3_env</Item>
    <Item>dlc_mrtasty_icevan_b3_env</Item>
    <Item>dlc_mrtasty_icevan_c4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_gc</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_g3_env</Item>
    <Item>dlc_mrtasty_icevan_c3_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_af</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_f3_env</Item>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_ac4</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_a4_env</Item>
    <Item>dlc_mrtasty_icevan_c4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_bg</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_b4_env</Item>
    <Item>dlc_mrtasty_icevan_g4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_dg</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_d4_env</Item>
    <Item>dlc_mrtasty_icevan_g4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_bd</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_b4_env</Item>
    <Item>dlc_mrtasty_icevan_d4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_bg4</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_g4_env</Item>
    <Item>dlc_mrtasty_icevan_b3_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_ge4</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_g3_env</Item>
    <Item>dlc_mrtasty_icevan_e4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_f#a4</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_f#4_env</Item>
    <Item>dlc_mrtasty_icevan_a3_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_f#d#4</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_f#3_env</Item>
    <Item>dlc_mrtasty_icevan_d#4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_f#d4</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_f#3_env</Item>
    <Item>dlc_mrtasty_icevan_d4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_ec#3</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_e3_env</Item>
    <Item>dlc_mrtasty_icevan_c#4_env</Item>
    <Item>dlc_mrtasty_icevan_a#3_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_d#c</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_d#3_env</Item>
    <Item>dlc_mrtasty_icevan_c4_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_gb</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_g3_env</Item>
    <Item>dlc_mrtasty_icevan_b3_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_g#b</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_g#3_env</Item>
    <Item>dlc_mrtasty_icevan_b3_env</Item>
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_chords_c#e3</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_c#3_env</Item>
    <Item>dlc_mrtasty_icevan_e3_env</Item>
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_chords_bd3</Name>
   <Header>
    <Flags value="0x00000010" />
    <Pitch value="64335" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_chords_bd</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_chords_bd</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_f3_env_wrap</Name>
   <Header>
    <Flags value="0x00000004" />
    <Volume value="64735" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_f3_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_f3_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_e4_env_wrap</Name>
   <Header>
    <Flags value="0x00000100" />
    <PreDelay value="100" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_e4_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_e4_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_g4_env_wrap</Name>
   <Header>
    <Flags value="0x00000100" />
    <PreDelay value="200" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_g4_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_g4_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_b4_env_wrap</Name>
   <Header>
    <Flags value="0x00000100" />
    <PreDelay value="300" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_b4_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_b4_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_c5_env_wrap</Name>
   <Header>
    <Flags value="0x00000104" />
    <Volume value="64935" />
    <PreDelay value="50" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_c5_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_c5_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_d5_env_wrap</Name>
   <Header>
    <Flags value="0x00000104" />
    <Volume value="64935" />
    <PreDelay value="150" />
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_d5_env</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_d5_env</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_f#2_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="64335" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f#3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_a#2_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="64335" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a#3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_c#2_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="64335" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c#3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_g1_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="63135" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_a2_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a2</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_c2_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="64335" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_d2_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="63135" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_c3_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_c#3_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c#3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_d3_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_d#3_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d#3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_e3_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_e3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_f3_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_f#3_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f#3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_g3_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_g#3_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g#3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_a3_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_a#3_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a#3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_b3_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_b3</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_c4_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_c#4_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c#4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_d4_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_d#4_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d#4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_e4_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_e4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_f4_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_f#4_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f#4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_g4_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_g#4_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g#4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_a4_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_a#4_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a#4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_b4_env</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA1AAAA" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_b4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_c5_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="1100" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_c#5_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="1200" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_d5_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="1200" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_d#5_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="1200" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d#4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_e5_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="1200" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_e4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_f#5_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="1200" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f#4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="EnvelopeSound">
   <Name>dlc_mrtasty_icevan_a5_env</Name>
   <Header>
    <Flags value="0x00000011" />
    <Flags2 value="0xAAA1AAAA" />
    <Pitch value="1200" />
   </Header>
   <UnkShort0 value="20" />
   <UnkShort1 value="0" />
   <UnkShort2 value="50" />
   <UnkShort3 value="0" />
   <UnkByte0 value="100" />
   <UnkByte1 value="0" />
   <UnkInt0 value="0" />
   <UnkShort4 value="0" />
   <UnkInt1 value="900" />
   <UnkInt2 value="0" />
   <CurvesUnkHash0>linear_rise</CurvesUnkHash0>
   <CurvesUnkHash1>linear_fall</CurvesUnkHash1>
   <CurvesUnkHash2>decaying_exponential</CurvesUnkHash2>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <ParameterHash4 />
   <AudioHash>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a4</AudioHash>
   <UnkInt3 value="0" />
   <ParameterHash5 />
   <UnkFloat0 value="-100" />
   <UnkFloat1 value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a2</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="64035" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c3</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="64335" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c#3</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="64435" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d3</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="64535" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d#3</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="64635" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_e3</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="64735" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f3</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="64835" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f#3</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="64935" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g3</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="65035" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g#3</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="65135" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a3</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="65235" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a#3</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="65335" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_b3</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="65435" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c4</Name>
   <Header>
    <Flags value="0x0020C000" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_c#4</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="100" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d4</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="200" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_d#4</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="300" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_e4</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="400" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f4</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="500" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_f#4</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="600" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g4</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="700" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_g#4</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="800" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a4</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="900" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_a#4</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="1000" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_dlc_mrtasty_icevan_note_b4</Name>
   <Header>
    <Flags value="0x0020C010" />
    <Pitch value="1100" />
    <DopplerFactor value="50" />
    <Category>vehicles_horns</Category>
    <Unk18 value="150" />
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>icevan_note</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_icevan_interior_tone_rattle_loop</Name>
   <Header>
    <Flags value="0x00008000" />
    <Category>vehicles_extras_loud</Category>
   </Header>
   <AudioHash0>dlc_mrtasty_icevan_interior_tone_rattle_loop_mt</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_interior_tone_rattle_loop_mt</Item>
    <Item />
   </AudioTracks>
  </Item>
  <Item type="MultitrackSound">
   <Name>dlc_mrtasty_icevan_interior_tone_rattle_loop_mt</Name>
   <Header>
    <Flags value="0x00000001" />
    <Flags2 value="0xAAA0AAAA" />
   </Header>
   <AudioTracks>
    <Item>dlc_mrtasty_icevan_interior_tone_rattle_loop_synth_pts</Item>
    <Item>dlc_mrtasty_icevan_interior_tone_rattle_loop_pts</Item>
    <Item>dlc_mrtasty_icevan_interior_tone_rumble_loop_pts</Item>
   </AudioTracks>
  </Item>
  <Item type="ParameterTransformSound">
   <Name>dlc_mrtasty_icevan_interior_tone_rattle_loop_synth_pts</Name>
   <Header>
    <Flags value="0x00010010" />
    <Pitch value="64735" />
    <LPFCutoff value="1000" />
   </Header>
   <AudioHash>hash_223D8176</AudioHash>
   <Items>
    <Item>
     <ParameterHash>speed</ParameterHash>
     <UnkFloat0 value="0" />
     <UnkFloat1 value="20" />
     <NestedData>
      <Item>
       <UnkFloat0 value="-1" />
       <UnkInt value="0" />
       <ParameterHash />
       <UnkFloat1 value="1E-05" />
       <UnkFloat2 value="1" />
       <Vectors>
        0.3033333, 0
        0.6133333, 1
       </Vectors>
      </Item>
     </NestedData>
    </Item>
   </Items>
  </Item>
  <Item type="ParameterTransformSound">
   <Name>dlc_mrtasty_icevan_interior_tone_rattle_loop_pts</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>dlc_mrtasty_icevan_interior_tone_rattle_loop_tl</AudioHash>
   <Items>
    <Item>
     <ParameterHash>speed</ParameterHash>
     <UnkFloat0 value="0" />
     <UnkFloat1 value="20" />
     <NestedData>
      <Item>
       <UnkFloat0 value="-1" />
       <UnkInt value="0" />
       <ParameterHash />
       <UnkFloat1 value="1E-05" />
       <UnkFloat2 value="1" />
       <Vectors>
        0.21, 0
        0.3887, 0.25
        0.45, 1
       </Vectors>
      </Item>
      <Item>
       <UnkFloat0 value="-1" />
       <UnkInt value="6" />
       <ParameterHash />
       <UnkFloat1 value="0" />
       <UnkFloat2 value="0.03335" />
       <Vectors>
        0.26, 0
        0.6, 0.54
       </Vectors>
      </Item>
     </NestedData>
    </Item>
   </Items>
  </Item>
  <Item type="ParameterTransformSound">
   <Name>dlc_mrtasty_icevan_interior_tone_rumble_loop_pts</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <AudioHash>hash_6F5AD2EE</AudioHash>
   <Items>
    <Item>
     <ParameterHash>speed</ParameterHash>
     <UnkFloat0 value="0" />
     <UnkFloat1 value="20" />
     <NestedData>
      <Item>
       <UnkFloat0 value="-1" />
       <UnkInt value="0" />
       <ParameterHash />
       <UnkFloat1 value="1E-05" />
       <UnkFloat2 value="1" />
       <Vectors>
        0.1, 0
        0.42, 0.3
        0.65, 1
       </Vectors>
      </Item>
     </NestedData>
    </Item>
   </Items>
  </Item>
  <Item type="TwinLoopSound">
   <Name>dlc_mrtasty_icevan_interior_tone_rattle_loop_tl</Name>
   <Header>
    <Flags value="0x00040005" />
    <Flags2 value="0xAAA8AAAA" />
    <Volume value="1400" />
    <HPFCutoff value="200" />
   </Header>
   <UnkShort0 value="500" />
   <UnkShort1 value="4500" />
   <UnkShort2 value="300" />
   <UnkShort3 value="600" />
   <UnkHash>equal_power_rise</UnkHash>
   <ParameterHash0 />
   <ParameterHash1 />
   <ParameterHash2 />
   <ParameterHash3 />
   <AudioTracks>
    <Item>hash_7978CFD1</Item>
    <Item>hash_F62D4938</Item>
   </AudioTracks>
  </Item>
  <Item type="RandomizedSound">
   <Name>police_scanner_category_mrtasty_rnd</Name>
   <Header>
    <Flags value="0x00000000" />
   </Header>
   <UnkByte value="0" />
   <UnkBytes />
   <Items>
    <Item key="police_scanner_vehicle_category_van_b" value="1" />
    <Item key="police_scanner_vehicle_category_truck_b" value="1" />
   </Items>
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_v8_wagon_exhaust_high</Name>
   <Header>
    <Flags value="0x00008004" />
    <Volume value="64735" />
    <Category>vehicles_engines</Category>
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>v8_wagon_exhaust_high</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_v8_wagon_exhaust_low</Name>
   <Header>
    <Flags value="0x00008004" />
    <Volume value="64935" />
    <Category>vehicles_engines</Category>
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>v8_wagon_exhaust_low</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_v8_wagon_engine_high</Name>
   <Header>
    <Flags value="0x00008004" />
    <Volume value="64935" />
    <Category>vehicles_engines</Category>
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>v8_wagon_engine_high</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_v8_wagon_engine_low</Name>
   <Header>
    <Flags value="0x00008004" />
    <Volume value="64335" />
    <Category>vehicles_engines</Category>
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>v8_wagon_engine_low</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_v8_wagon_eng_idle_loop</Name>
   <Header>
    <Flags value="0x00008004" />
    <Volume value="64535" />
    <Category>vehicles_engines</Category>
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>v8_wagon_eng_idle_loop</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_v8_wagon_ex_idle_loop</Name>
   <Header>
    <Flags value="0x00008004" />
    <Volume value="64535" />
    <Category>vehicles_engines</Category>
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>v8_wagon_ex_idle_loop</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="SimpleSound">
   <Name>dlc_mrtasty_mrtasty_v8_wagon_start</Name>
   <Header>
    <Flags value="0x00008004" />
    <Volume value="800" />
    <Category>vehicles_engines</Category>
   </Header>
   <ContainerName>dlc_mrtasty/mrtasty</ContainerName>
   <FileName>v8_wagon_start</FileName>
   <WaveSlotNum value="0" />
  </Item>
  <Item type="WrapperSound">
   <Name>dlc_mrtasty_mrtasty_v8_wagon_revs_off</Name>
   <Header>
    <Flags value="0x00000004" />
    <Volume value="63935" />
   </Header>
   <AudioHash0>dlc_mrtasty_mrtasty_v8_wagon_start</AudioHash0>
   <FrameStartTime value="0" />
   <AudioHash1 />
   <FrameTimeInterval value="0" />
   <Variables />
   <AudioTracks>
    <Item>dlc_mrtasty_mrtasty_v8_wagon_start</Item>
    <Item />
   </AudioTracks>
  </Item>
 </Items>
</Dat54>