<?xml version="1.0" encoding="UTF-8"?>
<!--STATE_OF_MIND's Revisions-->
<CActionResultContainer>
  <aActionResults>
    <Item type="CActionResult">
      <Name>AR_hitby_stealth_kill_a</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_stealth</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_stealth_kill_unarmed_a</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_stealth_kill_a_gardener</Name>
      <Priority value="900" />
      <ClipSet>clipset@missarmenian3_gardener</ClipSet>
      <FirstPersonClipSet>clipset@missarmenian3_gardener_fps</FirstPersonClipSet>
      <Anim>victim_stealth_kill_unarmed_a</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_stealth_kill_b</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_stealth</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_stealth_kill_unarmed_hook_r</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_stealth_kill_c</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_stealth</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_stealth_kill_unarmed_non_lethal_a</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_neck</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_stealth_kill_d</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_stealth</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_stealth_kill_unarmed_non_lethal_b</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_stealth_kill_knife</Name>
      <Priority value="900" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_knife_stealth_kill</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_neck</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_stealth_kill_bat</Name>
      <Priority value="900" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_stealth_kill</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_stealth_kill_small_wpn</Name>
      <Priority value="900" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_stealth_kill</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_failed_takedown_front</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_failed_takedown_front_waisthit</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_GIVE_THREAT_RESPONSE_AFTER_ACTION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_failed_takedown_front_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_knife_failed_takedowns_front</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_GIVE_THREAT_RESPONSE_AFTER_ACTION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_failed_takedown_front_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_failed_front_takedown</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_GIVE_THREAT_RESPONSE_AFTER_ACTION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_failed_takedown_front_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_non_lethal_front_takedown_fail</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_GIVE_THREAT_RESPONSE_AFTER_ACTION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_failed_takedown_rear</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_failed_takedown_rear_r_facehit</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_GIVE_THREAT_RESPONSE_AFTER_ACTION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_failed_takedown_rear_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_knife_failed_takedown_rear</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_GIVE_THREAT_RESPONSE_AFTER_ACTION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_failed_takedown_rear_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_failed_rear_takedown</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_GIVE_THREAT_RESPONSE_AFTER_ACTION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_failed_takedown_rear_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_non_lethal_rear_takedown_fail</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_GIVE_THREAT_RESPONSE_AFTER_ACTION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_b</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_takedown_front_uppercut</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_c</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_takedown_front_backslap</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_d</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_takedown_front_headbutt</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_e</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_takedown_front_slap</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_a</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_takedown_front_elbow</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_g</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_takedown_front_low_punch</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_pelvis</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_h</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_takedown_front_cross_r</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_psycho</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_psycho_front_takedown</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_psycho_b</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_psycho_front_takedown_b</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_psycho_c</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_psycho_front_takedown_c</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_knife</Name>
      <Priority value="900" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_knife_front_takedown</Anim>
      <AnimBlendInRate value="0.2500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_spine_1</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_knife_b</Name>
      <Priority value="900" />
      <ClipSet>melee@knife@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_knife_front_takedown_variation_a</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_spine_1</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_knife_c</Name>
      <Priority value="900" />
      <ClipSet>melee@knife@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_knife_front_takedown_variation_b</Anim>
      <AnimBlendInRate value="0.2500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_bat</Name>
      <Priority value="900" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_front_takedown_bat_headhit</Anim>
      <AnimBlendInRate value="0.3500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_small_wpn</Name>
      <Priority value="900" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_front_takedown</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
	<Item type="CActionResult">
      <Name>AR_hitby_takedown_front_hatchet_a</Name>
      <Priority value="900" />
      <ClipSet>melee@hatchet@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_front_takedown</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
	<Item type="CActionResult">
      <Name>AR_hitby_takedown_front_hatchet_b</Name>
      <Priority value="900" />
      <ClipSet>melee@hatchet@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_front_takedown_b</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.20000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
 </Item>
	<Item type="CActionResult">
      <Name>AR_hitby_takedown_front_machete_a</Name>
      <Priority value="900" />
      <ClipSet>anim@melee@machete@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_front_takedown</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.20000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
	<Item type="CActionResult">
      <Name>AR_hitby_takedown_front_machete_b</Name>
      <Priority value="900" />
      <ClipSet>anim@melee@machete@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_front_takedown_b</Anim>
      <AnimBlendInRate value="0.2500000" />
      <AnimBlendOutRate value="0.200000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_pistol</Name>
      <Priority value="900" />
      <ClipSet>melee@pistol@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_takedown_front_pistol</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_rifle</Name>
      <Priority value="900" />
      <ClipSet>melee@rifle@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_non_lethal_rifle_takedown_front</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_thrown</Name>
      <Priority value="900" />
      <ClipSet>melee@thrown@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@thrown@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_takedown_front</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_dog</Name>
      <Priority value="900" />
      <ClipSet>creatures@rottweiler@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_takedown_from_front</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS RA_ADD_ORIENT_TO_SPINE_Z_OFFSET</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_neck</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_cougar</Name>
      <Priority value="900" />
      <ClipSet>creatures@cougar@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_takedown_from_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS RA_ADD_ORIENT_TO_SPINE_Z_OFFSET</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_neck</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_takedown_rear_lefthook</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
	<Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_hatchet</Name>
      <Priority value="900" />
      <ClipSet>melee@hatchet@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_rear_takedown</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.200000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
	<Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_hatchet_b</Name>
      <Priority value="900" />
      <ClipSet>melee@hatchet@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_rear_takedown_b</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.200000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
	</Item>
	<Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_machete_a</Name>
      <Priority value="900" />
      <ClipSet>anim@melee@machete@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_rear_takedown</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
	<Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_machete_b</Name>
      <Priority value="900" />
      <ClipSet>anim@melee@machete@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_rear_takedown_b</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_b</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_takedown_rear_righthook</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.10000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_c</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_takedown_rear_backhit</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.100000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_neck</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_psycho</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_psycho_rear_takedown</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_psycho_b</Name>
      <Priority value="900" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_psycho_rear_takedown_b</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_knife</Name>
      <Priority value="900" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_knife_rear_takedown</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_spine_1</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_knife_b</Name>
      <Priority value="900" />
      <ClipSet>melee@knife@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_knife_rear_takedown_variation_a</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_neck</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_knife_c</Name>
      <Priority value="900" />
      <ClipSet>melee@knife@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_knife_rear_takedown_variation_b</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.200000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_neck</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_bat</Name>
      <Priority value="900" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_rear_takedown_bat_r_facehit</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_small_wpn</Name>
      <Priority value="900" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_rear_takedown</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_pistol</Name>
      <Priority value="900" />
      <ClipSet>melee@pistol@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_takedown_rear_pistol</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_rifle</Name>
      <Priority value="900" />
      <ClipSet>melee@rifle@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_non_lethal_rifle_takedown_rear</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_thrown</Name>
      <Priority value="900" />
      <ClipSet>melee@thrown@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@thrown@streamed_core_fps</FirstPersonClipSet>
      <Anim>victim_takedown_rear</Anim>
      <AnimBlendInRate value="0.500000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_dog</Name>
      <Priority value="900" />
      <ClipSet>creatures@rottweiler@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_takedown_from_back</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS RA_ADD_ORIENT_TO_SPINE_Z_OFFSET</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_neck</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_cougar</Name>
      <Priority value="900" />
      <ClipSet>creatures@cougar@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_takedown_from_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS RA_ADD_ORIENT_TO_SPINE_Z_OFFSET</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_neck</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_stealth_kill_a</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_stealth</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_stealth_kill_unarmed_a</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STEALTH_KILL RA_IS_LETHAL RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_stealth_kill_a</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_stealth_kill_a_gardener</Name>
      <Priority value="800" />
      <ClipSet>clipset@missarmenian3_gardener</ClipSet>
      <FirstPersonClipSet>clipset@missarmenian3_gardener_fps</FirstPersonClipSet>
      <Anim>plyr_stealth_kill_unarmed_a</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STEALTH_KILL RA_IS_LETHAL RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_stealth_kill_a</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_stealth_kill_b</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_stealth</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_stealth_kill_unarmed_hook_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STEALTH_KILL RA_IS_LETHAL RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_stealth_kill_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_stealth_kill_c</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_stealth</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_stealth_kill_unarmed_non_lethal_a</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STEALTH_KILL RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_stealth_kill_c</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_stealth_kill_d</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_stealth</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_stealth_kill_unarmed_non_lethal_b</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STEALTH_KILL RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_stealth_kill_d</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_stealth_kill_knife</Name>
      <Priority value="800" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_knife_stealth_kill</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STEALTH_KILL RA_IS_LETHAL RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_stealth_kill_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_stealth_kill_bat</Name>
      <Priority value="800" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_stealth_kill</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STEALTH_KILL RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_stealth_kill_small_wpn</Name>
      <Priority value="800" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_stealth_kill</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STEALTH_KILL RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_failed_takedown_front</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_failed_takedown_front_waisthit</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_failed_takedown_front</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_failed_takedown_front_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_knife_failed_takedowns_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_failed_takedown_front_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_failed_takedown_front_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_failed_front_takedown</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_failed_takedown_front_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_failed_takedown_front_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_non_lethal_front_takedown_fail</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_failed_takedown_front_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_failed_takedown_rear</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_failed_takedown_rear_r_facehit</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_failed_takedown_rear</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_failed_takedown_rear_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_knife_failed_takedown_rear</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_failed_takedown_rear_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_failed_takedown_rear_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_failed_rear_takedown</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_failed_takedown_rear_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_failed_takedown_rear_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_non_lethal_rear_takedown_fail</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_failed_takedown_rear_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_a</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_front_elbow</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.100000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_a</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_b</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_front_uppercut</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.100000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_c</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_front_backslap</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.100000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_c</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_d</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_front_headbutt</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.100000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_d</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_e</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_front_slap</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_e</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_g</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_front_low_punch</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.10000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_g</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_h</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_front_cross_r</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.100000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_h</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_psycho</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_psycho_front_takedown</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.100000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_psycho</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_psycho_b</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_psycho_front_takedown_b</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.100000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_psycho_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_psycho_c</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_psycho_front_takedown_c</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_psycho_c</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_knife</Name>
      <Priority value="800" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_knife_front_takedown</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_knife_b</Name>
      <Priority value="800" />
      <ClipSet>melee@knife@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_knife_front_takedown_variation_a</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_knife_var_a</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_knife_c</Name>
      <Priority value="800" />
      <ClipSet>melee@knife@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>plyr_knife_front_takedown_variation_b</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_knife_var_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_bat</Name>
      <Priority value="800" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_front_takedown_bat_headhit</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_small_wpn</Name>
      <Priority value="800" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_front_takedown</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
	<Item type="CActionResult">
      <Name>AR_takedown_front_hatchet_a</Name>
      <Priority value="800" />
      <ClipSet>melee@hatchet@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>plyr_front_takedown</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_hatchet_a</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
	<Item type="CActionResult">
      <Name>AR_takedown_front_hatchet_b</Name>
      <Priority value="800" />
      <ClipSet>melee@hatchet@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>plyr_front_takedown_b</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_hatchet_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
	</Item>
	<Item type="CActionResult">
      <Name>AR_takedown_front_machete_a</Name>
      <Priority value="800" />
      <ClipSet>anim@melee@machete@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>plyr_front_takedown</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_hatchet_a</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
	<Item type="CActionResult">
      <Name>AR_takedown_front_machete_b</Name>
      <Priority value="800" />
      <ClipSet>anim@melee@machete@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>plyr_front_takedown_b</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_hatchet_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_pistol</Name>
      <Priority value="800" />
      <ClipSet>melee@pistol@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@pistol@streamed_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_front_pistol</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.400000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_AIM_INTERRUPT RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_ALLOW_NO_TARGET_BRANCHES RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_pistol</Homing>
      <ActionBranchSet>ABS_standard_armed</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_rifle</Name>
      <Priority value="800" />
      <ClipSet>melee@rifle@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@rifle@streamed_fps</FirstPersonClipSet>
      <Anim>plyr_non_lethal_rifle_takedown_front</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.400000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_AIM_INTERRUPT RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_ALLOW_NO_TARGET_BRANCHES RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_rifle</Homing>
      <ActionBranchSet>ABS_standard_armed</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_thrown</Name>
      <Priority value="800" />
      <ClipSet>melee@thrown@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@thrown@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_front</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_AIM_INTERRUPT RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_h</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_dog</Name>
      <Priority value="800" />
      <ClipSet>creatures@rottweiler@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>takedown_from_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_dog</Homing>
      <ActionBranchSet>ABS_dog</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_cougar</Name>
      <Priority value="800" />
      <ClipSet>creatures@cougar@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>takedown_from_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_front_cougar</Homing>
      <ActionBranchSet>ABS_cougar</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_rear_lefthook</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_b</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>plyr_takedown_rear_righthook</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_c</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>plyr_takedown_rear_backhit</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_c</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_psycho</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_psycho_rear_takedown</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_psycho</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_psycho_b</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_psycho_rear_takedown_b</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_psycho_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_knife</Name>
      <Priority value="800" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_knife_rear_takedown</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_knife_b</Name>
      <Priority value="800" />
      <ClipSet>melee@knife@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_knife_rear_takedown_variation_a</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_knife_var_a</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_knife_c</Name>
      <Priority value="800" />
      <ClipSet>melee@knife@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>plyr_knife_rear_takedown_variation_b</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_knife_var_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_bat</Name>
      <Priority value="800" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_rear_takedown_bat_r_facehit</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_small_wpn</Name>
      <Priority value="800" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_rear_takedown</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
	<Item type="CActionResult">
      <Name>AR_takedown_rear_hatchet_a</Name>
      <Priority value="800" />
      <ClipSet>melee@hatchet@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>plyr_rear_takedown</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_hatchet_a</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
	<Item type="CActionResult">
      <Name>AR_takedown_rear_hatchet_b</Name>
      <Priority value="800" />
      <ClipSet>melee@hatchet@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>plyr_rear_takedown_b</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_hatchet_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
	</Item>
	<Item type="CActionResult">
      <Name>AR_takedown_rear_machete_a</Name>
      <Priority value="800" />
      <ClipSet>anim@melee@machete@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>plyr_rear_takedown</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_hatchet_a</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
	<Item type="CActionResult">
      <Name>AR_takedown_rear_machete_b</Name>
      <Priority value="800" />
      <ClipSet>anim@melee@machete@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>plyr_rear_takedown_b</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_hatchet_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_pistol</Name>
      <Priority value="800" />
      <ClipSet>melee@pistol@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@pistol@streamed_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_rear_pistol</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.400000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_AIM_INTERRUPT RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_ALLOW_NO_TARGET_BRANCHES RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_pistol</Homing>
      <ActionBranchSet>ABS_standard_armed</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_rifle</Name>
      <Priority value="800" />
      <ClipSet>melee@rifle@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@rifle@streamed_fps</FirstPersonClipSet>
      <Anim>plyr_non_lethal_rifle_takedown_rear</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.400000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_AIM_INTERRUPT RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_ALLOW_NO_TARGET_BRANCHES RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_rifle</Homing>
      <ActionBranchSet>ABS_standard_armed</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_thrown</Name>
      <Priority value="800" />
      <ClipSet>melee@thrown@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@thrown@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_rear</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_ALLOW_AIM_INTERRUPT RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_ENDS_IN_IDLE_POSE RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_dog</Name>
      <Priority value="800" />
      <ClipSet>creatures@rottweiler@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>takedown_from_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_dog</Homing>
      <ActionBranchSet>ABS_dog</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_cougar</Name>
      <Priority value="800" />
      <ClipSet>creatures@cougar@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>takedown_from_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_rear_cougar</Homing>
      <ActionBranchSet>ABS_cougar</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_heavy_finish</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_heavy_finishing_move</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_light_finish</Name>
      <Priority value="800" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_light_finishing_punch</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_head</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_heavy_recoil_left</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>recoil_heavy_left</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_IS_RECOIL RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_heavy_recoil_right</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>recoil_heavy_right</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_IS_RECOIL RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_recoil_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>recoil_knife</Anim>
      <AnimBlendInRate value="0.050000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_IS_RECOIL RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_recoil_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>recoil_swipe</Anim>
      <AnimBlendInRate value="0.050000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_IS_RECOIL RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_recoil_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>recoil_swipe</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_IS_RECOIL RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_recoil_kick_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>recoil_kick</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.010000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_IS_RECOIL RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_recoil_kick_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>recoil_kick</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.010000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_IS_RECOIL RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_walking</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_heavy_punch_c</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_forward_short</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_heavy_punch_a</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_ALLOW_DAZED_REACTION</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_running</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_heavy_punch_a</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_ALLOW_DAZED_REACTION</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_unarmed_counter_left_unarmed</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_counter_attack_l</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.25000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_unarmed_counter_left_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_counter_attack_l</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_unarmed_counter_left_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_counter_attack_l</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.25000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_unarmed_counter_left_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_counter_attack_l</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.25000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_knife_counter_left_unarmed</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_counter_attack_knife_l</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_knife_counter_left_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_counter_attack_knife_l</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_knife_counter_left_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_counter_attack_knife_l</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_knife_counter_left_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_counter_attack_knife_l</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_unarmed_counter_right_unarmed</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_counter_attack_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_unarmed_counter_right_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_counter_attack_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.25000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_unarmed_counter_right_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_counter_attack_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_unarmed_counter_right_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_counter_attack_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.25000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_knife_counter_right_unarmed</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_counter_attack_knife_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.25000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_knife_counter_right_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_counter_attack_knife_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.25000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_knife_counter_right_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_counter_attack_knife_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_knife_counter_right_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_counter_attack_knife_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_heavy_1a</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_heavy_punch_a</Anim>
      <AnimBlendInRate value="0.050000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_DAZED_REACTION</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_heavy_1b</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_heavy_punch_a_var_1</Anim>
      <AnimBlendInRate value="0.050000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_USE_KINEMATIC_PHYSICS RA_QUIT_TASK_AFTER_ACTION RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_DAZED_REACTION</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_heavy_2a</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_heavy_punch_b</Anim>
      <AnimBlendInRate value="0.050000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_DAZED_REACTION RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_heavy_2b</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_heavy_punch_b_var_1</Anim>
      <AnimBlendInRate value="0.050000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_DAZED_REACTION RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_heavy_2c</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_heavy_punch_c_var_1</Anim>
      <AnimBlendInRate value="0.050000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_DAZED_REACTION RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_heavy_3a</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_heavy_punch_c</Anim>
      <AnimBlendInRate value="0.050000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_DAZED_REACTION</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_heavy_3b</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_heavy_punch_c_var_1</Anim>
      <AnimBlendInRate value="0.050000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_ADDITIVE_ANIMS RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_DAZED_REACTION</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_daze_a</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>damage_01</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_IS_DAZED_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_daze_b</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>damage_02</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_IS_DAZED_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_daze_c</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>damage_03</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_IS_DAZED_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_daze_d</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>damage_04</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_IS_DAZED_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_hitby_rear</Name>
      <Priority value="700" />
      <ClipSet>creatures@rottweiler@melee@base@</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_from_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_dog</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_hitby_front</Name>
      <Priority value="700" />
      <ClipSet>creatures@rottweiler@melee@base@</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_from_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_dog</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_hitby_left</Name>
      <Priority value="700" />
      <ClipSet>creatures@rottweiler@melee@base@</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_from_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_dog</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_hitby_right</Name>
      <Priority value="700" />
      <ClipSet>creatures@rottweiler@melee@base@</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_from_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_dog</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_cougar_hitby_rear</Name>
      <Priority value="700" />
      <ClipSet>creatures@cougar@melee@base@</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_from_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_cougar</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_cougar_hitby_front</Name>
      <Priority value="700" />
      <ClipSet>creatures@cougar@melee@base@</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_from_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_cougar</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_cougar_hitby_left</Name>
      <Priority value="700" />
      <ClipSet>creatures@cougar@melee@base@</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_from_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_cougar</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_cougar_hitby_right</Name>
      <Priority value="700" />
      <ClipSet>creatures@cougar@melee@base@</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_from_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_cougar</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_no_melee</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_no_melee_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_no_melee_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>non_melee_damage_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_no_melee_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>non_melee_damage_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_no_melee_pistol</Name>
      <Priority value="700" />
      <ClipSet>melee@pistol@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@pistol@streamed_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_no_melee_rifle</Name>
      <Priority value="700" />
      <ClipSet>melee@rifle@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@rifle@streamed_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_rear_no_melee</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_rear_no_melee_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_rear_no_melee_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>non_melee_damage_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_rear_no_melee_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>non_melee_damage_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_rear_no_melee_pistol</Name>
      <Priority value="700" />
      <ClipSet>melee@pistol@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@pistol@streamed_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_rear_no_melee_rifle</Name>
      <Priority value="700" />
      <ClipSet>melee@rifle@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@rifle@streamed_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_rear_melee</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>melee_damage_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_melee</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>melee_damage_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_melee</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>melee_damage_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_rear_melee_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>melee_damage_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_melee_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>melee_damage_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_melee_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>melee_damage_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_rear_melee_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_damage_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_melee_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_damage_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_melee_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_damage_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_rear_melee_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_damage_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_melee_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_damage_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_melee_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_damage_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_melee</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>hit_heavy_punch_b</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_melee_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>melee_damage_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_melee_bat_var_a</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>melee_damage_front_var_a</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_melee_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_damage_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_melee_small_wpn_var_a</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_damage_front_var_a</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_melee_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_damage_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_front_melee_knife_var_a</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_damage_front_var_a</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_no_melee</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_no_melee_moving</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_walk_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_no_melee_moving_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>non_melee_damage_walk_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_no_melee_moving_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>non_melee_damage_walk_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_no_melee_moving_large_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_walk_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_no_melee_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_no_melee_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>non_melee_damage_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_no_melee_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>non_melee_damage_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_no_melee_pistol</Name>
      <Priority value="700" />
      <ClipSet>melee@pistol@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@pistol@streamed_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_left_no_melee_rifle</Name>
      <Priority value="700" />
      <ClipSet>melee@rifle@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@rifle@streamed_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_no_melee</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_no_melee_moving</Name>
      <Priority value="700" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_walk_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_no_melee_moving_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>non_melee_damage_walk_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_no_melee_moving_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>non_melee_damage_walk_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_no_melee_moving_large_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_walk_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_no_melee_bat</Name>
      <Priority value="700" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_no_melee_small_wpn</Name>
      <Priority value="700" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>non_melee_damage_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_no_melee_knife</Name>
      <Priority value="700" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet />
      <Anim>non_melee_damage_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_no_melee_pistol</Name>
      <Priority value="700" />
      <ClipSet>melee@pistol@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@pistol@streamed_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_right_no_melee_rifle</Name>
      <Priority value="700" />
      <ClipSet>melee@rifle@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@rifle@streamed_fps</FirstPersonClipSet>
      <Anim>non_melee_damage_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_counter_hit_reaction</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_any_no_anim</Name>
      <Priority value="700" />
      <ClipSet />
      <FirstPersonClipSet />
      <Anim />
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_AIM_INTERRUPT RA_IS_NON_MELEE_HIT_REACTION RA_ALLOW_STRAFING RA_QUIT_TASK_AFTER_ACTION</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_walking</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>walking_punch</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_TAG_SYNC_BLEND_OUT</ResultAttrs>
      <Homing>HOM_face_walking</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_walking_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_walking_attack_a</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_walking_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_walking_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_walking_attack</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_walking_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_walking_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_walking_attack_a</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_walking_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
	</Item>
    <Item type="CActionResult">
      <Name>AR_walking_machete</Name>
      <Priority value="500" />
      <ClipSet>anim@melee@machete@streamed_core</ClipSet>
      <FirstPersonClipSet>anim@melee@machete@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_walking_attack_a</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_walking_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_running</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>running_punch</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_running</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_running_long_range</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>running_punch_long_range</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_running_long_range</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_running_long_range_large_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_running_attack_long_range</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_TAG_SYNC_BLEND_OUT RA_UPDATE_MOVEBLENDRATIO</ResultAttrs>
      <Homing>HOM_face_running_long_range_large_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_FRONT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_running_shove</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>running_shove</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_running_shove</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_shove</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_very_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_running_shove_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>running_shove</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_running_shove</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_shove</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_very_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_running_shove_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>running_shove</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_running_shove</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_shove</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_very_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_running_shove_large_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>running_shove</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_running_shove</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_shove</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_very_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_running_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_running_attack_a</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_running_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_running_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_running_attack</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_running_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_FRONT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_running_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_running_attack_a</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_running_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_short</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>short_0_punch</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_0</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_HOOK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_short_no_target</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>short_0_punch</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_HOOK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_walking_no_target</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>walking_punch_no_target</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.000500" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_TAG_SYNC_BLEND_OUT RA_UPDATE_MOVEBLENDRATIO RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_counter</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_HOOK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_walking_no_target_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_walking_attack_a</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.005000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_counter_armed</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_walking_no_target_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_walking_attack</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_walking_no_target_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_walking_attack_a</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
	</Item>
    <Item type="CActionResult">
      <Name>AR_walking_no_target_machete</Name>
      <Priority value="500" />
      <ClipSet>anim@melee@machete@streamed_core</ClipSet>
      <FirstPersonClipSet>anim@melee@machete@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_walking_attack_a</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_running_no_target</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>running_punch_no_target</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.600000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_MELEE_TARGET_EVALUATION RA_TAG_SYNC_BLEND_OUT RA_UPDATE_MOVEBLENDRATIO RA_ENDS_IN_IDLE_POSE</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_HOOK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_short_surface_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>knife_short_range_0</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_short_surface_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>short_0_attack</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_short_surface_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_short_range_0</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_FACE</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_short_surface_machete</Name>
      <Priority value="500" />
      <ClipSet>anim@melee@machete@streamed_core</ClipSet>
      <FirstPersonClipSet>anim@melee@machete@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_short_range_0</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>HAMMER_FACE</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
	</Item>
    <Item type="CActionResult">
      <Name>AR_forward_short_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>knife_short_range_0</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_0_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_short_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>short_0_attack</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_0_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_short_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_short_range_0</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_0_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_FACE</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
	 </Item>
    <Item type="CActionResult">
      <Name>AR_forward_short_machete</Name>
      <Priority value="500" />
      <ClipSet>anim@melee@machete@streamed_core</ClipSet>
      <FirstPersonClipSet>anim@melee@machete@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_short_range_0</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_0_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>HAMMER_FACE</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_short_thrown</Name>
      <Priority value="500" />
      <ClipSet>melee@thrown@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@thrown@streamed_core_fps</FirstPersonClipSet>
      <Anim>plyr_takedown_front</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_takedown_front_h</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>	
    <Item type="CActionResult">
      <Name>AR_forward_close_mid</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_0</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_0</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_bat_mid</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_0</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.010000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_0</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_bat_mid_running</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_0_running</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_0</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_small_wpn_mid</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_0</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_0</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_small_wpn_mid_running</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_0_running</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_0</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_knife_mid</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_0</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_counter_right_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_knife_mid_running</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_0_running</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_0</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_low</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_low_var_a</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0_var_a</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_var_a</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_low_var_b</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0_var_b</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.10000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_var_b</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_low_psycho</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0_psycho</Anim>
      <AnimBlendInRate value="0.15000" />
      <AnimBlendOutRate value="0.020000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_psycho</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_knife_low</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.200000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_counter_right_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_forward_close_low</Name>
      <Priority value="500" />
      <ClipSet>creatures@rottweiler@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>ground_attack_0</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD RA_ALLOW_SCRIPT_TASK_ABORT</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_dog</Homing>
      <ActionBranchSet>ABS_dog</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_animal_head</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_cougar_forward_close_low</Name>
      <Priority value="500" />
      <ClipSet>creatures@cougar@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>ground_attack_0</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD RA_ALLOW_SCRIPT_TASK_ABORT</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_cougar</Homing>
      <ActionBranchSet>ABS_cougar</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_animal_head</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_knife_low_var_a</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_var_a</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.200000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_var_a_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_bat_low</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_bat_low_var_a</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0_var_a</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_small_wpn_low</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_var_a</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_close_small_wpn_low_var_a</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0_var_a</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_var_a</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_very_close_low</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_on_spot</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_on_spot</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_very_close_low_var_a</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_on_spot_var_a</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_on_spot</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_very_close_low_psycho_a</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_on_spot_psycho_a</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_on_spot_psycho</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_very_close_low_psycho_b</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_on_spot_psycho_b</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_on_spot_psycho</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_very_close_low_psycho_c</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_on_spot_psycho_c</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_on_spot_psycho</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_very_close_knife_low</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_on_spot</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_on_spot</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_very_close_knife_low_var_a</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_on_spot_var_a</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_on_spot</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_very_close_small_wpn_low</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_on_spot</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_on_spot</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_very_close_bat_low_body</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_on_spot_body</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_on_spot</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_very_close_bat_low_car</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_on_spot</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_on_spot</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_medium_low</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0_long</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.200000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_long</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_medium_low_psycho</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core_psycho</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0_long_psycho</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.200000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_long_psycho</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_medium</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>long_0_punch</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.200000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_0</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_medium_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>knife_long_range_0</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_0_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_medium_knife_low</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0_long</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_long</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_medium_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>long_0_attack</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_0_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_medium_bat_low</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0_long</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_long_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_medium_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_long_range_0</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_0_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_forward_medium_small_wpn_low</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_0_long</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_long_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_medium</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>long_-180_punch</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_-180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_medium_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>knife_long_range_-180</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.2500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_-180_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_medium_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>long_-180_attack</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_-180_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_medium_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_long_range_-180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_-180_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_close_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_-180</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_-180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_close_bat_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_-180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_-180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_close_small_wpn_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_-180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_-180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_close_knife_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_-180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_-180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_medium</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>long_180_punch</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_left_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_medium_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>knife_long_range_180</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_180_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_medium_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>long_180_attack</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_180_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_medium_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_long_range_+180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_180_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_close_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_180</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_close_bat_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_close_small_wpn_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_close_knife_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_close</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>short_-180_punch</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_-180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_left_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_ELBOW</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_close_knife</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>knife_short_range_-180</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_-180_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_close_bat</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>short_-180_attack</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_-180_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_close_small_wpn</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_short_range_-180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_-180_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_close_low</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_-180</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_-180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_close_knife_low</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_-180</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_-180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_close_bat_low</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_-180</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_-180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_left_close_small_wpn_low</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_-180</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_-180_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_close</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>short_180_punch</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_right_elbow</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_close_knife</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>knife_short_range_180</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_180_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_close_bat</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>short_180_attack</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_180_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_close_small_wpn</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_short_range_+180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_180_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_close_low</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_180</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_close_knife_low</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_close_bat_low</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_180</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rear_right_close_small_wpn_low</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_180</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_180_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_medium</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>long_-90_punch</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_-90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_medium_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>knife_long_range_-90</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_-90_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_medium_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>long_-90_attack</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_-90_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_medium_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_long_range_-90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_-90_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_close_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_-90</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_-90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_close_bat_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_-90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_-90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_close_small_wpn_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_-90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_-90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_close_knife_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_-90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_-90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_close</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>short_-90_punch</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_-90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_left_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_ELBOW</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_close_knife</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>knife_short_range_-90</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_-90_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_close_bat</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>short_-90_attack</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_-90_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_close_small_wpn</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_short_range_-90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_-90_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_close_low</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_-90</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_-90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_close_knife_low</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_-90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_-90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_close_bat_low</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_-90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_-90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_left_close_small_wpn_low</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_-90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_-90_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_medium</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>long_90_punch</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_medium_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>knife_long_range_90</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_90_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_medium_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>long_90_attack</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_90_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_medium_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_long_range_+90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_long_cardinal_90_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_close_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_90</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_close_knife_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_close_small_wpn_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_close_bat_mid</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>low_attack_90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_mid_cardinal_90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>KICK_STANDING</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_close</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>short_90_punch</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.1500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_heavy_cardinal</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_ELBOW</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_close_knife</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>knife_short_range_90</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_90_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet>Knife</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_close_bat</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>short_90_attack</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_90_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_close_small_wpn</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>small_melee_wpn_short_range_+90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_short_cardinal_90_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_close_low</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_90</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_close_knife_low</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_USE_LEG_IK RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_left_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet>PRONE_BOOSTER_SHOT</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_close_bat_low</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_90</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_bat</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_right_close_small_wpn_low</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>ground_attack_90</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.300000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD</ResultAttrs>
      <Homing>HOM_ground_cardinal_turn_90_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_counter_right</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>counter_attack_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_IS_COUNTER_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_counter_right</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_counter</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_HOOK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_counter_right_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>counter_attack_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_IS_COUNTER_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_counter_right_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_counter_armed</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_counter_right_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>counter_attack_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_IS_COUNTER_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_counter_right_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_counter_armed</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_counter_right_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>counter_attack_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_IS_COUNTER_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_counter_right_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_counter_armed</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_counter_left</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>counter_attack_l</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_IS_COUNTER_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME</ResultAttrs>
      <Homing>HOM_counter_left</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_counter</DamageAndReaction>
      <StrikeBoneSet>SB_left_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_HOOK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_counter_left_knife</Name>
      <Priority value="500" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>counter_attack_l</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_IS_COUNTER_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_counter_left_knife</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_counter_armed</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_counter_left_small_wpn</Name>
      <Priority value="500" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>counter_attack_l</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_IS_COUNTER_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_counter_left</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_counter_armed</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>HAMMER_TOWN</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_counter_left_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>counter_attack_l</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_IS_COUNTER_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_counter_left_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_counter_armed</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet>BAT_SMASH</NmReactionSet>
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dodge_left</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>dodge_generic_l</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_BLOCK RA_ALLOW_STRAFING</ResultAttrs>
      <Homing>HOM_face_medium_dodge</Homing>
      <ActionBranchSet>ABS_dodge_left</ActionBranchSet>
      <DamageAndReaction>DAR_dodge</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dodge_left_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>dodge_generic_l</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_BLOCK RA_ALLOW_STRAFING</ResultAttrs>
      <Homing>HOM_face_medium_dodge</Homing>
      <ActionBranchSet>ABS_dodge_left</ActionBranchSet>
      <DamageAndReaction>DAR_dodge</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dodge_right</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>dodge_generic_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_BLOCK RA_ALLOW_STRAFING</ResultAttrs>
      <Homing>HOM_face_medium_dodge</Homing>
      <ActionBranchSet>ABS_dodge_right</ActionBranchSet>
      <DamageAndReaction>DAR_dodge</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dodge_right_bat</Name>
      <Priority value="500" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>dodge_generic_r</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_BLOCK RA_ALLOW_STRAFING</ResultAttrs>
      <Homing>HOM_face_medium_dodge</Homing>
      <ActionBranchSet>ABS_dodge_right</ActionBranchSet>
      <DamageAndReaction>DAR_dodge</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_heavy_finish</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>heavy_finishing_punch</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.20000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_KNOCKOUT RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_knockout_far</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knockout</DamageAndReaction>
      <StrikeBoneSet>SB_left_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_LOW</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_light_finish</Name>
      <Priority value="500" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>light_finishing_punch</Anim>
      <AnimBlendInRate value="0.150000" />
      <AnimBlendOutRate value="0.20000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_KNOCKOUT RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_knockout</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_knockout</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_HOOK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_pistol_melee_aiming</Name>
      <Priority value="200" />
      <ClipSet>melee@pistol@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@pistol@streamed_fps</FirstPersonClipSet>
      <Anim>pistol_aim_whip</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_ALLOW_AIM_INTERRUPT RA_FORCE_DAMAGE RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_NO_TARGET_BRANCHES RA_USE_CACHED_DESIRED_HEADING</ResultAttrs>
      <Homing>HOM_face_pistol_whip</Homing>
      <ActionBranchSet>ABS_standard_armed</ActionBranchSet>
      <DamageAndReaction>DAR_armed_melee</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_POOL_CUE</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_pistol_melee_idle</Name>
      <Priority value="200" />
      <ClipSet>melee@pistol@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@pistol@streamed_fps</FirstPersonClipSet>
      <Anim>pistol_idle_whip</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.400000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_ALLOW_AIM_INTERRUPT RA_FORCE_DAMAGE RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_NO_TARGET_BRANCHES RA_USE_CACHED_DESIRED_HEADING</ResultAttrs>
      <Homing>HOM_face_pistol_whip</Homing>
      <ActionBranchSet>ABS_standard_armed</ActionBranchSet>
      <DamageAndReaction>DAR_armed_melee</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_POOL_CUE</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rifle_melee_aiming</Name>
      <Priority value="200" />
      <ClipSet>melee@rifle@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@rifle@streamed_fps</FirstPersonClipSet>
      <Anim>rifle_aim_whip</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_ALLOW_AIM_INTERRUPT RA_FORCE_DAMAGE RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_NO_TARGET_BRANCHES RA_USE_CACHED_DESIRED_HEADING</ResultAttrs>
      <Homing>HOM_face_rifle_whip</Homing>
      <ActionBranchSet>ABS_standard_armed</ActionBranchSet>
      <DamageAndReaction>DAR_armed_melee</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_POOL_CUE</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rifle_melee_idle</Name>
      <Priority value="200" />
      <ClipSet>melee@rifle@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@rifle@streamed_fps</FirstPersonClipSet>
      <Anim>rifle_idle_whip</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.400000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_ALLOW_AIM_INTERRUPT RA_FORCE_DAMAGE RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_NO_TARGET_BRANCHES RA_USE_CACHED_DESIRED_HEADING</ResultAttrs>
      <Homing>HOM_face_rifle_whip</Homing>
      <ActionBranchSet>ABS_standard_armed</ActionBranchSet>
      <DamageAndReaction>DAR_armed_melee</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_POOL_CUE</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_foward_surface_side_a</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>vehicle_kick</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_kick_surface</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_foward_surface_side_knife_a</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>vehicle_kick</Anim>
      <AnimBlendInRate value="0.300000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_kick_surface</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_foward_surface_side_bat_a</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>car_side_attack_a</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface_vertical_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_foward_surface_side_small_wpn_a</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>car_side_attack_a</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface_vertical_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_foward_surface_side_b</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>vehicle_kick_var_a</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_kick_surface</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_foward_surface_side_knife_b</Name>
      <Priority value="200" />
      <ClipSet>melee@knife@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@knife@streamed_core_fps</FirstPersonClipSet>
      <Anim>vehicle_kick_var_a</Anim>
      <AnimBlendInRate value="0.200000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_right_leg</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KICK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_foward_surface_side_bat_b</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>car_side_attack_b</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface_vertical_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_foward_surface_side_small_wpn_b</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>car_side_attack_b</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface_vertical_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_foward_surface_down_bat</Name>
      <Priority value="200" />
      <ClipSet>melee@large_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@large_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>car_down_attack</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface_horizontal_bat</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_foward_surface_down_small_wpn</Name>
      <Priority value="200" />
      <ClipSet>melee@small_wpn@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@small_wpn@streamed_core_fps</FirstPersonClipSet>
      <Anim>car_down_attack</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_face_surface_horizontal_small_wpn</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_small_wpn</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_melee_weapon</StrikeBoneSet>
      <Sound>MELEE_COMBAT_WOODEN_BAT</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_heavy_1a</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>heavy_punch_a</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_heavy_a</Homing>
      <ActionBranchSet>ABS_standard_heavy_combo_2a</ActionBranchSet>
      <DamageAndReaction>DAR_heavy</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_shove</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>shove</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING</ResultAttrs>
      <Homing>HOM_shove</Homing>
      <ActionBranchSet>ABS_standard</ActionBranchSet>
      <DamageAndReaction>DAR_shove</DamageAndReaction>
      <StrikeBoneSet>SB_left_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_SHOVE</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_heavy_1b</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>heavy_punch_a_var_1</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_heavy_a_var_1</Homing>
      <ActionBranchSet>ABS_standard_heavy_combo_2b</ActionBranchSet>
      <DamageAndReaction>DAR_heavy</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_heavy_2a</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>heavy_punch_b</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_heavy_b</Homing>
      <ActionBranchSet>ABS_standard_heavy_combo_3a</ActionBranchSet>
      <DamageAndReaction>DAR_heavy</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_LOW</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_heavy_2b</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>heavy_punch_b_var_1</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_heavy_b_var_1</Homing>
      <ActionBranchSet>ABS_standard_heavy_combo_3b</ActionBranchSet>
      <DamageAndReaction>DAR_heavy</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_heavy_2c</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>heavy_punch_b_var_2</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_heavy_b_var_2</Homing>
      <ActionBranchSet>ABS_standard_heavy_combo_3b</ActionBranchSet>
      <DamageAndReaction>DAR_heavy</DamageAndReaction>
      <StrikeBoneSet>SB_left_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_JAB</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_heavy_3a</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_core</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>heavy_punch_c</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_INITIAL_ACTION_COMBO RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_heavy_c</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_heavy</DamageAndReaction>
      <StrikeBoneSet>SB_left_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_HOOK</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_heavy_3b</Name>
      <Priority value="200" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet>melee@unarmed@streamed_core_fps</FirstPersonClipSet>
      <Anim>heavy_punch_c_var_1</Anim>
      <AnimBlendInRate value="0.1500000" />
      <AnimBlendOutRate value="0.150000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_VALID_FOR_RECOIL RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_ALLOW_INITIAL_ACTION_COMBO RA_ALLOW_MELEE_TARGET_EVALUATION</ResultAttrs>
      <Homing>HOM_heavy_c_var_1</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_heavy</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_UPPERCUT</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_swim_dive_still</Name>
      <Priority value="200" />
      <ClipSet>move_ped_diving</ClipSet>
      <FirstPersonClipSet>move_ped_swimming_first_person</FirstPersonClipSet>
      <Anim>MELEE_UNDERWATER_STILL</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_INTERRUPT_WHEN_OUT_OF_WATER</ResultAttrs>
      <Homing>HOM_swimming_underwater</Homing>
      <ActionBranchSet>ABS_swim_dive</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_swimming_knife</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE_UNDERWATER</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_swim_dive_still_unarmed</Name>
      <Priority value="200" />
      <ClipSet>move_ped_diving</ClipSet>
      <FirstPersonClipSet>move_ped_swimming_first_person</FirstPersonClipSet>
      <Anim>MELEE_UNDERWATER_STILL_UNARMED</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_INTERRUPT_WHEN_OUT_OF_WATER</ResultAttrs>
      <Homing>HOM_swimming_underwater</Homing>
      <ActionBranchSet>ABS_swim_dive</ActionBranchSet>
      <DamageAndReaction>DAR_heavy</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE_UNDERWATER</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_swim_dive_still_var_b</Name>
      <Priority value="200" />
      <ClipSet>move_ped_diving</ClipSet>
      <FirstPersonClipSet>move_ped_swimming_first_person</FirstPersonClipSet>
      <Anim>MELEE_UNDERWATER_STILL_VAR_B</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_INTERRUPT_WHEN_OUT_OF_WATER</ResultAttrs>
      <Homing>HOM_swimming_underwater</Homing>
      <ActionBranchSet>ABS_swim_dive</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_swimming_knife</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE_UNDERWATER</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_swim_dive_moving</Name>
      <Priority value="200" />
      <ClipSet>move_ped_diving</ClipSet>
      <FirstPersonClipSet>move_ped_swimming_first_person</FirstPersonClipSet>
      <Anim>MELEE_UNDERWATER_MOVING</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_INTERRUPT_WHEN_OUT_OF_WATER</ResultAttrs>
      <Homing>HOM_swimming_underwater</Homing>
      <ActionBranchSet>ABS_swim_dive</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_swimming_knife</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE_UNDERWATER</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_swim_dive_moving_unarmed</Name>
      <Priority value="200" />
      <ClipSet>move_ped_diving</ClipSet>
      <FirstPersonClipSet>move_ped_swimming_first_person</FirstPersonClipSet>
      <Anim>MELEE_UNDERWATER_MOVING_UNARMED</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_INTERRUPT_WHEN_OUT_OF_WATER</ResultAttrs>
      <Homing>HOM_swimming_underwater</Homing>
      <ActionBranchSet>ABS_swim_dive</ActionBranchSet>
      <DamageAndReaction>DAR_heavy</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_large</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE_UNDERWATER</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_swim_dive_moving_var_b</Name>
      <Priority value="200" />
      <ClipSet>move_ped_diving</ClipSet>
      <FirstPersonClipSet>move_ped_swimming_first_person</FirstPersonClipSet>
      <Anim>MELEE_UNDERWATER_MOVING_VAR_B</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_INTERRUPT_WHEN_OUT_OF_WATER</ResultAttrs>
      <Homing>HOM_swimming_underwater</Homing>
      <ActionBranchSet>ABS_swim_dive</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_swimming_knife</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE_UNDERWATER</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_swim_surface_still</Name>
      <Priority value="200" />
      <ClipSet>move_ped_swimming</ClipSet>
      <FirstPersonClipSet>move_ped_swimming_first_person</FirstPersonClipSet>
      <Anim>MELEE_SURFACE_STILL</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_INTERRUPT_WHEN_OUT_OF_WATER</ResultAttrs>
      <Homing>HOM_swimming_surface_still</Homing>
      <ActionBranchSet>ABS_swim_surface</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_swimming_knife</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE_WATER</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_swim_surface_still_unarmed</Name>
      <Priority value="200" />
      <ClipSet>move_ped_swimming</ClipSet>
      <FirstPersonClipSet>move_ped_swimming_first_person</FirstPersonClipSet>
      <Anim>MELEE_SURFACE_STILL_UNARMED</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_INTERRUPT_WHEN_OUT_OF_WATER</ResultAttrs>
      <Homing>HOM_swimming_surface_still</Homing>
      <ActionBranchSet>ABS_swim_surface</ActionBranchSet>
      <DamageAndReaction>DAR_heavy</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE_WATER</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_swim_surface_still_var_b</Name>
      <Priority value="200" />
      <ClipSet>move_ped_swimming</ClipSet>
      <FirstPersonClipSet>move_ped_swimming_first_person</FirstPersonClipSet>
      <Anim>MELEE_SURFACE_STILL_VAR_B</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_INTERRUPT_WHEN_OUT_OF_WATER</ResultAttrs>
      <Homing>HOM_swimming_surface_still</Homing>
      <ActionBranchSet>ABS_swim_surface</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_swimming_knife</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE_WATER</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_swim_surface_moving_unarmed</Name>
      <Priority value="200" />
      <ClipSet>move_ped_swimming</ClipSet>
      <FirstPersonClipSet>move_ped_swimming_first_person</FirstPersonClipSet>
      <Anim>MELEE_SURFACE_MOVING_UNARMED</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_INTERRUPT_WHEN_OUT_OF_WATER</ResultAttrs>
      <Homing>HOM_swimming_surface_moving</Homing>
      <ActionBranchSet>ABS_swim_surface</ActionBranchSet>
      <DamageAndReaction>DAR_heavy</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE_WATER</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_swim_surface_moving</Name>
      <Priority value="200" />
      <ClipSet>move_ped_swimming</ClipSet>
      <FirstPersonClipSet>move_ped_swimming_first_person</FirstPersonClipSet>
      <Anim>MELEE_SURFACE_MOVING</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_INTERRUPT_WHEN_OUT_OF_WATER</ResultAttrs>
      <Homing>HOM_swimming_surface_moving</Homing>
      <ActionBranchSet>ABS_swim_surface</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_swimming_knife</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE_WATER</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_swim_surface_moving_var_b</Name>
      <Priority value="200" />
      <ClipSet>move_ped_swimming</ClipSet>
      <FirstPersonClipSet>move_ped_swimming_first_person</FirstPersonClipSet>
      <Anim>MELEE_SURFACE_MOVING_VAR_B</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE RA_QUIT_TASK_AFTER_ACTION RA_INTERRUPT_WHEN_OUT_OF_WATER</ResultAttrs>
      <Homing>HOM_swimming_surface_moving</Homing>
      <ActionBranchSet>ABS_swim_surface</ActionBranchSet>
      <DamageAndReaction>DAR_knife</DamageAndReaction>
      <StrikeBoneSet>SB_right_arm_swimming_knife</StrikeBoneSet>
      <Sound>MELEE_COMBAT_KNIFE_WATER</Sound>
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    
    <Item type="CActionResult">
      <Name>AR_dog_attack</Name>
      <Priority value="100" />
      <ClipSet>creatures@rottweiler@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>attack</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_FORCE_DAMAGE</ResultAttrs>
      <Homing>HOM_face_medium_dog</Homing>
      <ActionBranchSet>ABS_dog</ActionBranchSet>
      <DamageAndReaction>DAR_dog_bite</DamageAndReaction>
      <StrikeBoneSet>SB_animal_paws</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_cougar_attack</Name>
      <Priority value="100" />
      <ClipSet>creatures@cougar@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>attack</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_KNOCKOUT RA_IS_STANDARD_ATTACK RA_FORCE_DAMAGE</ResultAttrs>
      <Homing>HOM_face_medium_cougar</Homing>
      <ActionBranchSet>ABS_cougar</ActionBranchSet>
      <DamageAndReaction>DAR_cougar_attack</DamageAndReaction>
      <StrikeBoneSet>SB_cougar_paws</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_plyr_intro</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@base</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_intro_plyr</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_INTRO RA_PLAYER_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING RA_ALLOW_NO_TARGET_INTERRUPT</ResultAttrs>
      <Homing />
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_ai_intro</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@base</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_intro_ai</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_INTRO RA_PLAYER_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING RA_ALLOW_NO_TARGET_INTERRUPT</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_ai_intro_var_a</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_intro_ai_var_a</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_INTRO RA_PLAYER_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING RA_ALLOW_NO_TARGET_INTERRUPT</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_ai_intro_var_b</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_intro_ai_var_b</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_INTRO RA_PLAYER_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING RA_ALLOW_NO_TARGET_INTERRUPT</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_ai_intro_var_c</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_variations</ClipSet>
      <FirstPersonClipSet />
      <Anim>melee_intro_ai_var_c</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_INTRO RA_PLAYER_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING RA_ALLOW_NO_TARGET_INTERRUPT</ResultAttrs>
      <Homing>HOM_face_no_cache</Homing>
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_taunt_a</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_01</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING RA_ALLOW_NO_TARGET_INTERRUPT</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction>DAR_special_ability_recharge</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_taunt_b</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_02</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING RA_ALLOW_NO_TARGET_INTERRUPT</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction>DAR_special_ability_recharge</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_taunt_c</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_03</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING RA_ALLOW_NO_TARGET_INTERRUPT</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction>DAR_special_ability_recharge</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_taunt_d</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_04</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING RA_ALLOW_NO_TARGET_INTERRUPT</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction>DAR_special_ability_recharge</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_taunt_e</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_05</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING RA_ALLOW_NO_TARGET_INTERRUPT</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction>DAR_special_ability_recharge</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_taunt_f</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_06</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING RA_ALLOW_NO_TARGET_INTERRUPT</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction>DAR_special_ability_recharge</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_taunt_g</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_07</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING RA_ALLOW_NO_TARGET_INTERRUPT</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction>DAR_special_ability_recharge</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_taunt_a</Name>
      <Priority value="0" />
      <ClipSet>creatures@rottweiler@melee@streamed_taunts@</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_01</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_ALLOW_STRAFING</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_dog</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_taunt_b</Name>
      <Priority value="0" />
      <ClipSet>creatures@rottweiler@melee@streamed_taunts@</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_02</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_ALLOW_STRAFING</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_dog</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_cougar_taunt_a</Name>
      <Priority value="0" />
      <ClipSet>creatures@cougar@melee@</ClipSet>
      <FirstPersonClipSet />
      <Anim>growling</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_ALLOW_STRAFING</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_cougar</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_support_taunt_a</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_background</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_01</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_support_taunt_b</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_background</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_02</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_support_taunt_c</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_background</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_03</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_support_taunt_d</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_background</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_04</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_support_taunt_e</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_background</ClipSet>
      <FirstPersonClipSet />
      <Anim>taunt_05</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_taunt</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_directional_taunt_left</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>gesture_left</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_directional_taunt_right</Name>
      <Priority value="0" />
      <ClipSet>melee@unarmed@streamed_taunts</ClipSet>
      <FirstPersonClipSet />
      <Anim>gesture_right</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAUNT RA_PLAYER_IS_UPPERBODY_ONLY RA_AI_IS_UPPERBODY_ONLY RA_ALLOW_STRAFING</ResultAttrs>
      <Homing />
      <ActionBranchSet />
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_front_dog_retriever</Name>
      <Priority value="900" />
      <ClipSet>creatures@retriever@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_takedown_from_front</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS RA_ADD_ORIENT_TO_SPINE_Z_OFFSET</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_neck</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hitby_takedown_rear_dog_retriever</Name>
      <Priority value="900" />
      <ClipSet>creatures@retriever@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>victim_takedown_from_back</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_QUIT_TASK_AFTER_ACTION RA_ACTIVATE_RAGDOLL_ON_COLLISION RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS RA_ADD_ORIENT_TO_SPINE_Z_OFFSET</ResultAttrs>
      <Homing>HOM_face_away</Homing>
      <ActionBranchSet />
      <DamageAndReaction>DAR_kill_self_neck</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_front_dog_retriever</Name>
      <Priority value="800" />
      <ClipSet>creatures@retriever@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>takedown_from_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_dog_retriever</Homing>
      <ActionBranchSet>ABS_dog_retriever</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_takedown_rear_dog_retriever</Name>
      <Priority value="800" />
      <ClipSet>creatures@retriever@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>takedown_from_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_TAKEDOWN RA_PROCESS_POST_HIT_RESULTS_ON_CONTACT_FRAME RA_DISABLE_PED_TO_PED_RAGDOLL RA_USE_KINEMATIC_PHYSICS</ResultAttrs>
      <Homing>HOM_takedown_dog_retriever</Homing>
      <ActionBranchSet>ABS_dog_retriever</ActionBranchSet>
      <DamageAndReaction>DAR_stealth_kill</DamageAndReaction>
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_retriever_hitby_rear</Name>
      <Priority value="700" />
      <ClipSet>creatures@retriever@melee@base@</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_from_back</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_dog_retriever</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_retriever_hitby_front</Name>
      <Priority value="700" />
      <ClipSet>creatures@retriever@melee@base@</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_from_front</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_dog_retriever</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_retriever_hitby_left</Name>
      <Priority value="700" />
      <ClipSet>creatures@retriever@melee@base@</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_from_left</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_dog_retriever</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_retriever_hitby_right</Name>
      <Priority value="700" />
      <ClipSet>creatures@retriever@melee@base@</ClipSet>
      <FirstPersonClipSet />
      <Anim>hit_from_right</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_HIT_REACTION RA_ALLOW_STRAFING RA_RESET_MIN_TIME_IN_MELEE</ResultAttrs>
      <Homing>HOM_face</Homing>
      <ActionBranchSet>ABS_dog_retriever</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_retriever_forward_close_low</Name>
      <Priority value="500" />
      <ClipSet>creatures@retriever@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>ground_attack_0</Anim>
      <AnimBlendInRate value="0.250000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_RESET_COMBO RA_FORCE_DAMAGE RA_ALLOW_STRAFING RA_ALLOW_MELEE_TARGET_EVALUATION RA_IGNORE_HIT_DOT_THRESHOLD RA_ALLOW_SCRIPT_TASK_ABORT</ResultAttrs>
      <Homing>HOM_ground_cardinal_0_dog</Homing>
      <ActionBranchSet>ABS_dog_retriever</ActionBranchSet>
      <DamageAndReaction>DAR_stomp</DamageAndReaction>
      <StrikeBoneSet>SB_animal_head</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_retriever_attack</Name>
      <Priority value="100" />
      <ClipSet>creatures@retriever@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>attack</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_FORCE_DAMAGE</ResultAttrs>
      <Homing>HOM_face_medium_dog</Homing>
      <ActionBranchSet>ABS_dog_retriever</ActionBranchSet>
      <DamageAndReaction>DAR_dog_bite</DamageAndReaction>
      <StrikeBoneSet>SB_animal_paws</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_dog_small_attack</Name>
      <Priority value="100" />
      <ClipSet>creatures@pug@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>attack</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_FORCE_DAMAGE</ResultAttrs>
      <Homing>HOM_face_medium_dog</Homing>
      <ActionBranchSet>ABS_dog_small</ActionBranchSet>
      <DamageAndReaction>DAR_dog_bite</DamageAndReaction>
      <StrikeBoneSet>SB_animal_paws</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_tiger_shark_attack</Name>
      <Priority value="100" />
      <ClipSet>creatures@shark@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>attack</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_FORCE_DAMAGE RA_QUIT_TASK_AFTER_ACTION RA_UPDATE_MOVEBLENDRATIO RA_ALLOW_PLAYER_EARLY_OUT</ResultAttrs>
      <Homing>HOM_shark</Homing>
      <ActionBranchSet>ABS_tiger_shark</ActionBranchSet>
      <DamageAndReaction>DAR_shark_bite</DamageAndReaction>
      <StrikeBoneSet>SB_shark_head</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hammerhead_shark_attack</Name>
      <Priority value="100" />
      <ClipSet>creatures@hammerhead@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>attack</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_FORCE_DAMAGE RA_QUIT_TASK_AFTER_ACTION RA_UPDATE_MOVEBLENDRATIO RA_ALLOW_PLAYER_EARLY_OUT</ResultAttrs>
      <Homing>HOM_shark</Homing>
      <ActionBranchSet>ABS_hammerhead_shark</ActionBranchSet>
      <DamageAndReaction>DAR_shark_bite</DamageAndReaction>
      <StrikeBoneSet>SB_shark_head</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_killer_whale_attack</Name>
      <Priority value="100" />
      <ClipSet>creatures@killerwhale@melee@streamed_core@</ClipSet>
      <FirstPersonClipSet />
      <Anim>attack</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.500000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_FORCE_DAMAGE RA_QUIT_TASK_AFTER_ACTION RA_UPDATE_MOVEBLENDRATIO RA_ALLOW_PLAYER_EARLY_OUT</ResultAttrs>
      <Homing>HOM_shark</Homing>
      <ActionBranchSet>ABS_killer_whale</ActionBranchSet>
      <DamageAndReaction>DAR_shark_bite</DamageAndReaction>
      <StrikeBoneSet>SB_shark_head</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_boar_attack</Name>
      <Priority value="100" />
      <ClipSet>CREATURES@BOAR@MELEE@STREAMED_CORE@</ClipSet>
      <FirstPersonClipSet />
      <Anim>attack</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_FORCE_DAMAGE RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_PLAYER_EARLY_OUT</ResultAttrs>
      <Homing>HOM_face_medium_dog</Homing>
      <ActionBranchSet>ABS_boar</ActionBranchSet>
      <DamageAndReaction>DAR_dog_bite</DamageAndReaction>
      <StrikeBoneSet>SB_boar_head</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_pig_attack</Name>
      <Priority value="100" />
      <ClipSet>CREATURES@PIG@MELEE@STREAMED_CORE@</ClipSet>
      <FirstPersonClipSet />
      <Anim>attack</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_FORCE_DAMAGE RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_PLAYER_EARLY_OUT</ResultAttrs>
      <Homing>HOM_face_medium_dog</Homing>
      <ActionBranchSet>ABS_pig</ActionBranchSet>
      <DamageAndReaction>DAR_dog_bite</DamageAndReaction>
      <StrikeBoneSet>SB_animal_paws</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_coyote_attack</Name>
      <Priority value="100" />
      <ClipSet>CREATURES@COYOTE@MELEE@STREAMED_CORE@</ClipSet>
      <FirstPersonClipSet />
      <Anim>attack</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_FORCE_DAMAGE</ResultAttrs>
      <Homing>HOM_face_medium_dog</Homing>
      <ActionBranchSet>ABS_coyote</ActionBranchSet>
      <DamageAndReaction>DAR_coyote_bite</DamageAndReaction>
      <StrikeBoneSet>SB_animal_head</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_deer_attack</Name>
      <Priority value="100" />
      <ClipSet>CREATURES@DEER@MELEE@STREAMED_CORE@</ClipSet>
      <FirstPersonClipSet />
      <Anim>attack</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK RA_FORCE_DAMAGE RA_QUIT_TASK_AFTER_ACTION RA_ALLOW_PLAYER_EARLY_OUT</ResultAttrs>
      <Homing>HOM_face_medium_dog</Homing>
      <ActionBranchSet>ABS_deer</ActionBranchSet>
      <DamageAndReaction>DAR_dog_bite</DamageAndReaction>
      <StrikeBoneSet>SB_deer_paws</StrikeBoneSet>
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_hen_flap</Name>
      <Priority value="100" />
      <ClipSet>creatures@hen@player_action@</ClipSet>
      <FirstPersonClipSet />
      <Anim>action_a</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_hen</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_rabbit_scratch</Name>
      <Priority value="100" />
      <ClipSet>creatures@rabbit@player_action@</ClipSet>
      <FirstPersonClipSet />
      <Anim>action_a</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_rabbit</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_cat_scratch</Name>
      <Priority value="100" />
      <ClipSet>creatures@cat@player_action@</ClipSet>
      <FirstPersonClipSet />
      <Anim>action_a</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_cat</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
    <Item type="CActionResult">
      <Name>AR_cow_moo</Name>
      <Priority value="100" />
      <ClipSet>creatures@cow@player_action@</ClipSet>
      <FirstPersonClipSet />
      <Anim>action_a</Anim>
      <AnimBlendInRate value="0.125000" />
      <AnimBlendOutRate value="0.250000" />
      <CameraAnimLeft />
      <CameraAnimRight />
      <ResultAttrs>RA_IS_STANDARD_ATTACK</ResultAttrs>
      <Homing />
      <ActionBranchSet>ABS_cow</ActionBranchSet>
      <DamageAndReaction />
      <StrikeBoneSet />
      <Sound />
      <NmReactionSet />
      <CameraTarget>CT_PED_HEAD</CameraTarget>
      <ApplyRelativeTargetPitch value="false" />
    </Item>
  </aActionResults>
</CActionResultContainer>
