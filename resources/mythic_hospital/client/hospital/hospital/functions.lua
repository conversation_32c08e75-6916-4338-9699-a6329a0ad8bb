local cam = nil

local inBedDict = "anim@gangops@morgue@table@"
local inBedAnim = "ko_front"
local getOutDict = 'switch@franklin@bed'
local getOutAnim = 'sleep_getup_rubeyes'

isInHospitalBed = false

tHospital.getHospitalBedState = function()
  return isInHospitalBed or false
end

tHospital.getBleedingLevel = function()
  return isBleeding or 0
end

function getHospitalBedState()
  return isInHospitalBed or false
end

exports('GetHospitalBedState', getHospitalBedState)

function SetBedCam()
  isInHospitalBed = true
  TriggerEvent('core:client:registerCharacterState', 'in_hospital_bed', true)
  local player = PlayerPedId()

  DoScreenFadeOut(1000)

  while not IsScreenFadedOut() do
    Citizen.Wait(100)
  end

  if IsPedDeadOrDying(player) then
    local playerPos = GetEntityCoords(player, true)
    NetworkResurrectLocalPlayer(playerPos, true, true, false)
  end

  bedObject = GetClosestObjectOfType(bedOccupyingData.x, bedOccupyingData.y, bedOccupyingData.z, 1.0, bedOccupyingData.model, false, false, false)
  FreezeEntityPosition(bedObject, true)


  DoScreenFadeOut(1) -- Have to do another fadeout ? 0 Idea why...

  SetEntityCoords(player, bedOccupyingData.x, bedOccupyingData.y, bedOccupyingData.z)
  SetEntityInvincible(PlayerPedId(), true)

  RequestAnimDict(inBedDict)
  while not HasAnimDictLoaded(inBedDict) do
    Citizen.Wait(0)
  end

  TaskPlayAnim(player, inBedDict , inBedAnim, 8.0, 1.0, -1, 1, 0, 0, 0, 0 )
  SetEntityHeading(player, bedOccupyingData.h + 180)
  --
  --cam = CreateCam("DEFAULT_SCRIPTED_CAMERA", 1)
  --SetCamActive(cam, true)
  --RenderScriptCams(true, false, 1, true, true)
  --AttachCamToPedBone(cam, player, 31085, 0, 0, 1.0 , true)
  --SetCamFov(cam, 90.0)
  --SetCamRot(cam, -90.0, 0.0, GetEntityHeading(player) + 180, true)

  DoScreenFadeIn(1000)

  Citizen.Wait(1000)
  FreezeEntityPosition(player, true)
end

function LeaveBed()
  local player = PlayerPedId()

  RequestAnimDict(getOutDict)
  while not HasAnimDictLoaded(getOutDict) do
    Citizen.Wait(0)
  end

  DoScreenFadeOut(1000)
  while not IsScreenFadedOut() do
    Citizen.Wait(100)
  end

  SetEntityInvincible(player, false)
  SetEntityHeading(player, bedOccupyingData.h - 90)
  TaskPlayAnim(player, getOutDict , getOutAnim, 100.0, 1.0, -1, 8, -1, 0, 0, 0)
  Citizen.Wait(1000)
  DoScreenFadeIn(1000)
  Citizen.Wait(4000)
  ClearPedTasks(player)
  FreezeEntityPosition(player, false)
  TriggerServerEvent('mythic_hospital:server:LeaveBed', bedOccupying)
  FreezeEntityPosition(bedObject, false)


  RenderScriptCams(0, true, 200, true, true)
  DestroyCam(cam, false)

  bedOccupying = nil
  bedObject = nil
  bedOccupyingData = nil
  isInHospitalBed = false
  TriggerEvent('core:client:registerCharacterState', 'in_hospital_bed', false)
end


function ButtonMessage(text)
  BeginTextCommandScaleformString("STRING")
  AddTextComponentScaleform(text)
  EndTextCommandScaleformString()
end

function Button(ControlButton)
  N_0xe83a3e3557a56640(ControlButton)
end

function InBedTooltip(scaleform, outside)
  local scaleform = RequestScaleformMovie(scaleform)
  while not HasScaleformMovieLoaded(scaleform) do
    Citizen.Wait(0)
  end

  PushScaleformMovieFunction(scaleform, "CLEAR_ALL")
  PopScaleformMovieFunctionVoid()

  PushScaleformMovieFunction(scaleform, "SET_CLEAR_SPACE")
  PushScaleformMovieFunctionParameterInt(200)
  PopScaleformMovieFunctionVoid()

  PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
  PushScaleformMovieFunctionParameterInt(1)
  Button(GetControlInstructionalButton(1, Config.Keys.GetUp, true))
  ButtonMessage(Config.Strings.GetUp)
  PopScaleformMovieFunctionVoid()

  PushScaleformMovieFunction(scaleform, "DRAW_INSTRUCTIONAL_BUTTONS")
  PopScaleformMovieFunctionVoid()

  PushScaleformMovieFunction(scaleform, "SET_BACKGROUND_COLOUR")
  PushScaleformMovieFunctionParameterInt(0)
  PushScaleformMovieFunctionParameterInt(0)
  PushScaleformMovieFunctionParameterInt(0)
  PushScaleformMovieFunctionParameterInt(80)
  PopScaleformMovieFunctionVoid()

  return scaleform
end
