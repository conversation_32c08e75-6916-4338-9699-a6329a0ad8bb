--vRP = Proxy.getInterface("vRP")
tHospital = {}
T.bindInstance("hospital", tHospital)
--Proxy.addInterface("hospital",tHospital)
--vRPserver = Tunnel.getInterface("vRP","vRP")
HospitalServer = P.getInstance("mythic_hospital","hospital")
pPolice = P.getInstance('blrp_core', 'police')
pVRP = P.getInstance('vrp', 'vrp')

function print3dText(coords, text)
    local onScreen, _x, _y = World3dToScreen2d(coords.x, coords.y, coords.z)

    if onScreen then
        local px, py, pz = table.unpack(GetGameplayCamCoords())
        local dist = #(vector3(px, py, pz) - vector3(coords.x, coords.y, coords.z))
        local scale = (1 / dist) * 20
        local fov = (1 / GetGameplayCamFov()) * 100
        local scale = scale * fov
        SetTextScale(0.35, 0.35)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(250, 250, 250, 255)		-- You can change the text color here
        SetTextDropshadow(1, 1, 1, 1, 255)
        SetTextEdge(2, 0, 0, 0, 150)
        SetTextDropShadow()
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        SetDrawOrigin(coords.x, coords.y, coords.z, 0)
        DrawText(0.0, 0.0)
        ClearDrawOrigin()
    end
end

function DrawUIText(font, centre, x, y, scale, r, g, b, a, text)
    SetTextFont(font)
    SetTextProportional(0)
    SetTextScale(scale, scale)
    SetTextColour(r, g, b, a)
    SetTextDropShadow(0, 0, 0, 0,255)
    SetTextEdge(1, 0, 0, 0, 255)
    SetTextDropShadow()
    SetTextOutline()
    SetTextCentre(centre)
    SetTextEntry("STRING")
    AddTextComponentString(text)
    DrawText(x , y)
end

function PrintHelpText(message)
    SetTextComponentFormat("STRING")
    AddTextComponentString(message)
    DisplayHelpTextFromStringLabel(0, 0, 1, -1)
end

function CreateBlip(blipData)
    blipData.blip = AddBlipForCoord(blipData.x, blipData.y, blipData.z)
    SetBlipSprite(blipData.blip, blipData.id)
    SetBlipAsShortRange(blipData.blip, blipData.short)
    SetBlipScale(blipData.blip, blipData.scale)
    SetBlipColour(blipData.blip, blipData.color)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(blipData.name)
    EndTextCommandSetBlipName(blipData.blip)
end
