@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,500;0,700;1,300&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    min-width: 100%;
    min-height: 100%;
    overflow: hidden;
    position: relative;
}

body {
    font-family: 'Roboto', sans-serif;
    background-size: cover;
    background-repeat: no-repeat;
}

#app {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.minigame {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.wrap {
  width: 50vh;
  background: #222222;
  border: 8px solid #505050;
  padding: 20px;
  /*height: 0px;*/
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  opacity: 0; /* TODO: change this for prod */
  /* opacity: 100%; */
  transition: .5s linear all;
  color: white;
}

.row {
  height: 6vh;
  display: flex;

  border-left: 1px solid black;
  border-right: 1px solid black;
  border-bottom: 1px solid #7f7f7f;
}

.row:first-child {
  border-top: 1px solid black;
}

.row:last-child {
  border-bottom: 1px solid black;
}

.cell {
  display: inline-flex;
  width: 6vh;
  height: 100%;
  font-size: 3vh;
  user-select: none;
  cursor: pointer;

  justify-content: center;
  align-items: center;

  color: #cccccc;

  border-right: 1px solid #323232;
}

.cell.fixed {
  cursor: auto !important;
  background-color: #111111;
}

.cell:first-child {
  border-left: none;
}

.cell:last-child {
  border-right: none;
}

i.correct {
  color: #008450;
}

i.incorrect {
  color: #B81D13;
}

i.placement {
  color: #EFB700;
}

.section-header .fa-solid {
  font-size: 42px;
}

.section-header {
  text-align: center;
  line-height: 32px;
}

.loading-bar {
  background: #505050;
  height: 20px;
  width: 100%;
  margin-top: 1rem;
}

.loading-bar-inner, .timer-bar-inner {
  width: 45%;
  height: 100%;
  background: #ffffff;
  transition: 1s linear width;
}

.timer-bar {
  background: #505050;
  height: 10px;
  width: 100%;
  margin-top: 1rem;
}

.timer-bar-inner {
  transition: 0s linear width;
}

.validate {
  background-color: #505050;
  border: none;
  color: white;
  padding: 1vh;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 2vh;
  margin-top: 10px;
  width: 25vh;
  cursor: pointer;
}
