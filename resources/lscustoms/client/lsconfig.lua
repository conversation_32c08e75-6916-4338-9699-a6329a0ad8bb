--[[
Los Santos Customs V1.1
Credits - MythicalBro
/////License/////
Do not reupload/re release any part of this script without my permission
]]
local colors = {
	{name = "Black", colorindex = 0},{name = "Carbon Black", colorindex = 147},
	{name = "G<PERSON>hit<PERSON>", colorindex = 1},{name = "Anthracite Black", colorindex = 11},
	{name = "Black Steel", colorindex = 2},{name = "Dark Steel", colorindex = 3},
	{name = "Silver", colorindex = 4},{name = "Bluish Silver", colorindex = 5},
	{name = "Rolled Steel", colorindex = 6},{name = "Shadow Silver", colorindex = 7},
	{name = "Stone Silver", colorindex = 8},{name = "Midnight Silver", colorindex = 9},
	{name = "Cast Iron Silver", colorindex = 10},{name = "Red", colorindex = 27},
	{name = "Torino Red", colorindex = 28},{name = "Formula Red", colorindex = 29},
	{name = "Lava Red", colorindex = 150},{name = "Blaze Red", colorindex = 30},
	{name = "Grace Red", colorindex = 31},{name = "Garnet Red", colorindex = 32},
	{name = "Sunset Red", colorindex = 33},{name = "Cabernet Red", colorindex = 34},
	{name = "Wine Red", colorindex = 143},{name = "Candy Red", colorindex = 35},
	{name = "Hot Pink", colorindex = 135},{name = "Pfsiter Pink", colorindex = 137},
	{name = "Salmon Pink", colorindex = 136},{name = "Sunrise Orange", colorindex = 36},
	{name = "Orange", colorindex = 38},{name = "Bright Orange", colorindex = 138},
	{name = "Gold", colorindex = 99},{name = "Bronze", colorindex = 90},
	{name = "Yellow", colorindex = 88},{name = "Race Yellow", colorindex = 89},
	{name = "Dew Yellow", colorindex = 91},{name = "Dark Green", colorindex = 49},
	{name = "Racing Green", colorindex = 50},{name = "Sea Green", colorindex = 51},
	{name = "Olive Green", colorindex = 52},{name = "Bright Green", colorindex = 53},
	{name = "Gasoline Green", colorindex = 54},{name = "Lime Green", colorindex = 92},
	{name = "Midnight Blue", colorindex = 141},
	{name = "Galaxy Blue", colorindex = 61},{name = "Dark Blue", colorindex = 62},
	{name = "Saxon Blue", colorindex = 63},{name = "Blue", colorindex = 64},
	{name = "Mariner Blue", colorindex = 65},{name = "Harbor Blue", colorindex = 66},
	{name = "Diamond Blue", colorindex = 67},{name = "Surf Blue", colorindex = 68},
	{name = "Nautical Blue", colorindex = 69},{name = "Racing Blue", colorindex = 73},
	{name = "Ultra Blue", colorindex = 70},{name = "Light Blue", colorindex = 74},
	{name = "Chocolate Brown", colorindex = 96},{name = "Bison Brown", colorindex = 101},
	{name = "Creeen Brown", colorindex = 95},{name = "Feltzer Brown", colorindex = 94},
	{name = "Maple Brown", colorindex = 97},{name = "Beechwood Brown", colorindex = 103},
	{name = "Sienna Brown", colorindex = 104},{name = "Saddle Brown", colorindex = 98},
	{name = "Moss Brown", colorindex = 100},{name = "Woodbeech Brown", colorindex = 102},
	{name = "Straw Brown", colorindex = 99},{name = "Sandy Brown", colorindex = 105},
	{name = "Bleached Brown", colorindex = 106},{name = "Schafter Purple", colorindex = 71},
	{name = "Spinnaker Purple", colorindex = 72},{name = "Midnight Purple", colorindex = 142},
	{name = "Bright Purple", colorindex = 145},{name = "Cream", colorindex = 107},
	{name = "Ice White", colorindex = 111},{name = "Frost White", colorindex = 112}
}

local metalcolors = {
	{name = "Brushed Steel",colorindex = 117},
	{name = "Brushed Black Steel",colorindex = 118},
	{name = "Brushed Aluminum",colorindex = 119},
	{name = "Pure Gold",colorindex = 158},
	{name = "Brushed Gold",colorindex = 159}
}

local mattecolors = {
	{name = "Black", colorindex = 12},
	{name = "Gray", colorindex = 13},
	{name = "Light Gray", colorindex = 14},
	{name = "Ice White", colorindex = 131},
	{name = "Blue", colorindex = 83},
	{name = "Dark Blue", colorindex = 82},
	{name = "Midnight Blue", colorindex = 84},
	{name = "Midnight Purple", colorindex = 149},
	{name = "Schafter Purple", colorindex = 148},
	{name = "Red", colorindex = 39},
	{name = "Dark Red", colorindex = 40},
	{name = "Orange", colorindex = 41},
	{name = "Yellow", colorindex = 42},
	{name = "Lime Green", colorindex = 55},
	{name = "Green", colorindex = 128},
	{name = "Frost Green", colorindex = 151},
	{name = "Foliage Green", colorindex = 155},
	{name = "Olive Drab", colorindex = 152},
	{name = "Dark Earth", colorindex = 153},
	{name = "Desert Tan", colorindex = 154}
}



-- local chameleon_colors = {
-- 	-- labels to be followed
-- ['	RED ORANGE FLIP'] = 190,
-- ['	ANOD PURPLE'] = 163,
-- ['	TURQ PURP FLIP'] = 178,
-- ['	MAGEN CYAN FLIP'] = 187,
-- ['	PURP GREEN FLIP'] = 183,
-- ['	BLACK PRISMA'] = 218,
-- ['	YKTA CHRISTMAS'] = 237,
-- ['	YKTA NITE DAY'] = 224,
-- ['	YKTA FOUR SEASO'] = 229,
-- ['	CREAM PEARL'] = 210,
-- ['	RED PRISMA'] = 216,
-- ['	GREEN BLUE FLIP'] = 171,
-- ['	YKTA MONOCHROME'] = 223,
-- ['	YKTA THE SEVEN'] = 234,
-- ['	RAINBOW PRISMA'] = 220,
-- ['	ORANG BLUE FLIP'] = 192,
-- ['	DARKTEALPEARL'] = 197,
-- ['	YKTA VERLIERER2'] = 225,
-- ['	OIL SLIC PRISMA'] = 219,
-- ['	YKTA M9 THROWBA'] = 230,
-- ['	YKTA FUBUKI'] = 242,
-- ['	DARKBLUEPEARL'] = 198,
-- ['	WHITE HOLO'] = 222,
-- ['	ANOD WINE'] = 162,
-- ['	ANOD BRONZE'] = 168,
-- ['	LIT BLUE PEARL'] = 202,
-- ['	ANOD RED'] = 161,
-- ['	GREEN PURP FLIP'] = 175,
-- ['	GREEN TURQ FLIP'] = 174,
-- ['	BLUE GREEN FLIP'] = 181,
-- ['	LIT PURP PEARL'] = 203,
-- ['	LIT PINK PEARL'] = 204,
-- ['	GREEN BROW FLIP'] = 173,
-- ['	YKTA ELECTRO'] = 240,
-- ['	TEAL PURP FLIP'] = 176,
-- ['	GREEN PRISMA'] = 217,
-- ['	YKTA SYNTHWAVE'] = 228,
-- ['	BLUE PEARL'] = 209,
-- ['	YELLOW PEARL'] = 207,
-- ['	DARKBLUEPRISMA'] = 213,
-- ['	OIL SLICK PEARL'] = 200,
-- ['	DARKGREENPEARL'] = 196,
-- ['	ANOD COPPER'] = 167,
-- ['	GREEN PEARL'] = 208,
-- ['	ORANG PURP FLIP'] = 191,
-- ['	ANOD GOLD'] = 170,
-- ['	DARKPURPPRISMA'] = 214,
-- ['	WHITE PRISMA'] = 211,
-- ['	TURQ RED FLIP'] = 177,
-- ['	CYAN PURP FLIP'] = 179,
-- ['	MAGEN ORAN FLIP'] = 189,
-- ['	MAGEN GREE FLIP'] = 184,
-- ['	DARKPURPLEPEARL'] = 199,
-- ['	YKTA FULL RBOW'] = 232,
-- ['	ANOD GREEN'] = 165,
-- ['	PURP RED FLIP'] = 182,
-- ['	WHITE PURP FLIP'] = 193,
-- ['	HOT PINK PRISMA'] = 215,
-- ['	YKTA BUBBLEGUM'] = 231,
-- ['	MAGEN YELL FLIP'] = 185,
-- ['	BLUE PINK FLIP'] = 180,
-- ['	BURG GREEN FLIP'] = 186,
-- ['	LIT GREEN PEARL'] = 201,
-- ['	ANOD LIME'] = 166,
-- ['	OFFWHITE PRISMA'] = 205,
-- ['	RED RAINBO FLIP'] = 194,
-- ['	COPPE PURP FLIP'] = 188,
-- ['	YKTA HSW'] = 239,
-- ['	ANOD CHAMPAGNE'] = 169,
-- ['	YKTA KAMENRIDER'] = 235,
-- ['	YKTA MONIKA'] = 241,
-- ['	YKTA SPRUNK EX'] = 226,
-- ['	YKTA VICE CITY'] = 227,
-- ['	BLACK HOLO'] = 221,
-- ['	BLU RAINBO FLIP'] = 195,
-- ['	GRAPHITE PRISMA'] = 212,
-- ['	GREEN RED FLIP'] = 172,
-- ['	PINK PEARL'] = 206,
-- ['	YKTA SUNSETS'] = 233,
-- ['	ANOD BLUE'] = 164,
-- ['	YKTA CHROMABERA'] = 236,
-- ['	YKTA TEMPERATUR'] = 238,
-- }


local chameleon_colors = {
	{ name = 'Anodized Purple', colorindex = 163},
	{ name = 'Anodized Gold', colorindex = 170},
	{ name = 'Anodized Wine', colorindex = 162},
	{ name = 'Anodized Blue', colorindex = 164},
	{ name = 'Anodized Bronze', colorindex = 168},
	{ name = 'Anodized Green', colorindex = 165},
	{ name = 'Anodized Red', colorindex = 161},
	{ name = 'Anodized Copper', colorindex = 167},
	{ name = 'Anodized Lime', colorindex = 166},
	{ name = 'Anodized Champagne', colorindex = 169},
	{ name = 'Turq & Purp Flip', colorindex = 178},
	{ name = 'Turq & Red Flip', colorindex = 177},
	{ name = 'Orange & Blue Flip', colorindex = 192},
	{ name = 'Orange & Purple Flip', colorindex = 191},
	{ name = 'Red & Orange Flip', colorindex = 190},
	{ name = 'Red Rainbow Flip', colorindex = 194},
	{ name = 'Green & Purple Flip', colorindex = 175},
	{ name = 'Green & Turq Flip', colorindex = 174},
	{ name = 'Magenta & Cyan Flip', colorindex = 187},
	{ name = 'Magenta & Orange Flip', colorindex = 189},
	{ name = 'Magenta & Green Flip', colorindex = 184},
	{ name = 'Magenta & Yellow Flip', colorindex = 185},
	{ name = 'Purp & Green Flip', colorindex = 183},
	{ name = 'Purp & Red Flip', colorindex = 182},
	{ name = 'Blue & Green Flip', colorindex = 181},
	{ name = 'Blue & Pink Flip', colorindex = 180},
	{ name = 'Blue Rainbow Flip', colorindex = 195},
	{ name = 'Burgundy & Green Flip', colorindex = 186},
	{ name = 'Green & Blue Flip', colorindex = 225},
	{ name = 'Green & Brown Flip', colorindex = 173},
	{ name = 'Copper & Purple Flip', colorindex = 188},
	{ name = 'Teal & Purple Flip', colorindex = 176},
	{ name = 'Cyan & Purple Flip', colorindex = 178},
	{ name = 'White & Purple Flip', colorindex = 193},
	{ name = 'Monochrome', colorindex = 223},
	{ name = 'White Holographic', colorindex = 222},
	{ name = 'Night & Day', colorindex = 224},
	{ name = 'Black Prismatic', colorindex = 218},
	{ name = 'Red Prismatic', colorindex = 216},
	{ name = 'Hot Pink Prismatic', colorindex = 215},
	{ name = 'Rainbow Prismatic', colorindex = 220},
	{ name = 'Green Prismatic', colorindex = 217},
	{ name = 'Oil Slick Prismatic', colorindex = 205},
	{ name = 'Off White Prismatic', colorindex = 219},
	{ name = 'Graphite Prismatic', colorindex = 212},
	{ name = 'Dark Blue Prismatic', colorindex = 213},
	{ name = 'Dark Purple Prismatic', colorindex = 214},
	{ name = 'White Prismatic', colorindex = 211},
	{ name = 'Blue Pearl', colorindex = 209},
	{ name = 'Green Pearl', colorindex = 208},
	{ name = 'Yellow Pearl', colorindex = 207},
	{ name = 'Pink Pearl', colorindex = 206},
	{ name = 'Creamy Pearl', colorindex = 210},
	{ name = 'Dark Pearl', colorindex = 197},
	{ name = 'Dark Purple Pearl', colorindex = 199},
	{ name = 'Dark Green Pearl', colorindex = 196},
	{ name = 'Lit Blue Pearl', colorindex = 202},
	{ name = 'Lit Purple Pearl', colorindex = 203},
	{ name = 'Lit Pink Pearl', colorindex = 204},
	{ name = 'Lit Green Pearl', colorindex = 201},
	{ name = 'Dark Blue Pearl', colorindex = 198},
	{ name = 'Oil Slick Pearl', colorindex = 200},
	{ name = 'Verlier 2', colorindex = 225},
	{ name = 'Black Holographic', colorindex = 221},
	{ name = 'M9 Throwback', colorindex = 230},
	{ name = 'Fubuki!', colorindex = 242},
	{ name = 'Electro!', colorindex = 240},
	{ name = 'Monika', colorindex = 241},
	{ name = 'Heat Waves', colorindex = 239},
	{ name = 'Sprunk Xtreme', colorindex = 226},
	{ name = 'Vice City', colorindex = 227},
	{ name = 'Synthwave Nights', colorindex = 228},
	{ name = 'Four Seasons', colorindex = 229},
	{ name = 'Maisonette 9 Throwback', colorindex = 230},
	{ name = 'Bubblegum', colorindex = 231},
	{ name = 'Full Rainbow', colorindex = 232},
	{ name = 'Sunset', colorindex = 233},
	{ name = 'The Seven', colorindex = 234},
	{ name = 'Kamen Rider', colorindex = 235},
	{ name = 'Chromatic Aberration', colorindex = 236},
	{ name = 'It\'s Christmas!', colorindex = 237},
	{ name = 'Temperature', colorindex = 238},
}

-- Future Color Fix/Additions
--[[
	{name = "Metallic Black", colorindex = 0},
	{name = "Metallic Graphite Black", colorindex = 1},
	{name = "Metallic Black Steal", colorindex = 2},
	{name = "Metallic Dark Silver", colorindex = 3},
	{name = "Metallic Silver", colorindex = 4},
	{name = "Metallic Blue Silver", colorindex = 5},
	{name = "Metallic Steel Gray", colorindex = 6},
	{name = "Metallic Shadow Silver", colorindex = 7},
	{name = "Metallic Stone Silver", colorindex = 8},
	{name = "Metallic Midnight Silver", colorindex = 9},
	{name = "Metallic Gun Metal", colorindex = 10},
	{name = "Metallic Anthracite Grey", colorindex = 11},
	{name = "Matte Black", colorindex = 12},
	{name = "Matte Gray", colorindex = 13},
	{name = "Matte Light Grey", colorindex = 14},
	{name = "Util Black", colorindex = 15},
	{name = "Util Black Poly", colorindex = 16},
	{name = "Util Dark silver", colorindex = 17},
	{name = "Util Silver", colorindex = 18},
	{name = "Util Gun Metal", colorindex = 19},
	{name = "Util Shadow Silver", colorindex = 20},
	{name = "Worn Black", colorindex = 21},
	{name = "Worn Graphite", colorindex = 22},
	{name = "Worn Silver Grey", colorindex = 23},
	{name = "Worn Silver", colorindex = 24},
	{name = "Worn Blue Silver", colorindex = 25},
	{name = "Worn Shadow Silver", colorindex = 26},
	{name = "Metallic Red", colorindex = 27},
	{name = "Metallic Torino Red", colorindex = 28},
	{name = "Metallic Formula Red", colorindex = 29},
	{name = "Metallic Blaze Red", colorindex = 30},
	{name = "Metallic Graceful Red", colorindex = 31},
	{name = "Metallic Garnet Red", colorindex = 32},
	{name = "Metallic Desert Red", colorindex = 33},
	{name = "Metallic Cabernet Red", colorindex = 34},
	{name = "Metallic Candy Red", colorindex = 35},
	{name = "Metallic Sunrise Orange", colorindex = 36},
	{name = "Metallic Classic Gold", colorindex = 37},
	{name = "Metallic Orange", colorindex = 38},
	{name = "Matte Red", colorindex = 39},
	{name = "Matte Dark Red", colorindex = 40},
	{name = "Matte Orange", colorindex = 41},
	{name = "Matte Yellow", colorindex = 42},
	{name = "Util Red", colorindex = 43},
	{name = "Util Bright Red", colorindex = 44},
	{name = "Util Garnet Red", colorindex = 45},
	{name = "Worn Red", colorindex = 46},
	{name = "Worn Golden Red", colorindex = 47},
	{name = "Worn Dark Red", colorindex = 48},
	{name = "Metallic Dark Green", colorindex = 49},
	{name = "Metallic Racing Green", colorindex = 50},
	{name = "Metallic Sea Green", colorindex = 51},
	{name = "Metallic Olive Green", colorindex = 52},
	{name = "Metallic Green", colorindex = 53},
	{name = "Metallic Gasoline Blue Green", colorindex = 54},
	{name = "Matte Lime Green", colorindex = 55},
	{name = "Util Dark Green", colorindex = 56},
	{name = "Util Green", colorindex = 57},
	{name = "Worn Dark Green", colorindex = 58},
	{name = "Worn Green", colorindex = 59},
	{name = "Worn Sea Wash", colorindex = 60},
	{name = "Metallic Midnight Blue", colorindex = 61},
	{name = "Metallic Dark Blue", colorindex = 62},
	{name = "Metallic Saxony Blue", colorindex = 63},
	{name = "Metallic Blue", colorindex = 64},
	{name = "Metallic Mariner Blue", colorindex = 65},
	{name = "Metallic Harbor Blue", colorindex = 66},
	{name = "Metallic Diamond Blue", colorindex = 67},
	{name = "Metallic Surf Blue", colorindex = 68},
	{name = "Metallic Nautical Blue", colorindex = 69},
	{name = "Metallic Bright Blue", colorindex = 70},
	{name = "Metallic Purple Blue", colorindex = 71},
	{name = "Metallic Spinnaker Blue", colorindex = 72},
	{name = "Metallic Ultra Blue", colorindex = 73},
	{name = "Metallic Bright Blue", colorindex = 74},
	{name = "Util Dark Blue", colorindex = 75},
	{name = "Util Midnight Blue", colorindex = 76},
	{name = "Util Blue", colorindex = 77},
	{name = "Util Sea Foam Blue", colorindex = 78},
	{name = "Util Lightning blue", colorindex = 79},
	{name = "Util Maui Blue Poly", colorindex = 80},
	{name = "Util Bright Blue", colorindex = 81},
	{name = "Matte Dark Blue", colorindex = 82},
	{name = "Matte Blue", colorindex = 83},
	{name = "Matte Midnight Blue", colorindex = 84},
	{name = "Worn Dark blue", colorindex = 85},
	{name = "Worn Blue", colorindex = 86},
	{name = "Worn Light blue", colorindex = 87},
	{name = "Metallic Taxi Yellow", colorindex = 88},
	{name = "Metallic Race Yellow", colorindex = 89},
	{name = "Metallic Bronze", colorindex = 90},
	{name = "Metallic Yellow Bird", colorindex = 91},
	{name = "Metallic Lime", colorindex = 92},
	{name = "Metallic Champagne", colorindex = 93},
	{name = "Metallic Pueblo Beige", colorindex = 94},
	{name = "Metallic Dark Ivory", colorindex = 95},
	{name = "Metallic Choco Brown", colorindex = 96},
	{name = "Metallic Golden Brown", colorindex = 97},
	{name = "Metallic Light Brown", colorindex = 98},
	{name = "Metallic Straw Beige", colorindex = 99},
	{name = "Metallic Moss Brown", colorindex = 100},
	{name = "Metallic Biston Brown", colorindex = 101},
	{name = "Metallic Beechwood", colorindex = 102},
	{name = "Metallic Dark Beechwood", colorindex = 103},
	{name = "Metallic Choco Orange", colorindex = 104},
	{name = "Metallic Beach Sand", colorindex = 105},
	{name = "Metallic Sun Bleeched Sand", colorindex = 106},
	{name = "Metallic Cream", colorindex = 107},
	{name = "Util Brown", colorindex = 108},
	{name = "Util Medium Brown", colorindex = 109},
	{name = "Util Light Brown", colorindex = 110},
	{name = "Metallic White", colorindex = 111},
	{name = "Metallic Frost White", colorindex = 112},
	{name = "Worn Honey Beige", colorindex = 113},
	{name = "Worn Brown", colorindex = 114},
	{name = "Worn Dark Brown", colorindex = 115},
	{name = "Worn straw beige", colorindex = 116},
	{name = "Brushed Steel", colorindex = 117},
	{name = "Brushed Black steel", colorindex = 118},
	{name = "Brushed Aluminium", colorindex = 119},
	{name = "Chrome", colorindex = 120},
	{name = "Worn Off White", colorindex = 121},
	{name = "Util Off White", colorindex = 122},
	{name = "Worn Orange", colorindex = 123},
	{name = "Worn Light Orange", colorindex = 124},
	{name = "Metallic Securicor Green", colorindex = 125},
	{name = "Worn Taxi Yellow", colorindex = 126},
	{name = "police car blue", colorindex = 127},
	{name = "Matte Green", colorindex = 128},
	{name = "Matte Brown", colorindex = 129},
	{name = "Worn Orange", colorindex = 130},
	{name = "Matte White", colorindex = 131},
	{name = "Worn White", colorindex = 132},
	{name = "Worn Olive Army Green", colorindex = 133},
	{name = "Pure White", colorindex = 134},
	{name = "Hot Pink", colorindex = 135},
	{name = "Salmon pink", colorindex = 136},
	{name = "Metallic Vermillion Pink", colorindex = 137},
	{name = "Orange", colorindex = 138},
	{name = "Green", colorindex = 139},
	{name = "Blue", colorindex = 140},
	{name = "Mettalic Black Blue", colorindex = 141},
	{name = "Metallic Black Purple", colorindex = 142},
	{name = "Metallic Black Red", colorindex = 143},
	{name = "hunter green", colorindex = 144},
	{name = "Metallic Purple", colorindex = 145},
	{name = "Metaillic V Dark Blue", colorindex = 146},
	{name = "MODSHOP BLACK1", colorindex = 147},
	{name = "Matte Purple", colorindex = 148},
	{name = "Matte Dark Purple", colorindex = 149},
	{name = "Metallic Lava Red", colorindex = 150},
	{name = "Matte Forest Green", colorindex = 151},
	{name = "Matte Olive Drab", colorindex = 152},
	{name = "Matte Desert Brown", colorindex = 153},
	{name = "Matte Desert Tan", colorindex = 154},
	{name = "Matte Foilage Green", colorindex = 155},
	{name = "DEFAULT ALLOY COLOR", colorindex = 156},
	{name = "Epsilon Blue", colorindex = 157},
	{name = "Pure Gold", colorindex = 158},
	{name = "Brushed Gold", colorindex = 159},
--]]


LSC_Config = {}
LSC_Config.prices = {}

--------Prices---------
LSC_Config.prices = {

------Window tint------
	windowtint = {
		{ name = "Green", tint = 6, price = 200},
		{ name = "Light Smoke", tint = 3, price = 400},
		{ name = "Dark Smoke", tint = 2, price = 800},
		{ name = "Pure Black", tint = 1, price = 1000},
		{ name = "Limo", tint = 5, price = 1000},
		{ name = "Stock", tint = 4, price = 0}
	},

-------Respray--------
-- %20 increase
----Primary color---
	--Chrome
	chrome = {
		colors = {
			{name = "Chrome", colorindex = 120}
		},
		price = 4000
	},
	--Classic
	classic = {
		colors = colors,
		price = 600
	},
	--Matte
	matte = {
		colors = mattecolors,
		price = 600
	},
	--Metallic
	metallic = {
		colors = colors,
		price = 2400
	},
	--Metals
	metal = {
		colors = metalcolors,
		price = 3000
	},
	--Chameleon
	chameleon = {
		colors = chameleon_colors,
		price = 3000,
	},

----Secondary color---
	--Chrome
	chrome2 = {
		colors = {
			{name = "Chrome", colorindex = 120}
		},
		price = 2000
	},
	--Classic
	classic2 = {
		colors = colors,
		price = 300
	},
	--Matte
	matte2 = {
		colors = mattecolors,
		price = 350
	},
	--Metallic
	metallic2 = {
		colors = colors,
		price = 1200
	},
	--Metals
	metal2 = {
		colors = metalcolors,
		price = 1700
	},

------Neon layout------
	neonlayout = {
		{name = "Front,Back and Sides", price = 1200},
		--[[ Future Addition
		{name = "Front", price = 250},
		{name = "Back", price = 250},
		{name = "Sides", price = 500},
		{name = "Front and Back", price = 500},
		{name = "Front and Sides", price = 750},
		{name = "Back and Sides", price = 750},
		{name = "Front, Back and Sides", price = 1000},--]]
	},
	--Neon color
	neoncolor = {
		{ name = "White", neon = {222,222,255}, price = 250},
		{ name = "Blue", neon = {2,21,255}, price = 250},
		{ name = "Electric Blue", neon = {3,83,255}, price = 250},
		{ name = "Mint Green", neon = {0,255,140}, price = 250},
		{ name = "Lime Green", neon = {94,255,1}, price = 250},
		{ name = "Yellow", neon = {255,255,0}, price = 250},
		{ name = "Golden Shower", neon = {255,150,5}, price = 250},
		{ name = "Orange", neon = {255,62,0}, price = 250},
		{ name = "Red", neon = {255,1,1}, price = 250},
		{ name = "Pony Pink", neon = {255,50,100}, price = 250},
		{ name = "Hot Pink",neon = {255,5,190}, price = 250},
		{ name = "Purple", neon = {35,1,255}, price = 250},
		{ name = "Blacklight", neon = {15,3,255}, price = 250},
	},

--------Plates---------
	plates = {
    { name = "Blue on White 1", plateindex = 0, price = 350},
    { name = "Blue On White 2", plateindex = 3, price = 350},
    { name = "Yellow on Black", plateindex = 1, price = 350},
    { name = "Yellow on Blue", plateindex = 2, price = 350},
    { name = 'SA Exempt', plateindex = 4, price = 350, dev_only = true},
    { name = 'Yankton', plateindex = 5, price = 350, dev_only = true},
    { name = 'DLC Plate 1', plateindex = 6, price = 350, dev_only = true},
    { name = 'Grand Senora', plateindex = 7, price = 5250},
    { name = 'Paleto Forest', plateindex = 8, price = 5250},
    { name = 'Del Perro Pier', plateindex = 9, price = 5250},
    { name = 'DLC Plate 5', plateindex = 10, price = 350, dev_only = true},
    { name = 'DLC Plate 6', plateindex = 11, price = 350, dev_only = true},
    { name = 'DLC Plate 7', plateindex = 12, price = 350, dev_only = true},
	},

--------Wheels--------
----Wheel accessories----
	wheelaccessories = {
		{ name = "Stock Tires", price = 250},
		{ name = "Custom Tires", price = 750},
		--{ name = "Bulletproof Tires", price = 5000},
		{ name = "White Tire Smoke",smokecolor = {254,254,254}, price = 650},
		{ name = "Black Tire Smoke", smokecolor = {1,1,1}, price = 650},
		{ name = "Blue Tire Smoke", smokecolor = {0,150,255}, price = 650},
		{ name = "Yellow Tire Smoke", smokecolor = {255,255,50}, price = 650},
		{ name = "Orange Tire Smoke", smokecolor = {255,153,51}, price = 650},
		{ name = "Red Tire Smoke", smokecolor = {255,10,10}, price = 650},
		{ name = "Green Tire Smoke", smokecolor = {10,255,10}, price = 650},
		{ name = "Purple Tire Smoke", smokecolor = {153,10,153}, price = 650},
		{ name = "Pink Tire Smoke", smokecolor = {255,102,178}, price = 650},
		{ name = "Gray Tire Smoke",smokecolor = {128,128,128}, price = 650},
	},

----Wheel color----
	wheelcolor = {
		colors = colors,
		price = 650,
	},

----Front wheel (Bikes)----
	frontwheel = {
		{name = "Stock", wtype = 6, mod = -1, price = 325},
		{name = "Speedway", wtype = 6, mod = 0, price = 650},
		{name = "Street Special", wtype = 6, mod = 1, price = 650},
		{name = "Racer", wtype = 6, mod = 2, price = 650},
		{name = "TrackStar", wtype = 6, mod = 3, price = 650},
		{name = "Overlord", wtype = 6, mod = 4, price = 650},
		{name = "Trident", wtype = 6, mod = 5, price = 650},
		{name = "Triple Threat", wtype = 6, mod = 6, price = 650},
		{name = "Stilleto", wtype = 6, mod = 7, price = 650},
		{name = "Wires", wtype = 6, mod = 8, price = 650},
		{name = "Bobber", wtype = 6, mod = 9, price = 650},
		{name = "Solidus", wtype = 6, mod = 10, price = 650},
		{name = "Ice Shield", wtype = 6, mod = 11, price = 650},
		{name = "Loops", wtype = 6, mod = 12, price = 650},
		{name = "Speedway (Chrome)", wtype = 6, mod = 13, price = 5000},
		{name = "Street Special (Chrome)", wtype = 6, mod = 14, price = 5000},
		{name = "Racer (Chrome)", wtype = 6, mod = 15, price = 5000},
		{name = "TrackStar (Chrome)", wtype = 6, mod = 16, price = 5000},
		{name = "Overlord (Chrome)", wtype = 6, mod = 17, price = 5000},
		{name = "Trident (Chrome)", wtype = 6, mod = 18, price = 5000},
		{name = "Triple Threat (Chrome)", wtype = 6, mod = 19, price = 5000},
		{name = "Stilleto (Chrome)", wtype = 6, mod = 20, price = 5000},
		{name = "Ice Shield (Chrome)", wtype = 6, mod = 21, price = 5000},
		{name = "Loops (Chrome)", wtype = 6, mod = 22, price = 5000},
	},

----Back wheel (Bikes)-----
	backwheel = {
		{name = "Stock", wtype = 6, mod = -1, price = 325},
		{name = "Speedway", wtype = 6, mod = 0, price = 650},
		{name = "Street Special", wtype = 6, mod = 1, price = 650},
		{name = "Racer", wtype = 6, mod = 2, price = 650},
		{name = "TrackStar", wtype = 6, mod = 3, price = 650},
		{name = "Overlord", wtype = 6, mod = 4, price = 650},
		{name = "Trident", wtype = 6, mod = 5, price = 650},
		{name = "Triple Threat", wtype = 6, mod = 6, price = 650},
		{name = "Stilleto", wtype = 6, mod = 7, price = 650},
		{name = "Wires", wtype = 6, mod = 8, price = 650},
		{name = "Bobber", wtype = 6, mod = 9, price = 650},
		{name = "Solidus", wtype = 6, mod = 10, price = 650},
		{name = "Ice Shield", wtype = 6, mod = 11, price = 650},
		{name = "Loops", wtype = 6, mod = 12, price = 650},
		{name = "Speedway (Chrome)", wtype = 6, mod = 13, price = 1000},
		{name = "Street Special (Chrome)", wtype = 6, mod = 14, price = 1000},
		{name = "Racer (Chrome)", wtype = 6, mod = 15, price = 1000},
		{name = "TrackStar (Chrome)", wtype = 6, mod = 16, price = 1000},
		{name = "Overlord (Chrome)", wtype = 6, mod = 17, price = 1000},
		{name = "Trident (Chrome)", wtype = 6, mod = 18, price = 1000},
		{name = "Triple Threat (Chrome)", wtype = 6, mod = 19, price = 1000},
		{name = "Stilleto (Chrome)", wtype = 6, mod = 20, price = 1000},
		{name = "Ice Shield (Chrome)", wtype = 6, mod = 21, price = 1000},
		{name = "Loops (Chrome)", wtype = 6, mod = 22, price = 1000},
	},

----Sport wheels-----
	sportwheels = {
		{name = "Stock", wtype = 0, mod = -1, price = 1000},
		{name = "Inferno", wtype = 0, mod = 0, price = 2000},
		{name = "Deep Five", wtype = 0, mod = 1, price = 2000},
		{name = "LozSpeed", wtype = 0, mod = 2, price = 2000},
		{name = "Diamond Cut", wtype = 0, mod = 3, price = 2000},
		{name = "Chrono", wtype = 0, mod = 4, price = 2000},
		{name = "Ferocci RR", wtype = 0, mod = 5, price = 2000},
		{name = "FiftyNine", wtype = 0, mod = 6, price = 2000},
		{name = "Mercie", wtype = 0, mod = 7, price = 2000},
		{name = "Synthetic Z", wtype = 0, mod = 8, price = 2000},
		{name = "Organic Type D", wtype = 0, mod = 9, price = 2000},
		{name = "Endo V1", wtype = 0, mod = 10, price = 2000},
		{name = "GT One", wtype = 0, mod = 11, price = 2000},
		{name = "Duper7", wtype = 0, mod = 12, price = 2000},
		{name = "Uzer", wtype = 0, mod = 13, price = 2000},
		{name = "GroundRide", wtype = 0, mod = 14, price = 2000},
		{name = "Spacer", wtype = 0, mod = 15, price = 2000},
		{name = "Venum", wtype = 0, mod = 16, price = 2000},
		{name = "Cosmo", wtype = 0, mod = 17, price = 2000},
		{name = "Dash VIP", wtype = 0, mod = 18, price = 2000},
		{name = "IceKid", wtype = 0, mod = 19, price = 2000},
		{name = "Ruffel'd", wtype = 0, mod = 20, price = 2000},
		{name = "WangenMaster", wtype = 0, mod = 21, price = 2000},
		{name = "Super Five", wtype = 0, mod = 22, price = 2000},
		{name = "Endo V2", wtype = 0, mod = 23, price = 2000},
		{name = "Split Six", wtype = 0, mod = 24, price = 2000},
		{name = "Inferno (Chrome)", wtype = 0, mod = 25, price = 4000},
		{name = "Deep Five (Chrome)", wtype = 0, mod = 26, price = 4000},
		{name = "LozSpeed (Chrome)", wtype = 0, mod = 27, price = 4000},
		{name = "Diamond Cut (Chrome)", wtype = 0, mod = 28, price = 4000},
		{name = "Chrono (Chrome)", wtype = 0, mod = 29, price = 4000},
		{name = "Ferocci RR (Chrome)", wtype = 0, mod = 30, price = 4000},
		{name = "FiftyNine (Chrome)", wtype = 0, mod = 31, price = 4000},
		{name = "Mercie (Chrome)", wtype = 0, mod = 32, price = 4000},
		{name = "Synthetic Z (Chrome)", wtype = 0, mod = 33, price = 4000},
		{name = "Organic Type D (Chrome)", wtype = 0, mod = 34, price = 4000},
		{name = "Endo V1 (Chrome)", wtype = 0, mod = 35, price = 4000},
		{name = "GT One (Chrome)", wtype = 0, mod = 36, price = 4000},
		{name = "Duper7 (Chrome)", wtype = 0, mod = 37, price = 4000},
		{name = "Uzer (Chrome)", wtype = 0, mod = 38, price = 4000},
		{name = "GroundRide (Chrome)", wtype = 0, mod = 39, price = 4000},
		{name = "Spacer (Chrome)", wtype = 0, mod = 40, price = 4000},
		{name = "Venum (Chrome)", wtype = 0, mod = 41, price = 4000},
		{name = "Cosmo (Chrome)", wtype = 0, mod = 42, price = 4000},
		{name = "Dash VIP (Chrome)", wtype = 0, mod = 43, price = 4000},
		{name = "IceKid (Chrome)", wtype = 0, mod = 44, price = 4000},
		{name = "Ruffel'd (Chrome)", wtype = 0, mod = 45, price = 4000},
		{name = "WangenMaster (Chrome)", wtype = 0, mod = 46, price = 4000},
		{name = "Super Five (Chrome)", wtype = 0, mod = 47, price = 4000},
		{name = "Endo V2 (Chrome)", wtype = 0, mod = 48, price = 4000},
		{name = "Split Six (Chrome)", wtype = 0, mod = 49, price = 4000},
		{name = "Ainnm", wtype = 0, mod = 50, price = 4000},
    {name = "Buffalo wheels", wtype = 0, mod = 51, price = 4000},


	},
-----Suv wheels------
	suvwheels = {
		{name = "Stock", wtype = 3, mod = -1, price = 1000},
		{name = "VIP", wtype = 3, mod = 0, price = 2000},
		{name = "Benefactor", wtype = 3, mod = 1, price = 2000},
		{name = "Cosmo", wtype = 3, mod = 2, price = 2000},
		{name = "Bippu", wtype = 3, mod = 3, price = 2000},
		{name = "Royal Six", wtype = 3, mod = 4, price = 2000},
		{name = "Fagorme", wtype = 3, mod = 5, price = 2000},
		{name = "Deluxe", wtype = 3, mod = 6, price = 2000},
		{name = "Iced Out", wtype = 3, mod = 7, price = 2000},
		{name = "Cognoscenti", wtype = 3, mod = 8, price = 2000},
		{name = "LozSpeed Ten", wtype = 3, mod = 9, price = 2000},
		{name = "Supernova", wtype = 3, mod = 10, price = 2000},
		{name = "Obey RS", wtype = 3, mod = 11, price = 2000},
		{name = "LozSpeed Baller", wtype = 3, mod = 12, price = 2000},
		{name = "Extra Vaganzo", wtype = 3, mod = 13, price = 2000},
		{name = "Split Six", wtype = 3, mod = 14, price = 2000},
		{name = "Empowered", wtype = 3, mod = 15, price = 2000},
		{name = "Sunrise", wtype = 3, mod = 16, price = 2000},
		{name = "Dash VIP", wtype = 3, mod = 17, price = 2000},
		{name = "Cutter", wtype = 3, mod = 18, price = 2000},
		{name = "VIP (Chrome)", wtype = 3, mod = 19, price = 4000},
		{name = "Benefactor (Chrome)", wtype = 3, mod = 20, price = 4000},
		{name = "Cosmo (Chrome)", wtype = 3, mod = 21, price = 4000},
		{name = "Bippu (Chrome)", wtype = 3, mod = 22, price = 4000},
		{name = "Royal Six (Chrome)", wtype = 3, mod = 23, price = 4000},
		{name = "Fagorme (Chrome)", wtype = 3, mod = 24, price = 4000},
		{name = "Deluxe (Chrome)", wtype = 3, mod = 25, price = 4000},
		{name = "Iced Out (Chrome)", wtype = 3, mod = 26, price = 4000},
		{name = "Cognoscenti (Chrome)", wtype = 3, mod = 27, price = 4000},
		{name = "LozSpeed Ten (Chrome)", wtype = 3, mod = 28, price = 4000},
		{name = "Supernova (Chrome)", wtype = 3, mod = 29, price = 4000},
		{name = "Obey RS (Chrome)", wtype = 3, mod = 30, price = 4000},
		{name = "LozSpeed Baller (Chrome)", wtype = 3, mod = 31, price = 4000},
		{name = "Extra Vaganzo (Chrome)", wtype = 3, mod = 32, price = 4000},
		{name = "Split Six (Chrome)", wtype = 3, mod = 33, price = 4000},
		{name = "Empowered (Chrome)", wtype = 3, mod = 34, price = 4000},
		{name = "Sunrise (Chrome)", wtype = 3, mod = 35, price = 4000},
		{name = "Dash VIP (Chrome)", wtype = 3, mod = 36, price = 4000},
		{name = "Cutter (Chrome)", wtype = 3, mod = 37, price = 4000},
	},
-----Offroad wheels-----
	offroadwheels = {
		{name = "Stock", wtype = 4, mod = -1, price = 1000},
		{name = "Raider", wtype = 4, mod = 0, price = 2000},
		{name = "MudSlinger", wtype = 4, mod = 1, price = 2000},
		{name = "Nevis", wtype = 4, mod = 2, price = 2000},
		{name = "Cairngorm", wtype = 4, mod = 3, price = 2000},
		{name = "Amazon", wtype = 4, mod = 4, price = 2000},
		{name = "Challenger", wtype = 4, mod = 5, price = 2000},
		{name = "DuneBasher", wtype = 4, mod = 6, price = 2000},
		{name = "FiveStar", wtype = 4, mod = 7, price = 2000},
		{name = "RockCrawler", wtype = 4, mod = 8, price = 2000},
		{name = "Mil-spec Steelie", wtype = 4, mod = 9, price = 2000},
		{name = "Raider (Chrome)", wtype = 4, mod = 10, price = 4000},
		{name = "MudSlinger (Chrome)", wtype = 4, mod = 11, price = 4000},
		{name = "Nevis (Chrome)", wtype = 4, mod = 12, price = 4000},
		{name = "Cairngorm (Chrome)", wtype = 4, mod = 13, price = 4000},
		{name = "Amazon (Chrome)", wtype = 4, mod = 14, price = 4000},
		{name = "Challenger (Chrome)", wtype = 4, mod = 15, price = 4000},
		{name = "DuneBasher (Chrome)", wtype = 4, mod = 16, price = 4000},
		{name = "FiveStar (Chrome)", wtype = 4, mod = 17, price = 4000},
		{name = "RockCrawler (Chrome)", wtype = 4, mod = 18, price = 4000},
		{name = "Mil-spec Steelie (Chrome)", wtype = 4, mod = 19, price = 4000},
	},
-----Tuner wheels------
	tunerwheels = {
		{name = "Stock", wtype = 5, mod = -1, price = 1000},
		{name = "Cosmo", wtype = 5, mod = 0, price = 2000},
		{name = "SuperMesh", wtype = 5, mod = 1, price = 2000},
		{name = "Outsider", wtype = 5, mod = 2, price = 2000},
		{name = "Rolla S", wtype = 5, mod = 3, price = 2000},
		{name = "DriftMeister", wtype = 5, mod = 4, price = 2000},
		{name = "Slicer", wtype = 5, mod = 5, price = 2000},
		{name = "El Quatro", wtype = 5, mod = 6, price = 2000},
		{name = "Dubbed", wtype = 5, mod = 7, price = 2000},
		{name = "FiveStar", wtype = 5, mod = 8, price = 2000},
		{name = "Slideways", wtype = 5, mod = 9, price = 2000},
		{name = "Apex", wtype = 5, mod = 10, price = 2000},
		{name = "Stanced EG", wtype = 5, mod = 11, price = 2000},
		{name = "CounterSteer", wtype = 5, mod = 12, price = 2000},
		{name = "Endo V1", wtype = 5, mod = 13, price = 2000},
		{name = "Endo V2 (Dish)", wtype = 5, mod = 14, price = 2000},
		{name = "Guppe Z", wtype = 5, mod = 15, price = 2000},
		{name = "Chokadori", wtype = 5, mod = 16, price = 2000},
		{name = "Chicane", wtype = 5, mod = 17, price = 2000},
		{name = "Saisoku", wtype = 5, mod = 18, price = 2000},
		{name = "Dished Eight", wtype = 5, mod = 19, price = 2000},
		{name = "Fujiwara", wtype = 5, mod = 20, price = 2000},
		{name = "Zokusha", wtype = 5, mod = 21, price = 2000},
		{name = "Battlevill", wtype = 5, mod = 22, price = 2000},
		{name = "Rally Master", wtype = 5, mod = 23, price = 2000},
		{name = "Cosmo (Chrome)", wtype = 5, mod = 24, price = 4000},
		{name = "SuperMesh (Chrome)", wtype = 5, mod = 25, price = 4000},
		{name = "Outsider (Chrome)", wtype = 5, mod = 26, price = 4000},
		{name = "Rolla S (Chrome)", wtype = 5, mod = 27, price = 4000},
		{name = "DriftMeister (Chrome)", wtype = 5, mod = 28, price = 4000},
		{name = "Slicer (Chrome)", wtype = 5, mod = 29, price = 4000},
		{name = "El Quatro (Chrome)", wtype = 5, mod = 30, price = 4000},
		{name = "Dubbed (Chrome)", wtype = 5, mod = 31, price = 4000},
		{name = "FiveStar (Chrome)", wtype = 5, mod = 32, price = 4000},
		{name = "Slideways (Chrome)", wtype = 5, mod = 33, price = 4000},
		{name = "Apex (Chrome)", wtype = 5, mod = 34, price = 4000},
		{name = "Stanced EG (Chrome)", wtype = 5, mod = 35, price = 4000},
		{name = "CounterSteer (Chrome)", wtype = 5, mod = 36, price = 4000},
		{name = "Endo V1 (Chrome)", wtype = 5, mod = 37, price = 4000},
		{name = "Endo V2 (Chrome)", wtype = 5, mod = 38, price = 4000},
		{name = "Guppe Z (Chrome)", wtype = 5, mod = 39, price = 4000},
		{name = "Chokadori (Chrome)", wtype = 5, mod = 40, price = 4000},
		{name = "Chicane (Chrome)", wtype = 5, mod = 41, price = 4000},
		{name = "Saisoku (Chrome)", wtype = 5, mod = 42, price = 4000},
		{name = "Dished Eight (Chrome)", wtype = 5, mod = 43, price = 4000},
		{name = "Fujiwara (Chrome)", wtype = 5, mod = 44, price = 4000},
		{name = "Zokusha (Chrome)", wtype = 5, mod = 45, price = 4000},
		{name = "Battlevill (Chrome)", wtype = 5, mod = 46, price = 4000},
		{name = "Rally Master (Chrome)", wtype = 5, mod = 47, price = 4000},
	},
-----Highend wheels------
	highendwheels = {
		{name = "Stock", wtype = 7, mod = -1, price = 1000},
		{name = "Shadow", wtype = 7, mod = 0, price = 2000},
		{name = "Hyper", wtype = 7, mod = 1, price = 2000},
		{name = "Blade", wtype = 7, mod = 2, price = 2000},
		{name = "Diamond", wtype = 7, mod = 3, price = 2000},
		{name = "Supa Gee", wtype = 7, mod = 4, price = 2000},
		{name = "Chromatic Z", wtype = 7, mod = 5, price = 2000},
		{name = "Mercie CH", wtype = 7, mod = 6, price = 2000},
		{name = "Obey RS", wtype = 7, mod = 7, price = 2000},
		{name = "GeeTee", wtype = 7, mod = 8, price = 2000},
		{name = "Cheetah R", wtype = 7, mod = 9, price = 2000},
		{name = "Solar", wtype = 7, mod = 10, price = 2000},
		{name = "Split Ten", wtype = 7, mod = 11, price = 2000},
		{name = "Dash VIP", wtype = 7, mod = 12, price = 2000},
		{name = "LozSpeed Ten", wtype = 7, mod = 13, price = 2000},
		{name = "Carbon Inferno", wtype = 7, mod = 14, price = 2000},
		{name = "Carbon Shadow", wtype = 7, mod = 15, price = 2000},
		{name = "Carbon Z", wtype = 7, mod = 16, price = 2000},
		{name = "Carbon Solar", wtype = 7, mod = 17, price = 2000},
		{name = "Carbon Cheetah R", wtype = 7, mod = 18, price = 2000},
		{name = "Carbon S Racer", wtype = 7, mod = 19, price = 2000},
		{name = "Shadow (Chrome)", wtype = 7, mod = 20, price = 5000},
		{name = "Hyper (Chrome)", wtype = 7, mod = 21, price = 5000},
		{name = "Blade (Chrome)", wtype = 7, mod = 22, price = 5000},
		{name = "Diamond (Chrome)", wtype = 7, mod = 23, price = 5000},
		{name = "Supa Gee (Chrome)", wtype = 7, mod = 24, price = 5000},
		{name = "Chromatic Z (Chrome)", wtype = 7, mod = 25, price = 5000},
		{name = "Mercie CH (Chrome)", wtype = 7, mod = 26, price = 5000},
		{name = "Obey RS (Chrome)", wtype = 7, mod = 27, price = 5000},
		{name = "Gee Tee (Chrome)", wtype = 7, mod = 28, price = 5000},
		{name = "Cheetah R (Chrome)", wtype = 7, mod = 29, price = 5000},
		{name = "Solar (Chrome)", wtype = 7, mod = 30, price = 5000},
		{name = "Split Ten (Chrome)", wtype = 7, mod = 31, price = 5000},
		{name = "Dash VIP (Chrome)", wtype = 7, mod = 32, price = 5000},
		{name = "LozSpeed Ten (Chrome)", wtype = 7, mod = 33, price = 5000},
		{name = "Carbon Inferno (Chrome)", wtype = 7, mod = 34, price = 5000},
		{name = "Carbon Shadow (Chrome)", wtype = 7, mod = 35, price = 5000},
		{name = "Carbon Z (Chrome)", wtype = 7, mod = 36, price = 5000},
		{name = "Carbon Solar (Chrome)", wtype = 7, mod = 37, price = 5000},
		{name = "Carbon Cheetah R (Chrome)", wtype = 7, mod = 38, price = 5000},
		{name = "Carbon S Racer (Chrome)", wtype = 7, mod = 39, price = 5000},
	},
-----Lowrider wheels------
	lowriderwheels = {
		{name = "Stock", wtype = 2, mod = -1, price = 1000},
		{name = "Flare", wtype = 2, mod = 0, price = 2000},
		{name = "Wired", wtype = 2, mod = 1, price = 2000},
		{name = "Triple Golds", wtype = 2, mod = 2, price = 2000},
		{name = "Big Worm", wtype = 2, mod = 3, price = 2000},
		{name = "SevenFive S", wtype = 2, mod = 4, price = 2000},
		{name = "Split Six", wtype = 2, mod = 5, price = 2000},
		{name = "FreshMesh", wtype = 2, mod = 6, price = 2000},
		{name = "LeadSled", wtype = 2, mod = 7, price = 2000},
		{name = "Turbine", wtype = 2, mod = 8, price = 2000},
		{name = "Super Fin", wtype = 2, mod = 9, price = 2000},
		{name = "Classic Rod", wtype = 2, mod = 10, price = 2000},
		{name = "Dollar", wtype = 2, mod = 11, price = 2000},
		{name = "Dukes", wtype = 2, mod = 12, price = 2000},
		{name = "Low Five", wtype = 2, mod = 13, price = 2000},
		{name = "Gooch", wtype = 2, mod = 14, price = 2000},
		{name = "Flare (Chrome)", wtype = 2, mod = 15, price = 5000},
		{name = "Wired (Chrome)", wtype = 2, mod = 16, price = 5000},
		{name = "Triple Golds (Chrome)", wtype = 2, mod = 17, price = 5000},
		{name = "Big Worm (Chrome)", wtype = 2, mod = 18, price = 5000},
		{name = "SevenFive S (Chrome)", wtype = 2, mod = 19, price = 5000},
		{name = "Split Six (Chrome)", wtype = 2, mod = 20, price = 5000},
		{name = "FreshMesh (Chrome)", wtype = 2, mod = 21, price = 5000},
		{name = "LeadSled (Chrome)", wtype = 2, mod = 22, price = 5000},
		{name = "Turbine (Chrome)", wtype = 2, mod = 23, price = 5000},
		{name = "Super Fin (Chrome)", wtype = 2, mod = 24, price = 5000},
		{name = "Classic Rod (Chrome)", wtype = 2, mod = 25, price = 5000},
		{name = "Dollar (Chrome)", wtype = 2, mod = 26, price = 5000},
		{name = "Dukes (Chrome)", wtype = 2, mod = 27, price = 5000},
		{name = "Low Five (Chrome)", wtype = 2, mod = 28, price = 5000},
		{name = "Gooch (Chrome)", wtype = 2, mod = 29, price = 5000},
	},
-----Muscle wheels-----
	musclewheels = {
		{name = "Stock", wtype = 1, mod = -1, price = 1000},
		{name = "Classic Five", wtype = 1, mod = 0, price = 2000},
		{name = "Dukes", wtype = 1, mod = 1, price = 2000},
		{name = "Muscle Freak", wtype = 1, mod = 2, price = 2000},
		{name = "Kracka", wtype = 1, mod = 3, price = 2000},
		{name = "Azrea", wtype = 1, mod = 4, price = 2000},
		{name = "Mecha", wtype = 1, mod = 5, price = 2000},
		{name = "Blacktop", wtype = 1, mod = 6, price = 2000},
		{name = "Drag SPl", wtype = 1, mod = 7, price = 2000},
		{name = "Revolver", wtype = 1, mod = 8, price = 2000},
		{name = "Classic Rod", wtype = 1, mod = 9, price = 2000},
		{name = "Fairlie", wtype = 1, mod = 10, price = 2000},
		{name = "Spooner", wtype = 1, mod = 11, price = 2000},
		{name = "FiveStar", wtype = 1, mod = 12, price = 2000},
		{name = "Old School", wtype = 1, mod = 13, price = 2000},
		{name = "El Jefe", wtype = 1, mod = 14, price = 2000},
		{name = "Dodman", wtype = 1, mod = 15, price = 2000},
		{name = "Six Gun", wtype = 1, mod = 16, price = 2000},
		{name = "Mercenary", wtype = 1, mod = 17, price = 2000},
		{name = "Classic Five (Chrome)", wtype = 1, mod = 18, price = 5000},
		{name = "Dukes (Chrome)", wtype = 1, mod = 19, price = 5000},
		{name = "Muscle Freak (Chrome)", wtype = 1, mod = 20, price = 5000},
		{name = "Kracka (Chrome)", wtype = 1, mod = 21, price = 5000},
		{name = "Azrea (Chrome)", wtype = 1, mod = 22, price = 5000},
		{name = "Mecha (Chrome)", wtype = 1, mod = 23, price = 5000},
		{name = "Blacktop (Chrome)", wtype = 1, mod = 24, price = 5000},
		{name = "Drag SPl (Chrome)", wtype = 1, mod = 25, price = 5000},
		{name = "Revolver (Chrome)", wtype = 1, mod = 26, price = 5000},
		{name = "Classic Rod (Chrome)", wtype = 1, mod = 27, price = 5000},
		{name = "Fairlie (Chrome)", wtype = 1, mod = 28, price = 5000},
		{name = "Spooner (Chrome)", wtype = 1, mod = 29, price = 5000},
		{name = "FiveStar (Chrome)", wtype = 1, mod = 30, price = 5000},
		{name = "Old School (Chrome)", wtype = 1, mod = 31, price = 5000},
		{name = "El Jefe (Chrome)", wtype = 1, mod = 32, price = 5000},
		{name = "Dodman (Chrome)", wtype = 1, mod = 33, price = 5000},
		{name = "Six Gun (Chrome)", wtype = 1, mod = 34, price = 5000},
		{name = "Mercenary (Chrome)", wtype = 1, mod = 35, price = 5000},

	},
-----Benny's Original Wheels-----
	bennysoriginalwheels = {
		{name = "Stock", wtype = 8, mod = -1, price = 1000},
		{name = "OG Hunnets", wtype = 8, mod = 0, price = 3000},
		{name = "OG Hunnets (Chrome Lip)", wtype = 8, mod = 1, price = 3000},
		{name = "Knock-Offs", wtype = 8, mod = 2, price = 3000},
		{name = "Knock-Offs (Chrome Lip)", wtype = 8, mod = 3, price = 3000},
		{name = "Spoked Out", wtype = 8, mod = 4, price = 3000},
		{name = "Spoked Out (Chrome Lip)", wtype = 8, mod = 5, price = 3000},
		{name = "Vintage Wire", wtype = 8, mod = 6, price = 3000},
		{name = "Vintage Wire (Chrome Lip)", wtype = 8, mod = 7, price = 3000},
		{name = "Smoothie", wtype = 8, mod = 8, price = 4000},
		{name = "Smoothie (Chrome Lip)", wtype = 8, mod = 9, price = 4000},
		{name = "Smoothie (Solid Color)", wtype = 8, mod = 10, price = 4000},
		{name = "Rod Me Up", wtype = 8, mod = 11, price = 4000},
		{name = "Rod Me Up (Chrome Lip)", wtype = 8, mod = 12, price = 4000},
		{name = "Rod Me Up (Solid Color)", wtype = 8, mod = 13, price = 4000},
		{name = "Clean", wtype = 8, mod = 14, price = 4000},
		{name = "Lotta Chrome", wtype = 8, mod = 15, price = 4000},
		{name = "Spindles", wtype = 8, mod = 16, price = 4000},
		{name = "Viking", wtype = 8, mod = 17, price = 4000},
		{name = "Triple Spoke", wtype = 8, mod = 18, price = 5000},
		{name = "Pharohe", wtype = 8, mod = 19, price = 5000},
		{name = "Tiger Style", wtype = 8, mod = 20, price = 5000},
		{name = "Three Wheelin", wtype = 8, mod = 21, price = 5000},
		{name = "Big Bar", wtype = 8, mod = 22, price = 5000},
		{name = "Biohazard", wtype = 8, mod = 23, price = 5000},
		{name = "Waves", wtype = 8, mod = 24, price = 5000},
		{name = "Lick Lick", wtype = 8, mod = 25, price = 5000},
		{name = "Spiralizer", wtype = 8, mod = 26, price = 5000},
		{name = "Hypnotics", wtype = 8, mod = 27, price = 6000},
		{name = "Psycho-Delic", wtype = 8, mod = 28, price = 6000},
		{name = "Half Cut", wtype = 8, mod = 29, price = 6000},
		{name = "Super Electric", wtype = 8, mod = 30, price = 6000},
		{name = "Tuxedo", wtype = 8, mod = 31, price = 6000},
		{name = "Spiked Star", wtype = 8, mod = 32, price = 6000},
		{name = "Caged Star", wtype = 8, mod = 33, price = 6000},
		{name = "Kingpin", wtype = 8, mod = 34, price = 6000},
		{name = "Urban Mauler", wtype = 8, mod = 35, price = 6000},
		{name = "Iron Sights", wtype = 8, mod = 36, price = 6000},
		{name = "Spinning Blade", wtype = 8, mod = 37, price = 6000},
		{name = "Mega Blade", wtype = 8, mod = 38, price = 7000},
		{name = "Black Widow", wtype = 8, mod = 39, price = 7000},
		{name = "Blackhawk", wtype = 8, mod = 40, price = 7000},
		{name = "Web Slinger", wtype = 8, mod = 41, price = 7000},
		{name = "Twin Shot", wtype = 8, mod = 42, price = 7000},
		{name = "Razor Spin", wtype = 8, mod = 43, price = 7000},
		{name = "Rattler", wtype = 8, mod = 44, price = 7000},
		{name = "Shogun", wtype = 8, mod = 45, price = 7000},
		{name = "Ninja Star", wtype = 8, mod = 46, price = 7000},
		{name = "Katana", wtype = 8, mod = 47, price = 7000},
		{name = "Firestorm", wtype = 8, mod = 48, price = 7000},
		{name = "Dragon Scale", wtype = 8, mod = 49, price = 7000},
		{name = "Vortex", wtype = 8, mod = 50, price = 7000},
		{name = "Solar Flare", wtype = 8, mod = 51, price = 7000},
		{name = "Cosmic Spin", wtype = 8, mod = 52, price = 7000},
		{name = "Orbital", wtype = 8, mod = 53, price = 7000},
		{name = "Crimson Fury", wtype = 8, mod = 54, price = 7000},
		{name = "Zephyr", wtype = 8, mod = 55, price = 7000},
		{name = "Inferno", wtype = 8, mod = 56, price = 7000},
		{name = "Thunderbolt", wtype = 8, mod = 57, price = 7000},
		{name = "Nightshade", wtype = 8, mod = 58, price = 7000},
		{name = "Bloodshot", wtype = 8, mod = 59, price = 7000},
		{name = "Silver Fang", wtype = 8, mod = 60, price = 7000},
		{name = "Chromatic", wtype = 8, mod = 61, price = 7000},
		{name = "Dynamo", wtype = 8, mod = 62, price = 7000},
		{name = "Nova", wtype = 8, mod = 63, price = 7000},
		{name = "Pulse", wtype = 8, mod = 64, price = 7000},
		{name = "Shocker", wtype = 8, mod = 65, price = 7000},
		{name = "Titan", wtype = 8, mod = 66, price = 7000},
		{name = "Phoenix", wtype = 8, mod = 67, price = 7000},
		{name = "Typhoon", wtype = 8, mod = 68, price = 7000},
		{name = "Lightning", wtype = 8, mod = 69, price = 7000},
		{name = "Stormbreaker", wtype = 8, mod = 70, price = 7000},
		{name = "Hellfire", wtype = 8, mod = 71, price = 7000},
		{name = "Twilight", wtype = 8, mod = 72, price = 7000},
		{name = "Dusk", wtype = 8, mod = 73, price = 7000},
		{name = "Ragnarok", wtype = 8, mod = 74, price = 7000},
		{name = "Maelstrom", wtype = 8, mod = 75, price = 7000},
		{name = "Phantom", wtype = 8, mod = 76, price = 7000},
		{name = "Harbinger", wtype = 8, mod = 77, price = 7000},
		{name = "Specter", wtype = 8, mod = 78, price = 7000},
		{name = "Revenant", wtype = 8, mod = 79, price = 7000},
		{name = "Grim Reaper", wtype = 8, mod = 80, price = 7000},

		--TODO add the rest of the wheels
		--goes to 216 with wheels https://gta.fandom.com/wiki/Vehicle_Customization_in_GTA_V/Wheels
	},
-----Benny's Bespoke Wheels-----
	bennysbespokewheels = {
		{name = "Stock", wtype = 9, mod = -1, price = 1000},
		{name = "Chrome OG Hunnets", wtype = 9, mod = 0, price = 4000},
		{name = "Gold OG Hunnets", wtype = 9, mod = 1, price = 4000},
		{name = "Chrome Wires", wtype = 9, mod = 2, price = 4000},
		{name = "Gold Wires", wtype = 9, mod = 3, price = 4000},
		{name = "Chrome Spoked Out", wtype = 9, mod = 4, price = 4000},
		{name = "Gold Spoked Out", wtype = 9, mod = 5, price = 4000},
		{name = "Chrome Knock-Offs", wtype = 9, mod = 6, price = 4000},
		{name = "Gold Knock-Offs", wtype = 9, mod = 7, price = 4000},
		{name = "Chrome Bigger Worm", wtype = 9, mod = 8, price = 4000},
		{name = "Gold Bigger Worm", wtype = 9, mod = 9, price = 5000},
		{name = "Chrome Vintage Wire", wtype = 9, mod = 10, price = 5000},
		{name = "Gold Vintage Wire", wtype = 9, mod = 11, price = 5000},
		{name = "Chrome Classic Wire", wtype = 9, mod = 12, price = 5000},
		{name = "Gold Classic Wire", wtype = 9, mod = 13, price = 5000},
		{name = "Chrome Smoothie", wtype = 9, mod = 14, price = 5000},
		{name = "Gold Smoothie", wtype = 9, mod = 15, price = 5000},
		{name = "Chrome Classic Rod", wtype = 9, mod = 16, price = 5000},
		{name = "Gold Classic Rod", wtype = 9, mod = 17, price = 5000},
		{name = "Chrome Dollar", wtype = 9, mod = 18, price = 6000},
		{name = "Gold Dollar", wtype = 9, mod = 19, price = 6000},
		{name = "Chrome Mighty Star", wtype = 9, mod = 20, price = 6000},
		{name = "Gold Mighty Star", wtype = 9, mod = 21, price = 6000},
		{name = "Chrome Decadent Dish", wtype = 9, mod = 22, price = 6000},
		{name = "Gold Decadent Dish", wtype = 9, mod = 23, price = 6000},
		{name = "Chrome Razor Style", wtype = 9, mod = 24, price = 6000},
		{name = "Gold Razor Style", wtype = 9, mod = 25, price = 6000},
		{name = "Chrome Celtic Knot", wtype = 9, mod = 26, price = 6000},
		{name = "Gold Celtic Knot", wtype = 9, mod = 27, price = 7000},
		{name = "Chrome Warrior Dish", wtype = 9, mod = 28, price = 7000},
		{name = "Gold Warrior Dish", wtype = 9, mod = 29, price = 7000},
		{name = "Gold Big Dog Spokes", wtype = 9, mod = 30, price = 7000},
		{name = "Chrome Spiralizer", wtype = 9, mod = 31, price = 7000},
		{name = "Gold Spiralizer", wtype = 9, mod = 32, price = 7000},
		{name = "Chrome High Five", wtype = 9, mod = 33, price = 7000},
		{name = "Gold High Five", wtype = 9, mod = 34, price = 7000},
		{name = "Chrome Hypnotic", wtype = 9, mod = 35, price = 8000},
		{name = "Gold Hypnotic", wtype = 9, mod = 36, price = 8000},
		{name = "Chrome Psycho P", wtype = 9, mod = 37, price = 8000},
		{name = "Gold Psycho P", wtype = 9, mod = 38, price = 8000},
		{name = "Chrome Pharoah", wtype = 9, mod = 39, price = 8000},
		{name = "Gold Pharoah", wtype = 9, mod = 40, price = 8000},
		{name = "Chrome Tiger Style", wtype = 9, mod = 41, price = 8000},
		{name = "Gold Tiger Style", wtype = 9, mod = 42, price = 8000},
		{name = "Chrome Super Electric", wtype = 9, mod = 43, price = 8000},
		{name = "Gold Super Electric", wtype = 9, mod = 44, price = 8000},
		{name = "Chrome Mechanic", wtype = 9, mod = 45, price = 8000},
		{name = "Gold Mechanic", wtype = 9, mod = 46, price = 8000},
		{name = "Chrome Barber Shop", wtype = 9, mod = 47, price = 8000},
		{name = "Gold Barber Shop", wtype = 9, mod = 48, price = 8000},
		{name = "Chrome Big Money", wtype = 9, mod = 49, price = 8000},
		{name = "Gold Big Money", wtype = 9, mod = 50, price = 8000},
		{name = "Chrome Classic Ride", wtype = 9, mod = 51, price = 8000},
		{name = "Gold Classic Ride", wtype = 9, mod = 52, price = 8000},
		{name = "Chrome Street Rod", wtype = 9, mod = 53, price = 8000},
		{name = "Gold Street Rod", wtype = 9, mod = 54, price = 8000},
		{name = "Chrome Turbo Disc", wtype = 9, mod = 55, price = 8000},
		{name = "Gold Turbo Disc", wtype = 9, mod = 56, price = 8000},
		{name = "Chrome Street Pro", wtype = 9, mod = 57, price = 8000},
		{name = "Gold Street Pro", wtype = 9, mod = 58, price = 8000},
		{name = "Chrome Jet Stream", wtype = 9, mod = 59, price = 8000},
		{name = "Gold Jet Stream", wtype = 9, mod = 60, price = 8000},
		{name = "Chrome Race Star", wtype = 9, mod = 61, price = 8000},
		{name = "Gold Race Star", wtype = 9, mod = 62, price = 8000},
		{name = "Chrome Drag Beast", wtype = 9, mod = 63, price = 8000},
		{name = "Gold Drag Beast", wtype = 9, mod = 64, price = 8000},
		{name = "Silver Drag Beast", wtype = 9, mod = 65, price = 8000},
		{name = "Bronze Drag Beast", wtype = 9, mod = 66, price = 8000},
		{name = "Diamond Drag Beast", wtype = 9, mod = 67, price = 8000},
		{name = "Carbon Drag Beast", wtype = 9, mod = 68, price = 8000},
		{name = "Steel Drag Beast", wtype = 9, mod = 69, price = 8000},
		{name = "Black Drag Beast", wtype = 9, mod = 70, price = 8000},
		{name = "Platinum Drag Beast", wtype = 9, mod = 71, price = 8000},
		{name = "Gold Track Pro", wtype = 9, mod = 72, price = 8000},
		{name = "Silver Track Pro", wtype = 9, mod = 73, price = 8000},
		{name = "Bronze Track Pro", wtype = 9, mod = 74, price = 8000},
		{name = "Diamond Track Pro", wtype = 9, mod = 75, price = 8000},
		{name = "Carbon Track Pro", wtype = 9, mod = 76, price = 8000},
		{name = "Steel Track Pro", wtype = 9, mod = 77, price = 8000},
		{name = "Black Track Pro", wtype = 9, mod = 78, price = 8000},
		{name = "Platinum Track Pro", wtype = 9, mod = 79, price = 8000},
		{name = "Gold Velocity Viper", wtype = 9, mod = 80, price = 8000},
		{name = "Silver Velocity Viper", wtype = 9, mod = 81, price = 8000},
		{name = "Bronze Velocity Viper", wtype = 9, mod = 82, price = 8000},
		{name = "Diamond Velocity Viper", wtype = 9, mod = 83, price = 8000},
		{name = "Carbon Velocity Viper", wtype = 9, mod = 84, price = 8000},
		{name = "Steel Velocity Viper", wtype = 9, mod = 85, price = 8000},
		{name = "Black Velocity Viper", wtype = 9, mod = 86, price = 8000},
		{name = "Platinum Velocity Viper", wtype = 9, mod = 87, price = 8000},
		{name = "Gold Phantom Power", wtype = 9, mod = 88, price = 8000},
		{name = "Silver Phantom Power", wtype = 9, mod = 89, price = 8000},
		{name = "Bronze Phantom Power", wtype = 9, mod = 90, price = 8000},
		{name = "Diamond Phantom Power", wtype = 9, mod = 91, price = 8000},
		{name = "Carbon Phantom Power", wtype = 9, mod = 92, price = 8000},
		{name = "Steel Phantom Power", wtype = 9, mod = 93, price = 8000},
		{name = "Black Phantom Power", wtype = 9, mod = 94, price = 8000},
		{name = "Platinum Phantom Power", wtype = 9, mod = 95, price = 8000},
		{name = "Gold Speedstar", wtype = 9, mod = 96, price = 8000},
		{name = "Silver Speedstar", wtype = 9, mod = 97, price = 8000},
		{name = "Bronze Speedstar", wtype = 9, mod = 98, price = 8000},
		{name = "Diamond Speedstar", wtype = 9, mod = 99, price = 8000},
		{name = "Carbon Speedstar", wtype = 9, mod = 100, price = 8000},
		{name = "Steel Speedstar", wtype = 9, mod = 101, price = 8000},
		{name = "Black Speedstar", wtype = 9, mod = 102, price = 8000},
		{name = "Platinum Speedstar", wtype = 9, mod = 103, price = 8000},
		{name = "Gold Turbo Max", wtype = 9, mod = 104, price = 8000},
		{name = "Silver Turbo Max", wtype = 9, mod = 105, price = 8000},
		{name = "Bronze Turbo Max", wtype = 9, mod = 106, price = 8000},
		{name = "Diamond Turbo Max", wtype = 9, mod = 107, price = 8000},
		{name = "Carbon Turbo Max", wtype = 9, mod = 108, price = 8000},
		{name = "Steel Turbo Max", wtype = 9, mod = 109, price = 8000},
		{name = "Black Turbo Max", wtype = 9, mod = 110, price = 8000},
		{name = "Platinum Turbo Max", wtype = 9, mod = 111, price = 8000},
		{name = "Gold Apex Spinner", wtype = 9, mod = 112, price = 8000},
		{name = "Silver Apex Spinner", wtype = 9, mod = 113, price = 8000},
		{name = "Bronze Apex Spinner", wtype = 9, mod = 114, price = 8000},
		{name = "Diamond Apex Spinner", wtype = 9, mod = 115, price = 8000},
		{name = "Carbon Apex Spinner", wtype = 9, mod = 116, price = 8000},
		{name = "Steel Apex Spinner", wtype = 9, mod = 117, price = 8000},
		{name = "Black Apex Spinner", wtype = 9, mod = 118, price = 8000},
		{name = "Platinum Apex Spinner", wtype = 9, mod = 119, price = 8000},
		{name = "Gold Sprint Master", wtype = 9, mod = 120, price = 8000},
		{name = "Silver Sprint Master", wtype = 9, mod = 121, price = 8000},
		{name = "Bronze Sprint Master", wtype = 9, mod = 122, price = 8000},
		{name = "Diamond Sprint Master", wtype = 9, mod = 123, price = 8000},
		{name = "Carbon Sprint Master", wtype = 9, mod = 124, price = 8000},
		{name = "Steel Sprint Master", wtype = 9, mod = 125, price = 8000},
		{name = "Black Sprint Master", wtype = 9, mod = 126, price = 8000},
		{name = "Platinum Sprint Master", wtype = 9, mod = 127, price = 8000},
		{name = "Gold Street King", wtype = 9, mod = 128, price = 8000},
		{name = "Silver Street King", wtype = 9, mod = 129, price = 8000},
		{name = "Bronze Street King", wtype = 9, mod = 130, price = 8000},
		{name = "Diamond Street King", wtype = 9, mod = 131, price = 8000},
		{name = "Carbon Street King", wtype = 9, mod = 132, price = 8000},
		{name = "Steel Street King", wtype = 9, mod = 133, price = 8000},
		{name = "Black Street King", wtype = 9, mod = 134, price = 8000},
		{name = "Platinum Street King", wtype = 9, mod = 135, price = 8000},
		{name = "Gold Street Crusher", wtype = 9, mod = 136, price = 8000},
		{name = "Silver Street Crusher", wtype = 9, mod = 137, price = 8000},
		{name = "Bronze Street Crusher", wtype = 9, mod = 138, price = 8000},
		{name = "Diamond Street Crusher", wtype = 9, mod = 139, price = 8000},
		{name = "Carbon Street Crusher", wtype = 9, mod = 140, price = 8000},
		{name = "Steel Street Crusher", wtype = 9, mod = 141, price = 8000},
		{name = "Black Street Crusher", wtype = 9, mod = 142, price = 8000},
		{name = "Platinum Street Crusher", wtype = 9, mod = 143, price = 8000},
		{name = "Gold Asphalt Burner", wtype = 9, mod = 144, price = 8000},
		{name = "Silver Asphalt Burner", wtype = 9, mod = 145, price = 8000},
		{name = "Bronze Asphalt Burner", wtype = 9, mod = 146, price = 8000},

		--TODO add the rest of the wheels
		--goes to 216 with wheels https://gta.fandom.com/wiki/Vehicle_Customization_in_GTA_V/Wheels
	},
-----Street wheels - Los Santos Summer Special DLC-----
	streetwheels = {
		{name = "Stock", wtype = 11, mod = -1, price = 1000},
		{name = "Retro Steelie", wtype = 11, mod = 0, price = 2000},
		{name = "Poverty Spec Steelie", wtype = 11, mod = 1, price = 2000},
		{name = "Concave Steelie", wtype = 11, mod = 2, price = 2000},
		{name = "Nebula", wtype = 11, mod = 3, price = 2000},
		{name = "Hotring Steelie", wtype = 11, mod = 4, price = 2000},
		{name = "Cup Champion", wtype = 11, mod = 5, price = 2000},
		{name = "Stanced EG Custom", wtype = 11, mod = 6, price = 2000},
		{name = "Kracka Custom", wtype = 11, mod = 7, price = 2000},
		{name = "Dukes Custom", wtype = 11, mod = 8, price = 2000},
		{name = "Endo v.3 Custom", wtype = 11, mod = 9, price = 3000},
		{name = "V8 Killer", wtype = 11, mod = 10, price = 3000},
		{name = "Fujiwara Custom", wtype = 11, mod = 11, price = 3000},
		{name = "Cosmo MKII", wtype = 11, mod = 12, price = 3000},
		{name = "Aero Star", wtype = 11, mod = 13, price = 3000},
		{name = "Hype Five", wtype = 11, mod = 14, price = 3000},
		{name = "Ruff Weld Mega Deep", wtype = 11, mod = 15, price = 3000},
		{name = "Mercie Concave", wtype = 11, mod = 16, price = 3000},
		{name = "Sugoi Concave", wtype = 11, mod = 17, price = 3000},
		{name = "Synthetic Z Concave", wtype = 11, mod = 18, price = 4000},
		{name = "Endo v.4 Dished", wtype = 11, mod = 19, price = 4000},
		{name = "Hyperfresh", wtype = 11, mod = 20, price = 4000},
		{name = "Truffade Concave", wtype = 11, mod = 21, price = 4000},
		{name = "Organic Type II", wtype = 11, mod = 22, price = 4000},
		{name = "Big Mamba", wtype = 11, mod = 23, price = 4000},
		{name = "Deep Flake", wtype = 11, mod = 24, price = 4000},
		{name = "Cosmo MKIII", wtype = 11, mod = 25, price = 4000},
		{name = "Concave Racer", wtype = 11, mod = 26, price = 4000},
		{name = "Deep Flake Reverse", wtype = 11, mod = 27, price = 5000},
		{name = "Wild Wagon", wtype = 11, mod = 28, price = 5000},
		{name = "Concave Mega Mesh", wtype = 11, mod = 29, price = 5000},
		-- Below are the Los Santos Tuners wheel update with logos
		{name = "Retro Steelie Yellow Atomic", wtype = 11, mod = 60, price = 5500},
		{name = "Poverty Spec Steelie Yellow Atomic", wtype = 11, mod = 61, price = 5500},
		{name = "Concave Steelie Yellow Atomic", wtype = 11, mod = 62, price = 5500},
		{name = "Nebula Yellow Atomic", wtype = 11, mod = 63, price = 5500},
		{name = "Hotring Steelie Yellow Atomic", wtype = 11, mod = 64, price = 5500},
		{name = "Cup Champion Yellow Atomic", wtype = 11, mod = 65, price = 5500},
		{name = "Stanced EG Custom Yellow Atomic", wtype = 11, mod = 66, price = 5500},
		{name = "Kracka Custom Yellow Atomic", wtype = 11, mod = 67, price = 5500},
		{name = "Dukes Custom Yellow Atomic", wtype = 11, mod = 68, price = 5500},
		{name = "Endo v.3 Custom Yellow Atomic", wtype = 11, mod = 69, price = 5500},
		{name = "V8 Killer Yellow Atomic", wtype = 11, mod = 70, price = 5500},
		{name = "Fujiwara Custom Yellow Atomic", wtype = 11, mod = 71, price = 5500},
		{name = "Cosmo MKII Yellow Atomic", wtype = 11, mod = 72, price = 5500},
		{name = "Aero Star Yellow Atomic", wtype = 11, mod = 73, price = 5500},
		{name = "Hype Five Yellow Atomic", wtype = 11, mod = 74, price = 5500},
		{name = "Ruff Weld Mega Deep Yellow Atomic", wtype = 11, mod = 75, price = 5500},
		{name = "Mercie Concave Yellow Atomic", wtype = 11, mod = 76, price = 5500},
		{name = "Sugoi Concave Yellow Atomic", wtype = 11, mod = 77, price = 5500},
		{name = "Synthetic Z Concave Yellow Atomic", wtype = 11, mod = 78, price = 5500},
		{name = "Endo v.4 Dished Yellow Atomic", wtype = 11, mod = 79, price = 5500},
		{name = "Hyperfresh Yellow Atomic", wtype = 11, mod = 80, price = 5500},
		{name = "Truffade Concave Yellow Atomic", wtype = 11, mod = 81, price = 5500},
		{name = "Organic Type II Yellow Atomic", wtype = 11, mod = 82, price = 5500},
		{name = "Big Mamba Yellow Atomic", wtype = 11, mod = 83, price = 5500},
		{name = "Deep Flake Yellow Atomic", wtype = 11, mod = 84, price = 5500},
		{name = "Cosmo MKIII Yellow Atomic", wtype = 11, mod = 85, price = 5500},
		{name = "Concave Racer Yellow Atomic", wtype = 11, mod = 86, price = 5500},
		{name = "Deep Flake Reverse Yellow Atomic", wtype = 11, mod = 87, price = 5500},
		{name = "Wild Wagon Yellow Atomic", wtype = 11, mod = 88, price = 5500},
		{name = "Concave Mega Mesh Yellow Atomic", wtype = 11, mod = 89, price = 5500},
		{name = "Retro Steelie Red Funkaru", wtype = 11, mod = 120, price = 5500},
		{name = "Poverty Spec Steelie Red Funkaru", wtype = 11, mod = 121, price = 5500},
		{name = "Concave Steelie Red Funkaru", wtype = 11, mod = 122, price = 5500},
		{name = "Nebula Red Funkaru", wtype = 11, mod = 123, price = 5500},
		{name = "Hotring Steelie Red Funkaru", wtype = 11, mod = 124, price = 5500},
		{name = "Cup Champion Red Funkaru", wtype = 11, mod = 125, price = 5500},
		{name = "Stanced EG Custom Red Funkaru", wtype = 11, mod = 126, price = 5500},
		{name = "Kracka Custom Red Funkaru", wtype = 11, mod = 127, price = 5500},
		{name = "Dukes Custom Red Funkaru", wtype = 11, mod = 128, price = 5500},
		{name = "Endo v.3 Custom Red Funkaru", wtype = 11, mod = 129, price = 5500},
		{name = "V8 Killer Red Funkaru", wtype = 11, mod = 130, price = 5500},
		{name = "Fujiwara Custom Red Funkaru", wtype = 11, mod = 131, price = 5500},
		{name = "Cosmo MKII Red Funkaru", wtype = 11, mod = 132, price = 5500},
		{name = "Aero Star Red Funkaru", wtype = 11, mod = 133, price = 5500},
		{name = "Hype Five Red Funkaru", wtype = 11, mod = 134, price = 5500},
		{name = "Ruff Weld Mega Deep Red Funkaru", wtype = 11, mod = 135, price = 5500},
		{name = "Mercie Concave Red Funkaru", wtype = 11, mod = 136, price = 5500},
		{name = "Sugoi Concave Red Funkaru", wtype = 11, mod = 137, price = 5500},
		{name = "Synthetic Z Concave Red Funkaru", wtype = 11, mod = 138, price = 5500},
		{name = "Endo v.4 Dished Red Funkaru", wtype = 11, mod = 139, price = 5500},
		{name = "Hyperfresh Red Funkaru", wtype = 11, mod = 140, price = 5500},
		{name = "Truffade Concave Red Funkaru", wtype = 11, mod = 141, price = 5500},
		{name = "Organic Type II Red Funkaru", wtype = 11, mod = 142, price = 5500},
		{name = "Big Mamba Red Funkaru", wtype = 11, mod = 143, price = 5500},
		{name = "Deep Flake Red Funkaru", wtype = 11, mod = 144, price = 5500},
		{name = "Cosmo MKIII Red Funkaru", wtype = 11, mod = 145, price = 5500},
		{name = "Concave Racer Red Funkaru", wtype = 11, mod = 146, price = 5500},
		{name = "Deep Flake Reverse Red Funkaru", wtype = 11, mod = 147, price = 5500},
		{name = "Wild Wagon Red Funkaru", wtype = 11, mod = 148, price = 5500},
		{name = "Concave Mega Mesh Red Funkaru", wtype = 11, mod = 149, price = 5500},
		{name = "Retro Steelie yellow chepalle", wtype = 11, mod = 150, price = 6500},
		{name = "Poverty Spec Steelie yellow chepalle", wtype = 11, mod = 151, price = 6500},
		{name = "Concave Steelie yellow chepalle", wtype = 11, mod = 152, price = 6500},
		{name = "Nebula yellow chepalle", wtype = 11, mod = 153, price = 6500},
		{name = "Hotring Steelie yellow chepalle", wtype = 11, mod = 154, price = 6500},
		{name = "Cup Champion yellow chepalle", wtype = 11, mod = 155, price = 6500},
		{name = "Stanced EG Custom yellow chepalle", wtype = 11, mod = 156, price = 6500},
		{name = "Kracka Custom yellow chepalle", wtype = 11, mod = 157, price = 6500},
		{name = "Dukes Custom yellow chepalle", wtype = 11, mod = 158, price = 6500},
		{name = "Endo v.3 Custom yellow chepalle", wtype = 11, mod = 159, price = 6500},
		{name = "V8 Killer yellow chepalle", wtype = 11, mod = 160, price = 6500},
		{name = "Fujiwara Custom yellow chepalle", wtype = 11, mod = 161, price = 6500},
		{name = "Cosmo MKII yellow chepalle", wtype = 11, mod = 162, price = 6500},
		{name = "Aero Star yellow chepalle", wtype = 11, mod = 163, price = 6500},
		{name = "Hype Five yellow chepalle", wtype = 11, mod = 164, price = 6500},
		{name = "Ruff Weld Mega Deep yellow chepalle", wtype = 11, mod = 165, price = 6500},
		{name = "Mercie Concave yellow chepalle", wtype = 11, mod = 166, price = 6500},
		{name = "Sugoi Concave yellow chepalle", wtype = 11, mod = 167, price = 6500},
		{name = "Synthetic Z Concave yellow chepalle", wtype = 11, mod = 168, price = 6500},
		{name = "Endo v.4 Dished yellow chepalle", wtype = 11, mod = 169, price = 6500},
		{name = "Hyperfresh yellow chepalle", wtype = 11, mod = 170, price = 6500},
		{name = "Truffade Concave yellow chepalle", wtype = 11, mod = 171, price = 6500},
		{name = "Organic Type II yellow chepalle", wtype = 11, mod = 172, price = 6500},
		{name = "Big Mamba yellow chepalle", wtype = 11, mod = 173, price = 6500},
		{name = "Deep Flake yellow chepalle", wtype = 11, mod = 174, price = 6500},
		{name = "Cosmo MKIII yellow chepalle", wtype = 11, mod = 175, price = 6500},
		{name = "Concave Racer yellow chepalle", wtype = 11, mod = 176, price = 6500},
		{name = "Deep Flake Reverse yellow chepalle", wtype = 11, mod = 177, price = 6500},
		{name = "Wild Wagon yellow chepalle", wtype = 11, mod = 178, price = 6500},
		{name = "Concave Mega Mesh yellow chepalle", wtype = 11, mod = 179, price = 6500},
		{name = "Retro Steelie Green chepalle", wtype = 11, mod = 180, price = 6500},
		{name = "Poverty Spec Steelie Green chepalle", wtype = 11, mod = 181, price = 6500},
		{name = "Concave Steelie Green chepalle", wtype = 11, mod = 182, price = 6500},
		{name = "Nebula Green chepalle", wtype = 11, mod = 183, price = 6500},
		{name = "Hotring Steelie Green chepalle", wtype = 11, mod = 184, price = 6500},
		{name = "Cup Champion Green chepalle", wtype = 11, mod = 185, price = 6500},
		{name = "Stanced EG Custom Green chepalle", wtype = 11, mod = 186, price = 6500},
		{name = "Kracka Custom Green chepalle", wtype = 11, mod = 187, price = 6500},
		{name = "Dukes Custom Green chepalle", wtype = 11, mod = 188, price = 6500},
		{name = "Endo v.3 Custom Green chepalle", wtype = 11, mod = 189, price = 6500},
		{name = "V8 Killer Green chepalle", wtype = 11, mod = 190, price = 6500},
		{name = "Fujiwara Custom Green chepalle", wtype = 11, mod = 191, price = 6500},
		{name = "Cosmo MKII Green chepalle", wtype = 11, mod = 192, price = 6500},
		{name = "Aero Star Green chepalle", wtype = 11, mod = 193, price = 6500},
		{name = "Hype Five Green chepalle", wtype = 11, mod = 194, price = 6500},
		{name = "Ruff Weld Mega Deep Green chepalle", wtype = 11, mod = 195, price = 6500},
		{name = "Mercie Concave Green chepalle", wtype = 11, mod = 196, price = 6500},
		{name = "Sugoi Concave Green chepalle", wtype = 11, mod = 197, price = 6500},
		{name = "Synthetic Z Concave Green chepalle", wtype = 11, mod = 198, price = 6500},
		{name = "Endo v.4 Dished Green chepalle", wtype = 11, mod = 199, price = 6500},
		{name = "Hyperfresh Green chepalle", wtype = 11, mod = 200, price = 6500},
		{name = "Truffade Concave Green chepalle", wtype = 11, mod = 201, price = 6500},
		{name = "Organic Type II Green chepalle", wtype = 11, mod = 202, price = 6500},
		{name = "Big Mamba Green chepalle", wtype = 11, mod = 203, price = 6500},
		{name = "Deep Flake Green chepalle", wtype = 11, mod = 204, price = 6500},
		{name = "Cosmo MKIII Green chepalle", wtype = 11, mod = 205, price = 6500},
		{name = "Concave Racer Green chepalle", wtype = 11, mod = 206, price = 6500},
		{name = "Deep Flake Reverse Green chepalle", wtype = 11, mod = 207, price = 6500},
		{name = "Wild Wagon Green chepalle", wtype = 11, mod = 208, price = 6500},
		{name = "Concave Mega Mesh Green chepalle", wtype = 11, mod = 209, price = 6500},
		{name = "Retro Steelie White Atomic", wtype = 11, mod = 30, price = 7500},
		{name = "Poverty Spec Steelie White Atomic", wtype = 11, mod = 31, price = 7500},
		{name = "Concave Steelie White Atomic", wtype = 11, mod = 32, price = 7500},
		{name = "Nebula White Atomic", wtype = 11, mod = 33, price = 7500},
		{name = "Hotring Steelie White Atomic", wtype = 11, mod = 34, price = 7500},
		{name = "Cup Champion White Atomic", wtype = 11, mod = 35, price = 7500},
		{name = "Stanced EG Custom White Atomic", wtype = 11, mod = 36, price = 7500},
		{name = "Kracka Custom White Atomic", wtype = 11, mod = 37, price = 7500},
		{name = "Dukes Custom White Atomic", wtype = 11, mod = 38, price = 7500},
		{name = "Endo v.3 Custom White Atomic", wtype = 11, mod = 39, price = 7500},
		{name = "V8 Killer White Atomic", wtype = 11, mod = 40, price = 7500},
		{name = "Fujiwara Custom White Atomic", wtype = 11, mod = 41, price = 7500},
		{name = "Cosmo MKII White Atomic", wtype = 11, mod = 42, price = 7500},
		{name = "Aero Star White Atomic", wtype = 11, mod = 43, price = 7500},
		{name = "Hype Five White Atomic", wtype = 11, mod = 44, price = 7500},
		{name = "Ruff Weld Mega Deep White Atomic", wtype = 11, mod = 45, price = 7500},
		{name = "Mercie Concave White Atomic", wtype = 11, mod = 46, price = 7500},
		{name = "Sugoi Concave White Atomic", wtype = 11, mod = 47, price = 7500},
		{name = "Synthetic Z Concave White Atomic", wtype = 11, mod = 48, price = 7500},
		{name = "Endo v.4 Dished White Atomic", wtype = 11, mod = 49, price = 7500},
		{name = "Hyperfresh White Atomic", wtype = 11, mod = 50, price = 7500},
		{name = "Truffade Concave White Atomic", wtype = 11, mod = 51, price = 7500},
		{name = "Organic Type II White Atomic", wtype = 11, mod = 52, price = 7500},
		{name = "Big Mamba White Atomic", wtype = 11, mod = 53, price = 7500},
		{name = "Deep Flake White Atomic", wtype = 11, mod = 54, price = 7500},
		{name = "Cosmo MKIII White Atomic", wtype = 11, mod = 55, price = 7500},
		{name = "Concave Racer White Atomic", wtype = 11, mod = 56, price = 7500},
		{name = "Deep Flake Reverse White Atomic", wtype = 11, mod = 57, price = 7500},
		{name = "Wild Wagon White Atomic", wtype = 11, mod = 58, price = 7500},
		{name = "Concave Mega Mesh White Atomic", wtype = 11, mod = 59, price = 7500},
		{name = "Retro Steelie White Funkaru", wtype = 11, mod = 90, price = 7500},
		{name = "Poverty Spec Steelie White Funkaru", wtype = 11, mod = 91, price = 7500},
		{name = "Concave Steelie White Funkaru", wtype = 11, mod = 92, price = 7500},
		{name = "Nebula White Funkaru", wtype = 11, mod = 93, price = 7500},
		{name = "Hotring Steelie White Funkaru", wtype = 11, mod = 94, price = 7500},
		{name = "Cup Champion White Funkaru", wtype = 11, mod = 95, price = 7500},
		{name = "Stanced EG Custom White Funkaru", wtype = 11, mod = 96, price = 7500},
		{name = "Kracka Custom White Funkaru", wtype = 11, mod = 97, price = 7500},
		{name = "Dukes Custom White Funkaru", wtype = 11, mod = 98, price = 7500},
		{name = "Endo v.3 Custom White Funkaru", wtype = 11, mod = 99, price = 7500},
		{name = "V8 Killer White Funkaru", wtype = 11, mod = 100, price = 7500},
		{name = "Fujiwara Custom White Funkaru", wtype = 11, mod = 101, price = 7500},
		{name = "Cosmo MKII White Funkaru", wtype = 11, mod = 102, price = 7500},
		{name = "Aero Star White Funkaru", wtype = 11, mod = 103, price = 7500},
		{name = "Hype Five White Funkaru", wtype = 11, mod = 104, price = 7500},
		{name = "Ruff Weld Mega Deep White Funkaru", wtype = 11, mod = 105, price = 7500},
		{name = "Mercie Concave White Funkaru", wtype = 11, mod = 106, price = 7500},
		{name = "Sugoi Concave White Funkaru", wtype = 11, mod = 107, price = 7500},
		{name = "Synthetic Z Concave White Funkaru", wtype = 11, mod = 108, price = 7500},
		{name = "Endo v.4 Dished White Funkaru", wtype = 11, mod = 109, price = 7500},
		{name = "Hyperfresh White Funkaru", wtype = 11, mod = 110, price = 7500},
		{name = "Truffade Concave White Funkaru", wtype = 11, mod = 111, price = 7500},
		{name = "Organic Type II White Funkaru", wtype = 11, mod = 112, price = 7500},
		{name = "Big Mamba White Funkaru", wtype = 11, mod = 113, price = 7500},
		{name = "Deep Flake White Funkaru", wtype = 11, mod = 114, price = 7500},
		{name = "Cosmo MKIII White Funkaru", wtype = 11, mod = 115, price = 7500},
		{name = "Concave Racer White Funkaru", wtype = 11, mod = 116, price = 7500},
		{name = "Deep Flake Reverse White Funkaru", wtype = 11, mod = 117, price = 7500},
		{name = "Wild Wagon White Funkaru", wtype = 11, mod = 118, price = 7500},
		{name = "Concave Mega Mesh White Funkaru", wtype = 11, mod = 119, price = 7500},
	},
-----Track wheels - Los Santos Tuners DLC-----
	trackwheels = {
		{name = "Stock", wtype = 12, mod = -1, price = 1000},
		{name = "Rally Throwback", wtype = 12, mod = 0, price = 2000},
		{name = "Gravel Trap", wtype = 12, mod = 1, price = 2000},
		{name = "Stove Top", wtype = 12, mod = 2, price = 2000},
		{name = "Stove Top Mesh", wtype = 12, mod = 3, price = 2000},
		{name = "Retro 3 Piece", wtype = 12, mod = 4, price = 2000},
		{name = "Rally Monoblock", wtype = 12, mod = 5, price = 2000},
		{name = "Forged 5", wtype = 12, mod = 6, price = 2000},
		{name = "Split Star", wtype = 12, mod = 7, price = 2000},
		{name = "Speed Boy", wtype = 12, mod = 8, price = 2000},
		{name = "90s Running", wtype = 12, mod = 9, price = 3000},
		{name = "Tropos", wtype = 12, mod = 10, price = 3000},
		{name = "Exos", wtype = 12, mod = 11, price = 3000},
		{name = "High Five", wtype = 12, mod = 12, price = 3000},
		{name = "Super Luxe", wtype = 12, mod = 13, price = 3000},
		{name = "Pure Business", wtype = 12, mod = 14, price = 3000},
		{name = "Pepper Pot", wtype = 12, mod = 15, price = 3000},
		{name = "Blacktop Blender", wtype = 12, mod = 16, price = 3000},
		{name = "Throwback", wtype = 12, mod = 17, price = 3000},
		{name = "Expressway", wtype = 12, mod = 18, price = 4000},
		{name = "Hidden Six", wtype = 12, mod = 19, price = 4000},
		{name = "Dinka SPL", wtype = 12, mod = 20, price = 4000},
		{name = "Retro Turbofan", wtype = 12, mod = 21, price = 4000},
		{name = "Conical Turbofan", wtype = 12, mod = 22, price = 4000},
		{name = "Ice Storm", wtype = 12, mod = 23, price = 4000},
		{name = "Super Turbine", wtype = 12, mod = 24, price = 4000},
		{name = "Modern Mesh", wtype = 12, mod = 25, price = 4000},
		{name = "Forged Star", wtype = 12, mod = 26, price = 4000},
		{name = "Snowflake", wtype = 12, mod = 27, price = 5000},
		{name = "Giga Mesh", wtype = 12, mod = 28, price = 5000},
		{name = "Mesh Meister", wtype = 12, mod = 29, price = 5000},
		-- Below are the Los Santos Tuners wheel update with logos
		{name = "Rally Throwback Yellow Atomic", wtype = 12, mod = 60, price = 5500},
		{name = "Gravel Trap Yellow Atomic", wtype = 12, mod = 61, price = 5500},
		{name = "Stove Top Yellow Atomic", wtype = 12, mod = 62, price = 5500},
		{name = "Stove Top Mesh Yellow Atomic", wtype = 12, mod = 63, price = 5500},
		{name = "Retro 3 Piece Yellow Atomic", wtype = 12, mod = 64, price = 5500},
		{name = "Rally Monoblock Yellow Atomic", wtype = 12, mod = 65, price = 5500},
		{name = "Forged 5 Yellow Atomic", wtype = 12, mod = 66, price = 5500},
		{name = "Split Star Yellow Atomic", wtype = 12, mod = 67, price = 5500},
		{name = "Speed Boy Yellow Atomic", wtype = 12, mod = 68, price = 5500},
		{name = "90s Running Yellow Atomic", wtype = 12, mod = 69, price = 5500},
		{name = "Tropos Yellow Atomic", wtype = 12, mod = 70, price = 5500},
		{name = "Exos Yellow Atomic", wtype = 12, mod = 71, price = 5500},
		{name = "High Five Yellow Atomic", wtype = 12, mod = 72, price = 5500},
		{name = "Super Luxe Yellow Atomic", wtype = 12, mod = 73, price = 5500},
		{name = "Pure Business Yellow Atomic", wtype = 12, mod = 74, price = 5500},
		{name = "Pepper Pot Yellow Atomic", wtype = 12, mod = 75, price = 5500},
		{name = "Blacktop Blender Yellow Atomic", wtype = 12, mod = 76, price = 5500},
		{name = "Throwback Yellow Atomic", wtype = 12, mod = 77, price = 5500},
		{name = "Expressway Yellow Atomic", wtype = 12, mod = 78, price = 5500},
		{name = "Hidden Six Yellow Atomic", wtype = 12, mod = 79, price = 5500},
		{name = "Dinka SPL Yellow Atomic", wtype = 12, mod = 80, price = 5500},
		{name = "Retro Turbofan Yellow Atomic", wtype = 12, mod = 81, price = 5500},
		{name = "Conical Turbofan  Yellow Atomic", wtype = 12, mod = 82, price = 5500},
		{name = "Ice Storm  Yellow Atomic", wtype = 12, mod = 83, price = 5500},
		{name = "Super Turbine Yellow Atomic", wtype = 12, mod = 84, price = 5500},
		{name = "Modern Mesh Yellow Atomic", wtype = 12, mod = 85, price = 5500},
		{name = "Forged Star Yellow Atomic", wtype = 12, mod = 86, price = 5500},
		{name = "Snowflake Yellow Atomic", wtype = 12, mod = 87, price = 5500},
		{name = "Giga Mesh Yellow Atomic", wtype = 12, mod = 88, price = 5500},
		{name = "Mesh Meister yellow Atomic", wtype = 12, mod = 89, price = 5500},
		{name = "Rally Throwback Red funkaru", wtype = 12, mod = 120, price = 5500},
		{name = "Gravel Trap Red funkaru", wtype = 12, mod = 121, price = 5500},
		{name = "Stove Top Red funkaru", wtype = 12, mod = 122, price = 5500},
		{name = "Stove Top Mesh Red funkaru", wtype = 12, mod = 123, price = 5500},
		{name = "Retro 3 Piece Red funkaru", wtype = 12, mod = 124, price = 5500},
		{name = "Rally Monoblock Red funkaru", wtype = 12, mod = 125, price = 5500},
		{name = "Forged 5 Red funkaru", wtype = 12, mod = 126, price = 5500},
		{name = "Split Star Red funkaru", wtype = 12, mod = 127, price = 5500},
		{name = "Speed Boy Red funkaru", wtype = 12, mod = 128, price = 5500},
		{name = "90s Running Red funkaru", wtype = 12, mod = 129, price = 5500},
		{name = "Tropos Red funkaru", wtype = 12, mod = 130, price = 5500},
		{name = "Exos Red funkaru", wtype = 12, mod = 131, price = 5500},
		{name = "High Five Red funkaru", wtype = 12, mod = 132, price = 5500},
		{name = "Super Luxe Red funkaru", wtype = 12, mod = 133, price = 5500},
		{name = "Pure Business Red funkaru", wtype = 12, mod = 134, price = 5500},
		{name = "Pepper Pot Red funkaru", wtype = 12, mod = 135, price = 5500},
		{name = "Blacktop Blender Red funkaru", wtype = 12, mod = 136, price = 5500},
		{name = "Throwback Red funkaru", wtype = 12, mod = 137, price = 5500},
		{name = "Expressway Red funkaru", wtype = 12, mod = 138, price = 5500},
		{name = "Hidden Six Red funkaru", wtype = 12, mod = 139, price = 5500},
		{name = "Dinka SPL Red funkaru", wtype = 12, mod = 140, price = 5500},
		{name = "Retro Turbofan Red funkaru", wtype = 12, mod = 141, price = 5500},
		{name = "Conical Turbofan Red funkaru", wtype = 12, mod = 142, price = 5500},
		{name = "Ice Storm Red funkaru", wtype = 12, mod = 143, price = 5500},
		{name = "Super Turbine Red funkaru", wtype = 12, mod = 144, price = 5500},
		{name = "Modern Mesh Red funkaru", wtype = 12, mod = 145, price = 5500},
		{name = "Forged Star Red funkaru", wtype = 12, mod = 146, price = 5500},
		{name = "Snowflake Red funkaru", wtype = 12, mod = 147, price = 5500},
		{name = "Giga Mesh Red funkaru", wtype = 12, mod = 148, price = 5500},
		{name = "Mesh Meister red funkaru", wtype = 12, mod = 149, price = 5500},
		{name = "Rally Throwback yellow chepalle", wtype = 12, mod = 150, price = 6500},
		{name = "Gravel Trap yellow chepalle", wtype = 12, mod = 151, price = 6500},
		{name = "Stove Top yellow chepalle", wtype = 12, mod = 152, price = 6500},
		{name = "Stove Top Mesh yellow chepalle", wtype = 12, mod = 153, price = 6500},
		{name = "Retro 3 Piece yellow chepalle", wtype = 12, mod = 154, price = 6500},
		{name = "Rally Monoblock yellow chepalle", wtype = 12, mod = 155, price = 6500},
		{name = "Forged 5 yellow chepalle", wtype = 12, mod = 156, price = 6500},
		{name = "Split Star yellow chepalle", wtype = 12, mod = 157, price = 6500},
		{name = "Speed Boy yellow chepalle", wtype = 12, mod = 158, price = 6500},
		{name = "90s Running yellow chepalle", wtype = 12, mod = 159, price = 6500},
		{name = "Tropos yellow chepalle", wtype = 12, mod = 160, price = 6500},
		{name = "Exos yellow chepalle", wtype = 12, mod = 161, price = 6500},
		{name = "High Five yellow chepalle", wtype = 12, mod = 162, price = 6500},
		{name = "Super Luxe yellow chepalle", wtype = 12, mod = 163, price = 6500},
		{name = "Pure Business yellow chepalle", wtype = 12, mod = 164, price = 6500},
		{name = "Pepper Pot yellow chepalle", wtype = 12, mod = 165, price = 6500},
		{name = "Blacktop Blender yellow chepalle", wtype = 12, mod = 166, price = 6500},
		{name = "Throwback yellow chepalle", wtype = 12, mod = 167, price = 6500},
		{name = "Expressway yellow chepalle", wtype = 12, mod = 168, price = 6500},
		{name = "Hidden Six yellow chepalle", wtype = 12, mod = 169, price = 6500},
		{name = "Dinka SPL yellow chepalle", wtype = 12, mod = 170, price = 6500},
		{name = "Retro Turbofan yellow chepalle", wtype = 12, mod = 171, price = 6500},
		{name = "Conical Turbofan yellow chepalle", wtype = 12, mod = 172, price = 6500},
		{name = "Ice Storm yellow chepalle", wtype = 12, mod = 173, price = 6500},
		{name = "Super Turbine yellow chepalle", wtype = 12, mod = 174, price = 6500},
		{name = "Modern Mesh yellow chepalle", wtype = 12, mod = 175, price = 6500},
		{name = "Forged Star yellow chepalle", wtype = 12, mod = 176, price = 6500},
		{name = "Snowflake yellow chepalle", wtype = 12, mod = 177, price = 6500},
		{name = "Giga Mesh yellow chepalle", wtype = 12, mod = 178, price = 6500},
		{name = "Mesh Meister yellow chepalle", wtype = 12, mod = 179, price = 6500},
		{name = "Rally Throwback Green chepalle", wtype = 12, mod = 180, price = 6500},
		{name = "Gravel Trap Green chepalle", wtype = 12, mod = 181, price = 6500},
		{name = "Stove Top Green chepalle", wtype = 12, mod = 182, price = 6500},
		{name = "Stove Top Mesh Green chepalle", wtype = 12, mod = 183, price = 6500},
		{name = "Retro 3 Piece Green chepalle", wtype = 12, mod = 184, price = 6500},
		{name = "Rally Monoblock Green chepalle", wtype = 12, mod = 185, price = 6500},
		{name = "Forged 5 Green chepalle", wtype = 12, mod = 186, price = 6500},
		{name = "Split Star Green chepalle", wtype = 12, mod = 187, price = 6500},
		{name = "Speed Boy Green chepalle", wtype = 12, mod = 188, price = 6500},
		{name = "90s Running Green chepalle", wtype = 12, mod = 189, price = 6500},
		{name = "Tropos Green chepalle", wtype = 12, mod = 190, price = 6500},
		{name = "Exos Green chepalle", wtype = 12, mod = 191, price = 6500},
		{name = "High Five Green chepalle", wtype = 12, mod = 192, price = 6500},
		{name = "Super Luxe Green chepalle", wtype = 12, mod = 193, price = 6500},
		{name = "Pure Business Green chepalle", wtype = 12, mod = 194, price = 6500},
		{name = "Pepper Pot Green chepalle", wtype = 12, mod = 195, price = 6500},
		{name = "Blacktop Blender Green chepalle", wtype = 12, mod = 196, price = 6500},
		{name = "Throwback Green chepalle", wtype = 12, mod = 197, price = 6500},
		{name = "Expressway Green chepalle", wtype = 12, mod = 198, price = 6500},
		{name = "Hidden Six Green chepalle", wtype = 12, mod = 199, price = 6500},
		{name = "Dinka SPL Green chepalle", wtype = 12, mod = 200, price = 6500},
		{name = "Retro Turbofan Green chepalle", wtype = 12, mod = 201, price = 6500},
		{name = "Conical Turbofan Green chepalle", wtype = 12, mod = 202, price = 6500},
		{name = "Ice Storm Green chepalle", wtype = 12, mod = 203, price = 6500},
		{name = "Super Turbine Green chepalle", wtype = 12, mod = 204, price = 6500},
		{name = "Modern Mesh Green chepalle", wtype = 12, mod = 205, price = 6500},
		{name = "Forged Star Green chepalle", wtype = 12, mod = 206, price = 6500},
		{name = "Snowflake Green chepalle", wtype = 12, mod = 207, price = 6500},
		{name = "Giga Mesh Green chepalle", wtype = 12, mod = 208, price = 6500},
		{name = "Mesh Meister Green chepalle", wtype = 12, mod = 209, price = 6500},
		{name = "Rally Throwback White Atomic", wtype = 12, mod = 30, price = 7500},
		{name = "Gravel Trap White Atomic", wtype = 12, mod = 31, price = 7500},
		{name = "Stove Top White Atomic", wtype = 12, mod = 32, price = 7500},
		{name = "Stove Top Mesh White Atomic", wtype = 12, mod = 33, price = 7500},
		{name = "Retro 3 Piece White Atomic", wtype = 12, mod = 34, price = 7500},
		{name = "Rally Monoblock White Atomic", wtype = 12, mod = 35, price = 7500},
		{name = "Forged 5 White Atomic", wtype = 12, mod = 36, price = 7500},
		{name = "Split Star White Atomic", wtype = 12, mod = 37, price = 7500},
		{name = "Speed Boy White Atomic", wtype = 12, mod = 38, price = 7500},
		{name = "90s Running White Atomic", wtype = 12, mod = 39, price = 7500},
		{name = "Tropos White Atomic", wtype = 12, mod = 40, price = 7500},
		{name = "Exos White Atomic", wtype = 12, mod = 41, price = 7500},
		{name = "High Five White Atomic", wtype = 12, mod = 42, price = 7500},
		{name = "Super Luxe White Atomic", wtype = 12, mod = 43, price = 7500},
		{name = "Pure Business White Atomic", wtype = 12, mod = 44, price = 7500},
		{name = "Pepper Pot White Atomic", wtype = 12, mod = 45, price = 7500},
		{name = "Blacktop Blender White Atomic", wtype = 12, mod = 46, price = 7500},
		{name = "Throwback White Atomic", wtype = 12, mod = 47, price = 7500},
		{name = "Expressway White Atomic", wtype = 12, mod = 48, price = 7500},
		{name = "Hidden Six White Atomic", wtype = 12, mod = 49, price = 7500},
		{name = "Dinka SPL White Atomic", wtype = 12, mod = 50, price = 7500},
		{name = "Retro Turbofan White Atomic", wtype = 12, mod = 51, price = 7500},
		{name = "Conical Turbofan White Atomic", wtype = 12, mod = 52, price = 7500},
		{name = "Ice Storm White Atomic", wtype = 12, mod = 53, price = 7500},
		{name = "Super Turbine White Atomic", wtype = 12, mod = 54, price = 7500},
		{name = "Modern Mesh White Atomic", wtype = 12, mod = 55, price = 7500},
		{name = "Forged Star White Atomic", wtype = 12, mod = 56, price = 7500},
		{name = "Snowflake White Atomic", wtype = 12, mod = 57, price = 7500},
		{name = "Giga Mesh White Atomic", wtype = 12, mod = 58, price = 7500},
		{name = "Mesh Meister White Atomic", wtype = 12, mod = 59, price = 7500},
		{name = "Rally Throwback White funkaru", wtype = 12, mod = 90, price = 7500},
		{name = "Gravel Trap White funkaru", wtype = 12, mod = 91, price = 7500},
		{name = "Stove Top White funkaru", wtype = 12, mod = 92, price = 7500},
		{name = "Stove Top Mesh White funkaru", wtype = 12, mod = 93, price = 7500},
		{name = "Retro 3 Piece White funkaru", wtype = 12, mod = 94, price = 7500},
		{name = "Rally Monoblock White funkaru", wtype = 12, mod = 95, price = 7500},
		{name = "Forged 5 White funkaru", wtype = 12, mod = 96, price = 7500},
		{name = "Split Star White funkaru", wtype = 12, mod = 97, price = 7500},
		{name = "Speed Boy White funkaru", wtype = 12, mod = 98, price = 7500},
		{name = "90s Running White funkaru", wtype = 12, mod = 99, price = 7500},
		{name = "Tropos White funkaru", wtype = 12, mod = 100, price = 7500},
		{name = "Exos White funkaru", wtype = 12, mod = 101, price = 7500},
		{name = "High Five White funkaru", wtype = 12, mod = 102, price = 7500},
		{name = "Super Luxe White funkaru", wtype = 12, mod = 103, price = 7500},
		{name = "Pure Business White funkaru", wtype = 12, mod = 104, price = 7500},
		{name = "Pepper Pot White funkaru", wtype = 12, mod = 105, price = 7500},
		{name = "Blacktop Blender White funkaru", wtype = 12, mod = 106, price = 7500},
		{name = "Throwback White funkaru", wtype = 12, mod = 107, price = 7500},
		{name = "Expressway White funkaru", wtype = 12, mod = 108, price = 7500},
		{name = "Hidden Six White funkaru", wtype = 12, mod = 109, price = 7500},
		{name = "Dinka SPL White funkaru", wtype = 12, mod = 110, price = 7500},
		{name = "Retro Turbofan White funkaru", wtype = 12, mod = 111, price = 7500},
		{name = "Conical Turbofan White funkaru", wtype = 12, mod = 112, price = 7500},
		{name = "Ice Storm White funkaru", wtype = 12, mod = 113, price = 7500},
		{name = "Super Turbine White funkaru", wtype = 12, mod = 114, price = 7500},
		{name = "Modern Mesh White funkaru", wtype = 12, mod = 115, price = 7500},
		{name = "Forged Star White funkaru", wtype = 12, mod = 116, price = 7500},
		{name = "Snowflake White funkaru", wtype = 12, mod = 117, price = 7500},
		{name = "Giga Mesh White funkaru", wtype = 12, mod = 118, price = 7500},
		{name = "Mesh Meister White funkaru", wtype = 12, mod = 119, price = 7500},
	},

---------Trim color--------
	trim = {
		colors = colors,
		price = 325
	},

----------Mods-----------
	mods = {

----------Light Bar--------
	[49] = {
		startprice = 4000,
		increaseby = 325
	},

----------Liveries--------
	[48] = {
		startprice = 8000,
		increaseby = 500
	},

----------Doors Right--------
	[47] = {
		startprice = 4000,
		increaseby = 325
	},
----------Windows--------
	[46] = {
		startprice = 4000,
		increaseby = 325
	},

----------Tank--------
	[45] = {
		startprice = 4000,
		increaseby = 325
	},

----------Trim--------
	[44] = {
		startprice = 4000,
		increaseby = 325
	},

----------Aerials--------
	[43] = {
		startprice = 4000,
		increaseby = 325
	},

----------Arch cover--------
	[42] = {
		startprice = 4000,
		increaseby = 325
	},

----------Struts--------
	[41] = {
		startprice = 4000,
		increaseby = 325
	},

----------Air filter--------
	[40] = {
		startprice = 4000,
		increaseby = 325
	},

----------Engine block--------
	[39] = {
		startprice = 4000,
		increaseby = 325
	},

----------Hydraulics--------
	[38] = {
		startprice = 4000,
		increaseby = 325
	},

----------Trunk--------
	[37] = {
		startprice = 4000,
		increaseby = 325
	},

----------Speakers--------
	[36] = {
		startprice = 4000,
		increaseby = 325
	},

----------Plaques--------
	[35] = {
		startprice = 4000,
		increaseby = 325
	},

----------Shift leavers--------
	[34] = {
		startprice = 200,
		increaseby = 65
	},

----------Steeringwheel--------
	[33] = {
		startprice = 200,
		increaseby = 65
	},

----------Seats--------
	[32] = {
		startprice = 4000,
		increaseby = 1000
	},

----------Door speaker--------
	[31] = {
		startprice = 2000,
		increaseby = 325
	},

----------Dial--------
	[30] = {
		startprice = 4000,
		increaseby = 325
	},
----------Dashboard--------
	[29] = {
		startprice = 4000,
		increaseby = 325
	},

----------Ornaments--------
	[28] = {
		startprice = 4000,
		increaseby = 325
	},

----------Trim--------
	[27] = {
		startprice = 4000,
		increaseby = 325
	},

----------Vanity plates--------
	[26] = {
		startprice = 450,
		increaseby = 75
	},

----------Plate holder--------
	[25] = {
		startprice = 100,
		increaseby = 10
	},

---------Headlights---------
	[22] = {
		{name = "Stock Lights", mod = 0, price = 0},
		{name = "Xenon Lights", mod = 1, price = 2000},
	},

----------Nitros---------
	[17] = {
		startprice = 4000,
		increaseby = 325
	},

----------Turbo---------
	[18] = {
		{ name = "None", mod = 0, price = 0},
		{ name = "Turbo Tuning", mod = 1, price = 16500},
	},

----------Subwoofer---------
	[19] = {
		startprice = 4000,
		increaseby = 325
	},

----------Hydraulics---------
	[21] = {
		startprice = 4000,
		increaseby = 325
	},
-----------Armor-------------
	[16] = {
		{name = "Armor Upgrade 20%",modtype = 16, mod = 0, price = 2500},
		{name = "Armor Upgrade 40%",modtype = 16, mod = 1, price = 5000},
		{name = "Armor Upgrade 60%",modtype = 16, mod = 2, price = 7500},
		{name = "Armor Upgrade 80%",modtype = 16, mod = 3, price = 10000},
		{name = "Armor Upgrade 100%",modtype = 16, mod = 4, price = 12500},
	},

---------Suspension-----------
	[15] = {
		{name = "Lowered Suspension",mod = 0, price = 1100},
		{name = "Street Suspension",mod = 1, price = 2200},
		{name = "Sport Suspension",mod = 2, price = 3300},
		{name = "Competition Suspension",mod = 3, price = 4400},
	},

-----------Horn----------
	[14] = {
		{name = "Truck Horn", mod = 0, price = 200},
		--{name = "Police Horn", mod = 1, price = 2000},
		{name = "Clown Horn", mod = 2, price = 2000},
		{name = "Musical Horn 1", mod = 3, price = 2000},
		{name = "Musical Horn 2", mod = 4, price = 2000},
		{name = "Musical Horn 3", mod = 5, price = 2000},
		{name = "Musical Horn 4", mod = 6, price = 2000},
		{name = "Musical Horn 5", mod = 7, price = 2000},
		{name = "Sad Trombone Horn", mod = 8, price = 2000},
		{name = "Classical Horn 1", mod = 9, price = 2000},
		{name = "Classical Horn 2", mod = 10, price = 2000},
		{name = "Classical Horn 3", mod = 11, price = 2000},
		{name = "Classical Horn 4", mod = 12, price = 2000},
		{name = "Classical Horn 5", mod = 13, price = 2000},
		{name = "Classical Horn 6", mod = 14, price = 2000},
		{name = "Classical Horn 7", mod = 15, price = 2000},
		{name = "Scale Do Horn", mod = 16, price = 2000},
		{name = "Scale Re Horn", mod = 17, price = 2000},
		{name = "Scale Mi Horn", mod = 18, price = 2000},
		{name = "Scale Fa Horn", mod = 19, price = 2000},
		{name = "Scale So Horn", mod = 20, price = 2000},
		{name = "Scale La Horn", mod = 21, price = 2000},
		{name = "Scale Ti Horn", mod = 22, price = 2000},
		{name = "Scale Do(high) Horn", mod = 23, price = 2000},
		{name = "Jazz Horn 1", mod = 25, price = 2000},
		{name = "Jazz Horn 2", mod = 26, price = 2000},
		{name = "Jazz Horn 3", mod = 27, price = 2000},
		{name = "Star Spangled Banner 1", mod = 28, price = 2000},
		{name = "Star Spangled Banner 2", mod = 29, price = 2000},
		{name = "Star Spangled Banner 3", mod = 30, price = 2000},
		{name = "Star Spangled Banner 4", mod = 31, price = 2000},
		{name = "Classical Loop 1", mod = 32, price = 2000},
		{name = "Classical Loop 2", mod = 33, price = 2000},
		{name = "Classical Loop 3", mod = 34, price = 2000},
		{name = "Classical Loop 4", mod = 35, price = 2000},
		{name = "Halloween Loop 1", mod = 38, price = 2000},
		{name = "Halloween Loop 2", mod = 40, price = 2000},
		{name = "San Andreas Loop", mod = 42, price = 2000},
		{name = "Liberty City Loop", mod = 44, price = 2000},
		{name = "Festive Horn 1", mod = 46, price = 2000},
		{name = "Festive Horn 2", mod = 47, price = 2000},
		{name = "Festive Horn 3", mod = 48, price = 2000},
		{name = "Festive Horn 4", mod = 49, price = 2000},
		{name = "Festive Horn 5", mod = 50, price = 2000},
		{name = "Air Horn 1", mod = 52, price = 2000},
		{name = "Air Horn 2", mod = 54, price = 2000},
		{name = "Air Horn 3", mod = 56, price = 2000},
	},

----------Transmission---------
	[13] = {
		{name = "Street Transmission", mod = 0, price = 4400},
		{name = "Sports Transmission", mod = 1, price = 8800},
		{name = "Race Transmission", mod = 2, price = 13200},
	},

-----------Brakes-------------
	[12] = {
		{name = "Street Brakes", mod = 0, price = 4400},
		{name = "Sport Brakes", mod = 1, price = 8800},
		{name = "Race Brakes", mod = 2, price = 13200},
	},

------------Engine----------
	[11] = {
		{name = "EMS Upgrade, Level 1", mod = 0, price = 4400},
		{name = "EMS Upgrade, Level 2", mod = 1, price = 8800},
		{name = "EMS Upgrade, Level 3", mod = 2, price = 13200},
		{name = "EMS Upgrade, Level 4", mod = 3, price = 17600}
	},

	--- %30 Price Increase
-------------Roof----------
	[10] = {
		startprice = 1250,
		increaseby = 520
	},

------------Fenders---------
	[8] = {
		startprice = 1500,
		increaseby = 520
	},

------------Hood----------
	[7] = {
		startprice = 500,
		increaseby = 195
	},

----------Grille----------
	[6] = {
		startprice = 600,
		increaseby = 195
	},

----------Roll cage----------
	[5] = {
		startprice = 750,
		increaseby = 250
	},

----------Exhaust----------
	[4] = {
		startprice = 500,
		increaseby = 195
	},

----------Skirts----------
	[3] = {
		startprice = 500,
		increaseby = 100
	},

-----------Rear bumpers----------
	[2] = {
		startprice = 600,
		increaseby = 165
	},

----------Front bumpers----------
	[1] = {
		startprice = 600,
		increaseby = 165
	},

----------Spoiler----------
	[0] = {
		startprice = 850,
		increaseby = 165
	},
	}

}

------Model Blacklist--------
--Doesn't allow specific vehicles to be upgraded
LSC_Config.ModelBlacklist = {
	--"police",
}

--Sets if garage will be locked if someone is inside it already
LSC_Config.lock = false

--Enable/disable old entering way
LSC_Config.oldenter = true

--Menu settings
LSC_Config.menu = {

-------Controls--------
	controls = {
		menu_up = 27,
		menu_down = 173,
		menu_left = 174,
		menu_right = 175,
		menu_select = 201,
		menu_back = 177
	},

-------Menu position-----
	--Possible positions:
	--Left
	--Right
	--Custom position, example: position = {x = 0.2, y = 0.2}
	position = "left",

-------Menu theme--------
	--Possible themes: light, darkred, bluish, greenish
	--Custom example:
	--[[theme = {
		text_color = { r = 255,g = 255, b = 255, a = 255},
		bg_color = { r = 0,g = 0, b = 0, a = 155},
		--Colors when button is selected
		stext_color = { r = 0,g = 0, b = 0, a = 255},
		sbg_color = { r = 255,g = 255, b = 0, a = 200},
	},]]
	theme = "light",

--------Max buttons------
	--Default: 10
	maxbuttons = 10,

-------Size---------
	--[[
	Default:
	width = 0.24
	height = 0.36
	]]
	width = 0.24,
	height = 0.36
}
