tCustoms = {}
T.bindInstance('customs', tCustoms)

pCustoms = P.getInstance('lscustoms', 'customs')
pVehicles = P.getInstance('blrp_vehicles', 'vehicles')

local vehicle_names = {}

livery_flags = 0

function getCustomComponentPrice(component, model, index, blacklist)
  local default_price = 5000

  if component == 'plaque' then
    default_price = 10000
  end

  if component == 'skirt' then
    default_price = 10000
  end

  -- Specific model has no restrictions
  local items = blacklist[model]

  if not items then
    return false
  end

  -- Specific item has no restrictions
  local check = items[index]

  if not check then
    return false
  end

  -- Not table, can't have price defined. Return default price
  if type(check) ~= 'table' then
    return default_price
  end

  return check[2]
end

function canAccessCustomComponent(model, index, blacklist)
  local items = blacklist[model]

  if not items then
    return true
  end

  -- Specific index has no restrictions
  local check = items[index]

  if not check then
    return true
  end

  -- If table, check is the first index
  if type(check) == 'table' then
    check = check[1]
  end

  -- string, check if current user has group
  if type(check) == 'string' then
    return exports.blrp_core:me().hasOrInheritsGroup(check)
  end

  -- function, return the yield of that
  if type(check) == 'function' then
    return check()
  end

  return true
end

local police_fleet_vehicles = {
  'policetaxi',
  'pol_hellion',
  'sheriffsar',
  'pol_kuruma',
  'shergreenwd',
  'polgreenwd',
  'polalamop2a',
  'polscoutp',
  'polfugitivep',
  'polgresleyp',
  'polspeedop',
  'polbisonp',
  'polcarap',
  'caddyleo',
  'poltorencep',
  'polstanierp',
  'polalamop',
  'polalamop2',
  'polroamerp',
  'polroamerp2',
  'emsalamop',
  'emsbisonp',
  'emsbuffalop',
  'emscarap',
  'emsgresleyp',
  'emsroamerp',
  'emsscoutp',
  'emsspeedop',
  'emsstalkerp',
  'emsstanierp',
  'kamacho2',
  'hellion2',
  'polnovak',
  'imperialdoc',
  --'nkgauntlet4',
}

local emergency_vehicles = {
  "pbus3",
  "police",
  "police2",
  "police3",
  "policet",
 -- "polsovereignp",
  "policeb4",
  "policeb5",
  "sheriff",
  "sheriff2",
  "ambulance",
  "stretcher",
  "lguard",
  "firetrukr",
  "cvpi",
  "charger",
  "pbus",
  "tahoe",
  "explorer",
  "explorer2",
  "pranger",
  "bcsosheriff",
  "bcsosheriff2",
  "bcsosheriff3",
  'sheriffsar',
  'polnovak',
  'emsnspeedo',
  'sandbulance',
  'caddyems',
}

local no_perf_upgrades = {
  "pbus3",
  "cvpi",
  "charger",
  "fpis",
  "pbus",
  "tahoe",
  "explorer",
  "explorer2",
  "fbi2",
  "GTFsubrb",
  "pranger",
  "seasparrow2",
  "bcsosheriff",
  "bcsosheriff2",
  "bcsosheriff3",
  "polalamop2a",
  "polscoutp",
  "polfugitivep",
  "polgresleyp",
  "polspeedop",
  "polbisonp",
  "polcarap",
  "poltorencep",
  "polstanierp",
  "polalamop",
  "polalamop2",
  "polroamerp",
  "polroamerp2",
  'emsalamop',
  'emsbisonp',
  'emsbuffalop',
  'emscarap',
  'caddyleo',
  'caddyems',
  'emsgresleyp',
  'emsroamerp',
  'emsscoutp',
  'emsspeedop',
  'emsstalkerp',
  'emsstanierp',
  'kamacho2',
  'polnovak',
  'imperialdoc',
}

local no_wheel_changes = {
  'phantoma',
  'phantom3a',
  'haulera',
  'packera',
  'cerberusc',
  'cerberusc2',
  'longpath',
  'sadler2',
  'asea2',
  'rancherxl2',
  'mesa2',
  'emperor3',
  'tractor3',
  'burrito5',
  'rrtow',
  'poltor',
}

local no_respray_models = {
  'boxville2',
  'vwe_speedo2',
}

function GetVehicleVIN(vehicle)
  local vehicleNetwork = NetworkGetNetworkIdFromEntity(vehicle)
  local VehicleVIN = pVehicles.GetVehicleVINFromNetworkID({ vehicleNetwork })
  if not VehicleVIN then
    VehicleVIN = exports.blrp_vehicles:GetVehicleNumberPlateTextTrimmed(vehicle)
  end

  return VehicleVIN
end

function stringsplit(inputstr, sep)
  if not inputstr then
    return nil
  end

  if not sep then
    sep = '%s'
  end

  local t = {}

  for str in string.gmatch(inputstr, "([^" .. sep .. "]+)") do
    table.insert(t, str)
  end

  return t
end

local current_garage = nil
local colors_changed = false

local Menu = SetMenu()
local myveh = {}

local gameplaycam = nil
local cam = nil

tCustoms.openAdminShop = function()
  if not IsPedInAnyVehicle(PlayerPedId(), false) then
    return
  end

  current_garage = { is_admin = true }
  DriveInGarage()
end

local function allowChameleon()
  return true
end

local function f(n)
	return (n + 0.00001)
end

local function firstToUpper(str)
    return (str:gsub("^%l", string.upper))
end

function repair()
	SetVehicleFixed(myveh.vehicle)
end

local function round(num, idp)
  if idp and idp>0 then
    local mult = 10^idp
    return math.floor(num * mult + 0.5) / mult
  end
  return math.floor(num + 0.5)
end

--Setup main menu
local LSCMenu = Menu.new("Los Santos Customs","CATEGORIES", 0.16,0.13,0.24,0.36,0,{255,255,255,255})
LSCMenu.config.pcontrol = false

--Add mod to menu
local function AddMod(mod,parent,header,name,info,stock)
  local menu_debug = exports.blrp_core:me().get('lscustoms_debug')
	local veh = myveh.vehicle

  local vehicle_model = GetEntityModel(veh)

  local num_mods = GetNumVehicleMods(veh, mod)

  local num_blacklist = 0

  if mod_blacklist[vehicle_model] and mod_blacklist[vehicle_model][mod] and type(mod_blacklist[vehicle_model][mod]) == 'table' then
    for mod_id, _ in pairs(mod_blacklist[vehicle_model][mod]) do
      num_blacklist = num_blacklist + 1
    end
  end

  local block_category = false

  if mod_blacklist[vehicle_model] and mod_blacklist[vehicle_model][mod] and type(mod_blacklist[vehicle_model][mod]) == 'boolean' then
    block_category = true
  end

  if menu_debug then
    print('AddMod', veh, mod, parent, header, name, info, stock)
  end

	SetVehicleModKit(veh, 0)

	if not block_category and ((num_mods and num_mods > 0 and num_mods > num_blacklist) or mod == 18 or mod == 22) then
    if menu_debug then
      name = '[ ' .. mod .. ' ] ' .. name
    end

		local m = parent:addSubMenu(header, name, info,true)
		if stock then
			local btn = m:addPurchase("Stock")
			btn.modtype = mod
			btn.mod = -1
		end

    -- Check if pricing is defined for this mod type
    if LSC_Config.prices.mods[mod] then
		if LSC_Config.prices.mods[mod].startprice then
			for i = 0, tonumber(GetNumVehicleMods(veh,mod)) -1 do
        local mod_price = LSC_Config.prices.mods[mod].startprice + (i * LSC_Config.prices.mods[mod].increaseby)

        local block_mod = (mod_blacklist[vehicle_model] and mod_blacklist[vehicle_model][mod] and mod_blacklist[vehicle_model][mod][i])

        local block_livery = (mod == 48 and not canAccessCustomComponent(vehicle_model, i, custom_liveries)) or false
        local block_plaque = (mod == 35 and not canAccessCustomComponent(vehicle_model, i, custom_plaques)) or false
        local block_skirt  = (mod ==  3 and not canAccessCustomComponent(vehicle_model, i, custom_skirts)) or false

        if current_garage.is_local then
          mod_price = 10
        end

        if not block_mod and not block_livery and not block_plaque and not block_skirt then
          local lbl = GetModTextLabel(veh,mod,i)

          if lbl ~= nil and not string.match(lbl, 'HSW') then
            local mod_name = tostring(GetLabelText(lbl))

            if mod_name == 'NULL' or not mod_name then
              print('Error looking up label for mod', lbl, GetLabelText(lbl))
            end

            if mod_name then
              if menu_debug then
                mod_name = '[ ' .. i .. ' ] ' .. mod_name
              end

              -- Handle price for custom liveries
              if mod == 48 then
                mod_price = getCustomComponentPrice('livery', vehicle_model, i, custom_liveries) or mod_price
              end

              -- Handle price for custom plaques
              if mod == 35 then
                mod_price = getCustomComponentPrice('plaque', vehicle_model, i, custom_plaques) or mod_price
              end

              if mod == 3 then
                mod_price = getCustomComponentPrice('skirt', vehicle_model, i, custom_skirts) or mod_price
              end

              local btn = m:addPurchase(mod_name, mod_price)

              btn.modtype = mod
              btn.mod = i
            end
          end
        end
			end
		else
			for n, v in pairs(LSC_Config.prices.mods[mod]) do
        local block_mod = (mod_blacklist[vehicle_model] and mod_blacklist[vehicle_model][mod] and mod_blacklist[vehicle_model][mod][v.mod])

        local block_livery = (mod == 48 and not canAccessCustomComponent(vehicle_model, i, custom_liveries)) or false
        local block_plaque = (mod == 35 and not canAccessCustomComponent(vehicle_model, i, custom_plaques)) or false
        local block_skirt  = (mod ==  3 and not canAccessCustomComponent(vehicle_model, i, custom_skirts)) or false

        if not block_mod and not block_livery and not block_plaque and not block_skirt then
          local v_name = v.name

          if menu_debug then
            v_name = '[ ' .. v.mod .. ' ] ' .. v_name
          end

  				btn = m:addPurchase(v_name, v.price)
          btn.modtype = mod
  				btn.mod = v.mod
        end
			end
		end
  else
    -- Fallback for undefined mod types: use vehicle mod labels with a default price
    for i = 0, tonumber(GetNumVehicleMods(veh, mod)) - 1 do
      local mod_price = 4000 -- Default price for undefined mods
      local block_mod = (mod_blacklist[vehicle_model] and mod_blacklist[vehicle_model][mod] and mod_blacklist[vehicle_model][mod][i])

      if not block_mod then
        local lbl = GetModTextLabel(veh, mod, i)
          if lbl ~= nil and not string.match(lbl, 'HSW') then
            local mod_name = tostring(GetLabelText(lbl))
            if mod_name == 'NULL' or not mod_name then
              mod_name = "Mod " .. i -- Fallback name if label is invalid
            end

            if menu_debug then
              mod_name = '[ ' .. i .. ' ] ' .. mod_name
            end

            local btn = m:addPurchase(mod_name, mod_price)
            btn.modtype = mod
            btn.mod = i
          end
        end
      end
    end
	end
end

--We actually need to get out of garage? o_O
local function DriveOutOfGarage()
	Citizen.CreateThread(function()

		local ped = PlayerPedId()
		local veh = myveh.vehicle
		local model = GetEntityModel(veh)
		local vcolors = table.pack(GetVehicleColours(veh))
		local ecolors = table.pack(GetVehicleExtraColours(veh))
		local vcolor1 = json.encode(vcolors[1])
		local vcolor2 = json.encode(vcolors[2])
		local ecolor1 = json.encode(ecolors[1])
		local ecolor2 = json.encode(ecolors[2])
		local wheels = json.encode(GetVehicleWheelType(veh))
		local smokecolor = table.pack(GetVehicleTyreSmokeColor(veh))
		local smokecolor1 = json.encode(smokecolor[1])
		local smokecolor2 = json.encode(smokecolor[2])
		local smokecolor3 = json.encode(smokecolor[3])
		local neoncolor = table.pack(GetVehicleNeonLightsColour(veh))
		local neoncolor1 = json.encode(neoncolor[1])
		local neoncolor2 = json.encode(neoncolor[2])
		local neoncolor3 = json.encode(neoncolor[3])
    if tonumber(myveh.neon) ~= nil then
      neon = json.encode(tonumber(myveh.neon))
    else
      neon = json.encode(0)
    end
    if vehicle_names[model] ~= nil then
      local plate = GetVehicleVIN(veh)
      pCustoms.updateVehicle({vehicle_names[model][1],myveh.mods,vcolor1,vcolor2,ecolor1,ecolor2,myveh.dashcolor,myveh.interiorcolor,myveh.wheeltype,myveh.plateindex,myveh.windowtint,smokecolor1,smokecolor2,smokecolor3,neoncolor1,neoncolor2,neoncolor3,neon,plate})
    end

		FreezeEntityPosition(ped, false)
		FreezeEntityPosition(veh, false)
		SetVehicleOnGroundProperly(veh)
		SetVehicleLights(veh, 0)
		SetVehicleInteriorlight(veh, false)

		NetworkRegisterEntityAsNetworked(veh)
		SetEntityVisible(ped, true,0)
		ClearPedTasks(ped)

    current_garage = nil
    colors_changed = false

		DisplayRadar(true)
		SetPlayerControl(PlayerId(),true)

	end)
end

--So we can actually enter it?
function DriveInGarage(is_local)
	SetPlayerControl(PlayerId(),false,256)

	local ped = PlayerPedId()
	local veh = GetVehiclePedIsUsing(ped)
  local veh_model = GetEntityModel(veh)

  livery_flags = pCustoms.getAllowedControlledLiveries()

	LSCMenu.buttons = {}

	DisplayRadar(false)
	if DoesEntityExist(veh) then
		--Set menu title
    LSCMenu:setTitle("Los Santos Customs")
    LSCMenu.title_sprite = "shopui_title_carmod"

    local vehicle_type = "default"
		local protected = false
		local no_upgrade = false
    local police_fleet = false
    local no_respray = false
    local no_wheels = false

    for _, v in pairs(no_respray_models) do
      if GetHashKey(v) == GetEntityModel(veh) then
        no_respray = true
      end
    end
    for _, v in pairs(no_wheel_changes) do
      if GetHashKey(v) == GetEntityModel(veh) then
        no_wheels = true
      end
    end
    if vehicle_names[GetEntityModel(veh)] ~= nil then
		    vehicle_type = vehicle_names[GetEntityModel(veh)][2]
    		for _, noupgradecar in pairs(no_perf_upgrades) do
    			if vehicle_names[GetEntityModel(veh)][1] == noupgradecar then
    		  		no_upgrade = true
    			end
    		end
        for _, emergencyCar in pairs(emergency_vehicles) do
    			if vehicle_names[GetEntityModel(veh)][1] == emergencyCar then
    		  		protected = true
    			end
    		end
        for _, fleetCar in pairs(police_fleet_vehicles) do
    			if vehicle_names[GetEntityModel(veh)][1] == fleetCar then
              protected = true
              no_upgrade = true
    		  		police_fleet = true
    			end
    		end
    end
		-------------------------------Load some settings-----------------------------------

		--Controls
		LSCMenu.config.controls = LSC_Config.menu.controls
		SetIbuttons({
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_back, 0),"Back"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_select, 0),"Select"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_up, 0),"Up"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_down, 0),"Down"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_left, 0),"Left"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_right, 0),"Right"},
		 },0)

		 --Max buttons
		LSCMenu:setMaxButtons(LSC_Config.menu.maxbuttons)

		--Width, height of menu
		LSCMenu.config.size.width = f(LSC_Config.menu.width) or 0.24;
		LSCMenu.config.size.height = f(LSC_Config.menu.height) or 0.36;

		--Position
		if type(LSC_Config.menu.position) == 'table' then
			LSCMenu.config.position = { x = LSC_Config.menu.position.x, y = LSC_Config.menu.position.y}
		elseif type(LSC_Config.menu.position) == 'string' then
			if LSC_Config.menu.position == "left" then
				LSCMenu.config.position = { x = 0.16, y = 0.13}
			elseif  LSC_Config.menu.position == "right" then
				LSCMenu.config.position = { x = 1-0.16, y = 0.13}
			end
		end

		--Theme
		if type(LSC_Config.menu.theme) == "table" then
			LSCMenu:setColors(LSC_Config.menu.theme.text_color,LSC_Config.menu.theme.stext_color,LSC_Config.menu.theme.bg_color,LSC_Config.menu.theme.sbg_color)
		elseif	type(LSC_Config.menu.theme) == "string" then
			if LSC_Config.menu.theme == "light" then
				--text_color,stext_color,bg_color,sbg_color
				LSCMenu:setColors({ r = 255,g = 255, b = 255, a = 255},{ r = 0,g = 0, b = 0, a = 255},{ r = 0,g = 0, b = 0, a = 155},{ r = 255,g = 255, b = 255, a = 255})
			elseif LSC_Config.menu.theme == "darkred" then
				LSCMenu:setColors({ r = 255,g = 255, b = 255, a = 255},{ r = 0,g = 0, b = 0, a = 255},{ r = 0,g = 0, b = 0, a = 155},{ r = 200,g = 15, b = 15, a = 200})
			elseif LSC_Config.menu.theme == "bluish" then
				LSCMenu:setColors({ r = 255,g = 255, b = 255, a = 255},{ r = 255,g = 255, b = 255, a = 255},{ r = 0,g = 0, b = 0, a = 100},{ r = 0,g = 100, b = 255, a = 200})
			elseif LSC_Config.menu.theme == "greenish" then
				LSCMenu:setColors({ r = 255,g = 255, b = 255, a = 255},{ r = 0,g = 0, b = 0, a = 255},{ r = 0,g = 0, b = 0, a = 100},{ r = 0,g = 200, b = 0, a = 200})
			end
		end

		LSCMenu:addSubMenu("CATEGORIES", "categories",nil, false)
		LSCMenu.categories.buttons = {}
		--Calculate price for vehicle repair and add repair  button
		local maxvehhp = 1000
		local damage = 0
		damage = (maxvehhp - GetVehicleBodyHealth(veh))/100
		LSCMenu:addPurchase("Repair vehicle",round(250+150*damage,0), "Full body repair and engine service.")

		--Setup table for vehicle with all mods, colors etc.
		SetVehicleModKit(veh,0)
		myveh.vehicle = veh
		myveh.model = GetDisplayNameFromVehicleModel(GetEntityModel(veh)):lower()
		myveh.color =  table.pack(GetVehicleColours(veh))
		myveh.extracolor = table.pack(GetVehicleExtraColours(veh))
		myveh.neoncolor = table.pack(GetVehicleNeonLightsColour(veh))
		myveh.smokecolor = table.pack(GetVehicleTyreSmokeColor(veh))
    myveh.dashcolor = GetVehicleDashboardColor(veh)
    myveh.interiorcolor = GetVehicleInteriorColor(veh)
		myveh.plateindex = GetVehicleNumberPlateTextIndex(veh)
		myveh.mods = {}
		for i = 0, 49 do
			myveh.mods[i] = {mod = nil}
		end
		for i,t in pairs(myveh.mods) do
			if i == 22 or i == 18 then
				if IsToggleModOn(veh,i) then
				t.mod = 1
				else
				t.mod = 0
				end
			elseif i == 23 or i == 24 then
				t.mod = GetVehicleMod(veh,i)
				t.variation = GetVehicleModVariation(veh, i)
			else
				t.mod = GetVehicleMod(veh,i)
			end
		end
		if GetVehicleWindowTint(veh) == -1 or GetVehicleWindowTint(veh) == 0 then
			myveh.windowtint = false
		else
			myveh.windowtint = GetVehicleWindowTint(veh)
		end
		myveh.wheeltype = GetVehicleWheelType(veh)
		myveh.bulletProofTyres = GetVehicleTyresCanBurst(veh)

		--Menu stuff
		local chassis,interior,bumper,fbumper,rbumper = false,false,false,false

		for i = 0,49 do
			if GetNumVehicleMods(veh,i) ~= nil and GetNumVehicleMods(veh,i) ~= false and GetNumVehicleMods(veh,i) > 0 then
				if i == 1 then
					bumper = true
					fbumper = true
				elseif i == 2 then
					bumper = true
					rbumper = true
				elseif (i >= 42 and i <= 46) or i == 5 then --If any chassis mod exist then add chassis menu
					chassis = true
				elseif i >= 27 and i <= 37 then --If any interior mod exist then add interior menu
					interior = true
				end
			end
		end
		if vehicle_type ~= "boats" and vehicle_type ~= "planes" and vehicle_type ~= "helicopters" then
			if not protected and GetEntityModel(veh) ~= `hellion` and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][0] ~= true) then
				AddMod(0,LSCMenu.categories,"SPOILER", "Spoiler", "Increase downforce.",true)
			end
			if not protected and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][3] ~= true) then
				AddMod(3,LSCMenu.categories,"SKIRTS", "Skirts", "Enhance your vehicle's look with custom side skirts.",true)
			end
			if not protected and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][4] ~= true) then
				AddMod(4,LSCMenu.categories,"EXHAUST", "Exhausts", "Customized sports exhausts.",true)
			end
			if not protected and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][6] ~= true) then
				AddMod(6,LSCMenu.categories,"GRILLE", "Grille", "Improved engine cooling.",true)
			end
			if not protected and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][7] ~= true) then
				AddMod(7,LSCMenu.categories,"HOOD", "Hood", "Enhance car engine cooling.",true)
			end
			if not protected and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][8] ~= true) then
				AddMod(8,LSCMenu.categories,"FENDERS", "Fenders", "Enhance body paneling with custom fenders.",true)
			end
      if not protected and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][9] ~= true) then
        AddMod(9, LSCMenu.categories, "LEFT FENDER", "Left Fender", "Additional fender customization options.", true)
      end
			if not protected and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][10] ~= true) then
				AddMod(10,LSCMenu.categories,"ROOF", "Roof", "Lower your center of gravity with lightweight roof panels.",true)
			end
			if not current_garage.is_dealership and vehicle_type ~= "boats" and not police_fleet and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][12] ~= true) then
				AddMod(12,LSCMenu.categories,"BRAKES", "Brakes", "Increase stopping power and eliminate brake fade.",true)
			end
			if not current_garage.is_dealership and not protected and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][13] ~= true) then
				AddMod(13,LSCMenu.categories,"TRANSMISSION", "Transmission", "Improved acceleration with close ratio transmission.",true)
			end
			if not protected then
				AddMod(14,LSCMenu.categories,"HORN", "Horn", "Custom air horns.",true)
			end
			if not protected and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][15] ~= true) and not current_garage.is_dealership then
				AddMod(15,LSCMenu.categories,"SUSPENSION","Suspension","Upgrade to a sports oriented suspension setup.",true)
			end
			--AddMod(16,LSCMenu.categories,"ARMOR","Armor","Protect your car's occupants with military spec composite body panels.",true)
      -- if not protected and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][17] ~= true) then
      --   AddMod(17, LSCMenu.categories, "NITROUS", "Nitrous", "Boost your acceleration with nitrous oxide.", false)
      -- end
			if not protected and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][18] ~= true) and not current_garage.is_dealership then
				AddMod(18, LSCMenu.categories, "TURBO", "Turbo", "Reduced lag turbocharger.",false)
			end
      -- if not protected and (not mod_blacklist[veh_model] or mod_blacklist[veh_model][21] ~= true) then
      --   AddMod(21, LSCMenu.categories, "HYDRAULICS", "Hydraulics", "Alternative hydraulics setup.", true)
      -- end

      if not protected or GetEntityModel(veh) == `polalamop2` then
        m = LSCMenu.categories:addSubMenu("WINDOWS", "Windows", "A selection of tinted windows.",true)
        btn = m:addPurchase("None")btn.tint = false
        for n, tint in pairs(LSC_Config.prices.windowtint) do
          btn = m:addPurchase(tint.name,tint.price)btn.tint = tint.tint
        end
      end

			if not protected then
				if chassis then
					LSCMenu.categories:addSubMenu("CHASSIS", "Chassis",nil, true)
					AddMod(42, LSCMenu.categories.Chassis, "ARCH COVER", "Arch cover", "",true) --headlight trim
					AddMod(43, LSCMenu.categories.Chassis, "AERIALS", "Aerials", "",true) --foglights
					AddMod(44, LSCMenu.categories.Chassis, "ROOF SCOOPS", "Roof Scoops", "",true) --roof scoops
					AddMod(45, LSCMenu.categories.Chassis, "Tank", "Tank", "",true)
					AddMod(46, LSCMenu.categories.Chassis, "DOORS", "Doors", "",true)-- windows
          AddMod(47, LSCMenu.categories.Chassis, "RIGHT DOOR", "Right Door", "", true)
					AddMod(5,LSCMenu.categories.Chassis,"ROLL CAGE", "Roll cage", "Stiffen your chassis with a rollcage.",true)
				end

				LSCMenu.categories:addSubMenu("ENGINE", "Engine",nil, true)
				AddMod(39, LSCMenu.categories.Engine, "ENGINE BLOCK", "Engine Block", "Custom engine block casings.",true)
				AddMod(40, LSCMenu.categories.Engine, "CAM COVER", "Cam Cover", "Optional cam covers.",true)
				AddMod(41, LSCMenu.categories.Engine, "STRUT BRACE", "Strut Brace", "A selection of support struts.",true)

        if not current_garage.is_dealership then
					AddMod(11,LSCMenu.categories.Engine,"ENGINE TUNES", "Engine Tunes", "Increases horsepower.",true)
        end

				if interior then
					local interior_submenu = LSCMenu.categories:addSubMenu("INTERIOR", "Interior","Products for maximum style and comfort.", true)
					--LSCMenu.categories.Interior:addSubMenu("TRIM", "Trim","A selection of interior designs.", true)
					AddMod(27, LSCMenu.categories.Interior, "TRIM DESIGN", "Trim Design", "",true)
					--There are'nt any working natives that could change interior color :(
					--LSCMenu.categories.Interior.Trim:addSubMenu("TRIM COLOR", "Trim Color","", true)
					AddMod(28, LSCMenu.categories.Interior, "ORNAMENTS", "Ornaments", "Add decorative items to your dash.",true)
					AddMod(29, LSCMenu.categories.Interior, "DASHBOARD", "Dashboard", "Custom control panel designs.",true)
					AddMod(30, LSCMenu.categories.Interior, "DIAL DESIGN", "Dials", "Customize the look of your dials.",true)
					AddMod(31, LSCMenu.categories.Interior, "DOORS", "Doors", "Install door upgrades.",true)
					AddMod(32, LSCMenu.categories.Interior, "SEATS", "Seats", "Options where style meets comfort.",true)
					AddMod(33, LSCMenu.categories.Interior, "STEERING WHEELS", "Steering Wheels", "Customize the link between you and your vehicle.",true)
					AddMod(34, LSCMenu.categories.Interior, "Shifter leavers", "Shifter leavers", "",true)
					AddMod(35, LSCMenu.categories.Interior, "Plaques", "Plaques", "",true)
					AddMod(36, LSCMenu.categories.Interior, "Speakers", "Speakers", "",true)
					AddMod(37, LSCMenu.categories.Interior, "Trunk", "Trunk", "",true)
          AddMod(19, LSCMenu.categories.Interior, "SUBWOOFER", "Subwoofer", "Enhance your audio with a subwoofer.", true)

          dcol = interior_submenu:addSubMenu("DASHBOARD COLORS", "Dashboard color", nil,true)
          dcol:addSubMenu("CHROME", "Chrome", nil,true)
          for n, c in pairs(LSC_Config.prices.chrome2.colors) do
            local btn = dcol.Chrome:addPurchase(c.name,LSC_Config.prices.chrome2.price)btn.colorindex = c.colorindex
            if btn.colorindex == myveh.dashcolor then
              btn.purchased = true
            end
          end
          dcol:addSubMenu("CLASSIC", "Classic", nil,true)
          for n, c in pairs(LSC_Config.prices.classic2.colors) do
            local btn = dcol.Classic:addPurchase(c.name,LSC_Config.prices.classic2.price)btn.colorindex = c.colorindex
            if btn.colorindex == myveh.dashcolor then
              btn.purchased = true
            end
          end
          dcol:addSubMenu("MATTE", "Matte", nil,true)
          for n, c in pairs(LSC_Config.prices.matte2.colors) do
            local btn = dcol.Matte:addPurchase(c.name,LSC_Config.prices.matte2.price)btn.colorindex = c.colorindex
            if btn.colorindex == myveh.dashcolor then
              btn.purchased = true
            end
          end
          dcol:addSubMenu("METALLIC", "Metallic", nil,true)
          for n, c in pairs(LSC_Config.prices.metallic2.colors) do
            local btn = dcol.Metallic:addPurchase(c.name,LSC_Config.prices.metallic2.price)btn.colorindex = c.colorindex
            if btn.colorindex == myveh.dashcolor then
              btn.purchased = true
            end
          end
          dcol:addSubMenu("METALS", "Metals", nil,true)
          for n, c in pairs(LSC_Config.prices.metal2.colors) do
            local btn = dcol.Metals:addPurchase(c.name,LSC_Config.prices.metal2.price)btn.colorindex = c.colorindex
            if btn.colorindex == myveh.dashcolor then
              btn.purchased = true
            end
          end

          icol = interior_submenu:addSubMenu("INTERIOR COLORS", "Interior color", nil,true)
          icol:addSubMenu("CHROME", "Chrome", nil,true)
          for n, c in pairs(LSC_Config.prices.chrome2.colors) do
            local btn = icol.Chrome:addPurchase(c.name,LSC_Config.prices.chrome2.price)btn.colorindex = c.colorindex
            if btn.colorindex == myveh.interiorcolor then
              btn.purchased = true
            end
          end
          icol:addSubMenu("CLASSIC", "Classic", nil,true)
          for n, c in pairs(LSC_Config.prices.classic2.colors) do
            local btn = icol.Classic:addPurchase(c.name,LSC_Config.prices.classic2.price)btn.colorindex = c.colorindex
            if btn.colorindex == myveh.interiorcolor then
              btn.purchased = true
            end
          end
          icol:addSubMenu("MATTE", "Matte", nil,true)
          for n, c in pairs(LSC_Config.prices.matte2.colors) do
            local btn = icol.Matte:addPurchase(c.name,LSC_Config.prices.matte2.price)btn.colorindex = c.colorindex
            if btn.colorindex == myveh.interiorcolor then
              btn.purchased = true
            end
          end
          icol:addSubMenu("METALLIC", "Metallic", nil,true)
          for n, c in pairs(LSC_Config.prices.metallic2.colors) do
            local btn = icol.Metallic:addPurchase(c.name,LSC_Config.prices.metallic2.price)btn.colorindex = c.colorindex
            if btn.colorindex == myveh.interiorcolor then
              btn.purchased = true
            end
          end
          icol:addSubMenu("METALS", "Metals", nil,true)
          for n, c in pairs(LSC_Config.prices.metal2.colors) do
            local btn = icol.Metals:addPurchase(c.name,LSC_Config.prices.metal2.price)btn.colorindex = c.colorindex
            if btn.colorindex == myveh.interiorcolor then
              btn.purchased = true
            end
          end
				end

				LSCMenu.categories:addSubMenu("PLATES", "Plates","Decorative identification.", true)
				LSCMenu.categories.Plates:addSubMenu("LICENSE", "License", "",true)
				for n, mod in pairs(LSC_Config.prices.plates) do
          if not mod.dev_only or GlobalState.is_dev then
            local plate_name = mod.name

            if exports.blrp_core:me().get('lscustoms_debug') then
              plate_name = '[' .. mod.plateindex .. '] ' .. mod.name
            end

					  local btn = LSCMenu.categories.Plates.License:addPurchase(plate_name,mod.price)btn.plateindex = mod.plateindex
          end
				end
				--Customize license plates
				AddMod(25, LSCMenu.categories.Plates, "Plate holder", "Plate holder", "",true) --
				AddMod(26, LSCMenu.categories.Plates, "Vanity plates", "Vanity plates", "",true) --
				--AddMod(47, LSCMenu.categories, "UNK47", "unk47", "",true)
				AddMod(49, LSCMenu.categories, "LIGHTBAR", "LightBar", "",true)
				AddMod(38,LSCMenu.categories,"HYDRAULICS","Hydraulics","",true)
				AddMod(48,LSCMenu.categories,"Liveries", "Liveries", "A selection of decals for your vehicle.",true)

				if bumper then
					LSCMenu.categories:addSubMenu("BUMPERS", "Bumpers", "Custom front and rear bumpers.",true)
					if fbumper then
						AddMod(1,LSCMenu.categories.Bumpers,"FRONT BUMPERS", "Front bumpers", "Custom front bumpers.",true)
					end
					if rbumper then
						AddMod(2,LSCMenu.categories.Bumpers,"REAR BUMPERS", "Rear bumpers", "Custom rear bumpers.",true)
					end
				end
      end

      if (not no_respray and not protected) or police_fleet then
        respray = LSCMenu.categories:addSubMenu("RESPRAY", "Respray", "Transforms vehicle appearance.",true)
        pcol = respray:addSubMenu("PRIMARY COLORS", "Primary color",  nil,true)
        pcol:addSubMenu("CHROME", "Chrome", nil,true)
        for n, c in pairs(LSC_Config.prices.chrome.colors) do
          local btn = pcol.Chrome:addPurchase(c.name,LSC_Config.prices.chrome.price)btn.colorindex = c.colorindex
          if btn.colorindex == myveh.color[1] then
            btn.purchased = true
          end
        end
        pcol:addSubMenu("CLASSIC", "Classic", nil,true)
        for n, c in pairs(LSC_Config.prices.classic.colors) do
          local btn = pcol.Classic:addPurchase(c.name,LSC_Config.prices.classic.price)btn.colorindex = c.colorindex
          if btn.colorindex == myveh.color[1] then
            btn.purchased = true
          end
        end
        pcol:addSubMenu("MATTE", "Matte", nil,true)
        for n, c in pairs(LSC_Config.prices.matte.colors) do
          local btn = pcol.Matte:addPurchase(c.name,LSC_Config.prices.matte.price)btn.colorindex = c.colorindex
          if btn.colorindex == myveh.color[1] then
            btn.purchased = true
          end
        end
        pcol:addSubMenu("METALLIC", "Metallic", nil,true)
        for n, c in pairs(LSC_Config.prices.metallic.colors) do
          local btn = pcol.Metallic:addPurchase(c.name,LSC_Config.prices.metallic.price)btn.colorindex = c.colorindex
          if btn.colorindex == myveh.color[1] and myveh.extracolor[1] == myveh.color[2] then
            btn.purchased = true
          end
        end
        pcol:addSubMenu("METALS", "Metals", nil,true)
        for n, c in pairs(LSC_Config.prices.metal.colors) do
          local btn = pcol.Metals:addPurchase(c.name,LSC_Config.prices.metal.price)btn.colorindex = c.colorindex
          if btn.colorindex == myveh.color[1] then
            btn.purchased = true
          end
        end

        if allowChameleon() then
          pcol:addSubMenu("CHAMELEON", "Chameleon", nil,true)
          for n, c in pairs(LSC_Config.prices.chameleon.colors) do
            local btn = pcol.Chameleon:addPurchase(c.name,LSC_Config.prices.chameleon.price)btn.colorindex = c.colorindex
            if btn.colorindex == myveh.color[1] then
              btn.purchased = true
            end
          end
        end

        scol = respray:addSubMenu("SECONDARY COLORS", "Secondary color", nil,true)
        scol:addSubMenu("CHROME", "Chrome", nil,true)
        for n, c in pairs(LSC_Config.prices.chrome2.colors) do
          local btn = scol.Chrome:addPurchase(c.name,LSC_Config.prices.chrome2.price)btn.colorindex = c.colorindex
          if btn.colorindex == myveh.color[2] then
            btn.purchased = true
          end
        end
        scol:addSubMenu("CLASSIC", "Classic", nil,true)
        for n, c in pairs(LSC_Config.prices.classic2.colors) do
          local btn = scol.Classic:addPurchase(c.name,LSC_Config.prices.classic2.price)btn.colorindex = c.colorindex
          if btn.colorindex == myveh.color[2] then
            btn.purchased = true
          end
        end
        scol:addSubMenu("MATTE", "Matte", nil,true)
        for n, c in pairs(LSC_Config.prices.matte2.colors) do
          local btn = scol.Matte:addPurchase(c.name,LSC_Config.prices.matte2.price)btn.colorindex = c.colorindex
          if btn.colorindex == myveh.color[2] then
            btn.purchased = true
          end
        end
        scol:addSubMenu("METALLIC", "Metallic", nil,true)
        for n, c in pairs(LSC_Config.prices.metallic2.colors) do
          local btn = scol.Metallic:addPurchase(c.name,LSC_Config.prices.metallic2.price)btn.colorindex = c.colorindex
          if btn.colorindex == myveh.color[2] and myveh.extracolor[1] == btn.colorindex then
            btn.purchased = true
          end
        end
        scol:addSubMenu("METALS", "Metals", nil,true)
        for n, c in pairs(LSC_Config.prices.metal2.colors) do
          local btn = scol.Metals:addPurchase(c.name,LSC_Config.prices.metal2.price)btn.colorindex = c.colorindex
          if btn.colorindex == myveh.color[2] then
            btn.purchased = true
          end
        end
      end

      if not protected and not no_wheels then
				LSCMenu.categories:addSubMenu("WHEELS", "Wheels", "Custom rims, tires and colors.",true)
					wtype = LSCMenu.categories.Wheels:addSubMenu("WHEEL TYPE", "Wheel type", "Custom rims in all styles and sizes.",true)
						if IsThisModelABike(GetEntityModel(veh)) then
							fwheels = wtype:addSubMenu("FRONT WHEEL", "Front wheel", nil,true)
								for n, w in pairs(LSC_Config.prices.frontwheel) do
									btn = fwheels:addPurchase(w.name,w.price)btn.wtype = w.wtype btn.modtype = 23 btn.mod = w.mod
								end
							bwheels = wtype:addSubMenu("BACK WHEEL", "Back wheel", nil,true)
								for n, w in pairs(LSC_Config.prices.backwheel) do
									btn = bwheels:addPurchase(w.name,w.price)btn.wtype = w.wtype btn.modtype = 24 btn.mod = w.mod
								end
						else
							sportw = wtype:addSubMenu("SPORT WHEELS", "Sport", nil,true)
								for n, w in pairs(LSC_Config.prices.sportwheels) do
									local btn = sportw:addPurchase(w.name,w.price)btn.wtype = w.wtype btn.modtype = 23 btn.mod = w.mod
								end
							musclew = wtype:addSubMenu("MUSCLE WHEELS", "Muscle", nil,true)
								for n, w in pairs(LSC_Config.prices.musclewheels) do
									local btn = musclew:addPurchase(w.name,w.price)btn.wtype =  w.wtype btn.modtype = 23 btn.mod = w.mod
								end
							lowriderw = wtype:addSubMenu("LOWRIDER WHEELS", "Lowrider", nil,true)
								for n, w in pairs(LSC_Config.prices.lowriderwheels) do
									local btn = lowriderw:addPurchase(w.name,w.price)btn.wtype =  w.wtype btn.modtype = 23 btn.mod = w.mod
								end
							suvw = wtype:addSubMenu("SUV WHEELS", "Suv", nil,true)
								for n, w in pairs(LSC_Config.prices.suvwheels) do
									local btn = suvw:addPurchase(w.name,w.price)btn.wtype = w.wtype btn.modtype = 23 btn.mod = w.mod
								end
							offroadw = wtype:addSubMenu("OFFROAD WHEELS", "Offroad", nil,true)
								for n, w in pairs(LSC_Config.prices.offroadwheels) do
									local btn = offroadw:addPurchase(w.name,w.price)btn.wtype = w.wtype btn.modtype = 23 btn.mod = w.mod
								end
							tunerw = wtype:addSubMenu("TUNER WHEELS", "Tuner", nil,true)
								for n, w in pairs(LSC_Config.prices.tunerwheels) do
									local btn = tunerw:addPurchase(w.name,w.price)btn.wtype = w.wtype btn.modtype = 23 btn.mod = w.mod
								end
							hughendw = wtype:addSubMenu("HIGHEND WHEELS", "Highend", nil,true)
								for n, w in pairs(LSC_Config.prices.highendwheels) do
									local btn = hughendw:addPurchase(w.name,w.price)btn.wtype = w.wtype btn.modtype = 23 btn.mod = w.mod
								end
              bennyow = wtype:addSubMenu('BENNNYS ORIGINAL WHEELS', "Benny's Original Wheels", nil, true)
                for n, w in pairs(LSC_Config.prices.bennysoriginalwheels) do
									local btn = bennyow:addPurchase(w.name,w.price)btn.wtype = w.wtype btn.modtype = 23 btn.mod = w.mod
                end
              bennybw = wtype:addSubMenu('BENNYS BESPOKE WHEELS', "Benny's Bespoke Wheels", nil, true)
                for n, w in pairs(LSC_Config.prices.bennysbespokewheels) do
									local btn = bennybw:addPurchase(w.name,w.price)btn.wtype = w.wtype btn.modtype = 23 btn.mod = w.mod
                end
              streetw = wtype:addSubMenu('STREET WHEELS', 'Street', nil, true)
                for n, w in pairs(LSC_Config.prices.streetwheels) do
									local btn = streetw:addPurchase(w.name,w.price)btn.wtype = w.wtype btn.modtype = 23 btn.mod = w.mod
                end
              trackw = wtype:addSubMenu('TRACK WHEELS', 'Track', nil, true)
                for n, w in pairs(LSC_Config.prices.trackwheels) do
									local btn = trackw:addPurchase(w.name,w.price)btn.wtype = w.wtype btn.modtype = 23 btn.mod = w.mod
                end
						end

				m = LSCMenu.categories.Wheels:addSubMenu("WHEEL COLOR", "Wheel color", "Custom wheel colors.",true)
					for n, c in pairs(LSC_Config.prices.wheelcolor.colors) do
						local btn = m:addPurchase(c.name,LSC_Config.prices.wheelcolor.price)btn.colorindex = c.colorindex
					end

				m = LSCMenu.categories.Wheels:addSubMenu("WHEEL ACCESSORIES", "Wheel accessories", "Bulletproof tires and custom burnout smoke.",true)
					for n, mod in pairs(LSC_Config.prices.wheelaccessories) do
						local btn = m:addPurchase(mod.name,mod.price)btn.smokecolor = mod.smokecolor
					end
      elseif no_wheels then
          LSCMenu.categories:addSubMenu("WHEELS", "Wheels", "Remove Wheels (This is non reversible)",true)
          wtype = LSCMenu.categories.Wheels:addSubMenu("WHEEL TYPE", "Wheel type", "Custom rims in all styles and sizes.",true)
          sportw = wtype:addSubMenu("SPORT WHEELS", "Sport", nil,true)
          local w = LSC_Config.prices.sportwheels[1]
          local btn = sportw:addPurchase(w.name,w.price)btn.wtype = w.wtype btn.modtype = 23 btn.mod = w.mod
			end
		else
			respray = LSCMenu.categories:addSubMenu("RESPRAY", "Respray", "Transforms vehicle appearance.",true)
			pcol = respray:addSubMenu("PRIMARY COLORS", "Primary color",  nil,true)
				pcol:addSubMenu("CHROME", "Chrome", nil,true)
				for n, c in pairs(LSC_Config.prices.chrome.colors) do
					local btn = pcol.Chrome:addPurchase(c.name,LSC_Config.prices.chrome.price)btn.colorindex = c.colorindex
					if btn.colorindex == myveh.color[1] then
						btn.purchased = true
					end
				end
				pcol:addSubMenu("CLASSIC", "Classic", nil,true)
				for n, c in pairs(LSC_Config.prices.classic.colors) do
					local btn = pcol.Classic:addPurchase(c.name,LSC_Config.prices.classic.price)btn.colorindex = c.colorindex
					if btn.colorindex == myveh.color[1] then
						btn.purchased = true
					end
				end
				pcol:addSubMenu("MATTE", "Matte", nil,true)
				for n, c in pairs(LSC_Config.prices.matte.colors) do
					local btn = pcol.Matte:addPurchase(c.name,LSC_Config.prices.matte.price)btn.colorindex = c.colorindex
					if btn.colorindex == myveh.color[1] then
						btn.purchased = true
					end
				end
				pcol:addSubMenu("METALLIC", "Metallic", nil,true)
				for n, c in pairs(LSC_Config.prices.metallic.colors) do
					local btn = pcol.Metallic:addPurchase(c.name,LSC_Config.prices.metallic.price)btn.colorindex = c.colorindex
					if btn.colorindex == myveh.color[1] and myveh.extracolor[1] == myveh.color[2] then
						btn.purchased = true
					end
				end

        if allowChameleon() then
          pcol:addSubMenu("CHAMELEON", "Chameleon", nil,true)
          for n, c in pairs(LSC_Config.prices.chameleon.colors) do
            local btn = pcol.Chameleon:addPurchase(c.name,LSC_Config.prices.chameleon.price)btn.colorindex = c.colorindex
            if btn.colorindex == myveh.color[1] then
              btn.purchased = true
            end
          end
        end

				pcol:addSubMenu("METALS", "Metals", nil,true)
				for n, c in pairs(LSC_Config.prices.metal.colors) do
					local btn = pcol.Metals:addPurchase(c.name,LSC_Config.prices.metal.price)btn.colorindex = c.colorindex
					if btn.colorindex == myveh.color[1] then
						btn.purchased = true
					end
				end
			scol = respray:addSubMenu("SECONDARY COLORS", "Secondary color", nil,true)
				scol:addSubMenu("CHROME", "Chrome", nil,true)
				for n, c in pairs(LSC_Config.prices.chrome2.colors) do
					local btn = scol.Chrome:addPurchase(c.name,LSC_Config.prices.chrome2.price)btn.colorindex = c.colorindex
					if btn.colorindex == myveh.color[2] then
						btn.purchased = true
					end
				end
				scol:addSubMenu("CLASSIC", "Classic", nil,true)
				for n, c in pairs(LSC_Config.prices.classic2.colors) do
					local btn = scol.Classic:addPurchase(c.name,LSC_Config.prices.classic2.price)btn.colorindex = c.colorindex
					if btn.colorindex == myveh.color[2] then
						btn.purchased = true
					end
				end
				scol:addSubMenu("MATTE", "Matte", nil,true)
				for n, c in pairs(LSC_Config.prices.chrome2.colors) do
					local btn = scol.Matte:addPurchase(c.name,LSC_Config.prices.matte2.price)btn.colorindex = c.colorindex
					if btn.colorindex == myveh.color[2] then
						btn.purchased = true
					end
				end
				scol:addSubMenu("METALLIC", "Metallic", nil,true)
				for n, c in pairs(LSC_Config.prices.metallic2.colors) do
					local btn = scol.Metallic:addPurchase(c.name,LSC_Config.prices.metallic2.price)btn.colorindex = c.colorindex
					if btn.colorindex == myveh.color[2] and myveh.extracolor[1] == btn.colorindex then
						btn.purchased = true
					end
				end
				scol:addSubMenu("METALS", "Metals", nil,true)
				for n, c in pairs(LSC_Config.prices.metal2.colors) do
					local btn = scol.Metals:addPurchase(c.name,LSC_Config.prices.metal2.price)btn.colorindex = c.colorindex
					if btn.colorindex == myveh.color[2] then
						btn.purchased = true
					end
				end

        if vehicle_type == 'planes' or vehicle_type == 'helicopters' then
          AddMod(48,LSCMenu.categories,"Liveries", "Liveries", "A selection of decals for your vehicle.",true)
        end
		end

		Citizen.CreateThread(function()
			SetVehicleOnGroundProperly(veh)
			SetVehicleLights(veh, 2)
			SetVehicleInteriorlight(veh, true)
			SetVehRadioStation(veh, 255)

			Citizen.Wait(100)
			LSCMenu:Open("categories", is_local)

			FreezeEntityPosition(veh, true)
			SetPlayerControl(PlayerId(),true)

      while current_garage do
        Citizen.Wait(0)

        -- Disable F to exit
        DisableControlAction(0, 75, true)
        DisableControlAction(1, 75, true)
        DisableControlAction(2, 75, true)
        DisableControlAction(3, 75, true)

        local veh_check = GetVehiclePedIsIn(PlayerPedId(), false)

        if not veh_check or veh_check <= 0 then
          UnfakeVeh()
          LSCMenu:Close()
        end
      end
		end)
	end
end

local function SaveCar()
	local ped = PlayerPedId()
	local veh = GetVehiclePedIsUsing(ped)
	local model = GetEntityModel(veh)
	local vcolors = table.pack(GetVehicleColours(veh))
	local ecolors = table.pack(GetVehicleExtraColours(veh))
	local vcolor1 = json.encode(vcolors[1])
	local vcolor2 = json.encode(vcolors[2])
	local ecolor1 = json.encode(ecolors[1])
	local ecolor2 = json.encode(ecolors[2])
	local wheels = json.encode(GetVehicleWheelType(veh))
	local smokecolor = table.pack(GetVehicleTyreSmokeColor(veh))
	local smokecolor1 = json.encode(smokecolor[1])
	local smokecolor2 = json.encode(smokecolor[2])
	local smokecolor3 = json.encode(smokecolor[3])
	local neoncolor = table.pack(GetVehicleNeonLightsColour(veh))
	local neoncolor1 = json.encode(neoncolor[1])
	local neoncolor2 = json.encode(neoncolor[2])
	local neoncolor3 = json.encode(neoncolor[3])

  -- TODO: save dashboard and interior color here

  if tonumber(myveh.neon) ~= nil then
    neon = json.encode(tonumber(myveh.neon))
  else
    neon = json.encode(0)
  end
  if vehicle_names[model] ~= nil then
    local plate = GetVehicleVIN(veh)
    pCustoms.updateVehicle({vehicle_names[model][1],myveh.mods,vcolor1,vcolor2,ecolor1,ecolor2,myveh.dashcolor,myveh.interiorcolor,myveh.wheeltype,myveh.plateindex,myveh.windowtint,smokecolor1,smokecolor2,smokecolor3,neoncolor1,neoncolor2,neoncolor3,neon,plate})
  end
end

--Draw text on screen
local function drawTxt(text,font,centre,x,y,scale,r,g,b,a)
	SetTextFont(font)
	SetTextProportional(0)
	SetTextScale(scale, scale)
	SetTextColour(r, g, b, a)
	SetTextDropShadow(0, 0, 0, 0,255)
	SetTextEdge(1, 0, 0, 0, 255)
	SetTextDropShadow()
	SetTextOutline()
	SetTextCentre(centre)
	SetTextEntry("STRING")
	AddTextComponentString(text)
	DrawText(x , y)
end

--Get the length of table
local function tablelength(T)
  local count = 0
  for _ in pairs(T) do count = count + 1 end
  return count
end

--Check if table contains value
local function tableContains(t,val)
	for k,v in pairs(t) do
		if v == val then
			return true
		end
	end
	return false
end

function ProximityLoop()
  if current_garage then
    return
  end

  local ped = PlayerPedId()

  if not IsPedSittingInAnyVehicle(ped) then
    return
  end

  local vehicle = GetVehiclePedIsUsing(ped)

  if not DoesEntityExist(vehicle) or GetPedInVehicleSeat(vehicle, -1) ~= ped then
    return
  end

  for _, garage in pairs(garages) do
    local distance = #(garage.coords - GetEntityCoords(ped))

    if distance <= 20 then
      DrawMarker(23, garage.coords.x, garage.coords.y, garage.coords.z - 0.99, 0, 0, 0, 0, 0, 0, 3.0001, 3.0001, 1.5001, 255, 165, 0,165, 0, 0, 0,0)
    end

    if distance <= 5 then
      if IsControlJustPressed(1, 201) then
        local state = Entity(vehicle).state
        local is_local = exports.blrp_vehicles:HasAnyVehicleFlags(state.flags, 'FLAG_LOCAL')

        if garage.is_local and not is_local then
          exports.blrp_core:me().notify('You cannot modify this vehicle here')
          return
        end

        if not garage.is_local and is_local then
          exports.blrp_core:me().notify('You cannot modify this vehicle here')
          return
        end

        if not exports.blrp_vehicles:CanAccessVehicle(vehicle) and not garage.is_local and not is_local then
          exports.blrp_core:me().notifyError("You don't have keys to this vehicle so you will only be able to preview modifications")
        end

        current_garage = garage
        DriveInGarage(is_local)
      else
        drawTxt("Press ~b~ENTER~w~ to enter ~b~Los Santos Customs", 4, 1, 0.5, 0.8, 1.0, 255, 255, 255, 255)
      end
    end
  end
end

Citizen.CreateThread(function()
  Citizen.Wait(5000)
  local veh_config = exports.blrp_vehicles:GetConfigVehicles().categories

  for garage_type, vehicle_params in pairs(veh_config) do

    if vehicle_params ~= '_config' then
      if not cfg.garage_types[garage_type] then
        cfg.garage_types[garage_type] = {}
      end

      cfg.garage_types[garage_type] = vehicle_params

    end
  end



  for k, v in pairs(cfg['garage_types']) do
    for name, v2 in pairs(v) do
      vehicle_names[GetHashKey(name)] = { name, k }
    end
  end

  for _, garage in pairs(garages) do
    if garage.blip then
  		local blip = AddBlipForCoord(garage.coords.x, garage.coords.y, garage.coords.z)
  		SetBlipSprite(blip, 72)
      SetBlipScale(blip, 0.7)
  		SetBlipAsShortRange(blip, true)
    end
	end

  print('LS Customs Initialized')

end)

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		ProximityLoop()
	end
end)

--Lets drive out of the garage
function LSCMenu:OnMenuClose(m)
	DriveOutOfGarage()
end

function LSCMenu:onSelectedIndexChanged(name, button)
	name = name:lower()
	local m = LSCMenu.currentmenu
	local price = button.price or 0
	local veh = myveh.vehicle
	p = m.parent or self.name
	if m == "main" then
		m = self
	end
	CheckPurchases(m)
	m = m.name:lower()
	p = p:lower()
	--set up temporary shitt, or in other words show preview of selected mod
	if m == "chrome" or m ==  "classic" or m ==  "matte" or m ==  "metals" then
		if p == "primary color" then
      ClearVehicleCustomPrimaryColour(veh)
      ClearVehicleCustomSecondaryColour(veh)
			SetVehicleColours(veh,button.colorindex,myveh.color[2])
      colors_changed = true
    elseif p == 'dashboard color' then
      SetVehicleDashboardColor(veh, button.colorindex)
      colors_changed = true
    elseif p == 'interior color' then
      SetVehicleInteriorColor(veh, button.colorindex)
      colors_changed = true
		else
      ClearVehicleCustomPrimaryColour(veh)
      ClearVehicleCustomSecondaryColour(veh)
			SetVehicleColours(veh,myveh.color[1],button.colorindex)
      colors_changed = true
		end
  elseif m == 'chameleon' then
    if p == 'primary color' then
      ClearVehicleCustomPrimaryColour(veh)
      ClearVehicleCustomSecondaryColour(veh)
			SetVehicleColours(veh, button.colorindex, button.colorindex)
      colors_changed = true
    end
	elseif m == "metallic" then
		if p == "primary color" then
      ClearVehicleCustomPrimaryColour(veh)
      ClearVehicleCustomSecondaryColour(veh)
			SetVehicleColours(veh,button.colorindex,myveh.color[2])
			SetVehicleExtraColours(veh, myveh.color[2], myveh.extracolor[2])
      colors_changed = true
    elseif p == 'dashboard color' then
      SetVehicleDashboardColor(veh, button.colorindex)
      colors_changed = true
    elseif p == 'interior color' then
      SetVehicleInteriorColor(veh, button.colorindex)
      colors_changed = true
		else
      ClearVehicleCustomPrimaryColour(veh)
      ClearVehicleCustomSecondaryColour(veh)
			SetVehicleColours(veh,myveh.color[1],button.colorindex)
			SetVehicleExtraColours(veh, button.colorindex, myveh.extracolor[2])
      colors_changed = true
		end
	elseif m == "wheel color" then
		SetVehicleExtraColours(veh,myveh.extracolor[1], button.colorindex)
	elseif button.modtype and button.mod then
		if button.modtype ~= 18 and button.modtype ~= 22 then
			if button.wtype then
				SetVehicleWheelType(veh,button.wtype)
			end
			SetVehicleMod(veh,button.modtype, button.mod)
		elseif button.modtype == 22 then
			ToggleVehicleMod(veh,button.modtype, button.mod)
		elseif button.modtype == 18 then
		end
	elseif m == "license" then
		SetVehicleNumberPlateTextIndex(veh,button.plateindex)
	elseif m == "neon color" then
		SetVehicleNeonLightsColour(veh,button.neon[1], button.neon[2], button.neon[3])
	elseif m == "windows" then
		SetVehicleWindowTint(veh, button.tint)
	else
	end
	if m == "horn" then
		--Maybe some way of playing the horn?
		OverrideVehHorn(veh,false,0)
		if IsHornActive(veh) or IsControlPressed(1,86) then
			StartVehicleHorn(veh, 10000, "HELDDOWN", 1)
		end
	end
end

function LSCMenu:OnMenuOpen() end

function LSCMenu:onButtonSelected(name, button)
  if button.purchased then
    exports.blrp_core:me().notify('You have already purchased this upgrade')
    return
  end

  local ped = PlayerPedId()
  local vehicle = GetVehiclePedIsUsing(ped)
  local state = Entity(vehicle).state
  local is_local = exports.blrp_vehicles:HasAnyVehicleFlags(state.flags, 'FLAG_LOCAL')

  if not current_garage.is_admin and not exports.blrp_vehicles:CanAccessVehicle(GetVehiclePedIsIn(PlayerPedId(), false))
    and not current_garage.is_local and not is_local then
    exports.blrp_core:me().notifyError("You don't have keys to this vehicle so you will only be able to preview modifications")
    return
  end

	pCustoms.buttonSelected({ name, button, current_garage.is_admin or false, current_garage.is_local or false, current_garage.is_dealership or false, NetworkGetNetworkIdFromEntity(GetVehiclePedIsIn(PlayerPedId(), false)) })
end

--So we get the button back from server +  bool that determines if we can prchase specific item or not
tCustoms.buttonSelected = function(name, button, canpurchase)
	name = name:lower()
	local m = LSCMenu.currentmenu
	local price = button.price or 0
	local veh = myveh.vehicle
	if m == "main" then
		m = LSCMenu
	end

	mname = m.name:lower()
	--Bunch of button shitt, that gets executed if button is selected + goes through checks
	if mname == "chrome" or mname ==  "classic" or mname ==  "matte" or mname ==  "metals" then
		if m.parent == "Primary color" then
			if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase) then
				myveh.color[1] = button.colorindex
			end
    elseif m.parent == 'Dashboard color' then
			if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase) then
				myveh.dashcolor = button.colorindex
			end
    elseif m.parent == 'Interior color' then
			if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase) then
				myveh.interiorcolor = button.colorindex
			end
		else
			if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase) then
				myveh.color[2] = button.colorindex
			end
		end
  elseif mname == 'chameleon' then
    if m.parent == 'Primary color' then
      if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase) then
				myveh.color[1] = button.colorindex
        myveh.color[2] = button.colorindex
			end
    end
	elseif mname == "metallic" then
		if m.parent == "Primary color" then
			if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase)then
				myveh.color[1] = button.colorindex
				myveh.extracolor[1] = myveh.color[2]
			end
    elseif m.parent == 'Dashboard color' then
			if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase) then
				myveh.dashcolor = button.colorindex
			end
    elseif m.parent == 'Interior color' then
			if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase) then
				myveh.interiorcolor = button.colorindex
			end
		else
			if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase)then
				myveh.extracolor[1] = button.colorindex
				myveh.color[2] = button.colorindex
			end
		end
	elseif mname == "liveries" or mname == "hydraulics" or mname == "lightbar" or mname == "horn" or mname == "tank" or mname == "ornaments" or  mname == "arch cover" or mname == "aerials" or mname == "roof scoops" or mname == "doors" or mname == "roll cage" or mname == "engine block" or mname == "cam cover" or mname == "strut brace" or mname == "trim design" or mname == "ormnametns" or mname == "dashboard" or mname == "dials" or mname == "seats" or mname == "steering wheels" or mname == "plate holder" or mname == "vanity plates" or mname == "shifter leavers" or mname == "plaques" or mname == "speakers" or mname == "trunk"  or mname == "subwoofer" or mname == "armor" or mname == "suspension" or mname == "transmission" or mname == "brakes" or mname == "engine tunes" or mname == "roof" or mname == "hood" or mname == "grille" or mname == "roll cage" or mname == "exhausts" or mname == "skirts" or mname == "rear bumpers" or mname == "front bumpers" or mname == "spoiler" then
		if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase)then
			myveh.mods[button.modtype].mod = button.mod
			SetVehicleMod(veh,button.modtype,button.mod)
		end
  elseif mname == "right door" then
		if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase)then
			if button.name == "Stock" then
				myveh.mods[47].mod = button.mod
				SetVehicleMod(veh,47,button.mod)
			else
				myveh.mods[button.modtype].mod = button.mod
				SetVehicleMod(veh,button.modtype,button.mod)
			end
		end
	elseif mname == "fenders" then
		if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase)then
			if button.name == "Stock" then
				myveh.mods[8].mod = button.mod
				SetVehicleMod(veh,8,button.mod)
			else
				myveh.mods[button.modtype].mod = button.mod
				SetVehicleMod(veh,button.modtype,button.mod)
			end
		end
  elseif mname == "left fender" then
    if button.name == "Stock" or button.purchased or CanPurchase(price, canpurchase) then
      if button.name == "Stock" then
        myveh.mods[9].mod = button.mod
        SetVehicleMod(veh, 9, button.mod)
      else
        myveh.mods[button.modtype].mod = button.mod
        SetVehicleMod(veh, button.modtype, button.mod)
      end
    end
	elseif mname == "turbo" or mname == "headlights" then
		if button.name == "None" or button.name == "Stock Lights" or button.purchased or CanPurchase(price, canpurchase) then
			myveh.mods[button.modtype].mod = button.mod
			ToggleVehicleMod(veh, button.modtype, button.mod)
		end
	elseif mname == "neon layout" then
		if button.name == "None"  then
			SetVehicleNeonLightEnabled(veh,0,false)
			SetVehicleNeonLightEnabled(veh,1,false)
			SetVehicleNeonLightEnabled(veh,2,false)
			SetVehicleNeonLightEnabled(veh,3,false)
			myveh.neoncolor[1] = 255
			myveh.neoncolor[2] = 255
			myveh.neoncolor[3] = 255
      myveh.neon = 0
			SetVehicleNeonLightsColour(veh,255,255,255)
		elseif button.purchased or CanPurchase(price, canpurchase) then
			if not myveh.neoncolor[1] then
				myveh.neoncolor[1] = 255
				myveh.neoncolor[2] = 255
				myveh.neoncolor[3] = 255
			end
      myveh.neon = 1
			SetVehicleNeonLightsColour(veh,myveh.neoncolor[1],myveh.neoncolor[2],myveh.neoncolor[3])
			SetVehicleNeonLightEnabled(veh,0,true)
			SetVehicleNeonLightEnabled(veh,1,true)
			SetVehicleNeonLightEnabled(veh,2,true)
			SetVehicleNeonLightEnabled(veh,3,true)
		end
	elseif mname == "neon color" then
		if button.purchased or CanPurchase(price, canpurchase) then
			myveh.neoncolor[1] = button.neon[1]
			myveh.neoncolor[2] = button.neon[2]
			myveh.neoncolor[3] = button.neon[3]
			SetVehicleNeonLightsColour(veh,button.neon[1],button.neon[2],button.neon[3])
		end
	elseif mname == "windows" then
		if button.name == "None" or button.purchased or CanPurchase(price, canpurchase) then
			myveh.windowtint = button.tint
			SetVehicleWindowTint(veh, button.tint)
		end
	elseif mname == "benny's original wheels" or mname == "benny's bespoke wheels" or mname == "street" or mname == "track" or mname == "sport" or mname == "muscle" or mname == "lowrider" or mname == "back wheel" or mname == "front wheel" or mname == "highend" or mname == "suv" or mname == "offroad" or mname == "tuner" then
		if button.purchased or CanPurchase(price, canpurchase) then
			myveh.wheeltype = button.wtype
			myveh.mods[button.modtype].mod = button.mod
			SetVehicleWheelType(veh,button.wtype)
			SetVehicleMod(veh,button.modtype,button.mod)
		end
	elseif mname == "wheel color" then
		if button.purchased or CanPurchase(price, canpurchase) then
			myveh.extracolor[2] = button.colorindex
			SetVehicleExtraColours(veh, myveh.extracolor[1], button.colorindex)
		end
	elseif mname == "wheel accessories" then
		if button.name == "Stock Tires" then
			SetVehicleModKit(veh,0)
			SetVehicleMod(veh,23,myveh.mods[23].mod,false)
			myveh.mods[23].variation = false
			if IsThisModelABike(GetEntityModel(veh)) then
				SetVehicleModKit(veh,0)
				SetVehicleMod(veh,24,myveh.mods[24].mod,false)
				myveh.mods[24].variation = false
			end
		elseif button.name == "Custom Tires" and (button.purchased or CanPurchase(price, canpurchase)) then
			SetVehicleModKit(veh,0)
			SetVehicleMod(veh,23,myveh.mods[23].mod,true)
			myveh.mods[23].variation = true
			if IsThisModelABike(GetEntityModel(veh)) then
				SetVehicleModKit(veh,0)
				SetVehicleMod(veh,24,myveh.mods[24].mod,true)
				myveh.mods[24].variation = true
			end
		--[[elseif button.name == "Bulletproof Tires" and  (button.purchased or CanPurchase(price, canpurchase)) then
			if GetVehicleTyresCanBurst(myveh.vehicle) then
				myveh.bulletProofTyres = false
				SetVehicleTyresCanBurst(veh,false)
			else
				myveh.bulletProofTyres = true
				SetVehicleTyresCanBurst(veh,true)
			end]]--
		elseif button.smokecolor ~= nil  and  (button.purchased or CanPurchase(price, canpurchase)) then
			SetVehicleModKit(veh,0)
			myveh.mods[20].mod = true
			ToggleVehicleMod(veh,20,true)
			myveh.smokecolor = button.smokecolor
			SetVehicleTyreSmokeColor(veh,button.smokecolor[1],button.smokecolor[2],button.smokecolor[3])
		end
	elseif mname == "license" then
		if button.purchased or CanPurchase(price, canpurchase) then
			myveh.plateindex = button.plateindex
			SetVehicleNumberPlateTextIndex(veh,button.plateindex)
		end
	elseif mname == "main" then
		if name == "repair vehicle" then
			if CanPurchase(price, canpurchase) then
				repair()
				LSCMenu:ChangeMenu("categories")
			end
		end
	end
  SaveCar()
	CheckPurchases(m)
end

--This was perfect until I tried different vehicles
local function PointCamAtBone(bone,ox,oy,oz)
	SetIbuttons({
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_back, 0),"Back"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_select, 0),"Select"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_up, 0),"Up"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_down, 0),"Down"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_left, 0),"Left"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_right, 0),"Right"},
			{GetControlInstructionalButton(1,0, 0),"Free camera"}
	 },0)
	SetCamActive(cam, true)
	local veh = myveh.vehicle
	local b = GetEntityBoneIndexByName(veh, bone)
	local bx,by,bz = table.unpack(GetWorldPositionOfEntityBone(veh, b))
	local ox2,oy2,oz2 = table.unpack(GetOffsetFromEntityGivenWorldCoords(veh, bx, by, bz))
	local x,y,z = table.unpack(GetOffsetFromEntityInWorldCoords(veh, ox2 + f(ox), oy2 + f(oy), oz2 +f(oz)))
	SetCamCoord(cam, x, y, z)
	PointCamAtCoord(cam,GetOffsetFromEntityInWorldCoords(veh, 0, oy2, oz2))
	RenderScriptCams( 1, 1, 1000, 0, 0)
end

local function MoveVehCam(pos,x,y,z)
	SetIbuttons({
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_back, 0),"Back"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_select, 0),"Select"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_up, 0),"Up"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_down, 0),"Down"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_left, 0),"Left"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_right, 0),"Right"},
			{GetControlInstructionalButton(1,0, 0),"Free camera"}
	 },0)
	SetCamActive(cam, true)
	local veh = myveh.vehicle
	local vx,vy,vz = table.unpack(GetEntityCoords(veh))
	local d = GetModelDimensions(GetEntityModel(veh))
	local length,width,height = d.y*-2, d.x*-2, d.z*-2
	local ox,oy,oz
	if pos == 'front' then
		ox,oy,oz= table.unpack(GetOffsetFromEntityInWorldCoords(veh, f(x), (length/2)+ f(y), f(z)))
	elseif pos == "front-top" then
		ox,oy,oz= table.unpack(GetOffsetFromEntityInWorldCoords(veh, f(x), (length/2) + f(y),(height) + f(z)))
	elseif pos == "back" then
		ox,oy,oz= table.unpack(GetOffsetFromEntityInWorldCoords(veh, f(x), -(length/2) + f(y),f(z)))
	elseif pos == "back-top" then
		ox,oy,oz= table.unpack(GetOffsetFromEntityInWorldCoords(veh, f(x), -(length/2) + f(y),(height/2) + f(z)))
	elseif pos == "left" then
		ox,oy,oz= table.unpack(GetOffsetFromEntityInWorldCoords(veh, -(width/2) + f(x), f(y), f(z)))
	elseif pos == "right" then
		ox,oy,oz= table.unpack(GetOffsetFromEntityInWorldCoords(veh, (width/2) + f(x), f(y), f(z)))
	elseif pos == "middle" then
		ox,oy,oz= table.unpack(GetOffsetFromEntityInWorldCoords(veh, f(x), f(y), (height/2) + f(z)))
	end
	SetCamCoord(cam, ox, oy, oz)
	PointCamAtCoord(cam,GetOffsetFromEntityInWorldCoords(veh, 0, 0, f(0)))
	RenderScriptCams( 1, 1, 1000, 0, 0)
end

function LSCMenu:OnMenuChange(last,current)
	UnfakeVeh()
	if last == "main" then
		last = self
	end
	if last.name == "categories" and current.name == "main" then
		LSCMenu:Close()
	end
	c = current.name:lower()
	--Camera,door stuff
	if c == "front bumpers" then
		MoveVehCam('front',-0.6,1.5,0.4)
	elseif  c == "rear bumpers" then
		MoveVehCam('back',-0.5,-1.5,0.2)
	elseif c == "Engine Tunes" then
		--PointCamAtBone('engine',0,-1.5,1.5)
	elseif c == "exhausts" then
		--PointCamAtBone("exhaust",0,-1.5,0)
	elseif c == "hood" then
		MoveVehCam('front-top',-0.5,1.3,1.0)
	elseif c == "headlights" then
		MoveVehCam('front',-0.6,1.3,0.6)
	elseif c == "license" or c == "plate holder" then
		MoveVehCam('back',0,-1,0.2)
	elseif c == "vanity plates" then
		MoveVehCam('front',-0.3,0.8,0.3)
	elseif c == "roof" then
		--MoveVehCam('middle',-1.2,2,1.5)
	elseif c == "fenders" then
		MoveVehCam('left',-1.8,-1.3,0.7)
	elseif c == "grille" then
		--MoveVehCam('front',-0.3,0.8,0.6)
	elseif c == "skirts" then
		MoveVehCam('left',-1.8,-1.3,0.7)
	elseif c == "spoiler" then
		MoveVehCam('back',0.5,-1.6,1.3)
	elseif c == "back wheel" then
		PointCamAtBone("wheel_lr",-1.4,0,0.3)
	elseif c == "front wheel" or c == "wheel accessories" or  c == "wheel color" or c == "sport" or c == "muscle" or c == "lowrider"  or c == "highend" or c == "suv" or c == "offroad" or c == "tuner" then
		PointCamAtBone("wheel_lf",-1.4,0,0.3)
	--[[elseif c == "windows" then
		if not IsThisModelABike(GetEntityModel(myveh.vehicle)) then
		PointCamAtBone("window_lf",-2.0,0,0.3)
		end]]
	elseif c == "neon color" then
		PointCamAtBone("neon_l",-2.0,2.0,0.4)
	elseif c == "shifter leavers" or c == "trim design" or c == "ornaments" or c == "dashboard" or c == "dials" or c == "seats" or c =="steering wheels" then
		--Set view mode to first person
		SetFollowVehicleCamViewMode(4)
	elseif c == "doors" and last.name:lower() == "interior" then
		--Open both front doors
		SetVehicleDoorOpen(myveh.vehicle, 0, 0, 0)
		SetVehicleDoorOpen(myveh.vehicle, 1, 0, 0)
	elseif c == "trunk" then
		--- doorIndex:
		-- 0 = Front Left Door
		-- 1 = Front Right Door
		-- 2 = Back Left Door
		-- 3 = Back Right Door
		-- 4 = Hood
		-- 5 = Trunk
		-- 6 = Back
		-- 7 = Back2
		SetVehicleDoorOpen(myveh.vehicle, 5, 0, 0)
	elseif c == "speakers" or  c == "engine block" or c == "air filter" or c == "strut brace" or c == "cam cover" then
		--Open hood and trunk
		SetVehicleDoorOpen(myveh.vehicle, 5, 0, 0)
		SetVehicleDoorOpen(myveh.vehicle, 4, 0, 0)
	elseif IsCamActive(cam) then
		--Go back to gameplaycam
		SetCamCoord(cam,GetGameplayCamCoords())
		SetCamRot(cam, GetGameplayCamRot(2), 2)
		RenderScriptCams( 1, 1, 0, 0, 0)
		RenderScriptCams( 0, 1, 1000, 0, 0)
		SetCamActive(gameplaycam, true)
		EnableGameplayCam(true)
		SetCamActive(cam, false)
		SetIbuttons({
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_back, 0),"Back"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_select, 0),"Select"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_up, 0),"Up"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_down, 0),"Down"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_left, 0),"Left"},
			{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_right, 0),"Right"}
		},0)
	else
		--Close all doors
		SetVehicleDoorShut(myveh.vehicle, 0, 0)
		SetVehicleDoorShut(myveh.vehicle, 1, 0)
		SetVehicleDoorShut(myveh.vehicle, 4, 0)
		SetVehicleDoorShut(myveh.vehicle, 5, 0)
		SetFollowVehicleCamViewMode(0)
	end
end


--Bunch of checks
function CheckPurchases(m)
	name = m.name:lower()
	if name == "chrome" or name ==  "classic" or name ==  "matte" or name ==  "metals" then
		if m.parent == "Primary color" then
			for i,b in pairs(m.buttons) do
				if b.purchased and b.colorindex ~= myveh.color[1] then
					if b.purchased ~= nil then b.purchased = false end
					b.sprite = nil
				elseif b.purchased == false and b.colorindex == myveh.color[1] then
					if b.purchased ~= nil then b.purchased = true end
					b.sprite = "garage"
				end
			end
    elseif m.parent == 'Dashboard color' then
			for i,b in pairs(m.buttons) do
				if b.purchased and b.colorindex ~= myveh.dashcolor then
					if b.purchased ~= nil then b.purchased = false end
					b.sprite = nil
				elseif b.purchased == false and b.colorindex == myveh.dashcolor then
					if b.purchased ~= nil then b.purchased = true end
					b.sprite = "garage"
				end
			end
    elseif m.parent == 'Interior color' then
			for i,b in pairs(m.buttons) do
				if b.purchased and b.colorindex ~= myveh.interiorcolor then
					if b.purchased ~= nil then b.purchased = false end
					b.sprite = nil
				elseif b.purchased == false and b.colorindex == myveh.interiorcolor then
					if b.purchased ~= nil then b.purchased = true end
					b.sprite = "garage"
				end
			end
		else
			for i,b in pairs(m.buttons) do
				if b.purchased and (b.colorindex ~= myveh.color[1] or myveh.extracolor[1] ~= myveh.color[2]) then
					if b.purchased ~= nil then b.purchased = false end
					b.sprite = nil
				elseif b.purchased == false and b.colorindex == myveh.color[1] and myveh.extracolor[1] == myveh.color[2] then
					if b.purchased ~= nil then b.purchased = true end
					b.sprite = "garage"
				end
			end
		end
  elseif name == 'chameleon' then
    if m.parent == 'Primary color' then
      for i,b in pairs(m.buttons) do
				if b.purchased and b.colorindex ~= myveh.color[1] then
					if b.purchased ~= nil then b.purchased = false end
					b.sprite = nil
				elseif b.purchased == false and b.colorindex == myveh.color[1] then
					if b.purchased ~= nil then b.purchased = true end
					b.sprite = "garage"
				end
			end
    end
	elseif name == "metallic" then
		if m.parent == "Primary color" then
			for i,b in pairs(m.buttons) do
				if b.purchased and b.colorindex ~= myveh.color[1] then
					if b.purchased ~= nil then b.purchased = false end
					b.sprite = nil
				elseif b.purchased == false and b.colorindex == myveh.color[1] then
					if b.purchased ~= nil then b.purchased = true end
					b.sprite = "garage"
				end
			end
    elseif m.parent == 'Dashboard color' then
			for i,b in pairs(m.buttons) do
				if b.purchased and b.colorindex ~= myveh.dashcolor then
					if b.purchased ~= nil then b.purchased = false end
					b.sprite = nil
				elseif b.purchased == false and b.colorindex == myveh.dashcolor then
					if b.purchased ~= nil then b.purchased = true end
					b.sprite = "garage"
				end
			end
    elseif m.parent == 'Interior color' then
			for i,b in pairs(m.buttons) do
				if b.purchased and b.colorindex ~= myveh.interiorcolor then
					if b.purchased ~= nil then b.purchased = false end
					b.sprite = nil
				elseif b.purchased == false and b.colorindex == myveh.interiorcolor then
					if b.purchased ~= nil then b.purchased = true end
					b.sprite = "garage"
				end
			end
		else
			for i,b in pairs(m.buttons) do
				if b.purchased and (b.colorindex ~= myveh.color[2] or myveh.extracolor[1] ~= b.colorindex) then
					if b.purchased ~= nil then b.purchased = false end
					b.sprite = nil
				elseif b.purchased == false and b.colorindex == myveh.color[2] and myveh.extracolor[1] == b.colorindex then
					if b.purchased ~= nil then b.purchased = true end
					b.sprite = "garage"
				end
			end
		end
	elseif name == "armor" or name == "suspension" or name == "transmission" or name == "brakes" or name == "engine tunes" or name == "roof" or name == "fenders" or name == "hood" or name == "grille" or name == "roll cage" or name == "exhausts" or name == "skirts" or name == "rear bumpers" or name == "front bumpers" or name == "spoiler" then
		for i,b in pairs(m.buttons) do
			if b.mod == -1  then
				if myveh.mods[b.modtype].mod == -1 then
					if b.purchased ~= nil then b.purchased = true end
					b.sprite = "garage"
				else
					if b.purchased ~= nil then b.purchased = false end
					b.sprite = nil
				end
			elseif b.mod == 0 or b.mod == false then
				if myveh.mods[b.modtype].mod == false or myveh.mods[b.modtype].mod == 0 then
					if b.purchased ~= nil then b.purchased = true end
					b.sprite = "garage"
				else
					if b.purchased ~= nil then b.purchased = false end
					b.sprite = nil
				end
			else
				if myveh.mods[b.modtype].mod == b.mod then
					if b.purchased ~= nil then b.purchased = true end
					b.sprite = "garage"
				else
					if b.purchased ~= nil then b.purchased = false end
					b.sprite = nil
				end
			end
		end
  elseif name == "left fender" then
  for i,b in pairs(m.buttons) do
    if b.mod == myveh.mods[b.modtype].mod then
      if b.purchased ~= nil then b.purchased = true end
      b.sprite = "garage"
    else
      if b.purchased ~= nil then b.purchased = false end
      b.sprite = nil
    end
  end
	elseif name == "neon layout" then
		for i,b in pairs(m.buttons) do
			if b.name == "None" then
				if IsVehicleNeonLightEnabled(myveh.vehicle, 0) == false and IsVehicleNeonLightEnabled(myveh.vehicle, 1) == false  and IsVehicleNeonLightEnabled(myveh.vehicle, 2) == false and IsVehicleNeonLightEnabled(myveh.vehicle, 3) == false then
					b.sprite = "garage"
				else
					b.sprite =  nil
				end
			elseif b.name == "Front,Back and Sides" then
				if IsVehicleNeonLightEnabled(myveh.vehicle, 0)  and IsVehicleNeonLightEnabled(myveh.vehicle, 1)  and IsVehicleNeonLightEnabled(myveh.vehicle, 2)  and IsVehicleNeonLightEnabled(myveh.vehicle, 3)  then
					b.sprite = "garage"
				else
					b.sprite =  nil
				end
			end
		end
	elseif name == "neon color" then
		for i,b in pairs(m.buttons) do
			if b.neon[1] == myveh.neoncolor[1] and b.neon[2] == myveh.neoncolor[2] and b.neon[3] == myveh.neoncolor[3] then
				b.sprite = "garage"
			else
				b.sprite = nil
			end
		end
	elseif name == "windows" then
		for i,b in pairs(m.buttons) do
			if myveh.windowtint == b.tint then
				b.sprite = "garage"
			else
				b.sprite = nil
			end
		end
  elseif name == "right door" then
    for i,b in pairs(m.buttons) do
      if b.mod == myveh.mods[b.modtype].mod then
        if b.purchased ~= nil then b.purchased = true end
        b.sprite = "garage"
      else
        if b.purchased ~= nil then b.purchased = false end
        b.sprite = nil
      end
    end
  elseif name == "benny's original wheels" or name == "benny's bespoke wheels" or name == "street" or name == "track" or name == "sport" or name == "muscle" or name == "lowrider" or name == "back wheel" or name == "front wheel" or name == "highend" or name == "suv" or name == "offroad" or name == "tuner" then
		for i,b in pairs(m.buttons) do
			if myveh.mods[b.modtype].mod == b.mod and myveh.wheeltype == b.wtype then
				b.sprite = "garage"
			else
				b.sprite = nil
			end
		end
	elseif name == "wheel color" then
		for i,b in pairs(m.buttons) do
			if b.colorindex == myveh.extracolor[2] then
				b.sprite = "garage"
			else
				b.sprite = nil
			end
		end
	elseif name == "wheel accessories" then
		for i,b in pairs(m.buttons) do
			if b.name == "Stock Tires" then
				if GetVehicleModVariation(myveh.vehicle,23) == false then
					b.sprite = "garage"
				else
					b.sprite = nil
				end
			elseif b.name == "Custom Tires" then
				local frontVariation = GetVehicleModVariation(myveh.vehicle, 23)
				local rearVariation = IsThisModelABike(GetEntityModel(myveh.vehicle)) and GetVehicleModVariation(myveh.vehicle, 24) -- for Bikes

				if frontVariation or rearVariation then
					b.sprite = "garage"
				else
					b.sprite = nil
				end
			elseif b.name == "Bulletproof Tires" then
				if GetVehicleTyresCanBurst(myveh.vehicle) == false then
					b.sprite = "garage"
				else
					b.sprite = nil
				end
			elseif b.smokecolor ~= nil then
				local col = table.pack(GetVehicleTyreSmokeColor(myveh.vehicle))
				if col[1] == b.smokecolor[1] and col[2] == b.smokecolor[2] and col[3] == b.smokecolor[3] then
					b.sprite = "garage"
				else
					b.sprite = nil
				end
			end
		end
	elseif name == "license" then
		for i,b in pairs(m.buttons) do
			if myveh.plateindex == b.plateindex then
				b.sprite = "garage"
			else
				b.sprite = nil
			end
		end
    
	elseif name == "tank" or name == "ornaments" or name == "arch cover" or name == "aerials" or name == "roof scoops" or name == "doors" or name == "roll cage" or name == "engine block" or name == "cam cover" or name == "strut brace" or name == "trim design" or name == "ornametns" or name == "dashboard" or name == "dials" or name == "seats" or name == "steering wheels" or name == "plate holder" or name == "vanity plates" or name == "shifter leavers" or name == "plaques" or name == "speakers" or name == "trunk" or name == "subwoofer" or name == "headlights" or name == "turbo" or  name == "hydraulics" or name == "lightbar" or name == "liveries" or name == "horn" then
		for i,b in pairs(m.buttons) do
			if myveh.mods[b.modtype].mod == b.mod then
				b.sprite = "garage"
			else
				b.sprite = nil
			end
		end
	end
end

--Show notifications and return if item can be purchased
function CanPurchase(price, canpurchase)
	if canpurchase then
		if LSCMenu.currentmenu == "main" then
			LSCMenu:showNotification("Your vehicle has been repaired.")
		else
			LSCMenu:showNotification("Item purchased.")
		end
		return true
	else
		LSCMenu:showNotification("~r~You cannot afford this purchase.")
		return false
	end
end

--Unfake vehicle, or in other words reset vehicle stuff to real so all the preview stuff would be gone
function UnfakeVeh()
  local veh = myveh.vehicle
  SetVehicleModKit(veh,0)
  SetVehicleWheelType(veh, myveh.wheeltype)
  for i,m in pairs(myveh.mods) do
    if i == 22 or i == 18 then
      ToggleVehicleMod(veh,i,m.mod)
    elseif i == 23 or i == 24 then
      SetVehicleMod(veh,i,m.mod,m.variation)
    else
      SetVehicleMod(veh,i,m.mod)
    end
  end

  if colors_changed then
    ClearVehicleCustomPrimaryColour(veh)
    ClearVehicleCustomSecondaryColour(veh)
    SetVehicleColours(veh,myveh.color[1], myveh.color[2])
    SetVehicleDashboardColor(veh, myveh.dashcolor)
    SetVehicleInteriorColor(veh, myveh.interiorcolor)
  end

  SetVehicleExtraColours(veh,myveh.extracolor[1], myveh.extracolor[2])
  SetVehicleNeonLightsColour(veh,myveh.neoncolor[1],myveh.neoncolor[2],myveh.neoncolor[3])
  SetVehicleNumberPlateTextIndex(veh, myveh.plateindex)
  SetVehicleWindowTint(veh, myveh.windowtint)
end

--This is something new o_O, just some things to draw instructional buttons
local Ibuttons = nil
--Set up scaleform
function SetIbuttons(buttons, layout)
	Citizen.CreateThread(function()
		if not HasScaleformMovieLoaded(Ibuttons) then
			Ibuttons = RequestScaleformMovie("INSTRUCTIONAL_BUTTONS")
			while not HasScaleformMovieLoaded(Ibuttons) do
				Citizen.Wait(0)
			end
		else
			Ibuttons = RequestScaleformMovie("INSTRUCTIONAL_BUTTONS")
			while not HasScaleformMovieLoaded(Ibuttons) do
				Citizen.Wait(0)
			end
		end
		local sf = Ibuttons
		local w,h = GetScreenResolution()
		PushScaleformMovieFunction(sf,"CLEAR_ALL")
		PopScaleformMovieFunction()
		PushScaleformMovieFunction(sf,"SET_DISPLAY_CONFIG")
		PushScaleformMovieFunctionParameterInt(w)
		PushScaleformMovieFunctionParameterInt(h)
		PushScaleformMovieFunctionParameterFloat(0.03)
		PushScaleformMovieFunctionParameterFloat(0.98)
		PushScaleformMovieFunctionParameterFloat(0.01)
		PushScaleformMovieFunctionParameterFloat(0.95)
		PushScaleformMovieFunctionParameterBool(true)
		PushScaleformMovieFunctionParameterBool(false)
		PushScaleformMovieFunctionParameterBool(false)
		PushScaleformMovieFunctionParameterInt(w)
		PushScaleformMovieFunctionParameterInt(h)
		PopScaleformMovieFunction()
		PushScaleformMovieFunction(sf,"SET_MAX_WIDTH")
		PushScaleformMovieFunctionParameterInt(1)
		PopScaleformMovieFunction()

		for i,btn in pairs(buttons) do
			PushScaleformMovieFunction(sf,"SET_DATA_SLOT")
			PushScaleformMovieFunctionParameterInt(i-1)
			PushScaleformMovieFunctionParameterString(btn[1])
			PushScaleformMovieFunctionParameterString(btn[2])
			PopScaleformMovieFunction()

		end
		if layout ~= 1 then
			PushScaleformMovieFunction(sf,"SET_PADDING")
			PushScaleformMovieFunctionParameterInt(10)
			PopScaleformMovieFunction()
		end
		PushScaleformMovieFunction(sf,"DRAW_INSTRUCTIONAL_BUTTONS")
		PushScaleformMovieFunctionParameterInt(layout)
		PopScaleformMovieFunction()
	end)
end

--Draw the scaleform
function DrawIbuttons()
	if HasScaleformMovieLoaded(Ibuttons) then
		DrawScaleformMovie(Ibuttons, 0.5, 0.5, 1.0, 1.0, 255, 255, 255, 255)
	end
end

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		if current_garage then
			SetLocalPlayerVisibleLocally(1)
		end
		if LSCMenu:isVisible() then
			DrawIbuttons()--Draw the scaleform if menu is visible
			if IsDisabledControlJustPressed(1,0) or IsControlJustPressed(1,0) then -- V
				if cam and IsCamActive(cam) then --If the script cam is active then we can change back to gameplay cam
					SetCamCoord(cam,GetGameplayCamCoords())
					SetCamRot(cam, GetGameplayCamRot(2), 2)
					RenderScriptCams( 1, 1, 0, 0, 0)
					RenderScriptCams( 0, 1, 1000, 0, 0)
					SetCamActive(gameplaycam, true)
					EnableGameplayCam(true)
					SetCamActive(cam, false)
					SetIbuttons({
							{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_back, 0),"Back"},
							{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_select, 0),"Select"},
							{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_up, 0),"Up"},
							{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_down, 0),"Down"},
							{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_left, 0),"Left"},
							{GetControlInstructionalButton(1,LSCMenu.config.controls.menu_right, 0),"Right"}
					 },0)
				end
			end
		end
	end
end)
