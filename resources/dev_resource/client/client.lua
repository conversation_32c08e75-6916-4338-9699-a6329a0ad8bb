if GlobalState.is_dev then
  local clone = nil

  RegisterCommand('cloneme', function()
    if clone and DoesEntityExist(clone) then
      DeleteEntity(clone)
    end

    local model = GetEntityModel(PlayerPedId())

    local coords = GetEntityCoords(PlayerPedId()) + (GetEntityForwardVector(PlayerPedId()) * 2) - vector3(0, 0, 0.97)

    while not HasModelLoaded(model) do
      Wait(0)
      RequestModel(model)
    end

    clone = CreatePed(4, model, coords.x, coords.y, coords.z, GetEntityHeading(PlayerPedId()) - 180.0, false, true)
    FreezeEntityPosition(clone, true)
    SetEntityInvincible(clone, true)
    SetBlockingOfNonTemporaryEvents(clone, true)

    for i = 1, 11 do
      SetPedComponentVariation(clone, i, GetPedDrawableVariation(PlayerPedId(), i), GetPedTextureVariation(PlayerPedId(), i), GetPedPaletteVariation(PlayerPedId(), i))
    end

    for _, i in pairs({ 0, 1, 2, 6, 7 }) do
      SetPedPropIndex(clone, i, GetPedPropIndex(PlayerPedId(), i), math.max(0, GetPedPropTextureIndex(PlayerPedId(), i)))
    end
  end)
end


local mba_configs = {
  {
    name = 'Dev TEST',
    enabled = false,

    entity_sets = {
      'mba_tribune',
      'mba_jumbotron',
      -- 'mba_fameorshame',
    },
  },

  {
    name = 'Badcon',
    enabled = false,

    entity_sets = {
      'mba_tribune',
      'mba_tarps',
      'mba_jumbotron',
      'mba_badcon',
    },
  },

  {
    name = 'Date Auction',
    enabled = false,

    entity_sets = {
      'mba_tribune',
      'mba_tarps',
      'mba_backstage',
      'mba_jumbotron',
      'mba_dateauction',
    },
  },

  ------------------------------

  {
    name = 'Basketball',
    enabled = false,

    entity_sets = {
      'mba_tribune',
      'mba_tarps',
      'mba_basketball',
      'mba_jumbotron',
    },
  },
  {
    name = 'Boxing',
    enabled = false,

    entity_sets = {
      'mba_tribune',
      'mba_tarps',
      'mba_fighting',
      'mba_boxing',
      'mba_jumbotron',
    },
  },
  {
    name = 'Concert',
    enabled = false,

    entity_sets = {
      'mba_tribune',
      'mba_tarps',
      'mba_backstage',
      'mba_concert',
      'mba_jumbotron',
    },
  },
  {
    name = 'Derby',
    enabled = false,

    entity_sets = {
      'mba_cover',
      'mba_terrain',
      'mba_derby',
      'mba_ring_of_fire',
    },
  },
  {
    name = 'Fame or Shame',
    enabled = false,

    entity_sets = {
      'mba_tribune',
      'mba_tarps',
      'mba_backstage',
      'mba_fameorshame',
      'mba_jumbotron',
    },
  },
  {
    name = 'Fashion',
    enabled = false,

    entity_sets = {
      'mba_tribune',
      'mba_tarps',
      'mba_backstage',
      'mba_fashion',
      'mba_jumbotron',
    },
  },
  {
    name = 'MMA',
    enabled = false,

    entity_sets = {
      'mba_tribune',
      'mba_tarps',
      'mba_fighting',
      'mba_mma',
      'mba_jumbotron',
    },
  },
  {
    name = 'Paintball',
    enabled = false,

    entity_sets = {
      'mba_tribune',
      'mba_chairs',
      'mba_paintball',
      'mba_jumbotron',
    },
  },
  {
    name = 'Wrestling',
    enabled = false,

    entity_sets = {
      'mba_tribune',
      'mba_tarps',
      'mba_fighting',
      'mba_wrestling',
      'mba_jumbotron',
    },
  },
  {
    name = 'Rocket League',
    enabled = true,

    entity_sets = {
      'mba_tribune',
      'mba_chairs',
      'mba_rocketleague',
    },
  },
}

-- for some reason this was causing issues with the loading - gabz_entityset_mods1.lua works better
--Citizen.CreateThread(function()
--  Citizen.Wait(10000)
--
--  RequestIpl('gabz_mba_milo_')
--
--  local interior_id = GetInteriorAtCoords(-324.2203, -1968.493, 20.60336)
--
--  -- Calculate sets to disable
--  for _, mba_config in pairs(mba_configs) do
--    for _, set_name in pairs(mba_config.entity_sets) do
--      DeactivateInteriorEntitySet(interior_id, set_name)
--    end
--  end
--
--  -- Calculate sets to enable
--  for _, mba_config in pairs(mba_configs) do
--    if mba_config.enabled then
--      for _, set_name in pairs(mba_config.entity_sets) do
--        ActivateInteriorEntitySet(interior_id, set_name)
--      end
--    end
--  end
--
--  RefreshInterior(interior_id)
--end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1000)

    TriggerEvent('gcPhone:setMenuStatus', false)
  end
end)

--[[ -- Evidence grid coords
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    local coords = GetEntityCoords(PlayerPedId()).xy

    local grid_coords = math.floor(coords / 100.0)

    drawTxt2(0.9, 1.4, 1.0, 1.0, 0.4, 'Coords: ' .. coords .. ' / Grid Coords: ' .. grid_coords, 255, 255, 255, 255)
  end
end)
]]

--[[ Animation timer
Citizen.CreateThread(function()
  local dict = 'amb@world_human_drinking@coffee@male@idle_a'
  local clip = 'idle_c'
  local blend_in_speed = 8.0 -- usually 8.0
  local blend_out_speed = -8.0  -- usually 8.0
  local duration = -1 -- usually false
  local flag = 18

  RequestAnimDict(dict)

  while not HasAnimDictLoaded(dict) do
    Citizen.Wait(0)
  end

  print('GetAnimDuration', GetAnimDuration(dict, clip))

  local start_time = GetGameTimer()

  TaskPlayAnim(PlayerPedId(), dict, clip, blend_in_speed, blend_out_speed, duration, flag)

  while not IsEntityPlayingAnim(PlayerPedId(), dict, clip, 3) do
    Citizen.Wait(0)
  end

  print('Animation start at', start_time)
  print('Animation is', dict, clip)
  print('Anim flag is', flag)

  local stopped = false

  while not stopped and IsEntityPlayingAnim(PlayerPedId(), dict, clip, 3) do
    Citizen.Wait(0)

    if IsControlPressed(0, 246) then
      stopped = true
    end
  end

  local end_time = GetGameTimer()

  print('Animation done at', end_time, 'Delta is', end_time - start_time)
end)
]]

--[[

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1000)

    local ped = PlayerPedId()
    local localInteriorId = GetInteriorFromEntity(ped)
    local localRoomHash = GetRoomKeyFromEntity(ped)
    local localRoomId = GetInteriorRoomIndexByHash(localInteriorId, localRoomHash)
    local localRoomName = GetInteriorRoomName(localInteriorId, localRoomId)

    print('interior info', localInteriorId, localRoomHash, localRoomName)
  end
end)

]]

Citizen.CreateThread(function()
    while true do
        DistantCopCarSirens(false)
        Citizen.Wait(400)
    end
end)

local lastVeh = nil

Citizen.CreateThread(function()
    LoadAnim("missfinale_c2leadinoutfin_c_int")
    while true do
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
        if (GetPedInVehicleSeat(veh, -1) == PlayerPedId()) and GetEntityModel(veh) == -1178021069 then
          lastVeh = veh
          SetCurrentPedWeapon(PlayerPedId(), GetHashKey("weapon_unarmed"), 1)
          if not IsEntityPlayingAnim(PlayerPedId(), 'missfinale_c2leadinoutfin_c_int', '_leadin_loop2_lester', 3) then
            TaskPlayAnim(PlayerPedId(), 'missfinale_c2leadinoutfin_c_int', '_leadin_loop2_lester', 8.0, 8.0, -1, 49, 1, false, false, false)
          end
        else
          if lastVeh then
            ClearPedSecondaryTask(PlayerPedId())
            lastVeh = nil
          end
        end
        Citizen.Wait(0)
    end
end)

LoadAnim = function(dict)
	while not HasAnimDictLoaded(dict) do
		RequestAnimDict(dict)

		Citizen.Wait(1)
	end
end


--[[ RegisterCommand("makeoffsets", function(source, args)
  local model_name = args[1] -- Get model name from command argument
  if not model_name or model_name == "" then
    TriggerServerEvent('chat:addMessage', { args = { '^1Error^7', 'Please provide a model name (e.g., /makeoffsets pata_shell2)' } })
    return
  end

  local model = GetHashKey(model_name)
  local shell_coords = vec3(-1847.498047, 3299.855957, 132.953003) -- Your shell base
  local coords = {
    ['door'] = vector3(-1852.512, 3280.117, 142.973),
    ['storage'] = vector3(-1844.943, 3283.886, 142.973),
    ['closet'] = vector3(-1864.342, 3291.577, 148.118),
  }

  -- Calculate offsets relative to shell_coords
  local offsets = {}
  for name, coord in pairs(coords) do
    offsets[name] = coord - shell_coords
  end

  -- Get model dimensions for extents
  RequestModel(model)
  local start_time = GetGameTimer()
  while not HasModelLoaded(model) and GetGameTimer() - start_time < 2000 do
    Citizen.Wait(0)
  end
  if not HasModelLoaded(model) then
    TriggerEvent('chat:addMessage', { args = { '^1Error^7', 'Failed to load model ' .. model_name .. ' for extents calculation' } })
    print("Failed to load model " .. model_name .. " for extents calculation")
    return
  end

  local min, max = GetModelDimensions(model)
  local extents = {
    min = min,
    max = max
  }
  SetModelAsNoLongerNeeded(model)

  -- Format offsets and extents for output
  local offset_str = string.format(
    "{ ['door'] = %s, ['storage'] = %s, ['closet'] = %s }",
    tostring(offsets['door']), tostring(offsets['storage']), tostring(offsets['closet'])
  )
  local extents_str = string.format(
    "{ min = %s, max = %s }",
    tostring(extents.min), tostring(extents.max)
  )

  -- Output to console and chat
  print('SHELL-OFFSETS for ' .. model_name .. ':', offset_str)
  print('SHELL-EXTENTS for ' .. model_name .. ':', extents_str)
end, false) ]]