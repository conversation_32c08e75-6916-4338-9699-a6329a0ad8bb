local Config = {}
local steamkey = '310C2377815B5BD4238B4DCF07F7DA80' --Steam API Key
local minimumAge = (4 * 7 * 24 * 60 * 60) -- seconds - four weeks
local minimumDiscordAge = (60 * 60 * 24 * 30) -- 30 days (adjust as needed)
local slist = {}

local timed_kicks = {}

exports('AddTimedKick', function(vrp_id, minutes, reason)
	vrp_id = tonumber(vrp_id)

	timed_kicks[vrp_id] = {
		end_time = os.time() + (minutes * 60),
		reason = reason
	}
end)

-- https://badlandsrp.com/uploads/monthly_2021_12/logo.png.ad3a1c768aa6f859567b0f0270d2a324.png
function createInfoCard(status, dots, bcso, sahp, lspd, lsfd, doc, doj, lawyer, information_message)
	if not information_message then
		information_message = ''
	end

	if not dots then
		dots = ''
	end

	local reconnect_text = ''

	if string.match(status, 'queue') then
		reconnect_text = [==[
		{
				"type": "TextBlock",
				"text": "Reconnect to the queue if the dots stop moving",
				"horizontalAlignment": "Center",
				"size": "Small",
				"fontType": "Default",
				"isSubtle": true,
				"isVisible": true
		},
		]==]
	end

	local faction_text = ''

	if bcso or sahp or lspd or lsfd or doc or doj or lawyer then
		faction_text = [==[
			{
					"type": "TextBlock",
					"text": "Attention!",
					"horizontalAlignment": "Center",
					"size": "Large",
					"fontType": "Default",
					"isSubtle": true,
					"isVisible": true,
					"color": "Warning",
					"spacing": "Large"
			},
			{
					"type": "RichTextBlock",
					"inlines": [
							{
									"type": "TextRun",
									"text": "There are not currently enough faction members clocked in to support the server population. If you are willing to commit to a minimum of 1 hour on a faction, please select the faction below to receive a prioritized queue and be teleported to your faction's clock in station. If you opt for queue priority, the following criteria will apply:"
							}
					]
			},
      {
          "type": "TextBlock",
          "text": "1. You must serve a minimum of 1 hour (60 minutes) in the faction you select \r2. You must clock in within 10 minutes of connecting to the server \r3. If you reconnect, you must continue to stay clocked in and finish your 60 minute commitment \r4. If you clock out prior to 1 hour served, you will be kicked from the server and de-prioritized",
					"wrap": true
      },
			{
				"type": "ActionSet",
				"actions": [
						%s
						%s
						%s
						%s
            %s
            %s
            %s
						{
								"type": "Action.Submit",
								"title": "Not Interested",
								"data": "civilian"
						}
				]
			},
		]==]

		local bcso_text = ''
		local sahp_text = ''
		local lspd_text = ''
		local lsfd_text = ''
		local doc_text = ''
    local doj_text = ''
    local lawyer_text = ''

		if bcso then
			bcso_text = [==[
			{
					"type": "Action.Submit",
					"title": "BCSO",
					"data": "bcso"
			},
			]==]
		end

    if sahp then
			sahp_text = [==[
			{
					"type": "Action.Submit",
					"title": "SAHP",
					"data": "sahp"
			},
			]==]
		end

		if lspd then
			lspd_text = [==[
			{
					"type": "Action.Submit",
					"title": "LSPD",
					"data": "lspd"
			},
			]==]
		end

		if lsfd then
			lsfd_text = [==[
			{
					"type": "Action.Submit",
					"title": "LSFD",
					"data": "lsfd"
			},
			]==]
		end

		if doc then
			doc_text = [==[
			{
					"type": "Action.Submit",
					"title": "DOC",
					"data": "doc"
			},
			]==]
		end

    if doj then
			doj_text = [==[
			{
					"type": "Action.Submit",
					"title": "DOJ",
					"data": "doj"
			},
			]==]
    end

    if lawyer then
			lawyer_text = [==[
			{
					"type": "Action.Submit",
					"title": "Lawyer",
					"data": "lawyer"
			},
			]==]
    end

		faction_text = string.format(faction_text, bcso_text, sahp_text, lspd_text, lsfd_text, doc_text, doj_text, lawyer_text)
	end

	local info_card = [==[
{
    "type": "AdaptiveCard",
    "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
    "version": "1.5",
    "body": [
        {
            "type": "Image",
            "url": "https://badlandsrp.com/uploads/monthly_2021_12/logo.png.ad3a1c768aa6f859567b0f0270d2a324.png",
            "isVisible": true,
            "horizontalAlignment": "Center"
        },
        {
            "type": "TextBlock",
            "text": "%s",
            "horizontalAlignment": "Center",
            "size": "medium",
            "fontType": "Default",
            "isSubtle": true,
            "isVisible": true
        },
        {
            "type": "TextBlock",
            "text": "%s",
            "horizontalAlignment": "Center",
            "size": "medium",
            "fontType": "Default",
            "isSubtle": true,
            "isVisible": true
        },
        %s
        {
            "type": "Container",
            "separator": true,
            "horizontalAlignment": "Center",
            "spacing": "Large"
        },
				{
					"type": "ActionSet",
					"horizontalAlignment": "Center",
					"actions": [
              {
                  "type": "Action.OpenUrl",
                  "title": "Store",
                  "url": "https://store.badlandsrp.com/",
                  "id": "W"
              },
			        {
			            "type": "Action.OpenUrl",
			            "title": "Forums",
			            "id": "F",
			            "url": "https://badlandsrp.com/"
			        },
			        {
			            "type": "Action.OpenUrl",
			            "title": "Discord",
			            "url": "https://discord.gg/badlandsrp",
			            "id": "D"
			        },
			        {
			            "type": "Action.OpenUrl",
			            "title": "Rules",
			            "url": "https://badlandsrp.com/topic/7311-badlandsrp-server-rules/",
			            "id": "C",
			            "style": "positive"
			        }
			    ]
				},
				%s
				{
						"type": "TextBlock",
						"text": "%s",
						"horizontalAlignment": "Center",
						"size": "Large",
						"fontType": "Default",
						"isSubtle": true,
						"isVisible": true,
						"color": "Default",
						"spacing": "Large"
				}
    ]
}	]==]

	--[[
		Bindings

		1 - Connection status
		2 - Dots
		3 - Faction block
		4 - Information message
	]]

	return string.format(info_card, status, dots, reconnect_text, faction_text, information_message)
end


function createBanCard(ban_reason, banning_admin, player_id)
  -- Default values for optional parameters
  if not ban_reason then
    ban_reason = "Must Appeal"
  end

  if not banning_admin then
    banning_admin = "Badlands RP"
  end

  if not player_id then
    player_id = "N/A"
  end

  -- Check for ineligibility
  local is_ineligible = string.match(ban_reason:lower(), "ineligible")

  local title_text = is_ineligible and "You are currently ineligible to join" or "You have been banned"

  ban_reason = is_ineligible and ban_reason or "Reason: " .. ban_reason
  banning_admin = is_ineligible and "" or "Banned by ".. banning_admin

  local appeal_instructions = is_ineligible and
    "You may apply for eligibility at BadlandsRP.com/support or click the button below. \\nYou must create an account, submit a New Request and select 'Player Eligibility Applications' under 'Department'." or
    "You may appeal your ban at BadlandsRP.com/support or click the button below. \\nYou must create an account, submit a New Request and select 'Ban Appeals' under 'Department'."

  local ban_card = [==[
{
    "type": "AdaptiveCard",
    "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
    "version": "1.5",
    "body": [
        {
            "type": "Image",
            "url": "https://badlandsrp.com/uploads/monthly_2021_12/logo.png.ad3a1c768aa6f859567b0f0270d2a324.png",
            "isVisible": true,
            "horizontalAlignment": "Center"
        },
        {
            "type": "TextBlock",
            "text": "%s",
            "horizontalAlignment": "Center",
            "size": "Large",
            "weight": "Bolder",
            "color": "Warning",
            "spacing": "Large"
        },
        {
            "type": "TextBlock",
            "text": "Player ID: %s",
            "horizontalAlignment": "Center",
            "wrap": true,
            "spacing": "Medium"
        },
        {
            "type": "TextBlock",
            "text": "%s",
            "horizontalAlignment": "Center",
            "wrap": true,
            "spacing": "Medium"
        },
        {
            "type": "TextBlock",
            "text": "%s",
            "horizontalAlignment": "Center",
            "wrap": true,
            "spacing": "Medium"
        },
        {
            "type": "TextBlock",
            "text": "%s",
            "horizontalAlignment": "Center",
            "wrap": true,
            "spacing": "Large",
            "isSubtle": true
        },
        {
            "type": "ActionSet",
            "horizontalAlignment": "Center",
            "actions": [
                {
                    "type": "Action.OpenUrl",
                    "title": "File an Appeal",
                    "url": "https://badlandsrp.com/support/"
                }
            ]
        }
    ]
}
]==]

  -- Bind the parameters to the card
  return string.format(ban_card, title_text, player_id, ban_reason, banning_admin, appeal_instructions)
end

----------------------------------------------------------------------------------------------------------------------

Config.Priority = {
  -- 12 = manual bump
  -- 11 = reconnect
  --  5 = new player priority
}

Config.MaxIDPriority = 52000

Config.RequireSteam = true
Config.PriorityOnly = false -- whitelist only server

Config.MaxTempPriority = 10 * 60

Config.IsBanned = function(src, callback)
	callback(false) -- not banned
	-- or callback(true, "reason") -- banned and the reason
end

-- easy localization
Config.Language = {
	joining = "Joining...",
	connecting = "Connecting...",
	err = "Error: Couldn't retrieve any of your id's, try restarting.",
	_err = "There was an error",
	pos = "You are %d/%d in queue",
	connectingerr = "Error adding you to connecting list",
	banned = "You are banned, you may appeal it at www.badlands.com | Reason: %s",
	steam = "Error: Steam must be running",
	prio = "You must be whitelisted to join this server. You may apply at www.whatever.net"
}
-----------------------------------------------------------------------------------------------------------------------

local Queue = {}
Queue.QueueList = {}
Queue.PlayerList = {}
Queue.PlayerCount = 0
Queue.Priority = {}
Queue.Connecting = {}
Queue.ThreadCount = 0

local debug = false
local displayQueue = false
local initHostName = false
local maxPlayers = GetConvarInt("sv_maxclients", 200)
local maxConnect = 45
local whitelistEnabled = true

local tostring = tostring
local tonumber = tonumber
local ipairs = ipairs
local pairs = pairs
local print = print
local string_sub = string.sub
local string_format = string.format
local string_lower = string.lower
local math_abs = math.abs
local math_floor = math.floor
local os_time = os.time
local table_insert = table.insert
local table_remove = table.remove

-----------------------------------------------------------------------------------------------------------------------

-- Dynamic priority
function loadPriority()
	print('Loading remote queue priority')

	local priority_users = MySQL.query.await('SELECT vrp_users.id, vrp_users.priority, vrp_user_ids.identifier FROM vrp_users JOIN vrp_user_ids ON vrp_user_ids.user_id = vrp_users.id AND vrp_user_ids.identifier LIKE "%steam%" WHERE vrp_users.priority > 0 ORDER BY vrp_users.priority DESC')

	for _, priority_user in pairs(priority_users) do
		Config.Priority[string.lower(priority_user.identifier)] = tonumber(priority_user.priority)
		Queue.Priority[string.lower(priority_user.identifier)] = tonumber(priority_user.priority)
	end

	print('Loaded remote queue priority')
end

exports('LoadPriority', loadPriority)

Citizen.CreateThread(function()
	loadPriority()

	-- EMS of the month
	local end_year = tonumber(os.date('%Y'))
	local end_month = tonumber(os.date('%m'))

	local start_year = end_year
	local start_month = (end_month - 1)

	if end_month == 1 then
		start_year = end_year - 1
		start_month = 12
	end

	if end_month < 10 then
		end_month = '0' .. end_month
	end

	if start_month < 10 then
		start_month = '0' .. start_month
	end

	print('EMS of the month start', start_year .. '-' .. start_month .. '-01')
	print('EMS of the month end  ', end_year .. '-' .. end_month .. '-01')

	local lsfd_priority_users = MySQL.query.await([[
	SELECT u.id, u.priority, c.firstname, c.lastname, c.rank_lsfd, GROUP_CONCAT(DISTINCT i.identifier) AS `identifiers`, SUM(ems_time_played) AS `total_ems_time` FROM bl_time_played b
		JOIN vrp_users u ON u.id = b.user_id
		JOIN characters c ON c.identifier = b.user_id AND c.rank_lsfd > -1
		JOIN vrp_user_ids i ON i.user_id = b.user_id AND i.identifier LIKE '%steam%'
		WHERE date >= ? AND date < ? AND ems_time_played > 0 AND (u.priority IS NULL or u.priority <= 6)
		GROUP BY u.id, u.priority, c.firstname, c.lastname, c.rank_lsfd
		ORDER BY total_ems_time DESC LIMIT 3
	]], {
		start_year .. '-' .. start_month .. '-01', -- 2022-09-01
		end_year .. '-' .. end_month .. '-01', -- 2022-10-01
	})

	local discord_log = 'Assigning EMS of the month queue priorty to the following users: '

	for _, lsfd_priority_user in pairs(lsfd_priority_users) do
		-- Assign priority to each STEAM ID
		for steam_id in string.gmatch(lsfd_priority_user.identifiers, "([^,]+)") do
			Config.Priority[string.lower(steam_id)] = 6
			Queue.Priority[string.lower(steam_id)] = 6
		end

		discord_log = discord_log .. '\r\n' .. lsfd_priority_user.firstname .. ' ' .. lsfd_priority_user.lastname .. ' (' .. lsfd_priority_user.id ..') (Identifiers: ' .. lsfd_priority_user.identifiers .. ')'
	end

	print(discord_log)

	if not GlobalState.is_dev then
		exports.blrp_core:LogDiscord('events', discord_log, 'QUEUE')
	end
end)

-----------------------------------------------------------------------------------------------------------------------

-- Exports

exports('GetQueuePlayers', function()
	return Queue.QueueList
end)

-----------------------------------------------------------------------------------------------------------------------

local new_player_prio_active = true

-- Dynamic faction queue variables

local faction_priority_enabled = true

local faction_priority_blacklisted = {}

AddEventHandler('pQueue:server:factionPriorityBlacklist', function(vrp_id)
	faction_priority_blacklisted[vrp_id] = true
end)

local last_priority_times = {
	['leo'] = 0,
	['lsfd'] = 0,
	['doc'] = 0,
  ['doj'] = 0,
  ['lawyer'] = 0,
}

local minimum_leo = 35
local minimum_lsfd = 15
local minimum_doc = 10
local minimum_doj = 4
local minimum_lawyer = 4

local current_leo = 0
local current_lsfd = 0
local current_doc = 0
local current_doj = 0
local current_lawyer = 0

queued_leo = 0
queued_lsfd = 0
queued_doc = 0
queued_doj = 0
queued_lawyer = 0

needs_bcso = false
needs_sahp = false
needs_lspd = false
needs_lsfd = false
needs_doc = false
needs_doj = false
needs_lawyer = false

Citizen.CreateThread(function()
	while true do
		if Queue.PlayerCount >= (maxPlayers - 2) then
			local data = exports.blrp_core:GetpQueueFactionData()

			if data and data.leo then
				current_leo = data.leo
			end

			if data and data.lsfd then
				current_lsfd = data.lsfd
			end

			if data and data.doc then
				current_doc = data.doc
			end

      if data and data.doj then
        current_doj = data.doj
      end

      if data and data.lawyer then
        current_lawyer = data.lawyer
      end
		end

		Citizen.Wait(15 * 1000)
	end
end)

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(250)

		calculateFactionQueueData()
	end
end)

function calculateFactionQueueData()
	local _queued_leo = 0
	local _queued_lsfd = 0
	local _queued_doc = 0
	local _queued_doj = 0
	local _queued_lawyer = 0

	for i = 1, Queue:GetSize() do
		local data = Queue.QueueList[i]

		if data.reprioritized_faction ~= nil then
			local _pos = false

			local target_priority = Queue:IsPriority(data.ids) or 1

			for k, v in ipairs(Queue.QueueList) do
				if target_priority then
					if not v.priority then
						_pos = k
					else
						if target_priority > v.priority then
							_pos = k
						end
					end

					if _pos then
						break
					end
				end
			end

			if _pos then
				data.priority = target_priority

				local log_message = string_format("%s[%s] accepted faction priority offer for %s and was re-prioritized to %d/%d in queue", data.name, data.ids[1], string.upper(data.reprioritized_faction), _pos, Queue:GetSize())

				TriggerEvent('vrp:logByUserID', data.vrp_id, log_message, 'PRIORITY-QUEUE')
				Queue:DebugPrint(log_message)
				Queue:SetPos(data.ids, _pos)
			else
				print('Failed to re-prioritize user', data.name, data.ids[1])
			end

			data.reprioritized_faction = nil
		end

		if data.queued_faction then
			if data.queued_faction == 'bcso' or data.queued_faction == 'lspd' or data.queued_faction == 'sahp' then
				_queued_leo = _queued_leo + 1
			end

			if data.queued_faction == 'lsfd' then
				_queued_lsfd = _queued_lsfd + 1
			end

			if data.queued_faction == 'doc' then
				_queued_doc = _queued_doc + 1
			end

			if data.queued_faction == 'doj' then
				_queued_doj = _queued_doj + 1
			end

			if data.queued_faction == 'lawyer' then
				_queued_lawyer = _queued_lawyer + 1
			end
		end
	end

	queued_leo = _queued_leo
	queued_lsfd = _queued_lsfd
	queued_doc = _queued_doc
	queued_doj = _queued_doj
	queued_lawyer = _queued_lawyer

	needs_bcso = (queued_leo + current_leo < minimum_leo) and (os.time() - last_priority_times['leo']) > (60 * 2)
  needs_sahp = needs_bcso
	needs_lspd = needs_bcso
	needs_lsfd = (queued_lsfd + current_lsfd < minimum_lsfd) and (os.time() - last_priority_times['lsfd']) > (60 * 2)
	needs_doc = (queued_doc + current_doc < minimum_doc) and (os.time() - last_priority_times['doc']) > (60 * 2)
  needs_doj = (queued_doj + current_doj < minimum_doj) and (os.time() - last_priority_times['doj']) > (60 * 2)
  needs_lawyer = (queued_lawyer + current_lawyer < minimum_lawyer) and (os.time() - last_priority_times['lawyer']) > (60 * 2)
end

-------------------------------------------------------------------------------------------------------------------------

-- converts hex steamid to SteamID 32
function Queue:HexIdToSteamId(hexId)
	local cid = math_floor(tonumber(string_sub(hexId, 7), 16))
	local steam64 = math_floor(tonumber(string_sub( cid, 2)))
	local a = steam64 % 2 == 0 and 0 or 1
	local b = math_floor(math_abs(6561197960265728 - steam64 - a) / 2)
	local sid = "steam_0:"..a..":"..(a == 1 and b -1 or b)
	return sid
end

function Queue:IsSteamRunning(src)
	for k,v in ipairs(GetPlayerIdentifiers(src)) do
		if string.sub(v, 1, 5) == "steam" then
			return true
		end
	end

	return false
end

function Queue:IsDiscordRunning(src)
	for k,v in ipairs(GetPlayerIdentifiers(src)) do
		if string.sub(v, 1, 7) == "discord" then
			return true
		end
	end

	return false
end

function Queue:DebugPrint(msg)
	if debug then
		msg = "QUEUE: " .. tostring(msg)
		print(msg)
	end
end

function Queue:IsInQueue(ids, rtnTbl, bySource, connecting)
	for k,v in ipairs(connecting and self.Connecting or self.QueueList) do
		local inQueue = false

		if not bySource then
			for i,j in ipairs(v.ids) do
				if inQueue then break end

				for q,e in ipairs(ids) do
					if e == j then inQueue = true break end
				end
			end
		else
			inQueue = ids == v.source
		end

		if inQueue then
			if rtnTbl then
				return k, connecting and self.Connecting[k] or self.QueueList[k]
			end

			return true
		end
	end

	return false
end

function Queue:IsPriority(ids, vrpId)
	if vrpId == nil then vrpId = 9999999 end
	local serverLabel = GetConvar('blrp_watermark','badlandsrp.com')
	for k,v in ipairs(ids) do
		v = string_lower(v)

		if string_sub(v, 1, 5) == "steam" and not self.Priority[v] then
			local steamid = self:HexIdToSteamId(v)
			if self.Priority[steamid] then return self.Priority[steamid] ~= nil and self.Priority[steamid] or false end
		end

		if self.Priority[v] then return self.Priority[v] ~= nil and self.Priority[v] or false end
	end

	return false
end

function Queue:AddToQueue(ids, connectTime, name, src, deferrals, vrpId, totalPlaytime)
	if self:IsInQueue(ids) then return end
	if vrpId == nil then vrpId = 999999 end
	local tmp = {
		source = src,
		ids = ids,
		name = name,
		firstconnect = connectTime,
		priority = self:IsPriority(ids, vrpId) or (src == "debug" and math.random(0, 0)),
		timeout = 0,
		deferrals = deferrals,
		vrp_id = vrpId,
		totalPlaytime = totalPlaytime,
	}
	local _pos = false
	local queueCount = self:GetSize() + 1

	for k,v in ipairs(self.QueueList) do
		if tmp.priority then
			if not v.priority then
				_pos = k
			else
				if tmp.priority > v.priority then
					_pos = k
				end
			end

			if _pos then
				self:DebugPrint(string_format("%s[%s] was prioritized and placed %d/%d in queue", tmp.name, ids[1], _pos, queueCount))
        exports.blrp_core:LogSplunkVrp(vrpId, 'PRIORITY-QUEUE', string_format("Was prioritized and placed %d/%d in queue", _pos, queueCount))
				break
			end
		end
	end

	if not _pos then
		_pos = self:GetSize() + 1
		self:DebugPrint(string_format("%s[%s] was placed %d/%d in queue", tmp.name, ids[1], _pos, queueCount))
		if queueCount and tonumber(queueCount) > 1 then -- only log if there is a queue
		  exports.blrp_core:LogSplunkVrp(vrpId, 'PRIORITY-QUEUE', string_format("Was placed %d/%d in queue", _pos, queueCount))
		end
	end

	table_insert(self.QueueList, _pos, tmp)
end

function Queue:RemoveFromQueue(ids, bySource)
	if self:IsInQueue(ids, false, bySource) then
		local pos, data = self:IsInQueue(ids, true, bySource)
		table_remove(self.QueueList, pos)
	end
end

exports('GetQueueSize', function()
	return #Queue.QueueList
end)

Citizen.CreateThread(function()
	while true do
		GlobalState.queue_size = #Queue.QueueList

		Citizen.Wait(60 * 1000)
	end
end)

function Queue:GetSize()
	return #self.QueueList
end

function Queue:ConnectingSize()
	return #self.Connecting
end

function Queue:IsInConnecting(ids, bySource, refresh)
	local inConnecting, tbl = self:IsInQueue(ids, refresh and true or false, bySource and true or false, true)

	if not inConnecting then return false end

	if refresh and inConnecting and tbl then
		self.Connecting[inConnecting].timeout = 0
	end

	return true
end

function Queue:RemoveFromConnecting(ids, bySource)
	for k,v in ipairs(self.Connecting) do
		local inConnecting = false

		if not bySource then
			for i,j in ipairs(v.ids) do
				if inConnecting then break end

				for q,e in ipairs(ids) do
					if e == j then inConnecting = true break end
				end
			end
		else
			inConnecting = ids == v.source
		end

		if inConnecting then
			table_remove(self.Connecting, k)
			return true
		end
	end

	return false
end

function Queue:AddToConnecting(ids, ignorePos, autoRemove, done)
	local function removeFromQueue()
		if not autoRemove then return end

		done(Config.Language.connectingerr)
		self:RemoveFromConnecting(ids)
		self:RemoveFromQueue(ids)
		self:DebugPrint("Player could not be added to the connecting list")
	end

	if self:ConnectingSize() >= maxConnect then removeFromQueue() return false end
	if ids[1] == "debug" then
		table_insert(self.Connecting, {source = ids[1], ids = ids, name = ids[1], firstconnect = ids[1], priority = ids[1], timeout = 0})
		return true
	end

	if self:IsInConnecting(ids) then self:RemoveFromConnecting(ids) end

	local pos, data = self:IsInQueue(ids, true)
	if not ignorePos and (not pos or pos > 1) then removeFromQueue() return false end

	table_insert(self.Connecting, data)
	self:RemoveFromQueue(ids)
	return true
end

function Queue:GetIds(src)
	local ids = GetPlayerIdentifiers(src)
	ids = (ids and ids[1]) and ids or {"ip:" .. GetPlayerEndpoint(src)}
	ids = ids ~= nil and ids or false

	if ids and #ids > 1 then
		for k,v in ipairs(ids) do
			if string.sub(v, 1, 3) == "ip:" then table_remove(ids, k) end
		end
	end

	return ids
end

local tempPriorityList = {}

function Queue:AddPriority(id, power)
	if not id then return false end
	id = string.lower(id)
	if Config.Priority[id] and Config.Priority[id] > power then
	  return false
  end -- Dont add player with hardcoded priority
	tempPriorityList[id] = os.time()

	if type(id) == "table" then
		for k, v in pairs(id) do
			if k and type(k) == "string" and v and type(v) == "number" then
				self.Priority[k] = v
			else
				self:DebugPrint("Error adding a priority id, invalid data passed")
				return false
			end
		end

		return true
	end

	power = (power and type(power) == "number") and power or 10
	self.Priority[string_lower(id)] = power

	return true
end

exports('AddReconnectPriority', function(vrp_id)
	local identifiers = MySQL.query.await('SELECT identifier FROM vrp_user_ids WHERE identifier LIKE "%steam%" AND user_id = ?', {
		vrp_id
	})

	for _, identifier in pairs(identifiers) do
		Queue:AddPriority(identifier.identifier, 11)
	end
end)

function Queue:RemovePriority(id)
	if not id then return false end
	if Config.Priority[id] then return false end -- Dont remove player with hardcoded priority
	tempPriorityList[id] = nil
	self.Priority[id] = nil
	return true
end

local function cleanTempPriority()
	for k,v in pairs(tempPriorityList) do
		if (os.time() - v) > Config.MaxTempPriority then
			Queue:RemovePriority(k)
		end
	end
	SetTimeout(120000, cleanTempPriority)
end
cleanTempPriority()

function Queue:UpdatePosData(src, ids, deferrals)
	local pos, data = self:IsInQueue(ids, true)
	self.QueueList[pos].source = src
	self.QueueList[pos].ids = ids
	self.QueueList[pos].timeout = 0
	self.QueueList[pos].deferrals = deferrals
end

function Queue:NotFull(firstJoin)
	local canJoin = self.PlayerCount + self:ConnectingSize() < maxPlayers and self:ConnectingSize() < maxConnect
	canJoin = firstJoin and (self:GetSize() <= 1 and canJoin) or canJoin
	return canJoin
end

function Queue:SetPos(ids, newPos, log)
	local pos, data = self:IsInQueue(ids, true)

	table_remove(self.QueueList, pos)
	table_insert(self.QueueList, newPos, data)

	if log or log == nil then
		Queue:DebugPrint("Set " .. data.name .. "[" .. data.ids[1] .. "] pos to " .. newPos)
	end
end

-- export
function AddPriority(id, power)
	return Queue:AddPriority(id, power)
end

-- export
function RemovePriority(id)
	return Queue:RemovePriority(id)
end

function PerformHttpRequestPromise(url, request_headers)
	local p = promise:new()

  if not request_headers then
    request_headers = {}
  end

  request_headers['Content-Type'] = 'application/json'

	PerformHttpRequest(url, function(status_code, response, headers)
		if not response then
			p:resolve({{}, status_code})
			return
		end

		local data = json.decode(response)

		if not data then
			p:resolve({{}, status_code})
			return
		end

		p:resolve({data, status_code})
	end, 'GET', json.encode({}), request_headers)

	return table.unpack(Citizen.Await(p))
end

local dev_allow_guests = true
local staff_only = false
local shutting_down = false

exports('SetShuttingDown', function(_shutting_down)
  shutting_down = _shutting_down
end)

local new_player_counter = 0
local new_player_lastpriority = 0

Citizen.CreateThread(function()
	AddEventHandler('playerConnecting', function(name, setCallback, deferrals)
		maxPlayers = GetConvarInt("sv_maxclients", 200)
		debug = GetConvar("sv_debugqueue", "true") == "true" and true or false
		displayQueue = GetConvar("sv_displayqueue", "true") == "true" and true or false
		initHostName = not initHostName and GetConvar("sv_hostname") or initHostName

		local src = source
		local ids = Queue:GetIds(src)
		local connectTime = os_time()
		local connecting = true

		deferrals.defer()

		Citizen.CreateThread(function()
			while connecting do
				Citizen.Wait(500)
				if not connecting then return end
				deferrals.presentCard(createInfoCard('Connecting ...'))
			end
		end)

		local function done(msg)
			local dots = nil

			if type(msg) == 'table' then
				dots = msg[2]
				msg = msg[1]
			end

			connecting = false
			if not msg then
				Citizen.Wait(1000)
				deferrals.done()
			else
				deferrals.presentCard(createInfoCard((tostring(msg) and tostring(msg) or ""), dots))
				CancelEvent()
			end
		end

    local function bannedCard(ban_reason, banning_admin, player_id)
      connecting = false

      deferrals.presentCard(createBanCard(ban_reason, banning_admin, player_id))
      CancelEvent()
    end

		local function update(msg, dots)
			connecting = false
			deferrals.presentCard(createInfoCard((tostring(msg) and tostring(msg) or ""), dots))
		end

		Citizen.Wait(1000)

		if not ids then
			done(Config.Language.err)
			Queue:RemoveFromQueue(ids)
			Queue:RemoveFromConnecting(ids)
			CancelEvent()
			return
		end

		if not Queue:IsSteamRunning(src) then
			done('Steam must be running to connect to this server')
			Queue:RemoveFromQueue(ids)
			Queue:RemoveFromConnecting(ids)
			CancelEvent()
			return
		end

		local whitelisting_data = nil

		update("Checking account details .. ")

		-- Get/Create vRP id for player.
		local user_id, user_identifiers = exports.vrp:getUserIdByIdentifiers(src, GetPlayerIdentifiers(src), true)

		-- Could not find/create a vRP ID
		if not user_id then
			deferrals.done('Unable to obtain Steam session')
			Queue:RemoveFromQueue(ids)
			Queue:RemoveFromConnecting(ids)
			CancelEvent()
			return
		end

    local user_ip = (GetPlayerEndpoint(src) or '0.0.0.0')

    MySQL.update([[
      UPDATE vrp_users
        SET
          last_login = ?,
          ip_address = ?
        WHERE
          id = ?
    ]], {
      (GetPlayerEndpoint(src) or '0.0.0.0') .. ' ' .. os.date('%H:%M:%S %d/%m/%Y'),
      user_ip,
      user_id
    })

		if not Queue:IsDiscordRunning(src) then
      TriggerEvent('vrp:logByUserID', user_id, '[vRP] Rejecting ' .. user_id .. ' due to no Discord ID / ip = ' .. (GetPlayerEndpoint(src) or '0.0.0.0'), 'CONNECTION')
			done('Discord must be running to connect to this server')
			Queue:RemoveFromQueue(ids)
			Queue:RemoveFromConnecting(ids)
			CancelEvent()
			return
		end

		local queue_data = MySQL.single.await('SELECT banned, banned_by_admin_id, staff, ban_reason, steam_check_bypass FROM vrp_users WHERE id = ?', {
			user_id
		})

		local faction_data = MySQL.single.await([[
      SELECT
          MAX(rank_lspd) as rank_lspd, MAX(rank_bcso) as rank_bcso, MAX(rank_sasp) as rank_sasp, MAX(rank_lsfd) as rank_lsfd,
          MAX(rank_doc) as rank_doc, MAX(rank_doj) as rank_doj, MAX(rank_lawyer) as rank_lawyer
        FROM characters WHERE identifier = ?
    ]], {
			user_id
		})

    local business_count = MySQL.scalar.await([[
      SELECT COUNT(*) AS business_count FROM core_character_business
        JOIN businesses ON core_character_business.business_id = businesses.id
        WHERE core_character_business.user_id = ?
    ]], {
      user_id
    })

		if GlobalState.is_dev and not dev_allow_guests and not queue_data.staff then
			print('Rejecting ' .. user_id .. ' due to no guests allowed on dev')
			done('Connection Rejected')
			Queue:RemoveFromQueue(ids)
			Queue:RemoveFromConnecting(ids)
			CancelEvent()
			return
		end

		if staff_only and not queue_data.staff then
			print('Rejecting ' .. user_id .. ' due to staff only mode')
			done('Server is in maintenance mode. Try again later')
			Queue:RemoveFromQueue(ids)
			Queue:RemoveFromConnecting(ids)
			CancelEvent()
			return
		end

    if shutting_down then
      print('Rejecting ' .. user_id .. ' due to server shutdown')
      done('Server shutting down for scheduled restart')
			Queue:RemoveFromQueue(ids)
			Queue:RemoveFromConnecting(ids)
			CancelEvent()
			return
    end

		whitelisting_data = {
			bcso = ((faction_data.rank_bcso or -1) > -1),
			sahp = ((faction_data.rank_sasp or -1) > -1),
			lspd = ((faction_data.rank_lspd or -1) > -1),
			lsfd = ((faction_data.rank_lsfd or -1) > -1),
			doc = ((faction_data.rank_doc or -1) > -1),
      doj = ((faction_data.rank_doj or -1) > -1),
      lawyer = ((faction_data.rank_lawyer or -1) > -1),
		}

		whitelisting_data.any = (
      whitelisting_data.bcso or
      whitelisting_data.sahp or
      whitelisting_data.lspd or
      whitelisting_data.lsfd or
      whitelisting_data.doc or
      whitelisting_data.doj or
      whitelisting_data.lawyer
    )

    if queue_data.banned then
      local ban_reason = queue_data.ban_reason
      local banning_admin = {}

      -- Detect legacy ban messages
      local legacy_match = string.match(ban_reason, '^(%d+)%s+(.+)%s%((.+)%)$')
      if legacy_match then
        local legacy_player_id, legacy_reason, legacy_admin_name = ban_reason:match('^(%d+)%s+(.+)%s%((.+)%)$')

        ban_reason = legacy_reason

        -- Check if admin name is in the database
        if queue_data.banned_by_admin_id ~= 0 then
          banning_admin = MySQL.single.await('SELECT id, staffName FROM vrp_users WHERE id = ?', {
            queue_data.banned_by_admin_id
          })

          if not banning_admin.staffName or #banning_admin.staffName == 0 then
            banning_admin.staffName = legacy_admin_name
          end
        else
          -- Use extracted admin name if not found in the database
          banning_admin.staffName = legacy_admin_name or 'Badlands RP'
        end
      else
        if queue_data.banned_by_admin_id ~= 0 then
          banning_admin = MySQL.single.await('SELECT id, staffName FROM vrp_users WHERE id = ?', {
            queue_data.banned_by_admin_id
          })
        else
          banning_admin.staffName = 'Badlands RP'
        end
      end

      -- Log the ban and display the banned card
      TriggerEvent('vrp:logByUserID', user_id, '[vRP] Rejecting ' .. user_id .. ' due to active ban / ban_reason = ' .. ban_reason or 'unknown' .. ' / ip = ' .. (GetPlayerEndpoint(src) or '0.0.0.0'), 'CONNECTION')
      bannedCard(ban_reason, banning_admin.staffName, user_id)
      Queue:RemoveFromQueue(ids)
      Queue:RemoveFromConnecting(ids)
      CancelEvent()
      return
    end


		local timed_kick = timed_kicks[tonumber(user_id)]

		if timed_kick then
			local difference = (timed_kick.end_time - os.time())

			if difference > 0 then
				local minutes = math.ceil(difference / 60)

				TriggerEvent('vrp:logByUserID', user_id, '[vRP] Rejecting ' .. user_id .. ' due to kick cooldown / minutes_left = ' .. minutes .. ' / ip = ' .. (GetPlayerEndpoint(src) or '0.0.0.0'), 'CONNECTION')
				done({ '[Kicked] Reason: ' .. timed_kick.reason, 'You may reconnect in ' .. minutes .. ' minutes' })
				Queue:RemoveFromQueue(ids)
				Queue:RemoveFromConnecting(ids)
				CancelEvent()
				return
			else
				timed_kicks[tonumber(user_id)] = nil
			end
		end

		if
      (GetConvar('sv_enableWhitelist', 'off')) == 'on' and
      not whitelisting_data.any and
      business_count <= 0
    then
			done('Not Whitelisted [Your ID: ' .. user_id .. ']')
			Queue:RemoveFromQueue(ids)
			Queue:RemoveFromConnecting(ids)
			CancelEvent()
			return
		end

		if not queue_data.steam_check_bypass and not queue_data.staff then
			local rejection_reason = 'You are ineligible to join. Appeal at badlandsrp.com.'
			local steamid64 = string.gsub(ids[1], 'steam:', '')
			steamid64 = tonumber(steamid64, 16)

			if not steamid64 then
				deferrals.done('Unable to obtain Steam session')
				Queue:RemoveFromQueue(ids)
				Queue:RemoveFromConnecting(ids)
				CancelEvent()
				return
			end

      local function rejectForEligibility(log_message)
        TriggerEvent('vrp:logByUserID', user_id, log_message, 'ELIGIBILITY')

        MySQL.update.await('UPDATE vrp_users SET banned = true, ban_reason = ?, banned_by_admin_id = 0 WHERE id = ?', {
          rejection_reason, user_id
        })

        MySQL.insert('INSERT INTO vrp_user_notes (user_id, added_by, note) VALUES (?, 0, ?)', {
          user_id, log_message
        })

        done(rejection_reason)
				Queue:RemoveFromQueue(ids)
				Queue:RemoveFromConnecting(ids)
				CancelEvent()
      end

      local function getDiscordAccountAge(discord_id)
        local discord_epoch = ************* -- Jan 1, 2015
        local timestamp = (discord_id >> 22) + discord_epoch
        return math.floor(timestamp / 1000)
      end



			--------------------------------------------------------------------------
			------------------------- IP BAN EVASION CHECK ---------------------------
			--------------------------------------------------------------------------

      local banned_users = MySQL.query.await('SELECT id, banned FROM vrp_users WHERE ip_address = ? AND id != ? AND banned = true ORDER BY id ASC', {
        user_ip, user_id
      })

      if #banned_users > 0 then
        local banned_ids = ''

        for _, v in pairs(banned_users) do
          if banned_ids ~= '' then
            banned_ids = banned_ids .. ', '
          end

          banned_ids = banned_ids .. v.id
        end

        rejectForEligibility('Flagged for eligibility due to IP match with existing banned account(s) (steam64 = ' .. steamid64 .. ', user_id = ' .. user_id .. ', banned_ids = ' .. banned_ids .. ')')
				return
      end

			--------------------------------------------------------------------------
			--------------------------- HARDWARE ID CHECK ----------------------------
			--------------------------------------------------------------------------

			local tokens = MySQL.Sync.fetchAll('SELECT * FROM vrp_user_tokens WHERE user_id = @user_id', {
				user_id = user_id
			})

			local token_string = ''

			for _, token_data in pairs(tokens) do
				if token_string ~= '' then
					token_string = token_string .. ','
				end

				token_string = token_string .. '"' .. token_data.token .. '"'
			end

			local user_matches = MySQL.Sync.fetchAll('SELECT user_id FROM vrp_user_tokens WHERE user_id != @user_id AND token IN (' .. token_string .. ')', {
				user_id = user_id
			})

			if #user_matches > 0 then
        local hwid_ids = {}

        for _, v in pairs(user_matches) do
          local found = false

          for __, vv in pairs(hwid_ids) do
            if vv == v.user_id then
              found = true
            end
          end

          if not found then
            table.insert(hwid_ids, v.user_id)
          end
        end

        rejectForEligibility('Flagged for eligibility due to HWID match with existing account(s) (steam64 = ' .. steamid64 .. ', user_id = ' .. user_id .. ', matched_ids = ' .. table.concat(hwid_ids, ', ') .. ')')
				return
			end

			--------------------------------------------------------------------------
			------------------------ VAC BANNED ACCOUNT CHECK ------------------------
			--------------------------------------------------------------------------

			local ban_data = PerformHttpRequestPromise('https://api.steampowered.com/ISteamUser/GetPlayerBans/v1/?key=' .. steamkey .. '&steamids=' .. steamid64)

			if ban_data and ban_data.players and ban_data.players[1] and ban_data.players[1].VACBanned then
        rejectForEligibility('Flagged for eligibility due to previous VAC ban (steam64 = ' .. steamid64 .. ', user_id = ' .. user_id .. ')')
				return
			end

			--------------------------------------------------------------------------
			------------------------- STEAM ACCOUNT AGE CHECK ------------------------
			--------------------------------------------------------------------------

			local age_data = PerformHttpRequestPromise('https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/?key=' .. steamkey .. '&steamids=' .. steamid64)

			if age_data and age_data.response and age_data.response.players and age_data.response.players[1] and age_data.response.players[1].timecreated then
				local timecreated = tonumber(age_data.response.players[1].timecreated)

				if not timecreated then
					timecreated = os.time() - minimumAge - 1
				end

				if (os.time() - timecreated) < minimumAge then
          rejectForEligibility('Flagged for eligibility due to account age (steam64 = ' .. steamid64 .. ', user_id = ' .. user_id .. ')')
					return
				end
			end

      -------------------------------------------------------------------------
      ------------------------- DISCORD PRESENCE CHECK ------------------------
      -------------------------------------------------------------------------
      -- Extract Discord ID(s) from identifiers
      local discord_ids = ''
      local discord_id_list = {}

      for _, v in pairs(user_identifiers) do
        if string.sub(v.identifier, 1, 7) == 'discord' then
          local id = string.sub(v.identifier, 9)
          table.insert(discord_id_list, id)
          if discord_ids ~= '' then discord_ids = discord_ids .. ',' end
          discord_ids = discord_ids .. id
        end
      end

      if GetConvar('discord_age_check', 'true') == 'true' then
        -- If any Discord IDs were found, check account age
        for _, discord_id in pairs(discord_id_list) do
          local accountCreated = getDiscordAccountAge(tonumber(discord_id))
          local ageInDays = math.floor((os.time() - accountCreated) / 86400)

          print(('Discord ID %s created on %s (%d days old)'):format(
            discord_id,
            os.date('%Y-%m-%d %H:%M:%S', accountCreated),
            ageInDays
          ))

          if os.time() - accountCreated < minimumDiscordAge then
            rejectForEligibility('Flagged for eligibility due to Discord account age (discord_id = ' .. discord_id .. ', user_id = ' .. user_id .. ')')
            return
          end
        end
      end

      if GetConvar('discord_presence_check', 'true') == 'true' then

        local discord_data, status_code = PerformHttpRequestPromise('http://' .. GetConvar('discord_bot_address', 'localhost') .. ':8080/id/' .. discord_ids, {
          ['X-Authorization'] = 'TOKEN ' .. GetConvar('discord_bot_key', '')
        })

        if status_code ~= 0 and status_code ~= 401 then
          local in_discord = false

          for discord_id, discord_user in pairs(discord_data or {}) do
            if discord_user then
              in_discord = true
              break
            end
          end

          if not in_discord then
            done('You must be in the BadlandsRP Discord to connect\\rJoin at https://discord.gg/BadlandsRP')
            Queue:RemoveFromQueue(ids)
            Queue:RemoveFromConnecting(ids)
            CancelEvent()
            return
          end
        elseif status_code == 0 then
          print('Discord bot request timed out')
        end
      end
		end

		if Config.PriorityOnly and not Queue:IsPriority(ids) then done(Config.Language.prio) return end

		local rejoined = false

		local totalPlaytime = MySQL.scalar.await('SELECT SUM(civ_time_played + ems_time_played + cop_time_played + sheriff_time_played + doc_time_played + doj_time_played + lawyer_time_played) AS `time_played_total` FROM bl_time_played WHERE user_id = ?', {
			user_id
		})

		if Queue:IsInQueue(ids) then
			rejoined = true
			Queue:UpdatePosData(src, ids, deferrals)
			Queue:DebugPrint(string_format("%s[%s] has rejoined queue after cancelling", name, ids[1]))
			local pos, data = Queue:IsInQueue(ids, true)
			local queueCount = Queue:GetSize() + 1
			exports.blrp_core:LogSplunkVrp(user_id, 'PRIORITY-QUEUE', string_format("Rejoined queue after cancelling and placed %d/%d in queue", pos, queueCount))
		else
			Queue:AddToQueue(ids, connectTime, name, src, deferrals, user_id, totalPlaytime)
		end

		if Queue:IsInConnecting(ids, false, true) then
			Queue:RemoveFromConnecting(ids)

			if Queue:NotFull() then
				-- let them in the server
				local added = Queue:AddToConnecting(ids, true, true, done)
				if not added then CancelEvent() return end

				done()
				Queue:DebugPrint(name .. "[" .. ids[1] .. "] is loading into the server")
				exports.blrp_core:LogSplunkVrp(user_id, 'PRIORITY-QUEUE', 'Loading in to the server')
				TriggerEvent("vRP:playerConnecting",name,src)
				return
			else
				Queue:AddToQueue(ids, connectTime, name, src, deferrals, user_id, totalPlaytime)
				Queue:SetPos(ids, 1)
			end
		end

		local pos, data = Queue:IsInQueue(ids, true)

		if not pos or not data then
			done(Config.Language._err .. "[3]")

			RemoveFromQueue(ids)
			RemoveFromConnecting(ids)

			CancelEvent()
			return
		end

		if Queue:NotFull(true) then
			-- let them in the server
			local added = Queue:AddToConnecting(ids, true, true, done)
			if not added then CancelEvent() return end

			done()
			Queue:DebugPrint(name .. "[" .. ids[1] .. "] is loading into the server")
			TriggerEvent("vRP:playerConnecting",name,src)
			return
		end

		update(string_format(Config.Language.pos, pos, Queue:GetSize()), '◻️')

		Citizen.CreateThread(function()
			if rejoined then return end

			Queue.ThreadCount = Queue.ThreadCount + 1
			local dot_count = 1

			local pSource = src
			local pName = name
			local identifiers = ids
      local new_player_prioritized = false

			local progress_iters = 0

			local message_display = ''
			local message_display_iters = 0

			while true do
				Citizen.Wait(1000)

				progress_iters = progress_iters + 1

				local pos, data = Queue:IsInQueue(identifiers, true)

				-- will return false if not in queue; timed out?
				if not pos or not data then
					if data and data.deferrals then data.deferrals.done(Config.Language._err) end
					CancelEvent()
					Queue:RemoveFromQueue(identifiers)
					Queue:RemoveFromConnecting(identifiers)
					Queue.ThreadCount = Queue.ThreadCount - 1
					return
				end

				if pos <= 1 and Queue:NotFull() then
					-- let them in the server
					local added = Queue:AddToConnecting(identifiers)

					data.deferrals.update(Config.Language.joining)
					Citizen.Wait(500)

					if not added then
						data.deferrals.done(Config.Language.connectingerr)
						CancelEvent()
						Queue.ThreadCount = Queue.ThreadCount - 1
						return
					end

					if GetPlayerEndpoint(pSource) ~= nil then
						data.deferrals.done()

						Queue:RemoveFromQueue(identifiers)
						Queue.ThreadCount = Queue.ThreadCount - 1
						Queue:DebugPrint(pName .. "[" .. identifiers[1] .. "] is loading into the server***")
						TriggerEvent("vRP:playerConnecting",pName,pSource)
					else
						data.deferrals.update("Data expired. Reconnect to refresh.")
						CancelEvent()
						Queue:AddPriority(identifiers[1], 11)
						Queue:DebugPrint(pName .. "[" .. identifiers[1] .. "] failed to load in server, source nil")
						exports.blrp_core:LogSplunkVrp(user_id, 'PRIORITY-QUEUE', 'Failed to load in to the server, data expired.')
						Queue.ThreadCount = Queue.ThreadCount - 1
						return
					end
					return
				end

				--------------------------------------------------------

        --[[
				-- New player priority
				if new_player_prio_active and not new_player_prioritized and data.totalPlaytime and data.totalPlaytime <= 60 then
					new_player_counter = new_player_counter + 1

					if new_player_counter % 5 == 0 and (os.time() - new_player_lastpriority) >= (15 * 60) then
						new_player_lastpriority = os.time()
            new_player_prioritized = true

						exports.blrp_core:LogSplunkVrp(user_id, 'PRIORITY-QUEUE', 'Received new player priority / total_playtime = ' .. data.totalPlaytime .. ' / new_player_counter = ' .. new_player_counter)
						Queue:AddPriority(identifiers[1], 5)
					end
				end
        ]]

				--------------------------------------------------------

				if message_display ~= '' then
					message_display_iters = message_display_iters + 1

					if message_display_iters == 10 then
						message_display = ''
						message_display_iters = 0
					end
				end

				if progress_iters == 2 then
					progress_iters = 0

					dot_count = dot_count + 1

					if dot_count > 3 then
						dot_count = 1
					end

					local dots = string.rep('◻️', dot_count)

					local queue_position = string_format(Config.Language.pos, pos, Queue:GetSize())

					if
						faction_priority_enabled and
						not faction_priority_blacklisted[user_id] and
						not data.priority and
						not data.offered_faction and
						whitelisting_data and
						whitelisting_data.any and
						(
							(whitelisting_data.bcso and needs_bcso) or
							(whitelisting_data.sahp and needs_sahp) or
							(whitelisting_data.lspd and needs_lspd) or
							(whitelisting_data.lsfd and needs_lsfd) or
							(whitelisting_data.doc and needs_doc) or
							(whitelisting_data.doj and needs_doj) or
							(whitelisting_data.lawyer and needs_lawyer)
						)
					then
						data.deferrals.presentCard(
							createInfoCard(
								queue_position,
								dots,
								(whitelisting_data and whitelisting_data.bcso and needs_bcso),
								(whitelisting_data and whitelisting_data.sahp and needs_sahp),
								(whitelisting_data and whitelisting_data.lspd and needs_lspd),
								(whitelisting_data and whitelisting_data.lsfd and needs_lsfd),
								(whitelisting_data and whitelisting_data.doc and needs_doc),
								(whitelisting_data and whitelisting_data.doj and needs_doj),
								(whitelisting_data and whitelisting_data.lawyer and needs_lawyer),
								message_display
							),
							function(selected_faction, selected_faction_raw)
								-- Fuck FiveM by the way, remarshal their shitty data to string
								if type(selected_faction) == 'table' then
									local _selected_faction = {}
									local _selected_faction_str = ''

									local max_index = 0

									for k, v in pairs(selected_faction) do
										local kn = tonumber(k)

										if kn then
											if kn > max_index then
												max_index = kn
											end

											_selected_faction[kn] = v
											selected_faction[k] = nil
										end
									end

									for j = 0, max_index do
										_selected_faction_str = _selected_faction_str .. _selected_faction[j]
									end

									selected_faction = _selected_faction_str
								end

								if data.offered_faction then
									return
								end

								if type(selected_faction) ~= 'string' then
									return
								end

								if selected_faction == 'civilian' then
									data.offered_faction = true
									return
								end

								if
                  selected_faction ~= 'bcso' and
                  selected_faction ~= 'sahp' and
                  selected_faction ~= 'lspd' and
                  selected_faction ~= 'lsfd' and
                  selected_faction ~= 'doc' and
                  selected_faction ~= 'doj' and
                  selected_faction ~= 'lawyer'
                then
									return
								end

								if _G['needs_' .. selected_faction] then
									local selected_faction_queue = selected_faction

									if
										((selected_faction == 'bcso' or selected_faction == 'sahp' or selected_faction == 'lspd') and (queued_leo + current_leo + 1 > minimum_leo)) or
										(selected_faction == 'lsfd' and (queued_lsfd + current_lsfd + 1 > minimum_lsfd)) or
										(selected_faction == 'doc' and (queued_doc + current_doc + 1 > minimum_doc)) or
										(selected_faction == 'doj' and (queued_doj + current_doj + 1 > minimum_doj)) or
										(selected_faction == 'lawyer' and (queued_lawyer + current_lawyer + 1 > minimum_lawyer))
									then
										message_display = 'Unable to prioritize. Another player was prioritized before you.'

										return
									end

									local priority_category = selected_faction

									if priority_category == 'bcso' or priority_category == 'sahp' or priority_category == 'lspd' then
										priority_category = 'leo'
									end

									last_priority_times[priority_category] = os.time()

									message_display = 'You have been added to priority queue for ' .. string.upper(selected_faction_queue)

									Queue:AddPriority(identifiers[1], 10)

									data.offered_faction = true
									data.vrp_id = user_id
									data.reprioritized_faction = selected_faction
									data.queued_faction = selected_faction_queue

									TriggerEvent('core:server:queue-connector:prioritize', user_id, selected_faction)

									calculateFactionQueueData()
								end
							end
						)
					else
						data.deferrals.presentCard(createInfoCard(queue_position, dots, false, false, false, false, false, false, false, message_display))
					end
				end
			end
		end)
	end)



	local function checkTimeOuts()
		local i = 1

		while i <= Queue:GetSize() do
			local data = Queue.QueueList[i]
			local lastMsg = GetPlayerLastMsg(data.source)

			if lastMsg == 0 or lastMsg >= 30000 then
				data.timeout = data.timeout + 1
			else
				data.timeout = 0
			end

			-- check just incase there is invalid data
			if not data.ids or not data.name or not data.firstconnect or data.priority == nil or not data.source then
				data.deferrals.done(Config.Language._err .. "[1]")
				table_remove(Queue.QueueList, i)
				Queue:DebugPrint(tostring(data.name) .. "[" .. tostring(data.ids[1]) .. "] was removed from the queue because it had invalid data")
			elseif (data.timeout >= 120) and data.source ~= "debug" and os_time() - data.firstconnect > 5 then
				-- remove by source incase they rejoined and were duped in the queue somehow
				data.deferrals.done(Config.Language._err .. "[2]")
				Queue:RemoveFromQueue(data.source, true)
				Queue:RemoveFromConnecting(data.source, true)
				Queue:DebugPrint(data.name .. "[" .. data.ids[1] .. "] was removed from the queue because they timed out")
			else
				i = i + 1
			end
		end

		i = 1

		while i <= Queue:ConnectingSize() do
			local data = Queue.Connecting[i]

			local lastMsg = GetPlayerLastMsg(data.source)

			data.timeout = data.timeout + 1

			if ((data.timeout >= 120 and lastMsg >= 35000) or data.timeout >= 340) and data.source ~= "debug" and os_time() - data.firstconnect > 5 then
				Queue:RemoveFromQueue(data.source, true)
				Queue:RemoveFromConnecting(data.source, true)
				Queue:DebugPrint(data.name .. "[" .. data.ids[1] .. "] was removed from the connecting queue because they timed out")
			else
				i = i + 1
			end
		end

		local qCount = Queue:GetSize()

		-- show queue count in server name
		if displayQueue and initHostName then SetConvar("sv_hostname", (qCount > 0 and "[" .. tostring(qCount) .. "] " or "") .. initHostName) end

		SetTimeout(1000, checkTimeOuts)
	end

	checkTimeOuts()
end)

local function playerActivated()
	local src = source
	local ids = Queue:GetIds(src)

	if not Queue.PlayerList[src] then
		Queue.PlayerCount = Queue.PlayerCount + 1
		Queue.PlayerList[src] = true
		Queue:RemoveFromQueue(ids)
		Queue:RemoveFromConnecting(ids)
	end
end

RegisterServerEvent("Queue:playerActivated")
AddEventHandler("Queue:playerActivated", playerActivated)

AddEventHandler('playerDropped', function()
	local src = source
	local ids = Queue:GetIds(src)

	if Queue.PlayerList[src] then
		Queue.PlayerCount = Queue.PlayerCount - 1
		Queue.PlayerList[src] = nil
		Queue:RemoveFromQueue(ids)
		Queue:RemoveFromConnecting(ids)
	end
end)

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		if exports and exports.connectqueue then TriggerEvent("queue:onReady") return end
	end
end)

-- debugging / testing commands
local testAdds = 0

local commands = {
  staffonly = function()
    staff_only = not staff_only
    print('Set staff_only to: ', staff_only)
  end,

  allowguests = function()
    if GetConvarInt('dev', -1) == 1 then
      dev_allow_guests = not dev_allow_guests
      print('Set dev_allow_guests to: ', dev_allow_guests)
    end
  end,

  fakebcso = function() if GetConvarInt('dev', -1) == 1 then needs_bcso = true print('Set needs_bcso to: ', needs_bcso) end end,
  fakesahp = function() if GetConvarInt('dev', -1) == 1 then needs_sahp = true print('Set needs_sahp to: ', needs_sahp) end end,
  fakelspd = function() if GetConvarInt('dev', -1) == 1 then needs_lspd = true print('Set needs_lspd to: ', needs_lspd) end end,
  fakelsfd = function() if GetConvarInt('dev', -1) == 1 then needs_lsfd = true print('Set needs_lsfd to: ', needs_lsfd) end end,
  fakedoc = function() if GetConvarInt('dev', -1) == 1 then needs_doc = true print('Set needs_doc to: ', needs_doc) end end,
  fakedoj = function() if GetConvarInt('dev', -1) == 1 then need_doj = true print('Set need_doj to: ', need_doj) end end,
  fakelawyer = function() if GetConvarInt('dev', -1) == 1 then need_lawyer = true print('Set need_lawyer to: ', need_lawyer) end end,

  fakeall = function()
    if GetConvarInt('dev', -1) == 1 then
      needs_bcso, needs_sahp, needs_lspd, needs_lsfd, needs_doc, need_doj, need_lawyer = true, true, true, true, true, true, true
      print("All fake roles enabled")
    end
  end,

  unfake = function()
    if GetConvarInt('dev', -1) == 1 then
      needs_bcso, needs_sahp, needs_lspd, needs_lsfd, needs_doc, need_doj, need_lawyer = false, false, false, false, false, false, false
      print("All fake roles reset")
    end
  end,

  qfpenable = function()
    faction_priority_enabled = true
    print('[Queue Faction Priority] Enabled: ' .. tostring(faction_priority_enabled))
  end,

  qfpdisable = function()
    faction_priority_enabled = false
    print('[Queue Faction Priority] Disabled: ' .. tostring(faction_priority_enabled))
  end,

  npptoggle = function()
    new_player_prio_active = not new_player_prio_active
    print('[Queue New Player Priority] Enabled: ' .. tostring(new_player_prio_active))
  end,

  addq = function()
    print("==ADDED FAKE QUEUE==")
    Queue:AddToQueue({"steam:110000103fd1bb1"..testAdds}, os_time(), "Fake Player", "debug")
    testAdds = testAdds + 1
  end,

  removeq = function(args)
    if not args[1] then return end
    print("REMOVED " .. Queue.QueueList[tonumber(args[1])].name .. " FROM THE QUEUE")
    local data = Queue.QueueList[tonumber(args[1])]
    table.remove(Queue.QueueList, tonumber(args[1]))
    if data.deferrals then data.deferrals.done("Removed from queue") end
  end,

  printq = function()
    print("==CURRENT QUEUE LIST==")
    for k, v in ipairs(Queue.QueueList) do
      print(k .. ": [src: " .. v.source .. "] " .. v.name .. "[" .. v.ids[1] .. "] | Priority: " .. tostring(v.priority and true or false))
    end
  end,

  addc = function()
    print("==ADDED FAKE CONNECTING QUEUE==")
    Queue:AddToConnecting({"debug"})
  end,

  removec = function(args)
    if not args[1] then return end
    print("==REMOVED FAKE CONNECTING QUEUE==")
    table.remove(Queue.Connecting, tonumber(args[1]))
  end,

  printc = function()
    print("==CURRENT CONNECTING LIST==")
    for k, v in ipairs(Queue.Connecting) do
      print(k .. ": [src: " .. v.source .. "] " .. v.name .. "[" .. v.ids[1] .. "] | Priority: " .. tostring(v.priority and true or false))
    end
  end,

  printl = function()
    for k, v in pairs(Queue.PlayerList) do
      print(k .. ": " .. tostring(v))
    end
  end,

  addp = function(args)
    if not args[1] then return end
    print("==ADDED "..args[1].." TO QUEUE PRIORITY==")
    AddPriority(args[1], 10)
  end,

  removep = function(args)
    if not args[1] then return end
    print("==REMOVED "..args[1].." FROM QUEUE PRIORITY==")
    RemovePriority(args[1])
  end,

  printp = function()
    print("==CURRENT PRIORITY LIST==")
    for k, v in pairs(Queue.Priority) do
      print(k .. ": " .. tostring(v))
    end
  end,

  printcount = function()
    print("Player Count: " .. Queue.PlayerCount)
  end,

  printt = function()
    print("Thread Count: " .. Queue.ThreadCount)
  end,

  printtp = function()
    print("==CURRENT TEMP PRIORITY LIST==")
    for k, v in pairs(tempPriorityList) do
      print(k .. "| Time: " .. tostring(v))
    end
  end
}

for cmd, func in pairs(commands) do
  RegisterCommand(cmd, function(source, args, rawCommand)
    if source == 0 then -- Server console only
      func(args)
    end
  end, true)
end

RegisterServerEvent("Queue:toggleWhitelist")
AddEventHandler("Queue:toggleWhitelist", function(toggle)
	whitelistEnabled = toggle
	if whitelistEnabled then
		print("whitelist on")
	else
		print("whitelist off")
	end
end)

-- prevent duplicating queue count in server name
AddEventHandler("onResourceStop", function(resource)
	if displayQueue and resource == GetCurrentResourceName() then SetConvar("sv_hostname", initHostName) end
end)

exports('PerformQueueBump', function(user_id, admin_id)
	local bumped = false

	for i = 1, Queue:GetSize() do
		local data = Queue.QueueList[i]

		if tonumber(data.vrp_id) == tonumber(user_id) then
			if data.priority and type(data.priority) == 'number' and data.priority >= 12 then
				break
			end

			local _pos = false

			Queue:AddPriority(data.ids[1], 12)
			Queue:DebugPrint('[Manual Priority] ' .. admin_id .. ' set ' .. data.name .. '[' .. data.ids[1] .. '] priority to 12')
			Queue.QueueList[i].priority = 12

			local target_priority = Queue:IsPriority(data.ids) or 1

			for k, v in ipairs(Queue.QueueList) do
				if target_priority then
					if not v.priority then
						_pos = k
						break
					end

					if target_priority > v.priority then
						_pos = k
						break
					end
				end
			end

			if _pos then
				exports.blrp_core:LogSplunkVrp(admin_id, 'PRIORITY-QUEUE', 'Performed manual queue bump', {
          bumped_vrp = data.vrp_id
        })
				Queue:SetPos(data.ids, _pos, false)
				Queue:DebugPrint('[Manual Priority] ' .. admin_id .. ' set ' .. data.name .. '[' .. data.ids[1] .. '] pos to ' .. _pos)

				bumped = true
			end

			break
		end
	end

	return bumped
end)
