GFX�  �    �  < 4�o    
  generic_texture_renderer �  �    �
  D     <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"> <rdf:Description rdf:about="" xmlns:xmp="http://ns.adobe.com/xap/1.0/"> <xmp:CreatorTool>Adobe Flash Professional CS6 - build 481</xmp:CreatorTool> <xmp:CreateDate>2017-08-03T11:37:28+03:00</xmp:CreateDate> <xmp:MetadataDate>2018-07-14T11:08:32+03:00</xmp:MetadataDate> <xmp:ModifyDate>2018-07-14T11:08:32+03:00</xmp:ModifyDate> </rdf:Description> <rdf:Description rdf:about="" xmlns:dc="http://purl.org/dc/elements/1.1/"> <dc:format>application/x-shockwave-flash</dc:format> </rdf:Description> <rdf:Description rdf:about="" xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/" xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"> <xmpMM:InstanceID>xmp.iid:1F7D623E3B87E8118AA9CA17CB33CDD1</xmpMM:InstanceID> <xmpMM:DocumentID>xmp.did:1F7D623E3B87E8118AA9CA17CB33CDD1</xmpMM:DocumentID> <xmpMM:OriginalDocumentID>xmp.did:6DD54FC61E78E7119FCEB345E11ED7D9</xmpMM:OriginalDocumentID> <xmpMM:DerivedFrom rdf:parseType="Resource"> <stRef:instanceID>xmp.iid:1B7D623E3B87E8118AA9CA17CB33CDD1</stRef:instanceID> <stRef:documentID>xmp.did:6DD54FC61E78E7119FCEB345E11ED7D9</stRef:documentID> <stRef:originalDocumentID>xmp.did:6DD54FC61E78E7119FCEB345E11ED7D9</stRef:originalDocumentID> </xmpMM:DerivedFrom> </rdf:Description> </rdf:RDF>  C���?    0
 � �  � f3   �)hY�f��* �	     �   @   ?     Texture �	*     ?    �   &  ݙ����� Texture @   ?     CONTENT ?(   �  TIMELINE  this �     Generic @< �	        ?     __Packages.Generic �X   �a _global Generic gfxExtensions globalMC contentMC getNextHighestDepth CONTENT attachMovie _width _height _x _y txdLoader Common TextureLoader MovieClip prototype SET_TEXTURE Texture parseFloat LOAD_IGNORE_CONTAINER eksDee http://s1.1zoom.me/b5266/323/Tropics_Sunrises_and_sunsets_Rivers_Stones_Sky_549409_1280x720.jpg parseInt LOAD_HTTP ASSetPropFlags �  � N� ��  � �   globalMovieClip � �         R� O� O�         N� R�
    N� RO� N�    O� N� 	�  O� N� 
        O� N�         O�         
� SO� O�  � N� i� N� � �,   ) txd txn posX posY sizeX sizeY � � N� N� 
   =O� N� N�    =O� N� N�    =O� N� N� 	   =O� N� N�
    N� RO� �"   ) posX posY sizeX sizeY � � � � N� N� 
   =O� N� N�    =O� N� N�    =O� N� N� 	   =O� N� N�    N� RO�     � N� N�    = �	        ?$     __Packages.Common.TextureLoader ��   �, _global Common Object TextureLoader MovieClip prototype LOAD txd_loader MovieClipLoader addListener img:// / loadClip LOAD_HTTP width _width height _height loader onLoadInit removeListener LOAD_IGNORE_CONTAINER LOAD_RESPECT_CONTAINER ratio Math min _x _y LOAD_HTTP_RESPECT_CONTAINER ASSetPropFlags �  � N�  �  �
         @O�  � N� N� w� � �      �         R� O� � N� i� N� � �,   ) textureDict textureFile targetMC [ �         @O�    N� 	R� 
G� G� G� �
    N� RO� 
�   ) url targetMC � � N� N�    C� �         @O� �   ) loadedMC 9 � NO� NO�    N� R� OO�    N� 	R�
    N� RO� �,   ) textureDict textureFile targetMC � � 
G� G� G� � N� N�    C� �         @O� �   ) loadedMC 9 � NO� NO�    N� R� OO�    N� 	R�
    N� RO� �,   	) textureDict textureFile targetMC t� 
G� G� G� � N� N� N� N
�    C� �         @O� �   ) loadedMC � � N� N
� N� N
�    � R� � N� O� N� O�
 
   N� N�    
GO�
 
   N� N�    
GO�    N� R� OO�    N� 	R�
    N� RO� �   ) url targetMC m� N� N� N� N
�    C� �         @O� �   ) loadedMC � � N� N
� N� N
�    � R� � N� O� N� O�
 
   N� N�    
GO�
 
   N� N�    
GO� N&� N&�    N� R� OO�    N� 	R�
    N� RO� �   ) targetMC 9 �	 x   O�	 <   O�    N� R� OO�    � N� N�    = @   