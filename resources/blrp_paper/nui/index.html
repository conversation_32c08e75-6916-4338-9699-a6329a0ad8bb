<html>

<head>
    <title>BLRP Note</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="css/main.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/1.9.1/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tailwindcss/typography/dist/typography.min.css"/>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cabin:ital,wght@0,400;0,500;1,400;1,500;1,600;1,700&family=Oswald:wght@200;300;400;500;600;700&family=Scada:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
</head>

<body>

<!--<main id="app" class="main flex justify-end ">-->
<!--    <div v-show="visible" class="content-container" :style="pageStyleVars">-->
<!--        <aside class="aside flex " :style="asideStyleVars">-->
<!--            <transition-group name="list" tag="ul" ref="links">-->
<!--                <li v-for="(item, index) in activeHeadings" v-bind:key="item.id">-->
<!--                            <span @click="(e) => handleLinkClick(e, item.id)" :href="item.id"-->
<!--                                  v-bind:class="{-->
<!--                                    'is-active': item.id === visibleId,-->
<!--                                    'is-child': item.depth === 2}">-->
<!--                                {{ item.id }}-->
<!--                            </span>-->
<!--                </li>-->
<!--            </transition-group>-->
<!--        </aside>-->
<!--        <div class="container-content markdown-body" :style="contentStyleVars"-->
<!--             v-html="parsed.replaceAll('[nobg]', '').replaceAll('[real]', '')" ref="content">-->
<!--        </div>-->
<!--&lt;!&ndash;        <div class="container-content markdown-body" :style="contentStyleVars"&ndash;&gt;-->
<!--&lt;!&ndash;             ref="content">&ndash;&gt;-->
<!--&lt;!&ndash;            {{ input }}&ndash;&gt;-->
<!--&lt;!&ndash;        </div>&ndash;&gt;-->
<!--    </div>-->
<!--</main>-->

<main id="app" class="main flex justify-end ">
    <div v-show="visible" class="content-container" :style="pageStyleVars">
        <div class="container-content markdown-body" :style="contentStyleVars"
             v-html="parsed.replaceAll('[nobg]', '').replaceAll('[real]', '')" ref="content">
        </div>
    </div>
</main>

<script src="https://cfx-nui-blrp_ui/ui/vue.global.prod.js"></script>
<!--<script src="https://unpkg.com/v-smooth-scroll"></script>-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/markdown-it/13.0.1/markdown-it.min.js"></script>-->
<link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/fontawesome.min.css" />
<link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/all.min.css" />
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.8.0/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

<script>
    const App = {
        data() {
            return {
                parsed: '...',
                original: '...',
                visibleId: '',
                previousVisibleId: '',
                mdLength: 0,
                visible: false,
                shouldSanitize: false,
                headings: [],
                chartInstances: [],
                charts: [],
            };
        },
        async created() {
            window.addEventListener('message', event => {
                if (!event.data) return false;

                if (event.data.type === 'paper:show') {
                    this.visible = true
                    this.original = event.data.contents
                    this.parsed = this.compileMarkdown(event.data.contents, true)

                    this.shouldSanitize = event.data.sanitize

                    this.$nextTick(() => {
                        this.findHeadings();

                        setTimeout(() => {
                            this.makeCharts();
                        }, 500)
                    })
                }

                if (event.data.type === 'paper:hide') {
                    this.hideInterface();
                }
            });

            document.onkeydown = evt => {
                if(!this.visible) return false;
                evt = evt || window.event;
                if (evt.keyCode === 27) this.hideInterface();
            }
        },
        mounted() {
            let options = {
                rootMargin: "0px 0px -200px 0px",
                threshold: 1
            };

            const debouncedFunction = this.debounce(this.handleObserver);
            this.observer = new IntersectionObserver(debouncedFunction, options);
            this.motionQuery = window.matchMedia("(prefers-reduced-motion)");
        },
        methods: {
            async hideInterface() {
                this.charts = [];
                for (const chartInstance of this.chartInstances) {
                    await chartInstance.destroy();
                }

                setTimeout(() => {
                    this.headings = [];
                    this.parsed = '...';
                    this.original = '...';
                    this.chartInstances = [];
                }, 500);

                this.visible = false

                fetch(`https://blrp_paper/escape`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json; charset=UTF-8',},
                    body: JSON.stringify({})
                }).then(resp => resp.json()).then(resp => {
                    return resp;
                });
            },
            slugify(text) {
                return text.toString().normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase().trim().replace(/\s+/g, "-").replace(/[^\w-]+/g, "").replace(/--+/g, "-");
            },
            getRelated(item) {
                if (item) {
                    const items = this.compiledHeadings;
                    const currentIdx = items.indexOf(item);
                    let idx = 0;

                    // find the correct (parent) index
                    if (item.depth === 1) {
                        idx = currentIdx + 1;
                    } else {
                        // find parent index
                        let found = false;
                        for (let j = currentIdx; j >= 0; j--) {
                            if (items[j].depth === 1 && !found) {
                                idx = j + 1;
                                found = true;
                            }
                        }
                    }

                    let children = [];
                    let isSameLevel = true;
                    for (idx; idx < items.length; idx++) {
                        if (items[idx].depth === 2 && isSameLevel) {
                            children.push(items[idx]);
                        } else if (items[idx].depth === 1) {
                            isSameLevel = false;
                        }
                    }
                    return children;
                }
            },
            handleObserver(entries, observer) {
                entries.forEach(entry => {
                    const {target, isIntersecting, intersectionRatio} = entry;
                    if (isIntersecting && intersectionRatio >= 1) {
                        this.visibleId = `#${target.getAttribute("id")}`;
                    }
                });
            },
            handleLinkClick(evt, itemId) {
                evt.preventDefault();
                let id = itemId.replace("#", "");
                let section = this.headings.find(
                    heading => heading.getAttribute("id") === id);

                section.setAttribute("tabindex", -1);
                section.focus();
                this.visibleId = itemId;

                // Vue.$smoothScroll({
                //     scrollTo: section,
                //     duration: 1000,
                //     offset: -50,
                // })

                section.scrollIntoView({
                    behavior: 'smooth'
                });

                // var topPos = section.offsetTop;
                // this.$refs.content.scrollTop = topPos - 50
            },
            debounce(fn) {
                var timeout;
                return function () {
                    var context = this;
                    var args = arguments;
                    if (timeout) {
                        window.cancelAnimationFrame(timeout);
                    }
                    timeout = window.requestAnimationFrame(function () {
                        fn.apply(context, args);
                    });
                };
            },
            compileMarkdown(content, first) {
                // let content = this.input.replace(/(<([^>]+)>)/gi, "");

                // const md = window.markdownit({
                //     html: true,
                // })
                //
                // content = md.render(content);

                content = marked.parse(content)

                content = content
                    .replace(/\[center]([\s\S]*?)\[\/center]/gm, (match, content) => {
                        return `<center> ${content} </center>`
                    })

                content = content
                    .replace(/\[f=(\d*)]([\s\S]*?)\[\/f]/gm, (match, fontSize, content ) => {
                        return `<span style="font-size: ${fontSize}"> ${content} </span>`
                    })

                content = content
                    .replace(/\[c=(.+?)](.+?)\[\/c]/gm, (match, color, content ) => {
                        if (color && color.length === 6) {
                            return `<span style="color: #${color}"> ${content} </span>`;
                        } else {
                            return `<span style="color: ${color}"> ${content} </span>`;
                        }
                    })

                content = content
                    .replace(/\[chart=(.+?)](.+?)\[\/chart]/gm, (match, name, data) => {
                        const config = JSON.parse(atob(data));

                        const uuid = Math.random() * (99999999 - 5) + 5

                        console.log('pushing charts')
                        this.charts.push({
                            id: uuid,
                            name: uuid,
                            config: config
                        })

                        return `<canvas class="chart-${uuid}" width="400" height="300"> </canvas>`;
                    })

                content = content
                    .replace(/\[i=(.+?)]/gm, (match, icon ) => {
                        return `<i class="${icon}" style="margin-right: 5px"> </i>`;
                    })

                content = content
                    .replace(/(http(s?):)([\/|.|\w|\s|-])*\.(?:jpg|jpeg|gif|png)(?!")(?!")(?!\))/gm, (match, link) => {
                        return `<img alt="..." src="${match}">`
                    })

                content = content
                    .replace(/\[l]/gm, (match) => {
                        return `<hr/>`
                    })

                // Custom badges
                content = content
                    .replace(/\[badge=(.+?)]([\s\S]*?)\[\/badge]/gm, (match, name, data) => {
                        const config = JSON.parse(atob(data));

                        return `
                            <div class="mx-4 block p-6 bg-${config.color}-100 rounded-lg border border-gray-200 shadow-md dark:bg-gray-800 dark:border-gray-700">
                                <div>
                                    <span>
                                        <i class="${config.icon} text-${config.iconColor}-800"> </i>
                                    </span>
                                    <span class=" text-${config.color}-800 text-md font-semibold
                                        mr-2 pl-2 pr-4 pt-1 pb-1 mb-3 rounded dark:bg-${config.color}-200 dark:text-${config.color}-900">
                                            ${config.label}
                                    </span>
                                </div>
                                <div class="text-sm">
                                    ${this.compileMarkdown(config.content)}
                                    <span class="text-gray-800"> <b>${config.hash}</b> • ${config.created_at} </span>
                                </div>
                            </div>`
                        })

                // Custom Box
                content = content
                    .replace(/\[box=(.+?)]([\s\S]*?)\[\/box]/gm, (match, title, content) => {
                        return `
                            <div class="mx-4 block p-6 bg-white rounded-lg border border-gray-200 shadow-md dark:bg-gray-800 dark:border-gray-700">
                                <div class="text-md font-semibold mr-2 pl-2 pr-4 pt-1 pb-1 mb-3">
                                    ${title}
                                </div>
                                <div class="text-sm">
                                    ${this.compileMarkdown(content)}
                                </div>
                            </div>`
                        })

                this.mdLength = content.length;

                return content;
            },
            findHeadings() {
                if (this.observer) {
                    this.headings = [...this.$refs.content.querySelectorAll("[id]")];
                    // console.log('headings', this.headings)
                    this.headings.map(heading => this.observer.observe(heading));
                }
            },
            makeCharts() {
                console.log('making charts')
                for (const chart of this.charts) {
                    setTimeout(() => {
                        const createdChart = new Chart(
                            document.getElementsByClassName(`chart-${chart.id}`),
                            chart.config,
                        );

                        this.chartInstances.push(createdChart);
                    }, 100)
                }
            },
        },
        watch: {
            parsed(oldValue, newValue) {
                if (newValue && oldValue !== newValue) {
                }
            }
        },
        computed: {
            pageStyleVars() {
                return {
                    '--width': '900px'
                    // 'background-image': !this.noBackground ? 'url("../paper.jpg")printPaperGive' : 'none',
                }
            },
            contentStyleVars() {
                return {
                    'background-image': !this.noBackground ? 'url("paper.jpg")' : 'none',
                }
            },
            asideStyleVars() {
                return {
                    'opacity': this.compiledHeadings.length > 8 ? '1' : '0',
                }
            },
            compiledHeadings() {
                let regexString = /#(.*)/g;
                const found = this.original.match(regexString);
                if(!found) return [];

                return found.map(item => {
                    let depth = (item.match(/#/g) || []).length;
                    let text = item.replace(/#/gi, "", "").trim();
                    return {depth, id: `#${this.slugify(text)}`, text};
                });
            },
            activeHeadings() {
                let activeItem = this.compiledHeadings.find(
                    item => item.id === this.visibleId);

                let relatedItems = this.getRelated(activeItem) || [];
                return this.compiledHeadings.filter(
                    item => item.depth === 1 || relatedItems.includes(item));
            },
            noBackground() {
                if ( this.original.includes('[nobg]') ) {
                    return true
                }

                const imageRegex = /(http(s?):)([\/|.|\w|\s|-])*\.(?:jpg|jpeg|gif|png)(?!")(?!")(?!\))/gm

                if ( this.original.match(imageRegex) ) {
                    if ( this.original.length < 355 ) return true
                }

                return false
            },
        }
    };

    const app = Vue.createApp(App);

    const root = app.mount('#app');
</script>
</body>
</html>
