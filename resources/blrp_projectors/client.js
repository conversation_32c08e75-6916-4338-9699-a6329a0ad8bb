async function loadScaleform(scaleform) {
  let scaleformHandle = RequestScaleformMovie(scaleform);

  return new Promise(resolve => {
    const interval = setInterval(() => {
      if (HasScaleformMovieLoaded(scaleformHandle)) {
        clearInterval(interval);
        resolve(scaleformHandle);
      } else {
        scaleformHandle = RequestScaleformMovie(scaleform);
      }
    }, 0);
  });
}

const projectors = [
  {
    id: "mrpd",
    url: "https://i.gyazo.com/ccfb13fa1ec4f9dd0bbc4827615a1680.png",
    scale: 0.1,
    sfName: "gtr_mrpd",
    sfHandle: null,
    txdHasBeenSet: false,
    duiObj: null,
    pos: [-1090.470, -832.200, 23.24357],
    width: 1850,
    height: 1080,
    rot: -129.0,
    range: 15
  },
  {
    id: "sandy",
    url: "https://i.gyazo.com/ac09366c3e1923013682c24ae0e0672b.jpg",
    scale: 0.1,
    sfName: "gtr_sandy",
    sfHandle: null,
    txdHasBeenSet: false,
    duiObj: null,
    pos: [1845.79, 3671.45, 38.74247],
    width: 1340,
    height: 875,
    rot: 150.0,
    range: 10
  }
];

async function initProjector(proj) {
  proj.sfHandle = await loadScaleform(proj.sfName);
  const txd = CreateRuntimeTxd(`${proj.id}_txd`);
  proj.duiObj = CreateDui(proj.url, proj.width, proj.height);
  const duiHandle = GetDuiHandle(proj.duiObj);
  CreateRuntimeTextureFromDuiHandle(txd, `${proj.id}_txn`, duiHandle);
}

on("onClientResourceStart", async (resName) => {
  if (resName === GetCurrentResourceName()) {
    for (const proj of projectors) {
      await initProjector(proj);
    }
  }
});

setTick(() => {
  let coords = GetEntityCoords(PlayerPedId());

  for (const proj of projectors) {
    if (proj.sfHandle !== null) {
      let distance = GetDistanceBetweenCoords(
        coords[0], coords[1], coords[2], proj.pos[0], proj.pos[1], proj.pos[2]
      );

      if (distance < proj.range) {
        if (!proj.txdHasBeenSet) {
          PushScaleformMovieFunction(proj.sfHandle, "SET_TEXTURE");
          PushScaleformMovieMethodParameterString(`${proj.id}_txd`); // txd
          PushScaleformMovieMethodParameterString(`${proj.id}_txn`); // txn
          PushScaleformMovieFunctionParameterInt(0); // x
          PushScaleformMovieFunctionParameterInt(0); // y
          PushScaleformMovieFunctionParameterInt(proj.width);
          PushScaleformMovieFunctionParameterInt(proj.height);
          PopScaleformMovieFunctionVoid();
          proj.txdHasBeenSet = true;
        }

        DrawScaleformMovie_3dNonAdditive(
          proj.sfHandle,
          proj.pos[0] - 1, proj.pos[1], proj.pos[2] + 2,
          0, 0, proj.rot,
          255, 255, 255,
          proj.scale * 1, proj.scale * (9 / 16), 1,
          2
        );
      } else {
        proj.txdHasBeenSet = false; // Reset so it can be reapplied when the player comes back
      }
    }
  }
});

onNet("blrp_projectors:client:setProjectorUrl", (projector_id, projector_url) => {
  let proj = projectors.find(p => p.id === projector_id);
  if (proj) {
    SetDuiUrl(proj.duiObj, projector_url);
  }
});
