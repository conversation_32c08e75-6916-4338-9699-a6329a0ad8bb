<div class="subgroup facepaint">
    <div class="list">
        <h3 class="header">Style</h3>
        <div class="controls">
            <button class="arrow left"></button>
            <select class="facepaintoverlay" data-prev="">
                <!--    NOTE: Facepaints come in two different types.
                              Some count as makeup and have fixed colors,
                              while others count as blusher and are colorable.

                              We need a more complicated select to accomodate that.
                -->

                <!-- "Makeup" facepaints -->
                <option value="16" class="makeup_1" data-index="4">Kiss My Axe</option>
                <option value="17" class="makeup_1" data-index="4">Panda Pussy</option>
                <option value="18" class="makeup_1" data-index="4">The Bat</option>
                <option value="19" class="makeup_1" data-index="4">Skull in Scarlet</option>
                <option value="20" class="makeup_1" data-index="4">Serpentine</option>
                <option value="21" class="makeup_1" data-index="4">The Veldt</option>
                <!-- TODO:  Missing names. Add them after finding out what the names are.
                <option value="22" class="makeup_1" data-index="4">22</option>
                <option value="23" class="makeup_1" data-index="4">23</option>
                <option value="24" class="makeup_1" data-index="4">24</option>
                <option value="25" class="makeup_1" data-index="4">25</option>
                -->
                <option value="26" class="makeup_1" data-index="4">Tribal Lines</option>
                <option value="27" class="makeup_1" data-index="4">Tribal Swirls</option>
                <option value="28" class="makeup_1" data-index="4">Tribal Orange</option>
                <option value="29" class="makeup_1" data-index="4">Tribal Red</option>
                <option value="30" class="makeup_1" data-index="4">Trapped in a Box</option>
                <option value="31" class="makeup_1" data-index="4">Clowning</option>
                <option value="33" class="makeup_1" data-index="4">Stars n Stripes</option>
                <option value="42" class="makeup_1" data-index="4">Shadow Demon</option>
                <option value="43" class="makeup_1" data-index="4">Fleshy Demon</option>
                <option value="44" class="makeup_1" data-index="4">Flayed Demon</option>
                <option value="45" class="makeup_1" data-index="4">Sorrow Demon</option>
                <option value="46" class="makeup_1" data-index="4">Smiler Demon</option>
                <option value="47" class="makeup_1" data-index="4">Cracked Demon</option>
                <option value="48" class="makeup_1" data-index="4">Danger Skull</option>
                <option value="49" class="makeup_1" data-index="4">Wicked Skull</option>
                <option value="50" class="makeup_1" data-index="4">Menace Skull</option>
                <option value="51" class="makeup_1" data-index="4">Bone Jaw Skull</option>
                <option value="52" class="makeup_1" data-index="4">Flesh Jaw Skull</option>
                <option value="53" class="makeup_1" data-index="4">Spirit Skull</option>
                <option value="54" class="makeup_1" data-index="4">Ghoul Skull</option>
                <option value="55" class="makeup_1" data-index="4">Phantom Skull</option>
                <option value="56" class="makeup_1" data-index="4">Gnasher Skull</option>
                <option value="57" class="makeup_1" data-index="4">Exposed Skull</option>
                <option value="58" class="makeup_1" data-index="4">Ghostly Skull</option>
                <option value="59" class="makeup_1" data-index="4">Fury Skull</option>
                <option value="60" class="makeup_1" data-index="4">Demi Skull</option>
                <option value="61" class="makeup_1" data-index="4">Inbred Skull</option>
                <option value="62" class="makeup_1" data-index="4">Spooky Skull</option>
                <option value="63" class="makeup_1" data-index="4">Slashed Skull</option>
                <option value="64" class="makeup_1" data-index="4">Web Sugar Skull</option>
                <option value="65" class="makeup_1" data-index="4">Señor Sugar Skull</option>
                <option value="66" class="makeup_1" data-index="4">Swirl Sugar Skull</option>
                <option value="67" class="makeup_1" data-index="4">Floral Sugar Skull</option>
                <option value="68" class="makeup_1" data-index="4">Mono Sugar Skull</option>
                <option value="69" class="makeup_1" data-index="4">Femme Sugar Skull</option>
                <option value="70" class="makeup_1" data-index="4">Demi Sugar Skull</option>
                <option value="71" class="makeup_1" data-index="4">Scarred Sugar Skull</option>
                <!-- "Blusher" facepaints -->
                <option value="7"  class="blush_1"  data-index="5">Waves Left</option>
                <option value="8"  class="blush_1"  data-index="5">Waves Right</option>
                <option value="9"  class="blush_1"  data-index="5">Totemic</option>
                <option value="10" class="blush_1"  data-index="5">Streaks Left</option>
                <option value="11" class="blush_1"  data-index="5">Streaks Right</option>
                <option value="12" class="blush_1"  data-index="5">Breakup Left</option>
                <option value="13" class="blush_1"  data-index="5">Breakup Right</option>
                <option value="14" class="blush_1"  data-index="5">Blotch Left</option>
                <option value="15" class="blush_1"  data-index="5">Blotch Right</option>
                <option value="16" class="blush_1"  data-index="5">Stripes</option>
                <option value="17" class="blush_1"  data-index="5">The Elder</option>
                <option value="18" class="blush_1"  data-index="5">Vertical Stripe</option>
                <option value="19" class="blush_1"  data-index="5">Clan</option>
                <option value="20" class="blush_1"  data-index="5">Splats Right</option>
                <option value="21" class="blush_1"  data-index="5">Splats Left</option>
                <option value="22" class="blush_1"  data-index="5">The Phoenix</option>
                <option value="23" class="blush_1"  data-index="5">Ancestral</option>
                <option value="24" class="blush_1"  data-index="5">Coverage</option>
                <option value="25" class="blush_1"  data-index="5">Symmetry</option>
                <option value="26" class="blush_1"  data-index="5">Fingerprints</option>
                <option value="27" class="blush_1"  data-index="5">Blotch Lower</option>
                <option value="28" class="blush_1"  data-index="5">The Spirit</option>
                <option value="29" class="blush_1"  data-index="5">Cheek Stripes</option>
                <option value="30" class="blush_1"  data-index="5">The Demon</option>
                <option value="31" class="blush_1"  data-index="5">Mandible</option>
                <option value="32" class="blush_1"  data-index="5">Frontal</option>
            </select>
            <button class="arrow right"></button>
        </div>
    </div>
    <div class="slider opacity">
        <h3 class="header">Opacity</h3>
        <div class="valuelabel center">50%</div>
        <div class="controls">
            <button class="arrow left"></button>
            <input type="range" min="0" max="100" class="facepaintoverlay">
            <button class="arrow right"></button>
        </div>
    </div>
</div>