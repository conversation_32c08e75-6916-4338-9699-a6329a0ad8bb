<?xml version="1.0" encoding="UTF-8"?>

<CWeaponInfoBlob>
  <SlotNavigateOrder>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="361" />
          <Entry>SLOT_PLASMA</Entry>
        </Item>
        <Item>
          <OrderNumber value="362" />
          <Entry>SLOT_SHANK</Entry>
        </Item>
        <Item>
          <OrderNumber value="363" />
          <Entry>SLOT_BEANBAG</Entry>
        </Item>
        <Item>
          <OrderNumber value="364" />
          <Entry>SLOT_LIGHTSABER</Entry>
        </Item>
        <Item>
          <OrderNumber value="365" />
          <Entry>SLOT_KATANA</Entry>
        </Item>
        <Item>
          <OrderNumber value="366" />
          <Entry>SLOT_HOTDOG</Entry>
        </Item>
        <Item>
          <OrderNumber value="367" />
          <Entry>SLOT_LIGHTSABER_BLUE</Entry>
        </Item>
        <Item>
          <OrderNumber value="368" />
          <Entry>SLOT_LIGHTSABER_PURPLE</Entry>
        </Item>
        <Item>
          <OrderNumber value="369" />
          <Entry>SLOT_LIGHTSABER_RED</Entry>
        </Item>
        <Item>
          <OrderNumber value="370" />
          <Entry>SLOT_LIGHTSABER_YELLOW</Entry>
        </Item>
        <Item>
            <OrderNumber value="407"/>
            <Entry>SLOT_WEAPON_glock17</Entry>
        </Item>
        <Item>
            <OrderNumber value="408"/>
            <Entry>SLOT_WEAPON_Lucille</Entry>
        </Item>
        <Item>
            <OrderNumber value="409"/>
            <Entry>SLOT_WEAPON_HUNTINGRIFLE</Entry>
        </Item>
        <Item>
          <OrderNumber value="252" />
          <Entry>SLOT_PUMPSHOTGUN2</Entry>
        </Item>
        <Item>
          <OrderNumber value="600" />
          <Entry>SLOT_HOBBYHORSE</Entry>
        </Item>
      </WeaponSlots>
    </Item>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="361" />
          <Entry>SLOT_PLASMA</Entry>
        </Item>
        <Item>
          <OrderNumber value="362" />
          <Entry>SLOT_SHANK</Entry>
        </Item>
        <Item>
          <OrderNumber value="363" />
          <Entry>SLOT_BEANBAG</Entry>
        </Item>
        <Item>
          <OrderNumber value="364" />
          <Entry>SLOT_LIGHTSABER</Entry>
        </Item>
        <Item>
          <OrderNumber value="365" />
          <Entry>SLOT_KATANA</Entry>
        </Item>
        <Item>
          <OrderNumber value="366" />
          <Entry>SLOT_HOTDOG</Entry>
        </Item>
        <Item>
					<OrderNumber value="407"/>
					<Entry>SLOT_WEAPON_glock17</Entry>
				</Item>
        <Item>
					<OrderNumber value="408"/>
					<Entry>SLOT_WEAPON_Lucille</Entry>
				</Item>
        <Item>
            <OrderNumber value="409"/>
            <Entry>SLOT_WEAPON_Lucille</Entry>
        </Item>
        <Item>
        <Item>
          <OrderNumber value="251" />
          <Entry>SLOT_PUMPSHOTGUN2</Entry>
        </Item>
          <OrderNumber value="600" />
          <Entry>SLOT_HOBBYHORSE</Entry>
        </Item>
      </WeaponSlots>
    </Item>
  </SlotNavigateOrder>
  <TintSpecValues />
  <FiringPatternAliases />
  <UpperBodyFixupExpressionData />
  <AimingInfos />
  <Infos>
    <Item>
      <Infos>
      <Item type="CWeaponInfo">
        <Name>WEAPON_PUMPSHOTGUN2</Name>
        <Model>w_sg_pumpshotgun2</Model>
        <Audio>AUDIO_ITEM_PUMPSHOTGUN</Audio>
        <Slot>SLOT_PUMPSHOTGUN2</Slot>
        <DamageType>BULLET</DamageType>
        <Explosion>
          <Default>DONTCARE</Default>
          <HitCar>DONTCARE</HitCar>
          <HitTruck>DONTCARE</HitTruck>
          <HitBike>DONTCARE</HitBike>
          <HitBoat>DONTCARE</HitBoat>
          <HitPlane>DONTCARE</HitPlane>
        </Explosion>
        <FireType>INSTANT_HIT</FireType>
        <WheelSlot>WHEEL_SHOTGUN</WheelSlot>
        <Group>GROUP_SHOTGUN</Group>
        <AmmoInfo ref="AMMO_SHOTGUN" />
        <AimingInfo ref="RIFLE_LO_PUMP_STRAFE" />
        <ClipSize value="8" />
        <AccuracySpread value="0.8000" />
        <AccurateModeAccuracyModifier value="0.75000" />
        <RunAndGunAccuracyModifier value="1.000000" />
        <RunAndGunAccuracyMinOverride value="-1.000000" />
        <RecoilAccuracyMax value="0.7500" />
        <RecoilErrorTime value="0.000000" />
        <RecoilRecoveryRate value="2.000000" />
        <RecoilAccuracyToAllowHeadShotAI value="1.000000" />
        <MinHeadShotDistanceAI value="1.000000" />
        <MaxHeadShotDistanceAI value="50.000000" />
        <HeadShotDamageModifierAI value="1.000000" />
        <RecoilAccuracyToAllowHeadShotPlayer value="0.000" />
        <MinHeadShotDistancePlayer value="0.000000" />
        <MaxHeadShotDistancePlayer value="0.000000" />
        <HeadShotDamageModifierPlayer value="0.0000" />
        <Damage value="15.0000000" />
        <DamageTime value="0.000000" />
        <DamageTimeInVehicle value="0.000000" />
        <DamageTimeInVehicleHeadShot value="0.000000" />
        <HitLimbsDamageModifier value="1.000000" />
        <NetworkHitLimbsDamageModifier value="0.9000" />
        <LightlyArmouredDamageModifier value="1.0000" />
        <Force value="13.000000" />
        <ForceHitPed value="25.000000" />
        <ForceHitVehicle value="300.000000" />
        <ForceHitFlyingHeli value="150.000000" />
        <OverrideForces>
          <Item>
            <BoneTag>BONETAG_HEAD</BoneTag>
            <ForceFront value="20.000000" />
            <ForceBack value="20.000000" />
          </Item>
          <Item>
            <BoneTag>BONETAG_NECK</BoneTag>
            <ForceFront value="20.000000" />
            <ForceBack value="20.000000" />
          </Item>
          <Item>
            <BoneTag>BONETAG_R_CLAVICLE</BoneTag>
            <ForceFront value="20.000000" />
            <ForceBack value="20.000000" />
          </Item>
          <Item>
            <BoneTag>BONETAG_L_CLAVICLE</BoneTag>
            <ForceFront value="20.000000" />
            <ForceBack value="20.000000" />
          </Item>
        </OverrideForces>
        <ForceMaxStrengthMult value="1.000000" />
        <ForceFalloffRangeStart value="10.000000" />
        <ForceFalloffRangeEnd value="50.000000" />
        <ForceFalloffMin value="1.000000" />
        <ProjectileForce value="0.00000" />
        <FragImpulse value="2.500000" />
        <Penetration value="0.10000" />
        <VerticalLaunchAdjustment value="0.00000" />
        <DropForwardVelocity value="0.000000" />
        <Speed value="2000.000000" />
        <BulletsInBatch value="5" />
        <BatchSpread value="0.06500" />
        <ReloadTimeMP value="1.000000" />
        <ReloadTimeSP value="1.000000" />
        <VehicleReloadTime value="1.500000" />
        <AnimReloadRate value="1.500000" />
        <BulletsPerAnimLoop value="0.25" />
        <TimeBetweenShots value="1.000000" />
        <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
        <SpinUpTime value="0.000000" />
        <SpinTime value="0.000000" />
        <SpinDownTime value="0.000000" />
        <AlternateWaitTime value="-1.000000" />
        <BulletBendingNearRadius value="0.000000" />
        <BulletBendingFarRadius value="0.000000" />
        <BulletBendingZoomedRadius value="0.375000" />
        <Fx>
          <EffectGroup>WEAPON_EFFECT_GROUP_SHOTGUN</EffectGroup>
          <FlashFx>muz_shotgun</FlashFx>
          <FlashFxAlt />
          <MuzzleSmokeFx>muz_smoking_barrel_shotgun</MuzzleSmokeFx>
          <MuzzleSmokeFxFP>muz_smoking_barrel_fp</MuzzleSmokeFxFP>
          <MuzzleSmokeFxMinLevel value="0.300000" />
          <MuzzleSmokeFxIncPerShot value="0.100000" />
          <MuzzleSmokeFxDecPerSec value="0.200000" />
          <ShellFx>eject_shotgun</ShellFx>
			    <ShellFxFP>eject_shotgun_fp</ShellFxFP>
          <TracerFx>bullet_tracer</TracerFx>
          <PedDamageHash>ShotgunLarge</PedDamageHash>
          <TracerFxChanceSP value="0.000000" />
          <TracerFxChanceMP value="1.000000" />
          <FlashFxChanceSP value="0.8000000" />
          <FlashFxChanceMP value="1.000000" />
          <FlashFxAltChance value="0.600000" />
          <FlashFxScale value="0.800000" />
          <FlashFxLightEnabled value="true" />
          <FlashFxLightCastsShadows value="false" />
          <FlashFxLightOffsetDist value="0.000000" />
          <FlashFxLightRGBAMin x="255.000000" y="93.000000" z="25.000000" />
          <FlashFxLightRGBAMax x="255.000000" y="100.000000" z="50.000000" />
          <FlashFxLightIntensityMinMax x="1.000000" y="2.000000" />
          <FlashFxLightRangeMinMax x="2.00000" y="2.500000" />
          <FlashFxLightFalloffMinMax x="1024.000000" y="1536.000000" />
          <GroundDisturbFxEnabled value="false" />
          <GroundDisturbFxDist value="5.000000" />
          <GroundDisturbFxNameDefault />
          <GroundDisturbFxNameSand />
          <GroundDisturbFxNameDirt />
          <GroundDisturbFxNameWater />
          <GroundDisturbFxNameFoliage />
        </Fx>
        <InitialRumbleDuration value="200" />
        <InitialRumbleIntensity value="1.000000" />
        <InitialRumbleIntensityTrigger value="0.950000" />
        <RumbleDuration value="200" />
        <RumbleIntensity value="1.000000" />
        <RumbleIntensityTrigger value="0.950000" />
        <RumbleDamageIntensity value="1.000000" />
        <InitialRumbleDurationFps value="250" />
        <InitialRumbleIntensityFps value="1.000000" />
        <RumbleDurationFps value="250" />
        <RumbleIntensityFps value="0.900000" />
        <NetworkPlayerDamageModifier value="1.000000" />
        <NetworkPedDamageModifier value="1.000000" />
        <NetworkHeadShotPlayerDamageModifier value="1.000000" />
        <LockOnRange value="35.000000" />
        <WeaponRange value="50.000000" />
        <BulletDirectionOffsetInDegrees value="0.000000" />
        <AiSoundRange value="-1.000000" />
        <AiPotentialBlastEventRange value="-1.000000" />
        <DamageFallOffRangeMin value="10.000000" />
        <DamageFallOffRangeMax value="40.000000" />
        <DamageFallOffModifier value="0.300000" />
        <VehicleWeaponHash />
        <DefaultCameraHash>SHOTGUN_AIM_CAMERA</DefaultCameraHash>
        <CoverCameraHash>SHOTGUN_AIM_IN_COVER_CAMERA</CoverCameraHash>
        <CoverReadyToFireCameraHash />
        <RunAndGunCameraHash>SHOTGUN_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
        <CinematicShootingCameraHash>SHOTGUN_CINEMATIC_SHOOTING_CAMERA</CinematicShootingCameraHash>
        <AlternativeOrScopedCameraHash />
        <RunAndGunAlternativeOrScopedCameraHash />
        <CinematicShootingAlternativeOrScopedCameraHash />
        <CameraFov value="45.000000" />
        <FirstPersonAimFovMin value="40.00000" />
        <FirstPersonAimFovMax value="45.00000" />
        <FirstPersonScopeFov value="30.00000" />
        <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
        <FirstPersonRNGRotationOffset x="2.000000" y="0.000000" z="2.000000" />
        <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
        <FirstPersonLTRotationOffset x="1.000000" y="0.000000" z="0.000000" />
        <FirstPersonScopeOffset x="0.00000" y="0.1000" z="0.0180" />
        <FirstPersonScopeAttachmentOffset x="0.00000" y="0.0000" z="0.0150" />
        <FirstPersonScopeRotationOffset x="0.00000" y="0.0000" z="0.0000" />
        <FirstPersonScopeAttachmentRotationOffset x="0.00000" y="0.0000" z="0.0000" />
        <FirstPersonAsThirdPersonIdleOffset x="-0.050000" y="0.000000" z="-0.050000" />
        <FirstPersonAsThirdPersonRNGOffset x="0.000000" y="0.000000" z="-0.075000" />
        <FirstPersonAsThirdPersonLTOffset x="0.020000" y="0.000000" z="-0.07500000" />
        <FirstPersonAsThirdPersonScopeOffset x="0.040" y="-0.02500000" z="-0.07500000" />
        <FirstPersonAsThirdPersonWeaponBlockedOffset x="0.0000000" y="0.100000" z="-0.050000" />
        <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
        <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
        <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
        <ZoomFactorForAccurateMode value="1.000000" />
        <RecoilShakeHash>SHOTGUN_RECOIL_SHAKE</RecoilShakeHash>
        <RecoilShakeHashFirstPerson>FPS_SHOTGUN_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
        <AccuracyOffsetShakeHash />
        <MinTimeBetweenRecoilShakes value="150" />
        <RecoilShakeAmplitude value="3.000000" />
        <ExplosionShakeAmplitude value="-1.000000" />
        <IkRecoilDisplacement value="0.02" />
        <IkRecoilDisplacementScope value="0.005" />
        <IkRecoilDisplacementScaleBackward value="1.0" />
        <IkRecoilDisplacementScaleVertical value="0.4" />
        <ReticuleHudPosition x="0.000000" y="0.000000" />
        <AimOffsetMin x="0.225000" y="0.250500" z="0.500000" />
        <AimProbeLengthMin value="0.445000" />
        <AimOffsetMax x="0.185000" y="0.000000" z="0.525000" />
        <AimProbeLengthMax value="0.325000" />
        <AimOffsetMinFPSIdle x="0.162000" y="0.225000" z="0.052000" />
        <AimOffsetMedFPSIdle x="0.187000" y="0.197000" z="0.321000" />
        <AimOffsetMaxFPSIdle x="0.155000" y="0.038000" z="0.364000" />
        <AimOffsetEndPosMinFPSIdle x="-0.284000" y="0.612000" z="-0.205000" />
        <AimOffsetEndPosMedFPSIdle x="-0.178000" y="0.639000" z="0.616000" />
        <AimOffsetEndPosMaxFPSIdle x="-0.21700" y="-0.096000" z="0.887000" />
        <AimOffsetMinFPSLT x="0.180000" y="0.231000" z="0.669000" />
        <AimOffsetMaxFPSLT x="0.048000" y="-0.225000" z="0.409000" />
        <AimOffsetMinFPSRNG x="0.120000" y="0.275000" z="0.509000" />
        <AimOffsetMaxFPSRNG x="0.138000" y="-0.212000" z="0.518000" />
        <AimOffsetMinFPSScope x="0.090000" y="0.078000" z="0.531000" />
        <AimOffsetMaxFPSScope x="0.006000" y="-0.059000" z="0.694000" />
        <TorsoAimOffset x="-1.100000" y="0.550000" />
        <TorsoCrouchedAimOffset x="0.150000" y="0.050000" />
        <LeftHandIkOffset x="0.100000" y="0.000000" z="-0.050000" />
        <ReticuleMinSizeStanding value="1.000000" />
        <ReticuleMinSizeCrouched value="1.000000" />
        <ReticuleScale value="0.000000" />
        <ReticuleStyleHash>WEAPONTYPE_SHOTGUN</ReticuleStyleHash>
        <FirstPersonReticuleStyleHash />
        <PickupHash>PICKUP_WEAPON_PUMPSHOTGUN</PickupHash>
        <MPPickupHash>PICKUP_AMMO_BULLET_MP</MPPickupHash>
        <HumanNameHash>WT_SG_PMP</HumanNameHash>
        <MovementModeConditionalIdle>MMI_2Handed</MovementModeConditionalIdle>
        <StatName>PUMP</StatName>
        <KnockdownCount value="-1" />
        <KillshotImpulseScale value="1.000000" />
        <NmShotTuningSet>Shotgun</NmShotTuningSet>
        <AttachPoints>
          <Item>
            <AttachBone>WAPClip</AttachBone>
            <Components>
              <Item>
                <Name>COMPONENT_PUMPSHOTGUN_CLIP_01</Name>
                <Default value="true" />
              </Item>
            </Components>
          </Item>
          <Item>
            <AttachBone>WAPFlshLasr</AttachBone>
            <Components>
              <Item>
                <Name>COMPONENT_AT_AR_FLSH</Name>
                <Default value="false" />
              </Item>
            </Components>
          </Item>
          <Item>
            <AttachBone>WAPSupp</AttachBone>
            <Components>
              <Item>
                <Name>COMPONENT_AT_SR_SUPP</Name>
                <Default value="false" />
              </Item>
            </Components>
          </Item>
          <Item>
            <AttachBone>gun_root</AttachBone>
            <Components>
              <Item>
                <Name>COMPONENT_PUMPSHOTGUN_VARMOD_LOWRIDER</Name>
                <Default value="false" />
              </Item>
            </Components>
          </Item>
        </AttachPoints>
        <GunFeedBone />
        <TargetSequenceGroup />
        <WeaponFlags>UsableClimbing CarriedInHand ApplyBulletForce Gun CanLockonOnFoot
          CanLockonInVehicle CanFreeAim TwoHanded AnimReload AnimCrouchFire UsableOnFoot
          UsableInCover NoLeftHandIKWhenBlocked NeedsGunCockingInCover HasLowCoverReloads
          HasLowCoverSwaps ProcessGripAnim TorsoIKForWeaponBlock LongWeapon UseFPSAimIK
          UseFPSSecondaryMotion</WeaponFlags>
        <TintSpecValues ref="TINT_DEFAULT" />
        <FiringPatternAliases ref="FIRING_PATTERN_PUMPSHOTGUN" />
        <ReloadUpperBodyFixupExpressionData ref="default" />
        <AmmoDiminishingRate value="3" />
        <AimingBreathingAdditiveWeight value="1.000000" />
        <FiringBreathingAdditiveWeight value="1.000000" />
        <StealthAimingBreathingAdditiveWeight value="1.000000" />
        <StealthFiringBreathingAdditiveWeight value="1.000000" />
        <AimingLeanAdditiveWeight value="1.000000" />
        <FiringLeanAdditiveWeight value="1.000000" />
        <StealthAimingLeanAdditiveWeight value="1.000000" />
        <StealthFiringLeanAdditiveWeight value="0.000000" />
        <ExpandPedCapsuleRadius value="0.000000" />
        <AudioCollisionHash />
        <HudDamage value="69" />
        <HudSpeed value="20" />
        <HudCapacity value="10" />
        <HudAccuracy value="30" />
        <HudRange value="43" />
      </Item>
        <Item type="CWeaponInfo">
          <Name>WEAPON_HOTDOG</Name>
          <Model>w_me_hotdog</Model>
          <Audio />
          <Slot>SLOT_BAT</Slot>
          <DamageType>MELEE</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>MELEE</FireType>
          <WheelSlot>WHEEL_UNARMED_MELEE</WheelSlot>
          <Group>GROUP_MELEE</Group>
          <AmmoInfo ref="NULL" />
          <AimingInfo ref="UNARMED_TARGETING_RESTRAINTS" />
          <ClipSize value="0" />
          <AccuracySpread value="0.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMinOverride value="-1.000000" />
          <RecoilAccuracyMax value="1.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="0.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="40.000000" />
          <HeadShotDamageModifierPlayer value="18.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <Force value="0.000000" />
          <ForceHitPed value="0.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="20.000000" />
              <ForceBack value="40.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="30.000000" />
              <ForceBack value="30.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_CLAVICLE</BoneTag>
              <ForceFront value="55.000000" />
              <ForceBack value="50.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_CLAVICLE</BoneTag>
              <ForceFront value="55.000000" />
              <ForceBack value="50.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.000000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="2250.000000" />
          <Penetration value="0.000000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="-1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.000000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_MELEE_METAL</EffectGroup>
            <FlashFx />
            <FlashFxAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <ShellFx />
            <TracerFx />
            <PedDamageHash />
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="0.000000" />
            <FlashFxChanceSP value="0.000000" />
            <FlashFxChanceMP value="0.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.000000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.000000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="0" />
          <InitialRumbleIntensity value="0.000000" />
          <InitialRumbleIntensityTrigger value="0.000000" />
          <RumbleDuration value="0" />
          <RumbleIntensity value="0.000000" />
          <RumbleIntensityTrigger value="0.000000" />
          <RumbleDamageIntensity value="1.000000" />
          <NetworkPlayerDamageModifier value="0.000000" />
          <NetworkPedDamageModifier value="0.000000" />
          <NetworkHeadShotPlayerDamageModifier value="0.000000" />
          <LockOnRange value="3.000000" />
          <WeaponRange value="1.600000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="1.600000" />
          <DamageFallOffRangeMax value="1.600000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>MELEE_AIM_CAMERA</DefaultCameraHash>
          <CoverCameraHash>MELEE_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash />
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="50.000000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash />
          <RecoilShakeHashFirstPerson />
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="1.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMin value="0.000000" />
          <AimOffsetMax x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMax value="0.000000" />
          <AimOffsetMinFPSIdle x="0.162000" y="0.225000" z="0.052000" />
          <AimOffsetMedFPSIdle x="0.187000" y="0.197000" z="0.321000" />
          <AimOffsetMaxFPSIdle x="0.155000" y="0.038000" z="0.364000" />
          <AimOffsetEndPosMinFPSIdle x="-0.284000" y="0.612000" z="-0.205000" />
          <AimOffsetEndPosMedFPSIdle x="-0.178000" y="0.639000" z="0.616000" />
          <AimOffsetEndPosMaxFPSIdle x="-0.21700" y="-0.096000" z="0.887000" />
          <AimOffsetMinFPSLT x="0.180000" y="0.231000" z="0.669000" />
          <AimOffsetMaxFPSLT x="0.048000" y="-0.225000" z="0.409000" />
          <AimOffsetMinFPSRNG x="0.120000" y="0.275000" z="0.509000" />
          <AimOffsetMaxFPSRNG x="0.138000" y="-0.212000" z="0.518000" />
          <AimOffsetMinFPSScope x="0.090000" y="0.078000" z="0.531000" />
          <AimOffsetMaxFPSScope x="0.006000" y="-0.059000" z="0.694000" />
          <TorsoAimOffset x="0.000000" y="0.000000" />
          <TorsoCrouchedAimOffset x="0.000000" y="0.000000" />
          <LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="1.000000" />
          <ReticuleStyleHash />
          <FirstPersonReticuleStyleHash />
          <PickupHash>PICKUP_WEAPON_BAT</PickupHash>
          <MPPickupHash />
          <HumanNameHash>WT_BAT</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>BAT</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>Normal</NmShotTuningSet>
          <AttachPoints />
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>CanFreeAim AllowMeleeBlock CarriedInHand CanLockonOnFoot CanLockonInVehicle TwoHanded MeleeClub UsableOnFoot UsableClimbing DoesRevivableDamage AllowCloseQuarterKills NoWheelStats UseFPSAimIK UseFPSSecondaryMotion AttachFPSLeftHandIKToRight UseFPSAimIK UseFPSSecondaryMotion</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="default" />
          <AmmoDiminishingRate value="0" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="0.000000" />
          <StealthFiringBreathingAdditiveWeight value="0.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="0.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="20" />
          <HudSpeed value="10" />
          <HudCapacity value="0" />
          <HudAccuracy value="0" />
          <HudRange value="0" />
        </Item>
        <Item type="CAmmoProjectileInfo">
          <Name>AMMO_PLASMAP</Name>
          <Model>w_pi_plasma_bullet</Model>
          <Audio />
          <Slot />
          <AmmoMax value="9999" />
          <AmmoMax50 value="9999" />
          <AmmoMax100 value="9999" />
          <AmmoMaxMP value="9999" />
          <AmmoMax50MP value="9999" />
          <AmmoMax100MP value="9999" />
          <AmmoFlags>InfiniteAmmo</AmmoFlags>
          <Damage value="1.000000" />
          <LifeTime value="100.000000" />
          <FromVehicleLifeTime value="100.000000" />
          <LifeTimeAfterImpact value="1.000000" />
          <LifeTimeAfterExplosion value="0.000000" />
          <ExplosionTime value="0.000000" />
          <LaunchSpeed value="50.000000" />
          <SeparationTime value="0.000000" />
          <TimeToReachTarget value="5.000000" />
          <Damping value="0.000000" />
          <GravityFactor value="0.000000" />
          <RicochetTolerance value="0.000000" />
          <PedRicochetTolerance value="0.000000" />
          <VehicleRicochetTolerance value="0.000000" />
          <Explosion>
            <Default>EXP_TAG_SNOWBALL</Default>
            <!--ROCKET-->
            <HitCar>EXP_TAG_SNOWBALL</HitCar>
            <!--CAR-->
            <HitTruck>EXP_TAG_SNOWBALL</HitTruck>
            <!--TRUCK-->
            <HitBike>EXP_TAG_SNOWBALL</HitBike>
            <!--BIKE-->
            <HitBoat>EXP_TAG_SNOWBALL</HitBoat>
            <!--BOAT-->
            <HitPlane>EXP_TAG_SNOWBALL</HitPlane>
            <!--PLANE-->
          </Explosion>
          <FuseFx />
          <TrailFx />
          <TrailFxUnderWater />
          <TrailFxFadeInTime value="0.000000" />
          <TrailFxFadeOutTime value="0.000000" />
          <PrimedFx />
          <DisturbFxDefault>proj_disturb_dust</DisturbFxDefault>
          <DisturbFxSand>proj_disturb_dust</DisturbFxSand>
          <DisturbFxWater>proj_disturb_dust</DisturbFxWater>
          <DisturbFxDirt>proj_disturb_dust</DisturbFxDirt>
          <DisturbFxFoliage>proj_disturb_dust</DisturbFxFoliage>
          <DisturbFxProbeDist value="0.000000" />
          <DisturbFxScale value="0.000000" />
          <LightOnlyActiveWhenStuck value="false" />
          <LightFlickers value="false" />
          <LightBone>gun_main_bone</LightBone>
          <LightColour x="0.000000" y="255.000000" z="0.000000" />
          <LightIntensity value="30.000000" />
          <LightRange value="1.000000" />
          <LightFalloffExp value="0.500000" />
          <LightFrequency value="5.000000" />
          <LightPower value="2.000000" />
          <CoronaSize value="0.500000" />
          <CoronaIntensity value="20.000000" />
          <CoronaZBias value="0.000000" />
          <ProjectileFlags>DestroyOnImpact ProcessImpacts NoPullPin ApplyDamageOnImpact AlignWithTrajectory</ProjectileFlags>
          <ForwardDragCoeff value="0.100000" />
          <SideDragCoeff value="1.800000" />
          <TimeBeforeHoming value="5.000000" />
          <TimeBeforeSwitchTargetMin value="0.000000" />
          <TimeBeforeSwitchTargetMax value="0.000000" />
          <ProximityRadius value="0.000000" />
          <PitchChangeRate value="0.000000" />
          <YawChangeRate value="0.000000" />
          <RollChangeRate value="0.000000" />
          <MaxRollAngleSin value="0.100000" />
          <ProximityActivationTime value="3.000000" />
          <ProximityTriggerRadius value="-1.000000" />
          <ProximityFuseTimePed value="1.000000" />
          <ProximityFuseTimeVehicleMin value="0.500000" />
          <ProximityFuseTimeVehicleMax value="0.050000" />
          <ProximityFuseTimeVehicleSpeed value="30.000000" />
          <ProximityLightColourUntriggered x="0.000000" y="0.000000" z="0.000000" />
          <ProximityLightFrequencyMultiplierTriggered value="4.000000" />
          <ChargedLaunchTime value="-1.000000" />
          <ChargedLaunchSpeedMult value="1.000000" />
        </Item>
      </Infos>
    </Item>
    <Item>
      <Infos>
        <Item type="CWeaponInfo">
					<Name>WEAPON_glock17</Name>
					<Model>w_pi_glock17</Model>
					<Audio>AUDIO_ITEM_COMBATPISTOL</Audio>
					<Slot>SLOT_WEAPON_glock17</Slot>
					<DamageType>BULLET</DamageType>
					<Explosion>
						<Default>DONTCARE</Default>
						<HitCar>DONTCARE</HitCar>
						<HitTruck>DONTCARE</HitTruck>
						<HitBike>DONTCARE</HitBike>
						<HitBoat>DONTCARE</HitBoat>
						<HitPlane>DONTCARE</HitPlane>
					</Explosion>
					<FireType>INSTANT_HIT</FireType>
					<WheelSlot>WHEEL_PISTOL</WheelSlot>
					<Group>GROUP_PISTOL</Group>
					<AmmoInfo ref="AMMO_PISTOL"/>
					<AimingInfo ref="PISTOL_2H_BASE_STRAFE"/>
					<ClipSize value="12"/>
          <AccuracySpread value="4.00000" />
          <AccurateModeAccuracyModifier value="0.300000" />
          <RunAndGunAccuracyModifier value="45.000000" />
          <RunAndGunAccuracyMinOverride value="-1.000000" />
          <RecoilAccuracyMax value="1.100000" />
          <RecoilErrorTime value="3.300000" />
          <RecoilRecoveryRate value="2.9000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="0.000000" />
          <MaxHeadShotDistancePlayer value="0.000000" />
          <HeadShotDamageModifierPlayer value="0.000000" />
          <Damage value="24.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.600000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.0500000" />
          <Force value="5.000000" />
          <ForceHitPed value="5.000000" />
          <ForceHitVehicle value="10.000000" />
          <ForceHitFlyingHeli value="0.000000" />
					<OverrideForces>
						<Item>
							<BoneTag>BONETAG_HEAD</BoneTag>
							<ForceFront value="100.000000"/>
							<ForceBack value="100.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_NECK</BoneTag>
							<ForceFront value="70.000000"/>
							<ForceBack value="70.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_L_THIGH</BoneTag>
							<ForceFront value="40.000000"/>
							<ForceBack value="1.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_R_THIGH</BoneTag>
							<ForceFront value="40.000000"/>
							<ForceBack value="1.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_L_CALF</BoneTag>
							<ForceFront value="70.000000"/>
							<ForceBack value="80.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_R_CALF</BoneTag>
							<ForceFront value="60.000000"/>
							<ForceBack value="100.000000"/>
						</Item>
					</OverrideForces>
					<ForceMaxStrengthMult value="1.000000"/>
					<ForceFalloffRangeStart value="0.000000"/>
					<ForceFalloffRangeEnd value="50.000000"/>
					<ForceFalloffMin value="1.000000"/>
					<ProjectileForce value="0.000000"/>
					<FragImpulse value="250.000000"/>
					<Penetration value="0.010000"/>
					<VerticalLaunchAdjustment value="0.000000"/>
					<DropForwardVelocity value="0.000000"/>
					<Speed value="2000.000000"/>
					<BulletsInBatch value="1"/>
					<BatchSpread value="0.000000"/>
					<ReloadTimeMP value="-1.000000"/>
					<ReloadTimeSP value="-1.000000"/>
					<VehicleReloadTime value="1.000000"/>
					<AnimReloadRate value="1.000000"/>
					<BulletsPerAnimLoop value="1"/>
					<TimeBetweenShots value="0.370000"/>
					<TimeLeftBetweenShotsWhereShouldFireIsCached value="0.250000"/>
					<SpinUpTime value="0.000000"/>
					<SpinTime value="0.000000"/>
					<SpinDownTime value="0.000000"/>
					<AlternateWaitTime value="-1.000000"/>
					<BulletBendingNearRadius value="0.000000"/>
					<BulletBendingFarRadius value="0.750000"/>
					<BulletBendingZoomedRadius value="0.375000"/>
					<FirstPersonBulletBendingNearRadius value="0.000000"/>
					<FirstPersonBulletBendingFarRadius value="0.750000"/>
					<FirstPersonBulletBendingZoomedRadius value="0.375000"/>
					<Fx>
						<EffectGroup>WEAPON_EFFECT_GROUP_PISTOL_SMALL</EffectGroup>
						<FlashFx>muz_pistol</FlashFx>
						<FlashFxAlt/>
						<FlashFxFP>muz_pistol_fp</FlashFxFP>
						<FlashFxAltFP/>
						<MuzzleSmokeFx>muz_smoking_barrel</MuzzleSmokeFx>
						<MuzzleSmokeFxFP>muz_smoking_barrel_fp</MuzzleSmokeFxFP>
						<MuzzleSmokeFxMinLevel value="0.300000"/>
						<MuzzleSmokeFxIncPerShot value="0.100000"/>
						<MuzzleSmokeFxDecPerSec value="0.275000"/>
						<ShellFx>eject_pistol</ShellFx>
						<ShellFxFP>eject_pistol_fp</ShellFxFP>
						<TracerFx>bullet_tracer</TracerFx>
						<PedDamageHash>BulletSmall</PedDamageHash>
						<TracerFxChanceSP value="0.200000"/>
						<TracerFxChanceMP value="1.000000"/>
						<FlashFxChanceSP value="1.000000"/>
						<FlashFxChanceMP value="1.000000"/>
						<FlashFxAltChance value="0.000000"/>
						<FlashFxScale value="0.800000"/>
						<FlashFxLightEnabled value="true"/>
						<FlashFxLightCastsShadows value="false"/>
						<FlashFxLightOffsetDist value="0.000000"/>
						<FlashFxLightRGBAMin x="255.000000" y="93.000000" z="25.000000"/>
						<FlashFxLightRGBAMax x="255.000000" y="100.000000" z="50.000000"/>
						<FlashFxLightIntensityMinMax x="1.000000" y="2.000000"/>
						<FlashFxLightRangeMinMax x="2.000000" y="2.500000"/>
						<FlashFxLightFalloffMinMax x="1024.000000" y="1536.000000"/>
						<GroundDisturbFxEnabled value="false"/>
						<GroundDisturbFxDist value="5.000000"/>
						<GroundDisturbFxNameDefault/>
						<GroundDisturbFxNameSand/>
						<GroundDisturbFxNameDirt/>
						<GroundDisturbFxNameWater/>
						<GroundDisturbFxNameFoliage/>
					</Fx>
					<InitialRumbleDuration value="160"/>
					<InitialRumbleIntensity value="0.200000"/>
					<InitialRumbleIntensityTrigger value="0.900000"/>
					<RumbleDuration value="90"/>
					<RumbleIntensity value="0.200000"/>
					<RumbleIntensityTrigger value="0.850000"/>
					<RumbleDamageIntensity value="1.000000"/>
					<InitialRumbleDurationFps value="160"/>
					<InitialRumbleIntensityFps value="1.000000"/>
					<RumbleDurationFps value="90"/>
					<RumbleIntensityFps value="1.000000"/>
					<NetworkPlayerDamageModifier value="1.000000"/>
					<NetworkPedDamageModifier value="1.000000"/>
					<NetworkHeadShotPlayerDamageModifier value="2.000000"/>
					<LockOnRange value="55.000000"/>
					<WeaponRange value="120.000000"/>
					<BulletDirectionOffsetInDegrees value="0.000000"/>
					<AiSoundRange value="-1.000000"/>
					<AiPotentialBlastEventRange value="-1.000000"/>
					<DamageFallOffRangeMin value="40.000000"/>
					<DamageFallOffRangeMax value="120.000000"/>
					<DamageFallOffModifier value="0.300000"/>
					<VehicleWeaponHash/>
					<DefaultCameraHash>DEFAULT_THIRD_PERSON_PED_AIM_CAMERA</DefaultCameraHash>
					<CoverCameraHash>DEFAULT_THIRD_PERSON_PED_AIM_IN_COVER_CAMERA</CoverCameraHash>
					<CoverReadyToFireCameraHash/>
					<RunAndGunCameraHash>DEFAULT_THIRD_PERSON_PED_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
					<CinematicShootingCameraHash>DEFAULT_THIRD_PERSON_PED_CINEMATIC_SHOOTING_CAMERA</CinematicShootingCameraHash>
					<AlternativeOrScopedCameraHash/>
					<RunAndGunAlternativeOrScopedCameraHash/>
					<CinematicShootingAlternativeOrScopedCameraHash/>
					<CameraFov value="45.000000"/>
					<FirstPersonScopeFov value="30.00000"/>
					<FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000"/>
					<FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000"/>
					<FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000"/>
					<FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000"/>
					<FirstPersonScopeOffset x="-0.00200" y="0.0000" z="0.0050"/>
					<FirstPersonScopeAttachmentOffset x="0.00000" y="0.0000" z="0.0000"/>
					<FirstPersonScopeRotationOffset x="-0.50000" y="0.0000" z="0.0000"/>
					<FirstPersonScopeAttachmentRotationOffset x="0.00000" y="0.0000" z="0.0000"/>
					<FirstPersonAsThirdPersonIdleOffset x="0.075" y="0.000000" z="0.000000"/>
					<FirstPersonAsThirdPersonRNGOffset x="-0.025" y="-0.025" z="-0.040"/>
					<FirstPersonAsThirdPersonLTOffset x="0.050" y="0.060" z="-0.0250"/>
					<FirstPersonAsThirdPersonScopeOffset x="0.070" y="0.000000" z="-0.020"/>
					<FirstPersonAsThirdPersonWeaponBlockedOffset x="000000" y="0.000000" z="00000"/>
					<FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000"/>
					<FirstPersonDofMaxNearInFocusDistance value="0.000000"/>
					<FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000"/>
					<ZoomFactorForAccurateMode value="1.300000"/>
					<RecoilShakeHash>PISTOL_RECOIL_SHAKE</RecoilShakeHash>
					<RecoilShakeHashFirstPerson>FPS_PISTOL_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
					<AccuracyOffsetShakeHash/>
					<MinTimeBetweenRecoilShakes value="150"/>
					<RecoilShakeAmplitude value="1.000000"/>
					<ExplosionShakeAmplitude value="-1.000000"/>
					<IkRecoilDisplacement value="0.005"/>
					<IkRecoilDisplacementScope value="0.005"/>
					<IkRecoilDisplacementScaleBackward value="1.0"/>
					<IkRecoilDisplacementScaleVertical value="0.4"/>
					<ReticuleHudPosition x="0.000000" y="0.000000"/>
					<AimOffsetMin x="0.225000" y="0.100000" z="0.600000"/>
					<AimProbeLengthMin value="0.375000"/>
					<AimOffsetMax x="0.170000" y="-0.200000" z="0.525000"/>
					<AimProbeLengthMax value="0.285000"/>
					<AimOffsetMinFPSIdle x="0.178000" y="0.392000" z="0.135000"/>
					<AimOffsetMedFPSIdle x="0.169000" y="0.312000" z="0.420000"/>
					<AimOffsetMaxFPSIdle x="0.187000" y="0.064000" z="0.649000"/>
					<AimOffsetMinFPSLT x="0.009000" y="0.334000" z="0.555000"/>
					<AimOffsetMaxFPSLT x="0.062000" y="-0.164000" z="0.588000"/>
					<AimOffsetMinFPSRNG x="0.114000" y="0.390000" z="0.485000"/>
					<AimOffsetMaxFPSRNG x="0.113000" y="-0.263000" z="0.586000"/>
					<AimOffsetMinFPSScope x="0.009000" y="0.421000" z="0.462000"/>
					<AimOffsetMaxFPSScope x="0.037000" y="-0.224000" z="0.639000"/>
					<AimOffsetEndPosMinFPSIdle x="0.208000" y="0.700000" z="0.003000"/>
					<AimOffsetEndPosMedFPSIdle x="0.203000" y="0.604000" z="0.553000"/>
					<AimOffsetEndPosMaxFPSIdle x="0.207000" y="-0.040000" z="0.942000"/>
					<TorsoAimOffset x="-1.300000" y="0.550000"/>
					<TorsoCrouchedAimOffset x="0.200000" y="0.050000"/>
					<LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000"/>
					<ReticuleMinSizeStanding value="0.650000"/>
					<ReticuleMinSizeCrouched value="0.550000"/>
					<ReticuleScale value="0.300000"/>
					<ReticuleStyleHash>WEAPON_PISTOL</ReticuleStyleHash>
					<FirstPersonReticuleStyleHash/>
					<PickupHash>PICKUP_WEAPON_COMBATPISTOL</PickupHash>
					<MPPickupHash>PICKUP_AMMO_BULLET_MP</MPPickupHash>
					<HumanNameHash>WEAPON_glock17</HumanNameHash>
					<MovementModeConditionalIdle>MMI_1Handed</MovementModeConditionalIdle>
					<StatName>CMBTPISTOL</StatName>
					<KnockdownCount value="3"/>
					<KillshotImpulseScale value="1.000000"/>
					<NmShotTuningSet>Normal</NmShotTuningSet>
					<AttachPoints>
						<Item>
							<AttachBone>WAPClip</AttachBone>
							<Components>
								<Item>
									<Default value="false"/>
									<Name>COMPONENT_GLOCK17_CLIP_02</Name>
								</Item>
							</Components>
						</Item>
						<Item>
							<AttachBone>WAPClip</AttachBone>
							<Components>
								<Item>
									<Default value="true"/>
									<Name>COMPONENT_GLOCK17_CLIP_01</Name>
								</Item>
							</Components>
						</Item>
						<Item>
							<AttachBone>WAPFlshLasr</AttachBone>
							<Components>
								<Item>
									<Default value="true"/>
									<Name>COMPONENT_AT_GLOCK17_FLSH</Name>
								</Item>
							</Components>
						</Item>
					</AttachPoints>
					<GunFeedBone/>
					<TargetSequenceGroup/>
					<WeaponFlags>CarriedInHand Gun CanLockonOnFoot CanLockonInVehicle CanFreeAim AnimReload AnimCrouchFire UsableOnFoot UsableClimbing UsableInCover HasLowCoverReloads HasLowCoverSwaps QuitTransitionToIdleIntroOnWeaponChange DisableLeftHandIkWhenOnFoot TorsoIKForWeaponBlock UseFPSAimIK UseFPSSecondaryMotion</WeaponFlags>
					<TintSpecValues ref="TINT_DEFAULT"/>
					<FiringPatternAliases ref="FIRING_PATTERN_PISTOL"/>
					<ReloadUpperBodyFixupExpressionData ref="default"/>
					<AmmoDiminishingRate value="3"/>
					<AimingBreathingAdditiveWeight value="1.000000"/>
					<FiringBreathingAdditiveWeight value="1.000000"/>
					<StealthAimingBreathingAdditiveWeight value="1.000000"/>
					<StealthFiringBreathingAdditiveWeight value="1.000000"/>
					<AimingLeanAdditiveWeight value="1.000000"/>
					<FiringLeanAdditiveWeight value="1.000000"/>
					<StealthAimingLeanAdditiveWeight value="1.000000"/>
					<StealthFiringLeanAdditiveWeight value="1.000000"/>
					<ExpandPedCapsuleRadius value="0.000000"/>
					<AudioCollisionHash/>
					<HudDamage value="27"/>
					<HudSpeed value="40"/>
					<HudCapacity value="10"/>
					<HudAccuracy value="50"/>
					<HudRange value="30"/>
				</Item>
        <Item type="CWeaponInfo">
          <Name>WEAPON_HUNTINGRIFLE</Name>
          <Model>w_sr_huntingrifle</Model>
          <Audio>AUDIO_ITEM_SNIPERRIFLE</Audio>
          <Slot>SLOT_WEAPON_HUNTINGRIFLE</Slot>
            <DamageType>BULLET</DamageType>
            <Explosion>
              <Default>DONTCARE</Default>
              <HitCar>DONTCARE</HitCar>
              <HitTruck>DONTCARE</HitTruck>
              <HitBike>DONTCARE</HitBike>
              <HitBoat>DONTCARE</HitBoat>
              <HitPlane>DONTCARE</HitPlane>
            </Explosion>
            <FireType>DELAYED_HIT</FireType>
            <WheelSlot>WHEEL_SNIPER</WheelSlot>
            <Group>GROUP_SNIPER</Group>
            <AmmoInfo ref="AMMO_SNIPER" />
            <AimingInfo ref="RIFLE_HI_BASE_STRAFE" />
            <ClipSize value="4" />
            <AccuracySpread value="5.000000" />
            <AccurateModeAccuracyModifier value="0.500000" />
            <RunAndGunAccuracyModifier value="2.000000" />
            <RunAndGunAccuracyMinOverride value="0.500000" />
            <RecoilAccuracyMax value="1.000000" />
            <RecoilErrorTime value="0.000000" />
            <RecoilRecoveryRate value="1.000000" />
            <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
            <MinHeadShotDistanceAI value="1000.000000" />
            <MaxHeadShotDistanceAI value="1000.000000" />
            <HeadShotDamageModifierAI value="1000.000000" />
            <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
            <MinHeadShotDistancePlayer value="5.000000" />
            <MaxHeadShotDistancePlayer value="300.000000" />
            <HeadShotDamageModifierPlayer value="1.000000" />
            <Damage value="1.000000" />
            <DamageTime value="0.000000" />
            <DamageTimeInVehicle value="0.000000" />
            <DamageTimeInVehicleHeadShot value="0.000000" />
            <HitLimbsDamageModifier value="1.000000" />
            <NetworkHitLimbsDamageModifier value="1.000000" />
            <LightlyArmouredDamageModifier value="0.750000" />
            <Force value="100.000000" />
            <ForceHitPed value="350.000000" />
            <ForceHitVehicle value="1500.000000" />
            <ForceHitFlyingHeli value="2500.000000" />
            <OverrideForces>
              <Item>
                <BoneTag>BONETAG_HEAD</BoneTag>
                <ForceFront value="100.000000" />
                <ForceBack value="50.000000" />
              </Item>
              <Item>
                <BoneTag>BONETAG_NECK</BoneTag>
                <ForceFront value="75.000000" />
                <ForceBack value="100.000000" />
              </Item>
            </OverrideForces>
            <ForceMaxStrengthMult value="1.000000" />
            <ForceFalloffRangeStart value="0.000000" />
            <ForceFalloffRangeEnd value="50.000000" />
            <ForceFalloffMin value="1.000000" />
            <ProjectileForce value="0.000000" />
            <FragImpulse value="750.000000" />
            <Penetration value="1.000000" />
            <VerticalLaunchAdjustment value="0.000000" />
            <DropForwardVelocity value="0.000000" />
            <Speed value="5000.000000" />
            <BulletsInBatch value="1" />
            <BatchSpread value="0.000000" />
            <ReloadTimeMP value="-1.000000" />
            <ReloadTimeSP value="-1.000000" />
            <VehicleReloadTime value="2.500000" />
            <AnimReloadRate value="1.000000" />
            <BulletsPerAnimLoop value="1" />
            <TimeBetweenShots value="1.567000" />
            <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
            <SpinUpTime value="0.000000" />
            <SpinTime value="0.000000" />
            <SpinDownTime value="0.000000" />
            <AlternateWaitTime value="-1.000000" />
            <BulletBendingNearRadius value="0.000000" />
            <BulletBendingFarRadius value="0.000000" />
            <BulletBendingZoomedRadius value="0.000000" />
            <Fx>
              <EffectGroup>WEAPON_EFFECT_GROUP_RIFLE_SNIPER</EffectGroup>
              <FlashFx>muz_alternate_star</FlashFx>
              <FlashFxAlt>muz_alternate_star</FlashFxAlt>
              <MuzzleSmokeFx />
              <MuzzleSmokeFxMinLevel value="0.000000" />
              <MuzzleSmokeFxIncPerShot value="0.000000" />
              <MuzzleSmokeFxDecPerSec value="0.000000" />
              <ShellFx>eject_sniper</ShellFx>
              <TracerFx>bullet_tracer</TracerFx>
              <PedDamageHash>BulletLarge</PedDamageHash>
              <TracerFxChanceSP value="0.150000" />
              <TracerFxChanceMP value="1.000000" />
              <FlashFxChanceSP value="1.000000" />
              <FlashFxChanceMP value="1.000000" />
              <FlashFxAltChance value="0.200000" />
              <FlashFxScale value="0.500000" />
              <FlashFxLightEnabled value="false" />
              <FlashFxLightCastsShadows value="true" />
              <FlashFxLightOffsetDist value="0.000000" />
              <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
              <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
              <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
              <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
              <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
              <GroundDisturbFxEnabled value="false" />
              <GroundDisturbFxDist value="5.000000" />
              <GroundDisturbFxNameDefault />
              <GroundDisturbFxNameSand />
              <GroundDisturbFxNameDirt />
              <GroundDisturbFxNameWater />
              <GroundDisturbFxNameFoliage />
            </Fx>
            <InitialRumbleDuration value="150" />
            <InitialRumbleIntensity value="0.800000" />
            <InitialRumbleIntensityTrigger value="0.850000" />
            <RumbleDuration value="150" />
            <RumbleIntensity value="0.800000" />
            <RumbleIntensityTrigger value="0.850000" />
            <RumbleDamageIntensity value="1.000000" />
            <InitialRumbleDurationFps value="	220" />
            <InitialRumbleIntensityFps value="1.000000" />
            <RumbleDurationFps value="220" />
            <RumbleIntensityFps value="1.000000" />
            <NetworkPlayerDamageModifier value="1.000000" />
            <NetworkPedDamageModifier value="1.000000" />
            <NetworkHeadShotPlayerDamageModifier value="1.000000" />
            <LockOnRange value="50.000000" />
            <WeaponRange value="1500.000000" />
            <BulletDirectionOffsetInDegrees value="0.000000" />
            <AiSoundRange value="-1.000000" />
            <AiPotentialBlastEventRange value="-1.000000" />
            <DamageFallOffRangeMin value="1500.000000" />
            <DamageFallOffRangeMax value="1500.000000" />
            <DamageFallOffModifier value="0.300000" />
            <VehicleWeaponHash />
            <DefaultCameraHash>DEFAULT_THIRD_PERSON_PED_AIM_CAMERA</DefaultCameraHash>
            <CoverCameraHash>DEFAULT_THIRD_PERSON_PED_AIM_IN_COVER_CAMERA</CoverCameraHash>
            <CoverReadyToFireCameraHash>SNIPER_FIXED_ZOOM_AIM_CAMERA</CoverReadyToFireCameraHash>
            <RunAndGunCameraHash>DEFAULT_THIRD_PERSON_PED_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
            <CinematicShootingCameraHash>DEFAULT_THIRD_PERSON_PED_CINEMATIC_SHOOTING_CAMERA</CinematicShootingCameraHash>
            <AlternativeOrScopedCameraHash />
            <RunAndGunAlternativeOrScopedCameraHash />
            <CinematicShootingAlternativeOrScopedCameraHash />
            <CameraFov value="45.000000" />
            <FirstPersonScopeOffset x="0.00000" y="0.00000" z="0.00000" />
            <FirstPersonScopeAttachmentOffset x="0.00000" y="0.120000" z="-0.015000" />
            <FirstPersonScopeRotationOffset x="0.00000" y="0.000000" z="0.000000" />
            <FirstPersonScopeAttachmentRotationOffset x="0.000000" y="0.000000" z="0.000000" />
            <FirstPersonAsThirdPersonIdleOffset x="-0.05" y="0.000000" z="-0.025" />
            <FirstPersonAsThirdPersonRNGOffset x="-0.05" y="0.000000" z="-0.025" />
            <FirstPersonAsThirdPersonLTOffset x="-0.05" y="0.000000" z="-0.025" />
            <FirstPersonAsThirdPersonScopeOffset x="0.11" y="0.000000" z="0.005" />
            <FirstPersonAsThirdPersonWeaponBlockedOffset x="-0.0500000" y="0.100000" z="-0.050000" />
            <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
            <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
            <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
            <ZoomFactorForAccurateMode value="1.000000" />
            <RecoilShakeHash>DEFAULT_FIRST_PERSON_RECOIL_SHAKE</RecoilShakeHash>
            <RecoilShakeHashFirstPerson>DEFAULT_FIRST_PERSON_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
            <AccuracyOffsetShakeHash />
            <MinTimeBetweenRecoilShakes value="150" />
            <RecoilShakeAmplitude value="0.280000" />
            <ExplosionShakeAmplitude value="-1.000000" />
            <IkRecoilDisplacement value="0.0" />
            <IkRecoilDisplacementScope value="0.0" />
            <IkRecoilDisplacementScaleBackward value="1.0" />
            <IkRecoilDisplacementScaleVertical value="0.4" />
            <ReticuleHudPosition x="0.000000" y="0.000000" />
            <AimOffsetMin x="0.125000" y="0.225000" z="0.600000" />
            <AimProbeLengthMin value="0.590000" />
            <AimOffsetMax x="0.135000" y="-0.225000" z="0.500000" />
            <AimProbeLengthMax value="0.550000" />
            <AimOffsetMinFPSIdle x="0.162000" y="0.225000" z="0.052000" />
            <AimOffsetMedFPSIdle x="0.187000" y="0.197000" z="0.321000" />
            <AimOffsetMaxFPSIdle x="0.155000" y="0.038000" z="0.364000" />
            <AimOffsetEndPosMinFPSIdle x="-0.284000" y="0.612000" z="-0.205000" />
            <AimOffsetEndPosMedFPSIdle x="-0.178000" y="0.639000" z="0.616000" />
            <AimOffsetEndPosMaxFPSIdle x="-0.21700" y="-0.096000" z="0.887000" />
            <AimOffsetMinFPSLT x="0.180000" y="0.231000" z="0.669000" />
            <AimOffsetMaxFPSLT x="0.048000" y="-0.225000" z="0.409000" />
            <AimOffsetMinFPSRNG x="0.120000" y="0.275000" z="0.509000" />
            <AimOffsetMaxFPSRNG x="0.138000" y="-0.212000" z="0.518000" />
            <AimOffsetMinFPSScope x="0.090000" y="0.078000" z="0.531000" />
            <AimOffsetMaxFPSScope x="0.006000" y="-0.059000" z="0.694000" />
            <TorsoAimOffset x="-0.340000" y="0.550000" />
            <TorsoCrouchedAimOffset x="0.160000" y="0.120000" />
            <LeftHandIkOffset x="0.100000" y="0.050000" z="0.000000" />
            <ReticuleMinSizeStanding value="0.600000" />
            <ReticuleMinSizeCrouched value="0.500000" />
            <ReticuleScale value="0.050000" />
            <ReticuleStyleHash>WEAPON_HUNTINGRIFLE</ReticuleStyleHash>
            <FirstPersonReticuleStyleHash>SNIPER_LARGE</FirstPersonReticuleStyleHash>
            <PickupHash>PICKUP_WEAPON_SNIPERRIFLE</PickupHash>
            <MPPickupHash>PICKUP_AMMO_BULLET_MP</MPPickupHash>
            <HumanNameHash>WT_SNIP_RIF</HumanNameHash>
            <MovementModeConditionalIdle>MMI_2Handed</MovementModeConditionalIdle>
            <StatName>SNIPERRFL</StatName>
            <KnockdownCount value="-1" />
            <KillshotImpulseScale value="1.000000" />
            <NmShotTuningSet>Sniper</NmShotTuningSet>
            <AttachPoints>
              <Item>
                <AttachBone>WAPClip</AttachBone>
                <Components>
                  <Item>
                    <Name>COMPONENT_SNIPERRIFLE_CLIP_01</Name>
                    <Default value="true" />
                  </Item>
                </Components>
              </Item>
              <Item>
                <AttachBone>WAPSupp</AttachBone>
                <Components>
                  <Item>
                    <Name>COMPONENT_AT_AR_SUPP_02</Name>
                    <Default value="false" />
                  </Item>
                </Components>
              </Item>
              <Item>
                <AttachBone>WAPScop</AttachBone>
                <Components>
                  <Item>
                    <Name>COMPONENT_AT_SCOPE_LARGE</Name>
                    <Default value="true" />
                  </Item>
                  <Item>
                    <Name>COMPONENT_AT_SCOPE_MAX</Name>
                    <Default value="false" />
                  </Item>
                </Components>
              </Item>
              <Item>
                <AttachBone>gun_root</AttachBone>
                <Components>
                  <Item>
                    <Name>COMPONENT_SNIPERRIFLE_VARMOD_LUXE</Name>
                    <Default value="false" />
                  </Item>
                </Components>
              </Item>
            </AttachPoints>
            <GunFeedBone />
            <TargetSequenceGroup />
            <WeaponFlags>CarriedInHand FirstPersonScope Gun CanFreeAim TwoHanded AnimReload AnimCrouchFire UsableOnFoot UsableInCover HasLowCoverReloads HasLowCoverSwaps DriveByMPOnly UseFPSAimIK UseFPSSecondaryMotion UseFPSAnimatedRecoil DisableFPSAimForScope</WeaponFlags>
            <TintSpecValues ref="TINT_DEFAULT" />
            <FiringPatternAliases ref="NULL" />
            <ReloadUpperBodyFixupExpressionData ref="default" />
            <AmmoDiminishingRate value="3" />
            <AimingBreathingAdditiveWeight value="1.000000" />
            <FiringBreathingAdditiveWeight value="1.000000" />
            <StealthAimingBreathingAdditiveWeight value="1.000000" />
            <StealthFiringBreathingAdditiveWeight value="1.000000" />
            <AimingLeanAdditiveWeight value="1.000000" />
            <FiringLeanAdditiveWeight value="1.000000" />
            <StealthAimingLeanAdditiveWeight value="1.000000" />
            <StealthFiringLeanAdditiveWeight value="1.000000" />
            <ExpandPedCapsuleRadius value="0.000000" />
            <AudioCollisionHash />
            <HudDamage value="96" />
            <HudSpeed value="25" />
            <HudCapacity value="10" />
            <HudAccuracy value="70" />
            <HudRange value="95" />
          </Item>
        <Item type="CWeaponInfo">
					<Name>WEAPON_Lucille</Name>
					<Model>w_lucille_bat</Model>
					<Audio/>
					<Slot>SLOT_WEAPON_Lucille</Slot>
					<DamageType>MELEE</DamageType>
					<Explosion>
						<Default>DONTCARE</Default>
						<HitCar>DONTCARE</HitCar>
						<HitTruck>DONTCARE</HitTruck>
						<HitBike>DONTCARE</HitBike>
						<HitBoat>DONTCARE</HitBoat>
						<HitPlane>DONTCARE</HitPlane>
					</Explosion>
					<FireType>MELEE</FireType>
					<WheelSlot>WHEEL_UNARMED_MELEE</WheelSlot>
					<Group>GROUP_MELEE</Group>
					<AmmoInfo ref="NULL"/>
					<AimingInfo ref="UNARMED_TARGETING_RESTRAINTS"/>
					<ClipSize value="0"/>
					<AccuracySpread value="0.000000"/>
					<AccurateModeAccuracyModifier value="0.500000"/>
					<RunAndGunAccuracyModifier value="2.000000"/>
					<RunAndGunAccuracyMaxModifier value="1.000000"/>
					<RecoilAccuracyMax value="1.000000"/>
					<RecoilErrorTime value="0.000000"/>
					<RecoilRecoveryRate value="1.000000"/>
					<RecoilAccuracyToAllowHeadShotAI value="1000.000000"/>
					<MinHeadShotDistanceAI value="1000.000000"/>
					<MaxHeadShotDistanceAI value="1000.000000"/>
					<HeadShotDamageModifierAI value="1000.000000"/>
					<RecoilAccuracyToAllowHeadShotPlayer value="0.175000"/>
					<MinHeadShotDistancePlayer value="5.000000"/>
					<MaxHeadShotDistancePlayer value="40.000000"/>
					<HeadShotDamageModifierPlayer value="18.000000"/>
					<Damage value="0.000000"/>
					<DamageTime value="0.000000"/>
					<DamageTimeInVehicle value="0.000000"/>
					<DamageTimeInVehicleHeadShot value="0.000000"/>
					<HitLimbsDamageModifier value="0.500000"/>
					<NetworkHitLimbsDamageModifier value="0.800000"/>
					<LightlyArmouredDamageModifier value="0.750000"/>
					<Force value="0.000000"/>
					<ForceHitPed value="150.000000"/>
					<ForceHitVehicle value="0.000000"/>
					<ForceHitFlyingHeli value="0.000000"/>
					<OverrideForces>
						<Item>
							<BoneTag>BONETAG_HEAD</BoneTag>
							<ForceFront value="20.000000"/>
							<ForceBack value="40.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_NECK</BoneTag>
							<ForceFront value="30.000000"/>
							<ForceBack value="30.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_R_CLAVICLE</BoneTag>
							<ForceFront value="55.000000"/>
							<ForceBack value="50.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_L_CLAVICLE</BoneTag>
							<ForceFront value="55.000000"/>
							<ForceBack value="50.000000"/>
						</Item>
					</OverrideForces>
					<ForceMaxStrengthMult value="1.000000"/>
					<ForceFalloffRangeStart value="0.000000"/>
					<ForceFalloffRangeEnd value="50.000000"/>
					<ForceFalloffMin value="1.000000"/>
					<ProjectileForce value="0.000000"/>
					<FragImpulse value="2250.000000"/>
					<Penetration value="0.000000"/>
					<VerticalLaunchAdjustment value="0.000000"/>
					<DropForwardVelocity value="0.000000"/>
					<Speed value="2000.000000"/>
					<BulletsInBatch value="1"/>
					<BatchSpread value="0.000000"/>
					<ReloadTimeMP value="-1.000000"/>
					<ReloadTimeSP value="-1.000000"/>
					<VehicleReloadTime value="-1.000000"/>
					<AnimReloadRate value="1.000000"/>
					<BulletsPerAnimLoop value="1"/>
					<TimeBetweenShots value="0.000000"/>
					<TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000"/>
					<SpinUpTime value="0.000000"/>
					<SpinTime value="0.000000"/>
					<SpinDownTime value="0.000000"/>
					<AlternateWaitTime value="-1.000000"/>
					<BulletBendingNearRadius value="0.000000"/>
					<BulletBendingFarRadius value="0.000000"/>
					<BulletBendingZoomedRadius value="0.000000"/>
					<Fx>
						<EffectGroup>WEAPON_EFFECT_GROUP_MELEE_METAL</EffectGroup>
						<FlashFx/>
						<FlashFxAlt/>
						<MuzzleSmokeFx/>
						<MuzzleSmokeFxMinLevel value="0.000000"/>
						<MuzzleSmokeFxIncPerShot value="0.000000"/>
						<MuzzleSmokeFxDecPerSec value="0.000000"/>
						<ShellFx/>
						<TracerFx/>
						<PedDamageHash/>
						<TracerFxChanceSP value="0.000000"/>
						<TracerFxChanceMP value="0.000000"/>
						<FlashFxChanceSP value="0.000000"/>
						<FlashFxChanceMP value="0.000000"/>
						<FlashFxAltChance value="0.000000"/>
						<FlashFxScale value="1.000000"/>
						<FlashFxLightEnabled value="false"/>
						<FlashFxLightCastsShadows value="false"/>
						<FlashFxLightOffsetDist value="0.000000"/>
						<FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000"/>
						<FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000"/>
						<FlashFxLightIntensityMinMax x="0.000000" y="0.000000"/>
						<FlashFxLightRangeMinMax x="0.000000" y="0.000000"/>
						<FlashFxLightFalloffMinMax x="0.000000" y="0.000000"/>
						<GroundDisturbFxEnabled value="false"/>
						<GroundDisturbFxDist value="5.000000"/>
						<GroundDisturbFxNameDefault/>
						<GroundDisturbFxNameSand/>
						<GroundDisturbFxNameDirt/>
						<GroundDisturbFxNameWater/>
						<GroundDisturbFxNameFoliage/>
					</Fx>
					<InitialRumbleDuration value="0"/>
					<InitialRumbleIntensity value="0.000000"/>
					<InitialRumbleIntensityTrigger value="0.000000"/>
					<RumbleDuration value="0"/>
					<RumbleIntensity value="0.000000"/>
					<RumbleIntensityTrigger value="0.000000"/>
					<RumbleDamageIntensity value="1.000000"/>
					<NetworkPlayerDamageModifier value="1.000000"/>
					<NetworkPedDamageModifier value="1.000000"/>
					<NetworkHeadShotPlayerDamageModifier value="1.000000"/>
					<LockOnRange value="7.000000"/>
					<WeaponRange value="1.600000"/>
					<BulletDirectionOffsetInDegrees value="0.000000"/>
					<AiSoundRange value="-1.000000"/>
					<AiPotentialBlastEventRange value="-1.000000"/>
					<DamageFallOffRangeMin value="1.600000"/>
					<DamageFallOffRangeMax value="1.600000"/>
					<DamageFallOffModifier value="0.300000"/>
					<VehicleWeaponHash/>
					<DefaultCameraHash>MELEE_AIM_CAMERA</DefaultCameraHash>
					<CoverCameraHash>MELEE_AIM_IN_COVER_CAMERA</CoverCameraHash>
					<CoverReadyToFireCameraHash/>
					<RunAndGunCameraHash/>
					<CinematicShootingCameraHash/>
					<AlternativeOrScopedCameraHash/>
					<RunAndGunAlternativeOrScopedCameraHash/>
					<CinematicShootingAlternativeOrScopedCameraHash/>
					<CameraFov value="50.000000"/>
					<ZoomFactorForAccurateMode value="1.000000"/>
					<RecoilShakeHash/>
					<RecoilShakeHashFirstPerson/>
					<AccuracyOffsetShakeHash/>
					<MinTimeBetweenRecoilShakes value="150"/>
					<RecoilShakeAmplitude value="1.000000"/>
					<ExplosionShakeAmplitude value="-1.000000"/>
					<ReticuleHudPosition x="0.000000" y="0.000000"/>
					<AimOffsetMin x="0.000000" y="0.000000" z="0.000000"/>
					<AimProbeLengthMin value="0.000000"/>
					<AimOffsetMax x="0.000000" y="0.000000" z="0.000000"/>
					<AimProbeLengthMax value="0.000000"/>
					<AimOffsetMinFPSIdle x="0.162000" y="0.225000" z="0.052000"/>
					<AimOffsetMedFPSIdle x="0.187000" y="0.197000" z="0.321000"/>
					<AimOffsetMaxFPSIdle x="0.155000" y="0.038000" z="0.364000"/>
					<AimOffsetEndPosMinFPSIdle x="-0.284000" y="0.612000" z="-0.205000"/>
					<AimOffsetEndPosMedFPSIdle x="-0.178000" y="0.639000" z="0.616000"/>
					<AimOffsetEndPosMaxFPSIdle x="-0.21700" y="-0.096000" z="0.887000"/>
					<AimOffsetMinFPSLT x="0.180000" y="0.231000" z="0.669000"/>
					<AimOffsetMaxFPSLT x="0.048000" y="-0.225000" z="0.409000"/>
					<AimOffsetMinFPSRNG x="0.120000" y="0.275000" z="0.509000"/>
					<AimOffsetMaxFPSRNG x="0.138000" y="-0.212000" z="0.518000"/>
					<AimOffsetMinFPSScope x="0.090000" y="0.078000" z="0.531000"/>
					<AimOffsetMaxFPSScope x="0.006000" y="-0.059000" z="0.694000"/>
					<TorsoAimOffset x="0.000000" y="0.000000"/>
					<TorsoCrouchedAimOffset x="0.000000" y="0.000000"/>
					<LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000"/>
					<ReticuleMinSizeStanding value="1.000000"/>
					<ReticuleMinSizeCrouched value="1.000000"/>
					<ReticuleScale value="1.000000"/>
					<ReticuleStyleHash/>
					<FirstPersonReticuleStyleHash/>
					<PickupHash>PICKUP_WEAPON_BAT</PickupHash>
					<MPPickupHash/>
					<HumanNameHash>WEAPON_Lucille</HumanNameHash>
					<MovementModeConditionalIdle/>
					<StatName>BAT</StatName>
					<KnockdownCount value="-1"/>
					<KillshotImpulseScale value="1.000000"/>
					<NmShotTuningSet>Normal</NmShotTuningSet>
					<AttachPoints/>
					<GunFeedBone/>
					<TargetSequenceGroup/>
					<WeaponFlags>AllowMeleeBlock CarriedInHand CanLockonOnFoot CanLockonInVehicle TwoHanded MeleeClub UsableOnFoot UsableClimbing DoesRevivableDamage AllowCloseQuarterKills NoWheelStats UseFPSAimIK UseFPSSecondaryMotion AttachFPSLeftHandIKToRight UseFPSAimIK UseFPSSecondaryMotion</WeaponFlags>
					<TintSpecValues ref="TINT_DEFAULT"/>
					<FiringPatternAliases ref="NULL"/>
					<ReloadUpperBodyFixupExpressionData ref="default"/>
					<AmmoDiminishingRate value="0"/>
					<AimingBreathingAdditiveWeight value="1.000000"/>
					<FiringBreathingAdditiveWeight value="1.000000"/>
					<StealthAimingBreathingAdditiveWeight value="0.000000"/>
					<StealthFiringBreathingAdditiveWeight value="0.000000"/>
					<AimingLeanAdditiveWeight value="1.000000"/>
					<FiringLeanAdditiveWeight value="1.000000"/>
					<StealthAimingLeanAdditiveWeight value="0.000000"/>
					<StealthFiringLeanAdditiveWeight value="0.000000"/>
					<ExpandPedCapsuleRadius value="0.000000"/>
					<AudioCollisionHash/>
					<HudDamage value="20"/>
					<HudSpeed value="10"/>
					<HudCapacity value="0"/>
					<HudAccuracy value="0"/>
					<HudRange value="0"/>
				</Item>
        <Item type="CWeaponInfo">
          <Name>WEAPON_LIGHTSABER</Name>
          <Model>W_ME_LIGHTSABER</Model>
          <Audio />
          <Slot>SLOT_LIGHTSABER</Slot>
          <DamageType>MELEE</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>MELEE</FireType>
          <WheelSlot>WHEEL_UNARMED_MELEE</WheelSlot>
          <Group>GROUP_MELEE</Group>
          <AmmoInfo ref="NULL" />
          <AimingInfo ref="UNARMED_TARGETING_RESTRAINTS" />
          <ClipSize value="0" />
          <AccuracySpread value="0.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMaxModifier value="1.000000" />
          <RecoilAccuracyMax value="1.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="40.000000" />
          <HeadShotDamageModifierPlayer value="18.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <Force value="0.000000" />
          <ForceHitPed value="110.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="75.000000" />
              <ForceBack value="85.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="60.000000" />
              <ForceBack value="60.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_CLAVICLE</BoneTag>
              <ForceFront value="90.000000" />
              <ForceBack value="100.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_CLAVICLE</BoneTag>
              <ForceFront value="90.000000" />
              <ForceBack value="100.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_SPINE3</BoneTag>
              <ForceFront value="75.000000" />
              <ForceBack value="90.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.300000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="2250.000000" />
          <Penetration value="0.000000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="-1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.000000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.000000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.000000" />
          <FirstPersonBulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_MELEE_SHARP</EffectGroup>
            <FlashFx />
            <FlashFxAlt />
            <FlashFxFP />
            <FlashFxFPAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxFP />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <ShellFx />
            <ShellFxFP />
            <TracerFx />
            <PedDamageHash />
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="0.000000" />
            <FlashFxChanceSP value="0.000000" />
            <FlashFxChanceMP value="0.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.000000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.200000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="0" />
          <InitialRumbleIntensity value="0.000000" />
          <InitialRumbleIntensityTrigger value="0.000000" />
          <RumbleDuration value="0" />
          <RumbleIntensity value="0.000000" />
          <RumbleIntensityTrigger value="0.000000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="0" />
          <InitialRumbleIntensityFps value="0.000000" />
          <RumbleDurationFps value="0" />
          <RumbleIntensityFps value="0.000000" />
          <NetworkPlayerDamageModifier value="0.000000" />
          <NetworkPedDamageModifier value="0.000000" />
          <NetworkHeadShotPlayerDamageModifier value="0.000000" />
          <LockOnRange value="10.000000" />
          <WeaponRange value="1.600000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="1.600000" />
          <DamageFallOffRangeMax value="1.600000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>MELEE_AIM_CAMERA</DefaultCameraHash>
          <CoverCameraHash>MELEE_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash />
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="50.000000" />
          <FirstPersonAimFovMin value="42.000000" />
          <FirstPersonAimFovMax value="47.000000" />
          <FirstPersonScopeFov value="0.000000" />
          <FirstPersonScopeAttachmentFov value="0.000000" />
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash />
          <RecoilShakeHashFirstPerson />
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="1.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.000000" />
          <IkRecoilDisplacementScope value="0.000000" />
          <IkRecoilDisplacementScaleBackward value="1.000000" />
          <IkRecoilDisplacementScaleVertical value="0.400000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMin value="0.000000" />
          <AimOffsetMax x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMax value="0.000000" />
          <AimOffsetMinFPSIdle x="0.178000" y="0.392000" z="0.135000" />
          <AimOffsetMedFPSIdle x="0.169000" y="0.312000" z="0.420000" />
          <AimOffsetMaxFPSIdle x="0.187000" y="0.064000" z="0.649000" />
          <AimOffsetMinFPSLT x="0.009000" y="0.334000" z="0.555000" />
          <AimOffsetMaxFPSLT x="0.062000" y="-0.164000" z="0.588000" />
          <AimOffsetMinFPSRNG x="0.114000" y="0.390000" z="0.485000" />
          <AimOffsetMaxFPSRNG x="0.113000" y="-0.263000" z="0.586000" />
          <AimOffsetMinFPSScope x="0.009000" y="0.421000" z="0.462000" />
          <AimOffsetMaxFPSScope x="0.037000" y="-0.224000" z="0.639000" />
          <AimOffsetEndPosMinFPSIdle x="0.208000" y="0.700000" z="0.003000" />
          <AimOffsetEndPosMedFPSIdle x="0.203000" y="0.604000" z="0.553000" />
          <AimOffsetEndPosMaxFPSIdle x="0.207000" y="-0.040000" z="0.942000" />
          <TorsoAimOffset x="0.000000" y="0.000000" />
          <TorsoCrouchedAimOffset x="0.000000" y="0.000000" />
          <LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="1.000000" />
          <ReticuleStyleHash />
          <FirstPersonReticuleStyleHash />
          <PickupHash />
          <MPPickupHash />
          <HumanNameHash>WT_LIGHTSABER</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>HATCHET</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>normal</NmShotTuningSet>
          <AttachPoints />
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>AllowMeleeBlock CarriedInHand CanLockonOnFoot CanLockonInVehicle MeleeClub UsableOnFoot UsableClimbing UsableInCover DoesRevivableDamage AllowCloseQuarterKills HasLowCoverSwaps NoWheelStats QuitTransitionToIdleIntroOnWeaponChange DisableLeftHandIkWhenOnFoot UseFPSAimIK UseFPSSecondaryMotion AllowMeleeBlock</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="DEFAULT" />
          <AmmoDiminishingRate value="0" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="0.000000" />
          <StealthFiringBreathingAdditiveWeight value="0.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="0.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="15" />
          <HudSpeed value="15" />
          <HudCapacity value="0" />
          <HudAccuracy value="0" />
          <HudRange value="0" />
          <VehicleAttackAngle value="25.000000" />
          <CamoDiffuseTexIdxs>
            <Item key="W_ME_LIGHTSABER">
              <Item key="0" value="1" />
              <Item key="1" value="2" />
            </Item>
          </CamoDiffuseTexIdxs>
        </Item>
        <Item type="CWeaponInfo">
          <Name>WEAPON_LIGHTSABER_BLUE</Name>
          <Model>W_ME_LIGHTSABER_BLUE</Model>
          <Audio />
          <Slot>SLOT_LIGHTSABER_BLUE</Slot>
          <DamageType>MELEE</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>MELEE</FireType>
          <WheelSlot>WHEEL_UNARMED_MELEE</WheelSlot>
          <Group>GROUP_MELEE</Group>
          <AmmoInfo ref="NULL" />
          <AimingInfo ref="UNARMED_TARGETING_RESTRAINTS" />
          <ClipSize value="0" />
          <AccuracySpread value="0.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMaxModifier value="1.000000" />
          <RecoilAccuracyMax value="1.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="40.000000" />
          <HeadShotDamageModifierPlayer value="18.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <Force value="0.000000" />
          <ForceHitPed value="110.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="75.000000" />
              <ForceBack value="85.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="60.000000" />
              <ForceBack value="60.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_CLAVICLE</BoneTag>
              <ForceFront value="90.000000" />
              <ForceBack value="100.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_CLAVICLE</BoneTag>
              <ForceFront value="90.000000" />
              <ForceBack value="100.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_SPINE3</BoneTag>
              <ForceFront value="75.000000" />
              <ForceBack value="90.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.300000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="2250.000000" />
          <Penetration value="0.000000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="-1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.000000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.000000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.000000" />
          <FirstPersonBulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_MELEE_SHARP</EffectGroup>
            <FlashFx />
            <FlashFxAlt />
            <FlashFxFP />
            <FlashFxFPAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxFP />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <ShellFx />
            <ShellFxFP />
            <TracerFx />
            <PedDamageHash />
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="0.000000" />
            <FlashFxChanceSP value="0.000000" />
            <FlashFxChanceMP value="0.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.000000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.200000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="0" />
          <InitialRumbleIntensity value="0.000000" />
          <InitialRumbleIntensityTrigger value="0.000000" />
          <RumbleDuration value="0" />
          <RumbleIntensity value="0.000000" />
          <RumbleIntensityTrigger value="0.000000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="0" />
          <InitialRumbleIntensityFps value="0.000000" />
          <RumbleDurationFps value="0" />
          <RumbleIntensityFps value="0.000000" />
          <NetworkPlayerDamageModifier value="0.000000" />
          <NetworkPedDamageModifier value="0.000000" />
          <NetworkHeadShotPlayerDamageModifier value="0.000000" />
          <LockOnRange value="10.000000" />
          <WeaponRange value="1.600000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="1.600000" />
          <DamageFallOffRangeMax value="1.600000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>MELEE_AIM_CAMERA</DefaultCameraHash>
          <CoverCameraHash>MELEE_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash />
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="50.000000" />
          <FirstPersonAimFovMin value="42.000000" />
          <FirstPersonAimFovMax value="47.000000" />
          <FirstPersonScopeFov value="0.000000" />
          <FirstPersonScopeAttachmentFov value="0.000000" />
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash />
          <RecoilShakeHashFirstPerson />
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="1.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.000000" />
          <IkRecoilDisplacementScope value="0.000000" />
          <IkRecoilDisplacementScaleBackward value="1.000000" />
          <IkRecoilDisplacementScaleVertical value="0.400000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMin value="0.000000" />
          <AimOffsetMax x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMax value="0.000000" />
          <AimOffsetMinFPSIdle x="0.178000" y="0.392000" z="0.135000" />
          <AimOffsetMedFPSIdle x="0.169000" y="0.312000" z="0.420000" />
          <AimOffsetMaxFPSIdle x="0.187000" y="0.064000" z="0.649000" />
          <AimOffsetMinFPSLT x="0.009000" y="0.334000" z="0.555000" />
          <AimOffsetMaxFPSLT x="0.062000" y="-0.164000" z="0.588000" />
          <AimOffsetMinFPSRNG x="0.114000" y="0.390000" z="0.485000" />
          <AimOffsetMaxFPSRNG x="0.113000" y="-0.263000" z="0.586000" />
          <AimOffsetMinFPSScope x="0.009000" y="0.421000" z="0.462000" />
          <AimOffsetMaxFPSScope x="0.037000" y="-0.224000" z="0.639000" />
          <AimOffsetEndPosMinFPSIdle x="0.208000" y="0.700000" z="0.003000" />
          <AimOffsetEndPosMedFPSIdle x="0.203000" y="0.604000" z="0.553000" />
          <AimOffsetEndPosMaxFPSIdle x="0.207000" y="-0.040000" z="0.942000" />
          <TorsoAimOffset x="0.000000" y="0.000000" />
          <TorsoCrouchedAimOffset x="0.000000" y="0.000000" />
          <LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="1.000000" />
          <ReticuleStyleHash />
          <FirstPersonReticuleStyleHash />
          <PickupHash />
          <MPPickupHash />
          <HumanNameHash>WT_LIGHTSABER_BLUE</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>HATCHET</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>normal</NmShotTuningSet>
          <AttachPoints />
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>AllowMeleeBlock CarriedInHand CanLockonOnFoot CanLockonInVehicle MeleeClub UsableOnFoot UsableClimbing UsableInCover DoesRevivableDamage AllowCloseQuarterKills HasLowCoverSwaps NoWheelStats QuitTransitionToIdleIntroOnWeaponChange DisableLeftHandIkWhenOnFoot UseFPSAimIK UseFPSSecondaryMotion AllowMeleeBlock</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="DEFAULT" />
          <AmmoDiminishingRate value="0" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="0.000000" />
          <StealthFiringBreathingAdditiveWeight value="0.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="0.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="15" />
          <HudSpeed value="15" />
          <HudCapacity value="0" />
          <HudAccuracy value="0" />
          <HudRange value="0" />
          <VehicleAttackAngle value="25.000000" />
          <CamoDiffuseTexIdxs>
            <Item key="W_ME_LIGHTSABER_BLUE">
              <Item key="0" value="1" />
              <Item key="1" value="2" />
            </Item>
          </CamoDiffuseTexIdxs>
        </Item>
        <Item type="CWeaponInfo">
          <Name>WEAPON_LIGHTSABER_PURPLE</Name>
          <Model>W_ME_LIGHTSABER_PURPLE</Model>
          <Audio />
          <Slot>SLOT_LIGHTSABER_PURPLE</Slot>
          <DamageType>MELEE</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>MELEE</FireType>
          <WheelSlot>WHEEL_UNARMED_MELEE</WheelSlot>
          <Group>GROUP_MELEE</Group>
          <AmmoInfo ref="NULL" />
          <AimingInfo ref="UNARMED_TARGETING_RESTRAINTS" />
          <ClipSize value="0" />
          <AccuracySpread value="0.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMaxModifier value="1.000000" />
          <RecoilAccuracyMax value="1.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="40.000000" />
          <HeadShotDamageModifierPlayer value="18.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <Force value="0.000000" />
          <ForceHitPed value="110.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="75.000000" />
              <ForceBack value="85.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="60.000000" />
              <ForceBack value="60.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_CLAVICLE</BoneTag>
              <ForceFront value="90.000000" />
              <ForceBack value="100.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_CLAVICLE</BoneTag>
              <ForceFront value="90.000000" />
              <ForceBack value="100.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_SPINE3</BoneTag>
              <ForceFront value="75.000000" />
              <ForceBack value="90.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.300000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="2250.000000" />
          <Penetration value="0.000000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="-1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.000000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.000000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.000000" />
          <FirstPersonBulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_MELEE_SHARP</EffectGroup>
            <FlashFx />
            <FlashFxAlt />
            <FlashFxFP />
            <FlashFxFPAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxFP />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <ShellFx />
            <ShellFxFP />
            <TracerFx />
            <PedDamageHash />
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="0.000000" />
            <FlashFxChanceSP value="0.000000" />
            <FlashFxChanceMP value="0.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.000000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.200000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="0" />
          <InitialRumbleIntensity value="0.000000" />
          <InitialRumbleIntensityTrigger value="0.000000" />
          <RumbleDuration value="0" />
          <RumbleIntensity value="0.000000" />
          <RumbleIntensityTrigger value="0.000000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="0" />
          <InitialRumbleIntensityFps value="0.000000" />
          <RumbleDurationFps value="0" />
          <RumbleIntensityFps value="0.000000" />
          <NetworkPlayerDamageModifier value="0.000000" />
          <NetworkPedDamageModifier value="0.000000" />
          <NetworkHeadShotPlayerDamageModifier value="0.000000" />
          <LockOnRange value="10.000000" />
          <WeaponRange value="1.600000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="1.600000" />
          <DamageFallOffRangeMax value="1.600000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>MELEE_AIM_CAMERA</DefaultCameraHash>
          <CoverCameraHash>MELEE_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash />
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="50.000000" />
          <FirstPersonAimFovMin value="42.000000" />
          <FirstPersonAimFovMax value="47.000000" />
          <FirstPersonScopeFov value="0.000000" />
          <FirstPersonScopeAttachmentFov value="0.000000" />
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash />
          <RecoilShakeHashFirstPerson />
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="1.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.000000" />
          <IkRecoilDisplacementScope value="0.000000" />
          <IkRecoilDisplacementScaleBackward value="1.000000" />
          <IkRecoilDisplacementScaleVertical value="0.400000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMin value="0.000000" />
          <AimOffsetMax x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMax value="0.000000" />
          <AimOffsetMinFPSIdle x="0.178000" y="0.392000" z="0.135000" />
          <AimOffsetMedFPSIdle x="0.169000" y="0.312000" z="0.420000" />
          <AimOffsetMaxFPSIdle x="0.187000" y="0.064000" z="0.649000" />
          <AimOffsetMinFPSLT x="0.009000" y="0.334000" z="0.555000" />
          <AimOffsetMaxFPSLT x="0.062000" y="-0.164000" z="0.588000" />
          <AimOffsetMinFPSRNG x="0.114000" y="0.390000" z="0.485000" />
          <AimOffsetMaxFPSRNG x="0.113000" y="-0.263000" z="0.586000" />
          <AimOffsetMinFPSScope x="0.009000" y="0.421000" z="0.462000" />
          <AimOffsetMaxFPSScope x="0.037000" y="-0.224000" z="0.639000" />
          <AimOffsetEndPosMinFPSIdle x="0.208000" y="0.700000" z="0.003000" />
          <AimOffsetEndPosMedFPSIdle x="0.203000" y="0.604000" z="0.553000" />
          <AimOffsetEndPosMaxFPSIdle x="0.207000" y="-0.040000" z="0.942000" />
          <TorsoAimOffset x="0.000000" y="0.000000" />
          <TorsoCrouchedAimOffset x="0.000000" y="0.000000" />
          <LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="1.000000" />
          <ReticuleStyleHash />
          <FirstPersonReticuleStyleHash />
          <PickupHash />
          <MPPickupHash />
          <HumanNameHash>WT_LIGHTSABER_PURPLE</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>HATCHET</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>normal</NmShotTuningSet>
          <AttachPoints />
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>AllowMeleeBlock CarriedInHand CanLockonOnFoot CanLockonInVehicle MeleeClub UsableOnFoot UsableClimbing UsableInCover DoesRevivableDamage AllowCloseQuarterKills HasLowCoverSwaps NoWheelStats QuitTransitionToIdleIntroOnWeaponChange DisableLeftHandIkWhenOnFoot UseFPSAimIK UseFPSSecondaryMotion AllowMeleeBlock</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="DEFAULT" />
          <AmmoDiminishingRate value="0" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="0.000000" />
          <StealthFiringBreathingAdditiveWeight value="0.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="0.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="15" />
          <HudSpeed value="15" />
          <HudCapacity value="0" />
          <HudAccuracy value="0" />
          <HudRange value="0" />
          <VehicleAttackAngle value="25.000000" />
          <CamoDiffuseTexIdxs>
            <Item key="W_ME_LIGHTSABER_PURPLE">
              <Item key="0" value="1" />
              <Item key="1" value="2" />
            </Item>
          </CamoDiffuseTexIdxs>
        </Item>
        <Item type="CWeaponInfo">
          <Name>WEAPON_LIGHTSABER_RED</Name>
          <Model>W_ME_LIGHTSABER_RED</Model>
          <Audio />
          <Slot>SLOT_LIGHTSABER_RED</Slot>
          <DamageType>MELEE</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>MELEE</FireType>
          <WheelSlot>WHEEL_UNARMED_MELEE</WheelSlot>
          <Group>GROUP_MELEE</Group>
          <AmmoInfo ref="NULL" />
          <AimingInfo ref="UNARMED_TARGETING_RESTRAINTS" />
          <ClipSize value="0" />
          <AccuracySpread value="0.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMaxModifier value="1.000000" />
          <RecoilAccuracyMax value="1.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="40.000000" />
          <HeadShotDamageModifierPlayer value="18.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <Force value="0.000000" />
          <ForceHitPed value="110.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="75.000000" />
              <ForceBack value="85.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="60.000000" />
              <ForceBack value="60.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_CLAVICLE</BoneTag>
              <ForceFront value="90.000000" />
              <ForceBack value="100.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_CLAVICLE</BoneTag>
              <ForceFront value="90.000000" />
              <ForceBack value="100.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_SPINE3</BoneTag>
              <ForceFront value="75.000000" />
              <ForceBack value="90.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.300000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="2250.000000" />
          <Penetration value="0.000000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="-1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.000000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.000000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.000000" />
          <FirstPersonBulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_MELEE_SHARP</EffectGroup>
            <FlashFx />
            <FlashFxAlt />
            <FlashFxFP />
            <FlashFxFPAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxFP />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <ShellFx />
            <ShellFxFP />
            <TracerFx />
            <PedDamageHash />
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="0.000000" />
            <FlashFxChanceSP value="0.000000" />
            <FlashFxChanceMP value="0.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.000000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.200000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="0" />
          <InitialRumbleIntensity value="0.000000" />
          <InitialRumbleIntensityTrigger value="0.000000" />
          <RumbleDuration value="0" />
          <RumbleIntensity value="0.000000" />
          <RumbleIntensityTrigger value="0.000000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="0" />
          <InitialRumbleIntensityFps value="0.000000" />
          <RumbleDurationFps value="0" />
          <RumbleIntensityFps value="0.000000" />
          <NetworkPlayerDamageModifier value="0.000000" />
          <NetworkPedDamageModifier value="0.000000" />
          <NetworkHeadShotPlayerDamageModifier value="0.000000" />
          <LockOnRange value="10.000000" />
          <WeaponRange value="1.600000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="1.600000" />
          <DamageFallOffRangeMax value="1.600000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>MELEE_AIM_CAMERA</DefaultCameraHash>
          <CoverCameraHash>MELEE_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash />
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="50.000000" />
          <FirstPersonAimFovMin value="42.000000" />
          <FirstPersonAimFovMax value="47.000000" />
          <FirstPersonScopeFov value="0.000000" />
          <FirstPersonScopeAttachmentFov value="0.000000" />
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash />
          <RecoilShakeHashFirstPerson />
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="1.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.000000" />
          <IkRecoilDisplacementScope value="0.000000" />
          <IkRecoilDisplacementScaleBackward value="1.000000" />
          <IkRecoilDisplacementScaleVertical value="0.400000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMin value="0.000000" />
          <AimOffsetMax x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMax value="0.000000" />
          <AimOffsetMinFPSIdle x="0.178000" y="0.392000" z="0.135000" />
          <AimOffsetMedFPSIdle x="0.169000" y="0.312000" z="0.420000" />
          <AimOffsetMaxFPSIdle x="0.187000" y="0.064000" z="0.649000" />
          <AimOffsetMinFPSLT x="0.009000" y="0.334000" z="0.555000" />
          <AimOffsetMaxFPSLT x="0.062000" y="-0.164000" z="0.588000" />
          <AimOffsetMinFPSRNG x="0.114000" y="0.390000" z="0.485000" />
          <AimOffsetMaxFPSRNG x="0.113000" y="-0.263000" z="0.586000" />
          <AimOffsetMinFPSScope x="0.009000" y="0.421000" z="0.462000" />
          <AimOffsetMaxFPSScope x="0.037000" y="-0.224000" z="0.639000" />
          <AimOffsetEndPosMinFPSIdle x="0.208000" y="0.700000" z="0.003000" />
          <AimOffsetEndPosMedFPSIdle x="0.203000" y="0.604000" z="0.553000" />
          <AimOffsetEndPosMaxFPSIdle x="0.207000" y="-0.040000" z="0.942000" />
          <TorsoAimOffset x="0.000000" y="0.000000" />
          <TorsoCrouchedAimOffset x="0.000000" y="0.000000" />
          <LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="1.000000" />
          <ReticuleStyleHash />
          <FirstPersonReticuleStyleHash />
          <PickupHash />
          <MPPickupHash />
          <HumanNameHash>WT_LIGHTSABER_RED</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>HATCHET</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>normal</NmShotTuningSet>
          <AttachPoints />
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>AllowMeleeBlock CarriedInHand CanLockonOnFoot CanLockonInVehicle MeleeClub UsableOnFoot UsableClimbing UsableInCover DoesRevivableDamage AllowCloseQuarterKills HasLowCoverSwaps NoWheelStats QuitTransitionToIdleIntroOnWeaponChange DisableLeftHandIkWhenOnFoot UseFPSAimIK UseFPSSecondaryMotion AllowMeleeBlock</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="DEFAULT" />
          <AmmoDiminishingRate value="0" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="0.000000" />
          <StealthFiringBreathingAdditiveWeight value="0.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="0.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="15" />
          <HudSpeed value="15" />
          <HudCapacity value="0" />
          <HudAccuracy value="0" />
          <HudRange value="0" />
          <VehicleAttackAngle value="25.000000" />
          <CamoDiffuseTexIdxs>
            <Item key="W_ME_LIGHTSABER_RED">
              <Item key="0" value="1" />
              <Item key="1" value="2" />
            </Item>
          </CamoDiffuseTexIdxs>
        </Item>
        <Item type="CWeaponInfo">
          <Name>WEAPON_LIGHTSABER_YELLOW</Name>
          <Model>W_ME_LIGHTSABER_YELLOW</Model>
          <Audio />
          <Slot>SLOT_LIGHTSABER_YELLOW</Slot>
          <DamageType>MELEE</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>MELEE</FireType>
          <WheelSlot>WHEEL_UNARMED_MELEE</WheelSlot>
          <Group>GROUP_MELEE</Group>
          <AmmoInfo ref="NULL" />
          <AimingInfo ref="UNARMED_TARGETING_RESTRAINTS" />
          <ClipSize value="0" />
          <AccuracySpread value="0.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMaxModifier value="1.000000" />
          <RecoilAccuracyMax value="1.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="40.000000" />
          <HeadShotDamageModifierPlayer value="18.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <Force value="0.000000" />
          <ForceHitPed value="110.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="75.000000" />
              <ForceBack value="85.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="60.000000" />
              <ForceBack value="60.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_CLAVICLE</BoneTag>
              <ForceFront value="90.000000" />
              <ForceBack value="100.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_CLAVICLE</BoneTag>
              <ForceFront value="90.000000" />
              <ForceBack value="100.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_SPINE3</BoneTag>
              <ForceFront value="75.000000" />
              <ForceBack value="90.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.300000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="2250.000000" />
          <Penetration value="0.000000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="-1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.000000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.000000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.000000" />
          <FirstPersonBulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_MELEE_SHARP</EffectGroup>
            <FlashFx />
            <FlashFxAlt />
            <FlashFxFP />
            <FlashFxFPAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxFP />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <ShellFx />
            <ShellFxFP />
            <TracerFx />
            <PedDamageHash />
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="0.000000" />
            <FlashFxChanceSP value="0.000000" />
            <FlashFxChanceMP value="0.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.000000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.200000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="0" />
          <InitialRumbleIntensity value="0.000000" />
          <InitialRumbleIntensityTrigger value="0.000000" />
          <RumbleDuration value="0" />
          <RumbleIntensity value="0.000000" />
          <RumbleIntensityTrigger value="0.000000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="0" />
          <InitialRumbleIntensityFps value="0.000000" />
          <RumbleDurationFps value="0" />
          <RumbleIntensityFps value="0.000000" />
          <NetworkPlayerDamageModifier value="0.000000" />
          <NetworkPedDamageModifier value="0.000000" />
          <NetworkHeadShotPlayerDamageModifier value="0.000000" />
          <LockOnRange value="10.000000" />
          <WeaponRange value="1.600000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="1.600000" />
          <DamageFallOffRangeMax value="1.600000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>MELEE_AIM_CAMERA</DefaultCameraHash>
          <CoverCameraHash>MELEE_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash />
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="50.000000" />
          <FirstPersonAimFovMin value="42.000000" />
          <FirstPersonAimFovMax value="47.000000" />
          <FirstPersonScopeFov value="0.000000" />
          <FirstPersonScopeAttachmentFov value="0.000000" />
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash />
          <RecoilShakeHashFirstPerson />
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="1.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.000000" />
          <IkRecoilDisplacementScope value="0.000000" />
          <IkRecoilDisplacementScaleBackward value="1.000000" />
          <IkRecoilDisplacementScaleVertical value="0.400000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMin value="0.000000" />
          <AimOffsetMax x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMax value="0.000000" />
          <AimOffsetMinFPSIdle x="0.178000" y="0.392000" z="0.135000" />
          <AimOffsetMedFPSIdle x="0.169000" y="0.312000" z="0.420000" />
          <AimOffsetMaxFPSIdle x="0.187000" y="0.064000" z="0.649000" />
          <AimOffsetMinFPSLT x="0.009000" y="0.334000" z="0.555000" />
          <AimOffsetMaxFPSLT x="0.062000" y="-0.164000" z="0.588000" />
          <AimOffsetMinFPSRNG x="0.114000" y="0.390000" z="0.485000" />
          <AimOffsetMaxFPSRNG x="0.113000" y="-0.263000" z="0.586000" />
          <AimOffsetMinFPSScope x="0.009000" y="0.421000" z="0.462000" />
          <AimOffsetMaxFPSScope x="0.037000" y="-0.224000" z="0.639000" />
          <AimOffsetEndPosMinFPSIdle x="0.208000" y="0.700000" z="0.003000" />
          <AimOffsetEndPosMedFPSIdle x="0.203000" y="0.604000" z="0.553000" />
          <AimOffsetEndPosMaxFPSIdle x="0.207000" y="-0.040000" z="0.942000" />
          <TorsoAimOffset x="0.000000" y="0.000000" />
          <TorsoCrouchedAimOffset x="0.000000" y="0.000000" />
          <LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="1.000000" />
          <ReticuleStyleHash />
          <FirstPersonReticuleStyleHash />
          <PickupHash />
          <MPPickupHash />
          <HumanNameHash>WT_LIGHTSABER_YELLOW</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>HATCHET</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>normal</NmShotTuningSet>
          <AttachPoints />
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>AllowMeleeBlock CarriedInHand CanLockonOnFoot CanLockonInVehicle MeleeClub UsableOnFoot UsableClimbing UsableInCover DoesRevivableDamage AllowCloseQuarterKills HasLowCoverSwaps NoWheelStats QuitTransitionToIdleIntroOnWeaponChange DisableLeftHandIkWhenOnFoot UseFPSAimIK UseFPSSecondaryMotion AllowMeleeBlock</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="DEFAULT" />
          <AmmoDiminishingRate value="0" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="0.000000" />
          <StealthFiringBreathingAdditiveWeight value="0.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="0.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="15" />
          <HudSpeed value="15" />
          <HudCapacity value="0" />
          <HudAccuracy value="0" />
          <HudRange value="0" />
          <VehicleAttackAngle value="25.000000" />
          <CamoDiffuseTexIdxs>
            <Item key="W_ME_LIGHTSABER_YELLOW">
              <Item key="0" value="1" />
              <Item key="1" value="2" />
            </Item>
          </CamoDiffuseTexIdxs>
        </Item>
        <Item type="CWeaponInfo">
          <Name>WEAPON_BEANBAG</Name>
          <Model>w_sg_beanbag</Model>
          <Audio>AUDIO_ITEM_PUMPSHOTGUN</Audio>
          <Slot>SLOT_BEANBAG</Slot>
          <DamageType>ELECTRIC</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>INSTANT_HIT</FireType>
          <WheelSlot>WHEEL_SHOTGUN</WheelSlot>
          <Group>GROUP_SHOTGUN</Group>
          <AmmoInfo ref="AMMO_STUNGUN" />
          <AimingInfo ref="RIFLE_LO_PUMP_STRAFE" />
          <ClipSize value="8" />
          <AccuracySpread value="13.000000" />
          <AccurateModeAccuracyModifier value="0.300000" />
          <RunAndGunAccuracyModifier value="45.000000" />
          <RunAndGunAccuracyMinOverride value="-1.000000" />
          <RecoilAccuracyMax value="1.500000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="2.000000" />
          <MaxHeadShotDistancePlayer value="5.000000" />
          <HeadShotDamageModifierPlayer value="18.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="1.000000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <Force value="16.000000" />
          <ForceHitPed value="50.000000" />
          <ForceHitVehicle value="350.000000" />
          <ForceHitFlyingHeli value="120.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="25.000000" />
              <ForceBack value="20.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="45.000000" />
              <ForceBack value="20.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_CLAVICLE</BoneTag>
              <ForceFront value="20.000000" />
              <ForceBack value="20.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_CLAVICLE</BoneTag>
              <ForceFront value="20.000000" />
              <ForceBack value="20.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.000000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="500.000000" />
          <Penetration value="0.010000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="8" />
          <BatchSpread value="0.08000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="1.000000" />
          <AnimReloadRate value="0.5600000" />
          <BulletsPerAnimLoop value="8" />
          <TimeBetweenShots value="0.800000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.375000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_SHOTGUN</EffectGroup>
            <FlashFx>muz_shotgun</FlashFx>
            <FlashFxAlt />
            <MuzzleSmokeFx>muz_smoking_barrel_shotgun</MuzzleSmokeFx>
            <MuzzleSmokeFxFP>muz_smoking_barrel_fp</MuzzleSmokeFxFP>
            <MuzzleSmokeFxMinLevel value="0.300000" />
            <MuzzleSmokeFxIncPerShot value="0.100000" />
            <MuzzleSmokeFxDecPerSec value="0.200000" />
            <ShellFx>eject_shotgun</ShellFx>
            <ShellFxFP>eject_shotgun_fp</ShellFxFP>
            <TracerFx />
            <PedDamageHash>ShotgunLarge</PedDamageHash>
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="1.000000" />
            <FlashFxChanceSP value="0.8000000" />
            <FlashFxChanceMP value="1.000000" />
            <FlashFxAltChance value="0.600000" />
            <FlashFxScale value="0.800000" />
            <FlashFxLightEnabled value="true" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.000000" />
            <FlashFxLightRGBAMin x="255.000000" y="93.000000" z="25.000000" />
            <FlashFxLightRGBAMax x="255.000000" y="100.000000" z="50.000000" />
            <FlashFxLightIntensityMinMax x="1.000000" y="2.000000" />
            <FlashFxLightRangeMinMax x="2.00000" y="2.500000" />
            <FlashFxLightFalloffMinMax x="1024.000000" y="1536.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="200" />
          <InitialRumbleIntensity value="1.000000" />
          <InitialRumbleIntensityTrigger value="0.950000" />
          <RumbleDuration value="200" />
          <RumbleIntensity value="1.000000" />
          <RumbleIntensityTrigger value="0.950000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="250" />
          <InitialRumbleIntensityFps value="1.000000" />
          <RumbleDurationFps value="250" />
          <RumbleIntensityFps value="0.900000" />
          <NetworkPlayerDamageModifier value="1.000000" />
          <NetworkPedDamageModifier value="1.000000" />
          <NetworkHeadShotPlayerDamageModifier value="1.000000" />
          <LockOnRange value="45.000000" />
          <WeaponRange value="15.000000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="10.000000" />
          <DamageFallOffRangeMax value="20.000000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>SHOTGUN_AIM_CAMERA</DefaultCameraHash>
          <CoverCameraHash>SHOTGUN_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash>SHOTGUN_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
          <CinematicShootingCameraHash>SHOTGUN_CINEMATIC_SHOOTING_CAMERA</CinematicShootingCameraHash>
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="45.000000" />
          <FirstPersonAimFovMin value="47.000000" />
          <FirstPersonAimFovMax value="70.000000" />
          <FirstPersonScopeFov value="40.00000"/>
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="2.000000" y="0.000000" z="2.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="1.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeOffset x="0.00000" y="0.1000" z="0.0180" />
          <FirstPersonScopeAttachmentOffset x="0.00000" y="0.0000" z="0.0150" />
          <FirstPersonScopeRotationOffset x="0.00000" y="0.0000" z="0.0000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.00000" y="0.0000" z="0.0000" />
          <FirstPersonAsThirdPersonIdleOffset x="-0.050000" y="0.000000" z="-0.050000" />
          <FirstPersonAsThirdPersonRNGOffset x="0.000000" y="0.000000" z="-0.075000" />
          <FirstPersonAsThirdPersonLTOffset x="0.020000" y="0.000000" z="-0.07500000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.040" y="-0.02500000" z="-0.07500000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="0.0000000" y="0.100000" z="-0.050000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash>SHOTGUN_RECOIL_SHAKE</RecoilShakeHash>
          <RecoilShakeHashFirstPerson>FPS_SHOTGUN_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="3.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.02" />
          <IkRecoilDisplacementScope value="0.005" />
          <IkRecoilDisplacementScaleBackward value="1.0" />
          <IkRecoilDisplacementScaleVertical value="0.4" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.225000" y="0.250500" z="0.500000" />
          <AimProbeLengthMin value="0.445000" />
          <AimOffsetMax x="0.185000" y="0.000000" z="0.525000" />
          <AimProbeLengthMax value="0.325000" />
          <AimOffsetMinFPSIdle x="0.162000" y="0.225000" z="0.052000" />
          <AimOffsetMedFPSIdle x="0.187000" y="0.197000" z="0.321000" />
          <AimOffsetMaxFPSIdle x="0.155000" y="0.038000" z="0.364000" />
          <AimOffsetEndPosMinFPSIdle x="-0.284000" y="0.612000" z="-0.205000" />
          <AimOffsetEndPosMedFPSIdle x="-0.178000" y="0.639000" z="0.616000" />
          <AimOffsetEndPosMaxFPSIdle x="-0.21700" y="-0.096000" z="0.887000" />
          <AimOffsetMinFPSLT x="0.180000" y="0.231000" z="0.669000" />
          <AimOffsetMaxFPSLT x="0.048000" y="-0.225000" z="0.409000" />
          <AimOffsetMinFPSRNG x="0.120000" y="0.275000" z="0.509000" />
          <AimOffsetMaxFPSRNG x="0.138000" y="-0.212000" z="0.518000" />
          <AimOffsetMinFPSScope x="0.090000" y="0.078000" z="0.531000" />
          <AimOffsetMaxFPSScope x="0.006000" y="-0.059000" z="0.694000" />
          <TorsoAimOffset x="-1.100000" y="0.550000" />
          <TorsoCrouchedAimOffset x="0.150000" y="0.050000" />
          <LeftHandIkOffset x="0.100000" y="0.000000" z="-0.050000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="0.000000" />
          <ReticuleStyleHash>WEAPONTYPE_SHOTGUN</ReticuleStyleHash>
          <FirstPersonReticuleStyleHash />
          <PickupHash>PICKUP_WEAPON_PUMPSHOTGUN</PickupHash>
          <MPPickupHash>PICKUP_AMMO_BULLET_MP</MPPickupHash>
          <HumanNameHash>WT_BEANBAG</HumanNameHash>
          <MovementModeConditionalIdle>MMI_2Handed</MovementModeConditionalIdle>
          <StatName>PUMP</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>Shotgun</NmShotTuningSet>
          <AttachPoints>
            <Item>
              <AttachBone>WAPClip</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_PUMPSHOTGUN_CLIP_01</Name>
                  <Default value="true" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>WAPFlshLasr</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_AT_AR_FLSH</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>WAPSupp</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_AT_SR_SUPP</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
          </AttachPoints>
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>CarriedInHand ApplyBulletForce Gun CanLockonOnFoot CanLockonInVehicle CanFreeAim TwoHanded AnimReload AnimCrouchFire UsableOnFoot UsableInCover NoLeftHandIKWhenBlocked NeedsGunCockingInCover HasLowCoverReloads HasLowCoverSwaps ProcessGripAnim TorsoIKForWeaponBlock LongWeapon UseFPSAimIK UseFPSSecondaryMotion</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="FIRING_PATTERN_PUMPSHOTGUN" />
          <ReloadUpperBodyFixupExpressionData ref="default" />
          <AmmoDiminishingRate value="3" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="1.000000" />
          <StealthFiringBreathingAdditiveWeight value="1.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="1.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="69" />
          <HudSpeed value="20" />
          <HudCapacity value="10" />
          <HudAccuracy value="30" />
          <HudRange value="40" />
        </Item>
        <Item type="CWeaponInfo">
          <Name>WEAPON_PLASMAP</Name>
          <Model>W_PI_PLASMAPISTOL</Model>
          <Audio>AUDIO_ITEM_RAYCARBINE</Audio>
          <Slot>SLOT_PLASMA</Slot>
          <DamageType>ELECTRIC</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>PROJECTILE</FireType>
          <WheelSlot>WHEEL_PISTOL</WheelSlot>
          <Group>GROUP_PISTOL</Group>
          <AmmoInfo ref="AMMO_PLASMAP" />
          <AimingInfo ref="PISTOL_2H_BASE_STRAFE" />
          <ClipSize value="317237127" />
          <AccuracySpread value="1.500000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMaxModifier value="1.000000" />
          <RecoilAccuracyMax value="1.000000" />
          <RecoilErrorTime value="3.300000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="40.000000" />
          <HeadShotDamageModifierPlayer value="1.000000" />
          <Damage value="1.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="1.000000" />
          <NetworkHitLimbsDamageModifier value="1.000000" />
          <LightlyArmouredDamageModifier value="1.000000" />
          <Force value="50.000000" />
          <ForceHitPed value="125.000000" />
          <ForceHitVehicle value="750.000000" />
          <ForceHitFlyingHeli value="750.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="100.000000" />
              <ForceBack value="80.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="60.000000" />
              <ForceBack value="70.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.000000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="250.000000" />
          <Penetration value="0.010000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.500000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.750000" />
          <BulletBendingZoomedRadius value="0.375000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.750000" />
          <FirstPersonBulletBendingZoomedRadius value="0.375000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_RIFLE_ASSAULT</EffectGroup>
            <FlashFx>muz_xs_sr_carbine</FlashFx>
            <FlashFxAlt />
            <FlashFxFP>muz_xs_sr_carbine</FlashFxFP>
            <FlashFxAltFP />
            <MuzzleSmokeFx>muz_smoking_barrel</MuzzleSmokeFx>
            <MuzzleSmokeFxFP>muz_smoking_barrel_fp</MuzzleSmokeFxFP>
            <MuzzleSmokeFxMinLevel value="0.300000" />
            <MuzzleSmokeFxIncPerShot value="0.100000" />
            <MuzzleSmokeFxDecPerSec value="0.275000" />
            <MuzzleOverrideOffset x="0.000000" y="0.000000" z="0.000000" />
            <ShellFx>eject_pistol</ShellFx>
            <ShellFxFP>eject_pistol_fp</ShellFxFP>
            <TracerFx>bullet_tracer_xs_sr</TracerFx>
            <PedDamageHash>BulletSmall</PedDamageHash>
            <TracerFxChanceSP value="0.200000" />
            <TracerFxChanceMP value="1.000000" />
            <FlashFxChanceSP value="1.000000" />
            <FlashFxChanceMP value="1.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="0.800000" />
            <FlashFxLightEnabled value="true" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.000000" />
            <FlashFxLightRGBAMin x="0.000000" y="93.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="255.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="2.000000" y="3.000000" />
            <FlashFxLightRangeMinMax x="2.000000" y="2.500000" />
            <FlashFxLightFalloffMinMax x="1024.000000" y="1536.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="150" />
          <InitialRumbleIntensity value="0.200000" />
          <InitialRumbleIntensityTrigger value="0.850000" />
          <RumbleDuration value="95" />
          <RumbleIntensity value="0.200000" />
          <RumbleIntensityTrigger value="0.800000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="150" />
          <InitialRumbleIntensityFps value="1.000000" />
          <RumbleDurationFps value="95" />
          <RumbleIntensityFps value="1.000000" />
          <NetworkPlayerDamageModifier value="1.000000" />
          <NetworkPedDamageModifier value="1.000000" />
          <NetworkHeadShotPlayerDamageModifier value="1.000000" />
          <LockOnRange value="55.000000" />
          <WeaponRange value="120.000000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="40.000000" />
          <DamageFallOffRangeMax value="120.000000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>DEFAULT_THIRD_PERSON_PED_AIM_CAMERA</DefaultCameraHash>
          <CoverCameraHash>DEFAULT_THIRD_PERSON_PED_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash>DEFAULT_THIRD_PERSON_PED_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
          <CinematicShootingCameraHash>DEFAULT_THIRD_PERSON_PED_CINEMATIC_SHOOTING_CAMERA</CinematicShootingCameraHash>
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="45.000000" />
          <FirstPersonScopeFov value="30.00000"/>
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeOffset x="0.00000" y="0.0000" z="0.0010" />
          <FirstPersonScopeAttachmentOffset x="0.00000" y="0.0000" z="0.0000" />
          <FirstPersonScopeRotationOffset x="-0.30000" y="0.0000" z="0.0000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.00000" y="0.0000" z="0.0000" />
          <FirstPersonAsThirdPersonIdleOffset x="0.075" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonRNGOffset x="-0.025" y="-0.025" z="-0.040" />
          <FirstPersonAsThirdPersonLTOffset x="0.050" y="0.060" z="-0.0250" />
          <FirstPersonAsThirdPersonScopeOffset x="0.070" y="0.000000" z="-0.020" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="000000" y="0.000000" z="000000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <ZoomFactorForAccurateMode value="1.300000" />
          <RecoilShakeHash>PISTOL_RECOIL_SHAKE</RecoilShakeHash>
          <RecoilShakeHashFirstPerson>FPS_PISTOL_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="1.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.01" />
          <IkRecoilDisplacementScope value="0.005" />
          <IkRecoilDisplacementScaleBackward value="1.0" />
          <IkRecoilDisplacementScaleVertical value="0.4" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.200000" y="0.100000" z="0.600000" />
          <AimProbeLengthMin value="0.300000" />
          <AimOffsetMax x="0.175000" y="-0.200000" z="0.500000" />
          <AimProbeLengthMax value="0.275000" />
          <AimOffsetMinFPSIdle x="0.178000" y="0.392000" z="0.135000" />
          <AimOffsetMedFPSIdle x="0.169000" y="0.312000" z="0.420000" />
          <AimOffsetMaxFPSIdle x="0.187000" y="0.064000" z="0.649000" />
          <AimOffsetMinFPSLT x="0.009000" y="0.334000" z="0.555000" />
          <AimOffsetMaxFPSLT x="0.062000" y="-0.164000" z="0.588000" />
          <AimOffsetMinFPSRNG x="0.114000" y="0.390000" z="0.485000" />
          <AimOffsetMaxFPSRNG x="0.113000" y="-0.263000" z="0.586000" />
          <AimOffsetMinFPSScope x="0.009000" y="0.421000" z="0.462000" />
          <AimOffsetMaxFPSScope x="0.037000" y="-0.224000" z="0.639000" />
          <AimOffsetEndPosMinFPSIdle x="0.208000" y="0.700000" z="0.003000" />
          <AimOffsetEndPosMedFPSIdle x="0.203000" y="0.604000" z="0.553000" />
          <AimOffsetEndPosMaxFPSIdle x="0.207000" y="-0.040000" z="0.942000" />
          <TorsoAimOffset x="-1.300000" y="0.550000" />
          <TorsoCrouchedAimOffset x="0.200000" y="0.050000" />
          <LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000" />
          <ReticuleMinSizeStanding value="0.650000" />
          <ReticuleMinSizeCrouched value="0.550000" />
          <ReticuleScale value="0.300000" />
          <ReticuleStyleHash>WEAPON_PISTOL</ReticuleStyleHash>
          <FirstPersonReticuleStyleHash />
          <PickupHash>PICKUP_WEAPON_PISTOL</PickupHash>
          <MPPickupHash>PICKUP_AMMO_BULLET_MP</MPPickupHash>
          <HumanNameHash>WT_PLASMAP</HumanNameHash>
          <MovementModeConditionalIdle>MMI_1Handed</MovementModeConditionalIdle>
          <StatName>PISTOL</StatName>
          <KnockdownCount value="3" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>Normal</NmShotTuningSet>
          <AttachPoints />
          <GunFeedBone />
          <TargetSequenceGroup>PISTOL</TargetSequenceGroup>
          <WeaponFlags>CarriedInHand Gun CanLockonOnFoot CanLockonInVehicle CanFreeAim AnimReload AnimCrouchFire UsableOnFoot UsableClimbing UsableInCover HasLowCoverReloads DisplayRechargeTimeHUD HasLowCoverSwaps QuitTransitionToIdleIntroOnWeaponChange DisableLeftHandIkWhenOnFoot TorsoIKForWeaponBlock UseFPSAimIK UseFPSSecondaryMotion</WeaponFlags>
          <TintSpecValues ref="TINT_SCIFI" />
          <FiringPatternAliases ref="FIRING_PATTERN_PISTOL" />
          <ReloadUpperBodyFixupExpressionData ref="default" />
          <AmmoDiminishingRate value="3" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="1.000000" />
          <StealthFiringBreathingAdditiveWeight value="1.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="1.000000" />
          <StealthFiringLeanAdditiveWeight value="1.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="0" />
          <HudSpeed value="40" />
          <HudCapacity value="10" />
          <HudAccuracy value="40" />
          <HudRange value="25" />
        </Item>
        <Item type="CWeaponInfo">
          <Name>WEAPON_SHANK</Name>
          <Model>w_me_shank</Model>
          <Audio />
          <Slot>SLOT_SHANK</Slot>
          <DamageType>MELEE</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>MELEE</FireType>
          <WheelSlot>WHEEL_UNARMED_MELEE</WheelSlot>
          <Group>GROUP_MELEE</Group>
          <AmmoInfo ref="NULL" />
          <AimingInfo ref="UNARMED_TARGETING_RESTRAINTS" />
          <ClipSize value="0" />
          <AccuracySpread value="0.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMaxModifier value="1.000000" />
          <RecoilAccuracyMax value="1.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="40.000000" />
          <HeadShotDamageModifierPlayer value="18.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <Force value="0.000000" />
          <ForceHitPed value="100.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces />
          <ForceMaxStrengthMult value="1.200000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="2250.000000" />
          <Penetration value="0.000000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="-1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.000000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.000000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.000000" />
          <FirstPersonBulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_MELEE_SHARP</EffectGroup>
            <FlashFx />
            <FlashFxAlt />
            <FlashFxFP />
            <FlashFxFPAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxFP />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <ShellFx />
            <ShellFxFP />
            <TracerFx />
            <PedDamageHash />
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="0.000000" />
            <FlashFxChanceSP value="0.000000" />
            <FlashFxChanceMP value="0.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.000000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.200000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="0" />
          <InitialRumbleIntensity value="0.000000" />
          <InitialRumbleIntensityTrigger value="0.000000" />
          <RumbleDuration value="0" />
          <RumbleIntensity value="0.000000" />
          <RumbleIntensityTrigger value="0.000000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="0" />
          <InitialRumbleIntensityFps value="0.000000" />
          <RumbleDurationFps value="0" />
          <RumbleIntensityFps value="0.000000" />
          <NetworkPlayerDamageModifier value="1.000000" />
          <NetworkPedDamageModifier value="1.000000" />
          <NetworkHeadShotPlayerDamageModifier value="1.000000" />
          <LockOnRange value="5.000000" />
          <WeaponRange value="1.600000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="1.600000" />
          <DamageFallOffRangeMax value="1.600000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>MELEE_AIM_CAMERA</DefaultCameraHash>
          <CoverCameraHash>MELEE_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash />
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="50.000000" />
          <FirstPersonAimFovMin value="42.000000" />
          <FirstPersonAimFovMax value="47.000000" />
          <FirstPersonScopeFov value="0.000000" />
          <FirstPersonScopeAttachmentFov value="0.000000" />
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash />
          <RecoilShakeHashFirstPerson />
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="1.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.000000" />
          <IkRecoilDisplacementScope value="0.000000" />
          <IkRecoilDisplacementScaleBackward value="1.000000" />
          <IkRecoilDisplacementScaleVertical value="0.400000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMin value="0.000000" />
          <AimOffsetMax x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMax value="0.000000" />
          <AimOffsetMinFPSIdle x="0.178000" y="0.392000" z="0.135000" />
          <AimOffsetMedFPSIdle x="0.169000" y="0.312000" z="0.420000" />
          <AimOffsetMaxFPSIdle x="0.187000" y="0.064000" z="0.649000" />
          <AimOffsetMinFPSLT x="0.009000" y="0.334000" z="0.555000" />
          <AimOffsetMaxFPSLT x="0.062000" y="-0.164000" z="0.588000" />
          <AimOffsetMinFPSRNG x="0.114000" y="0.390000" z="0.485000" />
          <AimOffsetMaxFPSRNG x="0.113000" y="-0.263000" z="0.586000" />
          <AimOffsetMinFPSScope x="0.009000" y="0.421000" z="0.462000" />
          <AimOffsetMaxFPSScope x="0.037000" y="-0.224000" z="0.639000" />
          <AimOffsetEndPosMinFPSIdle x="0.208000" y="0.700000" z="0.003000" />
          <AimOffsetEndPosMedFPSIdle x="0.203000" y="0.604000" z="0.553000" />
          <AimOffsetEndPosMaxFPSIdle x="0.207000" y="-0.040000" z="0.942000" />
          <TorsoAimOffset x="0.000000" y="0.000000" />
          <TorsoCrouchedAimOffset x="0.000000" y="0.000000" />
          <LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="1.000000" />
          <ReticuleStyleHash />
          <FirstPersonReticuleStyleHash />
          <PickupHash>PICKUP_WEAPON_DAGGER</PickupHash>
          <MPPickupHash />
          <HumanNameHash>WT_SHANK</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>DAGGER</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>normal</NmShotTuningSet>
          <AttachPoints />
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>CarriedInHand ArmourPenetrating CanLockonOnFoot CanLockonInVehicle MeleeBlade UsableOnFoot UsableUnderwater UsableClimbing UsableInCover DoesRevivableDamage AllowCloseQuarterKills HasLowCoverSwaps NoWheelStats DangerousLookingMeleeWeapon QuitTransitionToIdleIntroOnWeaponChange DisableLeftHandIkWhenOnFoot UseFPSAimIK UseFPSSecondaryMotion AllowMeleeBlock</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="DEFAULT" />
          <AmmoDiminishingRate value="0" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="0.000000" />
          <StealthFiringBreathingAdditiveWeight value="0.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="0.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="20" />
          <HudSpeed value="20" />
          <HudCapacity value="0" />
          <HudAccuracy value="0" />
          <HudRange value="0" />
          <VehicleAttackAngle value="25.000000" />
          <CamoDiffuseTexIdxs>
            <Item key="w_me_shank">
              <Item key="0" value="1" />
              <Item key="1" value="2" />
            </Item>
          </CamoDiffuseTexIdxs>
        </Item>
        <Item type="CWeaponInfo">
          <Name>WEAPON_katana</Name>
          <Model>w_me_katana</Model>
          <Audio />
          <Slot>SLOT_katana</Slot>
          <DamageType>MELEE</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>MELEE</FireType>
          <WheelSlot>WHEEL_UNARMED_MELEE</WheelSlot>
          <Group>GROUP_MELEE</Group>
          <AmmoInfo ref="NULL" />
          <AimingInfo ref="UNARMED_TARGETING_RESTRAINTS" />
          <ClipSize value="0" />
          <AccuracySpread value="0.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMinOverride value="1.000000" />
          <RecoilAccuracyMax value="1.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="40.000000" />
          <HeadShotDamageModifierPlayer value="18.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <VehicleDamageModifier value="1.000000" />
          <Force value="0.000000" />
          <ForceHitPed value="100.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="75.000000" />
              <ForceBack value="85.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="60.000000" />

              <ForceBack value="60.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_CLAVICLE</BoneTag>
              <ForceFront value="90.000000" />
              <ForceBack value="100.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_CLAVICLE</BoneTag>
              <ForceFront value="90.000000" />
              <ForceBack value="100.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_SPINE3</BoneTag>
              <ForceFront value="75.000000" />

              <ForceBack value="90.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.300000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="2250.000000" />
          <Penetration value="0.000000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="-1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.000000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.000000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.000000" />
          <FirstPersonBulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_MELEE_SHARP</EffectGroup>
            <FlashFx />
            <FlashFxAlt />
            <FlashFxFP />
            <FlashFxFPAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxFP />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <MuzzleOverrideOffset x="0.000000" y="0.000000" z="0.000000" />
            <ShellFx />
            <ShellFxFP />
            <TracerFx />
            <PedDamageHash />
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="0.000000" />
            <FlashFxChanceSP value="0.000000" />
            <FlashFxChanceMP value="0.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.000000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.200000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="0" />
          <InitialRumbleIntensity value="0.000000" />
          <InitialRumbleIntensityTrigger value="0.000000" />
          <RumbleDuration value="0" />
          <RumbleIntensity value="0.000000" />
          <RumbleIntensityTrigger value="0.000000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="0" />
          <InitialRumbleIntensityFps value="0.000000" />
          <RumbleDurationFps value="0" />
          <RumbleIntensityFps value="0.000000" />
          <NetworkPlayerDamageModifier value="1.000000" />
          <NetworkPedDamageModifier value="1.000000" />
          <NetworkHeadShotPlayerDamageModifier value="1.000000" />
          <LockOnRange value="10.000000" />
          <WeaponRange value="1.600000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="1.600000" />
          <DamageFallOffRangeMax value="1.600000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>MELEE_AIM_CAMERA</DefaultCameraHash>
          <AimCameraHash />
          <FireCameraHash />
          <CoverCameraHash>MELEE_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash />
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="50.000000" />
          <FirstPersonAimFovMin value="42.000000" />
          <FirstPersonAimFovMax value="47.000000" />
          <FirstPersonScopeFov value="0.000000" />
          <FirstPersonScopeAttachmentFov value="0.000000" />
          <FirstPersonDrivebyIKOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash />
          <RecoilShakeHashFirstPerson />
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="1.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.000000" />
          <IkRecoilDisplacementScope value="0.000000" />
          <IkRecoilDisplacementScaleBackward value="1.000000" />
          <IkRecoilDisplacementScaleVertical value="0.400000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMin value="0.000000" />
          <AimOffsetMax x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMax value="0.000000" />
          <AimOffsetMinFPSIdle x="0.178000" y="0.392000" z="0.135000" />
          <AimOffsetMedFPSIdle x="0.169000" y="0.312000" z="0.420000" />
          <AimOffsetMaxFPSIdle x="0.187000" y="0.064000" z="0.649000" />
          <AimOffsetMinFPSLT x="0.009000" y="0.334000" z="0.555000" />
          <AimOffsetMaxFPSLT x="0.062000" y="-0.164000" z="0.588000" />
          <AimOffsetMinFPSRNG x="0.114000" y="0.390000" z="0.485000" />
          <AimOffsetMaxFPSRNG x="0.113000" y="-0.263000" z="0.586000" />
          <AimOffsetMinFPSScope x="0.009000" y="0.421000" z="0.462000" />
          <AimOffsetMaxFPSScope x="0.037000" y="-0.224000" z="0.639000" />
          <AimOffsetEndPosMinFPSIdle x="0.208000" y="0.700000" z="0.003000" />
          <AimOffsetEndPosMedFPSIdle x="0.203000" y="0.604000" z="0.553000" />
          <AimOffsetEndPosMaxFPSIdle x="0.207000" y="-0.040000" z="0.942000" />
          <AimOffsetEndPosMinFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMedFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMaxFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeRadiusOverrideFPSIdle value="0.000000" />
          <AimProbeRadiusOverrideFPSIdleStealth value="0.000000" />
          <AimProbeRadiusOverrideFPSLT value="0.000000" />
          <AimProbeRadiusOverrideFPSRNG value="0.000000" />
          <AimProbeRadiusOverrideFPSScope value="0.000000" />
          <TorsoAimOffset x="0.000000" y="0.000000" />
          <TorsoCrouchedAimOffset x="0.000000" y="0.000000" />
          <LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="1.000000" />
          <ReticuleStyleHash />
          <FirstPersonReticuleStyleHash />
          <PickupHash>PICKUP_WEAPON_katana</PickupHash>
          <MPPickupHash />
          <HumanNameHash>WT_katana</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>katana</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>normal</NmShotTuningSet>
          <AttachPoints />
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>AllowMeleeBlock CarriedInHand ArmourPenetrating CanLockonOnFoot CanLockonInVehicle MeleeClub UsableOnFoot UsableClimbing UsableInCover DoesRevivableDamage AllowCloseQuarterKills HasLowCoverSwaps NoWheelStats QuitTransitionToIdleIntroOnWeaponChange DisableLeftHandIkWhenOnFoot UseFPSAimIK MeleeHatchet UseFPSSecondaryMotion AllowMeleeBlock DangerousLookingMeleeWeapon CanUseInVehMelee</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="default" />
          <AmmoDiminishingRate value="0" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="0.000000" />
          <StealthFiringBreathingAdditiveWeight value="0.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="0.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="15" />
          <HudSpeed value="15" />
          <HudCapacity value="0" />
          <HudAccuracy value="0" />
          <HudRange value="0" />
          <VehicleAttackAngle value="25.000000" />
          <TorsoIKAngleLimit value="-1.000000" />
          <MeleeRightFistTargetHealthDamageScaler value="-1.000000" />
          <AirborneAircraftLockOnMultiplier value="1.000000" />
          <ArmouredVehicleGlassDamageOverride value="-1.000000" />
          <CamoDiffuseTexIdxs>
            <Item key="w_me_katana">
              <Item value="1" key="0" />
              <Item value="2" key="1" />
            </Item>
          </CamoDiffuseTexIdxs>
          <RotateBarrelBone />
          <RotateBarrelBone2 />
        </Item>
        <Item type="CWeaponInfo">
          <Name>WEAPON_HOBBYHORSE</Name>
          <Model>w_me_hobbyhorse</Model>
          <Audio />
          <Slot>SLOT_HOBBYHORSE</Slot>
					<DamageType>MELEE</DamageType>
					<Explosion>
						<Default>DONTCARE</Default>
						<HitCar>DONTCARE</HitCar>
						<HitTruck>DONTCARE</HitTruck>
						<HitBike>DONTCARE</HitBike>
						<HitBoat>DONTCARE</HitBoat>
						<HitPlane>DONTCARE</HitPlane>
					</Explosion>
					<FireType>MELEE</FireType>
          <WheelSlot>WHEEL_UNARMED_MELEE</WheelSlot>
          <Group>GROUP_MELEE</Group>
          <AmmoInfo ref="NULL" />
          <AimingInfo ref="UNARMED_TARGETING_RESTRAINTS" />
          <ClipSize value="0" />
          <AccuracySpread value="0.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMinOverride value="-1.000000" />
          <RecoilAccuracyMax value="1.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="0.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="40.000000" />
          <HeadShotDamageModifierPlayer value="18.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <Force value="0.000000" />
          <ForceHitPed value="0.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="20.000000" />
              <ForceBack value="40.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="30.000000" />
              <ForceBack value="30.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_CLAVICLE</BoneTag>
              <ForceFront value="55.000000" />
              <ForceBack value="50.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_CLAVICLE</BoneTag>
              <ForceFront value="55.000000" />
              <ForceBack value="50.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.000000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="2250.000000" />
          <Penetration value="0.000000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="-1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.000000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_MELEE_METAL</EffectGroup>
            <FlashFx />
            <FlashFxAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <ShellFx />
            <TracerFx />
            <PedDamageHash />
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="0.000000" />
            <FlashFxChanceSP value="0.000000" />
            <FlashFxChanceMP value="0.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.000000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.000000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="0" />
          <InitialRumbleIntensity value="0.000000" />
          <InitialRumbleIntensityTrigger value="0.000000" />
          <RumbleDuration value="0" />
          <RumbleIntensity value="0.000000" />
          <RumbleIntensityTrigger value="0.000000" />
          <RumbleDamageIntensity value="1.000000" />
          <NetworkPlayerDamageModifier value="0.000000" />
          <NetworkPedDamageModifier value="0.000000" />
          <NetworkHeadShotPlayerDamageModifier value="0.000000" />
          <LockOnRange value="3.000000" />
          <WeaponRange value="1.600000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="1.600000" />
          <DamageFallOffRangeMax value="1.600000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>MELEE_AIM_CAMERA</DefaultCameraHash>
          <CoverCameraHash>MELEE_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash />
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="50.000000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash />
          <RecoilShakeHashFirstPerson />
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="1.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMin value="0.000000" />
          <AimOffsetMax x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMax value="0.000000" />
          <AimOffsetMinFPSIdle x="0.162000" y="0.225000" z="0.052000" />
          <AimOffsetMedFPSIdle x="0.187000" y="0.197000" z="0.321000" />
          <AimOffsetMaxFPSIdle x="0.155000" y="0.038000" z="0.364000" />
          <AimOffsetEndPosMinFPSIdle x="-0.284000" y="0.612000" z="-0.205000" />
          <AimOffsetEndPosMedFPSIdle x="-0.178000" y="0.639000" z="0.616000" />
          <AimOffsetEndPosMaxFPSIdle x="-0.21700" y="-0.096000" z="0.887000" />
          <AimOffsetMinFPSLT x="0.180000" y="0.231000" z="0.669000" />
          <AimOffsetMaxFPSLT x="0.048000" y="-0.225000" z="0.409000" />
          <AimOffsetMinFPSRNG x="0.120000" y="0.275000" z="0.509000" />
          <AimOffsetMaxFPSRNG x="0.138000" y="-0.212000" z="0.518000" />
          <AimOffsetMinFPSScope x="0.090000" y="0.078000" z="0.531000" />
          <AimOffsetMaxFPSScope x="0.006000" y="-0.059000" z="0.694000" />
          <TorsoAimOffset x="0.000000" y="0.000000" />
          <TorsoCrouchedAimOffset x="0.000000" y="0.000000" />
          <LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="1.000000" />
          <ReticuleStyleHash />
          <FirstPersonReticuleStyleHash />
          <PickupHash>PICKUP_WEAPON_BAT</PickupHash>
          <MPPickupHash />
          <HumanNameHash>WEAPON_HOBBYHORSE</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>BAT</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>Normal</NmShotTuningSet>
          <AttachPoints />
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>CanFreeAim AllowMeleeBlock CarriedInHand CanLockonOnFoot CanLockonInVehicle TwoHanded MeleeClub UsableOnFoot UsableClimbing DoesRevivableDamage AllowCloseQuarterKills NoWheelStats UseFPSAimIK UseFPSSecondaryMotion AttachFPSLeftHandIKToRight UseFPSAimIK UseFPSSecondaryMotion</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="default" />
          <AmmoDiminishingRate value="0" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="0.000000" />
          <StealthFiringBreathingAdditiveWeight value="0.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="0.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="20" />
          <HudSpeed value="10" />
          <HudCapacity value="0" />
          <HudAccuracy value="0" />
          <HudRange value="0" />
        </Item>
      </Infos>
    </Item>
    <Item>
      <Infos />
    </Item>
    <Item>
      <Infos />
    </Item>
  </Infos>
  <VehicleWeaponInfos />
  <Name>DLC - AKP</Name>
</CWeaponInfoBlob>
