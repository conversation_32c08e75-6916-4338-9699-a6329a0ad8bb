pHousing = P.getInstance('housing', 'housing')
pProceduralProperties = P.getInstance('blrp_core', 'proceduralproperties')

tHousing = {}
T.bindInstance('housing', tHousing)

current_house_data = nil

local render_storage = true
local render_wardrobe = true

local function calculate3dTextRenders()
  render_storage = current_house_data.flags & PRF_DISABLE_STORAGE == 0
  render_wardrobe = current_house_data.flags & PRF_DISABLE_WARDROBE == 0
end

RegisterNetEvent('core:client:syncSingleHouse', function(data)
  if not current_house_data or tonumber(current_house_data.property_id) ~= tonumber(data.id) then
    return
  end

  current_house_data.flags = data.flags

  calculate3dTextRenders()
end)

tHousing.getCurrentHouse = function()
  if not current_house_data then
    return false
  end

  return {
    property_id = current_house_data.property_id,
    business_name = current_house_data.business_name,
  }
end

exports('GetCurrentHouse', tHousing.getCurrentHouse)

-----

local houses_tracked = {}

RegisterNetEvent('core:client:syncHousing', function(houses)
  houses_tracked = houses
end)

RegisterNetEvent('core:client:syncSingleHouse', function(house)
  houses_tracked[house.id] = house
end)

exports('NearbyHouse', function()
  if current_house_data then
    return houses_tracked[current_house_data.property_id], true, current_house_data.exit_coords
  end

  local nearby = nil
  local closest = 1000
  local ped_coords = GetEntityCoords(PlayerPedId())

  for _, _house in pairs(houses_tracked) do
    if _house.door_location then
      local door_coords = vector3(_house.door_location.x, _house.door_location.y, _house.door_location.z)
      local distance = #(ped_coords - door_coords)

      if distance < closest and distance <= 2.0 then
        nearby = _house
        closest = distance
      end
    end
  end

  if nearby then
    return nearby, false, false
  end

  return false, false, false
end)

-----

exports('CurrentPropertyIsWarehouse', function()
  if not current_house_data then
    return
  end

  return current_house_data.business_id ~= nil
end)

exports('CanModifyCurrentHouseFurniture', function()
  if not current_house_data then
    return
  end

  local me = exports.blrp_core:me()

  if me.hasGroup('LEO') or me.hasGroup('Dynasty 8') then
    return true
  end

  local char_id = tonumber(me.get('id'))

  if char_id == tonumber(current_house_data.owner_character_id or 0) then
    return true
  end

  if char_id == tonumber(current_house_data.coowner_character_id or 0) then
    return true
  end

  local current_house_business = tonumber(current_house_data.business_id or 0)
  local business_perms = me.get('business_perms')

  for business_name, business_info in pairs(business_perms) do
    if tonumber(business_info._business_id) == current_house_business and business_info.warehouse then
      return true
    end

    if
      business_info._house_designer_permission and
      exports.blrp_core:HasAnyBusinessExtraFlags(business_info.extra_flags, business_info._house_designer_permission)
    then
      return true
    end
  end

  return true
end)

exports('OpenFurnitureMenu', function()
  if not current_house_data then
    return
  end

  TriggerServerEvent('blrp_housing:server:openFurnitureMenu', current_house_data.property_id)
end)

local routing_bucket_name = ''

RegisterNetEvent('core:client:instancing:routingBucketChanged', function(bucket_number, bucket_name)
  routing_bucket_name = bucket_name
end)

local furniture_target_id = nil
local furniture_target_business = false

local spawned_shell_handle = nil
spawned_furniture_data = {}
spawned_forced_furniture_data = {}
local entering_house = false
local is_dev = GlobalState.is_dev

exports('GetSpawnedFurniture', function()
  return spawned_furniture_data
end)

local disabled_zone_id = nil

exports('GetDisabledZone', function()
  return disabled_zone_id
end)

local FurnitureIPLs = {
  'bl_h_furniture_ipl',
}

local cachedIntoxication = 0

Citizen.CreateThread(function()
    while true do
      if current_house_data then
        cachedIntoxication = exports.blrp_core:getIntoxication()
      end
      Citizen.Wait(2000)
    end
end)

RegisterNetEvent('blrp_housing:client:enterHouse', function(house_data, furniture, bypass_distance_check)
  if entering_house then return end

  entering_house = true

  TriggerServerEvent('instance:enterInstance', 'property-' .. house_data.property_id, false)

  -- Wait until we're in the target routing bucket before spawning anything
  while routing_bucket_name ~= 'property-' .. house_data.property_id do
    Citizen.Wait(1)
  end

  exports.cd_easytime:PauseSync(true)

  local shell_model = Config.Shells[house_data.type].model

  while not HasModelLoaded(shell_model) do
    RequestModel(shell_model)
    Citizen.Wait(0)
  end

  shell_object = CreateObject(shell_model, house_data.shell_spawn_coords.x, house_data.shell_spawn_coords.y, house_data.shell_spawn_coords.z)

  spawned_shell_handle = shell_object


  for _, iplName in ipairs(FurnitureIPLs) do
    RequestIpl(iplName)
  end

  SetEntityHeading(shell_object, 0.0)
  FreezeEntityPosition(shell_object, true)

  DoScreenFadeOut(750)
  while not IsScreenFadedOut() do Wait(0) end

  -------------------------------------------------------------------------
  -------------------------- FURNITURE SPAWNING ---------------------------
  -------------------------------------------------------------------------

  local function loadFurniture()
    for furniture_key, furniture_data in ipairs(furniture) do
      createFurnitureSingle(house_data, furniture_data)
    end
    if Config.ForcedFurniture[house_data.type] then
      for _, furn in ipairs(Config.ForcedFurniture[house_data.type]) do
        createForcedFurnitureSingle(house_data, furn)
      end
    end
  end

  if bypass_distance_check or house_data.flags & PRF_ACCELERATE_LOADING ~= 0 then
    Citizen.CreateThread(function()
      loadFurniture()
    end)
  else
    loadFurniture()
  end

  -------------------------------------------------------------------------
  ----------------------------- END FURNITURE -----------------------------
  -------------------------------------------------------------------------

  exports.blrp_core:RemoveProceduralProperty()

  for i = 1, 25 do
    SetEntityCoords(PlayerPedId(), house_data.exit_coords.x, house_data.exit_coords.y, house_data.exit_coords.z, true, true, true, false)
    Wait(50)
  end

  while IsEntityWaitingForWorldCollision(PlayerPedId()) do
    SetEntityCoords(PlayerPedId(), house_data.exit_coords.x, house_data.exit_coords.y, house_data.exit_coords.z, true, true, true, false)
    Wait(50)
  end

  while not houses_tracked[house_data.property_id] do Wait(0) end

  DoScreenFadeIn(1500)

  entering_house = false
  current_house_data = house_data

  calculate3dTextRenders()

  local tracked_house_data = houses_tracked[house_data.property_id]

  disabled_zone_id = GetZoneAtCoords(GetEntityCoords(PlayerPedId()))

  -- Disable popzone of house (disables ambient ptfx, emitters, etc)
  SetZoneEnabled(disabled_zone_id, false)

  while current_house_data do
    ClearPedWetness(PlayerPedId())
    NetworkOverrideClockTime(0, 1, 0)
    ClearOverrideWeather()
    ClearWeatherTypePersist()
    SetWeatherTypePersist('EXTRASUNNY')
    SetWeatherTypeNow('EXTRASUNNY')
    SetWeatherTypeNowPersist('EXTRASUNNY')
    if cachedIntoxication == 0 then
      SetTimecycleModifier('blrp_house_timecycle')
    end
    SetZoneEnabled(GetZoneFromNameId('PrLog'), false) -- Remove North Yankton snow

    local player_coords = GetEntityCoords(PlayerPedId())

    if render_storage or render_wardrobe then
      if not exports.gizmo:gizmoInUse() and not exports['kossek-darts']:inDartsGame() then
        -- Storage
        if
          render_storage and
          house_data.storage_coords and
          #(player_coords - house_data.storage_coords) <= 2.0
        then
          Draw3DText(house_data.storage_coords.x, house_data.storage_coords.y, house_data.storage_coords.z, "Press ~r~[~w~E~r~]~w~ to open storage")

          if IsControlJustReleased(0, 38) then
            TriggerServerEvent('core:server:properties:useStorage', house_data.property_id)
          end
        end

        -- Closet
        if
          render_wardrobe and
          house_data.closet_coords and
          #(player_coords - house_data.closet_coords) <= 2.0
        then
          Draw3DText(house_data.closet_coords.x, house_data.closet_coords.y, house_data.closet_coords.z, "Press ~r~[~w~E~r~]~w~ to open closet")
          if IsControlJustReleased(0, 38) then
            TriggerServerEvent('core:server:properties:useCloset', house_data.property_id)
          end
        end
      end
    end

    Citizen.Wait(0)
  end

  ClearTimecycleModifier()
  exports.cd_easytime:PauseSync(false)
end)

RegisterNetEvent('blrp_housing:leaveHouse', function(exitCoord, property_id)
  local current_address = current_house_data.address

  current_house_data = nil

  -- Enable popzone of house (disables ambient ptfx, emitters, etc)
  if disabled_zone_id then
    SetZoneEnabled(disabled_zone_id, true)
    disabled_zone_id = nil
  end

  SetZoneEnabled(GetZoneFromNameId('PrLog'), true) -- Re-Add North Yankton snow

  DoScreenFadeOut(750)

  while not IsScreenFadedOut() do Wait(0) end

  FreezeEntityPosition(PlayerPedId(), true)

  Citizen.Wait(100)

  -- Delete entities
  for _, furniture in ipairs(spawned_furniture_data) do
    DeleteEntity(furniture.handle)
  end

  for _, furniture in ipairs(spawned_forced_furniture_data) do
    DeleteEntity(furniture.handle)
  end

  for _, iplName in ipairs(FurnitureIPLs) do
    RemoveIpl(iplName)
  end

  DeleteEntity(spawned_shell_handle)

  spawned_furniture_data = {}
  spawned_forced_furniture_data = {}

  -- Handle exiting into procedural
  local leave_instance = true
  local location_id, instance_id, unit_number = exports.blrp_core:DoesAddrExitToProcedural(current_address)

  if location_id and instance_id then
    leave_instance = false

    pProceduralProperties.enter({ location_id, instance_id, true })

    local new_exit_coords = exports.blrp_core:GetCoordsForProceduralUnit(unit_number)

    if new_exit_coords then
      exitCoord = new_exit_coords
    end
  end

  -- Set coordinates
  SetEntityCoords(PlayerPedId(), exitCoord.x, exitCoord.y, exitCoord.z, true, true, true, false)

  for i = 1, 25 do
    SetEntityCoords(PlayerPedId(), exitCoord.x, exitCoord.y, exitCoord.z, true, true, true, false)
    Wait(50)
  end

  while IsEntityWaitingForWorldCollision(PlayerPedId()) do
    SetEntityCoords(PlayerPedId(), exitCoord.x, exitCoord.y, exitCoord.z, true, true, true, false)
    Wait(50)
  end

  if type(exitCoord) == 'vector4' then
    SetEntityHeading(PlayerPedId(), exitCoord.w)
  end

  FreezeEntityPosition(PlayerPedId(), false)

  if leave_instance then
    TriggerServerEvent('instance:leaveInstance', 'property-' .. property_id)
  end

  DoScreenFadeIn(1500)
end)

RegisterNetEvent('blrp_housing:forceLeaveHouse', function(property_id)
  current_house_data = nil

  -- Enable popzone of house (disables ambient ptfx, emitters, etc)
  if disabled_zone_id then
    SetZoneEnabled(disabled_zone_id, true)
    disabled_zone_id = nil
  end

  SetZoneEnabled(GetZoneFromNameId('PrLog'), true) -- Re-Add North Yankton snow
  -- Delete furniture
  for _, furniture in ipairs(spawned_furniture_data) do
    DeleteEntity(furniture.handle)
  end
  spawned_furniture_data = {}
  for _, furniture in ipairs(spawned_forced_furniture_data) do
    DeleteEntity(furniture.handle)
  end
  spawned_forced_furniture_data = {}
  DeleteEntity(spawned_shell_handle)
  TriggerServerEvent('instance:leaveInstance', 'property-' .. property_id)
end)

RegisterNetEvent('blrp_housing:client:createFurnitureSingle', function(house_data, furniture_data)
  createFurnitureSingle(house_data, furniture_data)
end)

function createFurnitureSingle(house_data, furniture_data)
  local offset_vector = furniture_data.offset_vector
  local offset_rotation = furniture_data.offset_rotation

  if furniture_data.placed and offset_vector and offset_rotation then
    local model = GetHashKey(furniture_data.name_model)

    local start_time = GetGameTimer()

    while not HasModelLoaded(model) do
      RequestModel(model)
      Citizen.Wait(0)

      if GetGameTimer() - start_time >= 1500 then
        print('cancelling loading for furniture model due to timeout', furniture_data.name_model)
        break
      end
    end

    if HasModelLoaded(model) then
      local furniture_spawn_coords = house_data.shell_spawn_coords + offset_vector

      local furniture_object = CreateObject(model, furniture_spawn_coords.x, furniture_spawn_coords.y, furniture_spawn_coords.z)

      if furniture_data.tex_var then
        SetObjectTextureVariation(furniture_object, furniture_data.tex_var)
      end

      SetEntityRotation(furniture_object, offset_rotation.x, offset_rotation.y, offset_rotation.z)

      FreezeEntityPosition(furniture_object, true)
      SetEntityLodDist(furniture_object, 100)

      SetEntityCoordsNoOffset(furniture_object, furniture_spawn_coords.x, furniture_spawn_coords.y, furniture_spawn_coords.z)

      table.insert(spawned_furniture_data, {
        id = furniture_data.id,
        handle = furniture_object,
        coords = furniture_spawn_coords
      })

      local state = Entity(furniture_object).state

      if furniture_data.storage_owner then
        state.placed_furniture_has_lock = true
        state.placed_furniture_locked = furniture_data.storage_locked
      end

      state.placed_furniture_label = furniture_data.storage_label
      state.name_display = furniture_data.name_display
      state.placed_furniture_id = furniture_data.id
      state.offset_vector = offset_vector
      state.offset_rotation = offset_rotation

      SetModelAsNoLongerNeeded(model)
    end
  end
end

function createForcedFurnitureSingle(house_data, furniture_data)
  local offset_vector = furniture_data.coords
  local offset_rotation = furniture_data.rotation
  if offset_vector and offset_rotation then
    local model = GetHashKey(furniture_data.model)
    local start_time = GetGameTimer()
    while not HasModelLoaded(model) do
      RequestModel(model)
      Citizen.Wait(0)
      if GetGameTimer() - start_time >= 1500 then
        print('cancelling loading for forced furniture model due to timeout', furniture_data.model)
        break
      end
    end
    if HasModelLoaded(model) then
      local furniture_spawn_coords = house_data.shell_spawn_coords + offset_vector
      local furniture_object = CreateObject(model, furniture_spawn_coords.x, furniture_spawn_coords.y, furniture_spawn_coords.z)
      if furniture_data.tex_var then
        SetObjectTextureVariation(furniture_object, furniture_data.tex_var)
      end
      SetEntityRotation(furniture_object, offset_rotation.x, offset_rotation.y, offset_rotation.z, 2, true)
      FreezeEntityPosition(furniture_object, true)
      SetEntityLodDist(furniture_object, 100)
      SetEntityCoordsNoOffset(furniture_object, furniture_spawn_coords.x, furniture_spawn_coords.y, furniture_spawn_coords.z)

      table.insert(spawned_forced_furniture_data, {
        handle = furniture_object,
        coords = furniture_spawn_coords
      })

      -- Mark it as non-editable
      local state = Entity(furniture_object).state
      state.is_forced_furniture = true
      SetModelAsNoLongerNeeded(model)
    end
  end
end


RegisterNetEvent('blrp_housing:furniture:updateLabel', function(furniture_id, label)
  if not spawned_furniture_data then
    return
  end

  for _, furniture in pairs(spawned_furniture_data) do
    if furniture.id == furniture_id then
      local handle = furniture.handle

      if handle and DoesEntityExist(handle) then
        Entity(handle).state.placed_furniture_label = label
        break
      end
    end
  end
end)

RegisterNetEvent('blrp_housing:furniture:updateLock', function(furniture_id, locked, has_lock)
  if not spawned_furniture_data then
    return
  end

  for _, furniture in pairs(spawned_furniture_data) do
    if furniture.id == furniture_id then
      local handle = furniture.handle

      if handle and DoesEntityExist(handle) then
        local state = Entity(handle).state

        if has_lock ~= nil then
          state.placed_furniture_has_lock = has_lock
        end

        state.placed_furniture_locked = locked

        break
      end
    end
  end
end)

RegisterNetEvent('blrp_housing:client:deleteFurnitureSingle', function(furniture_data)
  deleteFurnitureSingle(furniture_data)
end)

AddEventHandler('blrp_housing:client:targetInteractFurniture', function(entity, event_data)
  if not event_data.action or not event_data.entity_hash then
    return
  end

  local state = Entity(entity).state
  local placed_furniture_id = state.placed_furniture_id
  local name_display = state.name_display
  local offset_vector = state.offset_vector
  local offset_rotation = state.offset_rotation

  if not placed_furniture_id then
    return
  end

  if event_data.action == 'toggleowner' then
    pHousing.toggleOwner({ current_house_data.property_id, placed_furniture_id })
    return
  end

  if event_data.action == 'togglelock' then
    pHousing.toggleFurnitureLock({ current_house_data.property_id, placed_furniture_id })
    return
  end

  if event_data.action == 'label' then
    pHousing.updateFurnitureLabel({ current_house_data.property_id, placed_furniture_id })
    return
  end

  if event_data.action == 'remove' and not exports.blrp_core:me().request('Do you want to pick up the '.. name_display ..'?') then
    return
  end

  if Config.storage_props[event_data.entity_hash] and not pHousing.canRemoveFurniture({ event_data.entity_hash, placed_furniture_id }) then
    exports.blrp_core:me().notify('Unable to move or pick up a storage that contains items')
    return
  end

  local success = pHousing.updateSingleFurniture({
    current_house_data.property_id,
    {
      id = placed_furniture_id,
      placed = false,
      is_removing = (event_data.action == 'remove')
    }
  })

  if event_data.action ~= 'move' then
    return
  end

  if not success then
    return
  end

  pHousing.initControlsPlaceFurniture({ current_house_data.property_id, placed_furniture_id, offset_vector, offset_rotation })
end)

function deleteFurnitureSingle(furniture_data)
  for spawned_key, spawned_furniture in ipairs(spawned_furniture_data) do
    if spawned_furniture.id == furniture_data.id then
      DeleteEntity(spawned_furniture.handle)
    end
  end
end
