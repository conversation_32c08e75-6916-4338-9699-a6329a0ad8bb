math.randomseed(os.time())

tFuel = T.getInstance('blrp_fuel', 'fuel')
tLockpick = T.getInstance('blrp_lockpick', 'LockPick')
tSurvival = T.getInstance('blrp_core', 'survival')
tUi = T.getInstance('blrp_ui', 'ui')
tVehicles = T.getInstance('blrp_vehicles', 'vehicles')
tZones = T.getInstance('blrp_zones', 'zones')
tHotwire = T.getInstance('blrp_hotwire', 'HotWire')

vehicles = {}

pVehicles = {}
P.bindInstance('vehicles', pVehicles)

pVehicles.setPrespawn = function(network_id)
  local vehicle = NetworkGetEntityFromNetworkId(network_id)

  if not vehicle or vehicle <= 0 then
    return
  end

  Entity(vehicle).state.flags = vehicle_flags.FLAG_PRESPAWN

  Citizen.Wait(50)
end

server_start_date = os.date('%Y%m%d')

if GlobalState.is_dev then
  Citizen.CreateThread(function()
    Citizen.Wait(1200)

    local players = GetPlayers()
    for _, player in pairs(players) do
      player = tonumber(player)
      TriggerEvent('blrp_vehicles:server:syncOwnedVehicles', player)
    end
  end)
end

function getCharacterRelations(character_id, key_by_cid)
  local relations = {}

  if key_by_cid then
    relations[character_id] = true
  else
    table.insert(relations, character_id)
  end

  local owned_properties = exports.blrp_core:PropertiesGetByCharacter(character_id, false)

  for _, property in pairs(owned_properties) do
    if property.flags & PRF_ENABLE_SHARED_GARAGE ~= 0 then
      for _, v in pairs({ 'owner_character_id', 'coowner_character_id' }) do
        local c_id = tonumber(property[v] or 0)

        if c_id ~= character_id and c_id ~= 0 then
          if key_by_cid then
            relations[property[v]] = true
          else
            table.insert(relations, property[v])
          end
        end
      end
    end
  end

  return relations
end

--[[

vehicle - {
  uid = (string) unique ID for vehicle,
    In the case of a player garage vehicle, the id of the DB row
    In the case of a spawned/lockpicked vehicle, a random number
  db_uid = (id) database id,
  network_id = (number) network ID,
  plate = (string) vehicle plate,
  owner_char_id = (number) owner character ID as number,
  model = (string) spawn code for vehicle,
  lockpicked = (bool),
  hotwired = (bool),
  is_local = (bool),
  chopped = (bool),
}

]]

function getVehicles(truth_test, first)
  local _vehicles = {}

  for _, vehicle_data in pairs(vehicles) do
    if truth_test(vehicle_data) then
      if first then
        return vehicle_data
      end

      table.insert(_vehicles, vehicle_data)
    end
  end

  if first then
    return false
  end

  return _vehicles
end

exports('GetVehicles', getVehicles)

function getVehicleByPlate(plate)
  for _, vehicle_data in pairs(vehicles) do
    if vehicle_data.plate == plate then
      return vehicle_data
    end
  end

  return nil
end

function getVehicleByDbUid(db_uid)
  for _, vehicle_data in pairs(vehicles) do
    if vehicle_data.db_uid == db_uid then
      return vehicle_data
    end
  end

  return nil
end

function getVehicleByNidModelPlate(network_id, model, plate)
  for _, vehicle_data in pairs(vehicles) do
    if
      vehicle_data.network_id == network_id and
      vehicle_data.plate == plate and
      GetHashKey(vehicle_data.model) == model
    then
      return vehicle_data
    end
  end

  return nil
end

exports('GetVehicleByPlate', getVehicleByPlate)
exports('GetVehicleByDbUid', getVehicleByDbUid)
exports('GetVehicleByNidModelPlate', getVehicleByNidModelPlate)

exports('UpdateVehiclePlate', function(db_uid, plate, plate_index)
  local vehicle_data = getVehicleByDbUid(db_uid)

  if not vehicle_data then
    return false, 'Unable to find target vehicle'
  end

  local affected_rows = MySQL.update.await('UPDATE vrp_user_vehicles SET registration = ?, platetype = ? WHERE id = ? LIMIT 1', {
    plate, plate_index, db_uid
  })

  if affected_rows == 0 then
    return false, 'Unable to update database'
  end

  return true
end)

function VehiclesGetEntityFromServerUid(server_uid)
  local vehicle_data = vehicles[server_uid]

  if not vehicle_data then
    return nil
  end

  local vehicle = NetworkGetEntityFromNetworkId(vehicle_data.network_id)

  if not vehicle or vehicle == 0 then
    return nil
  end

  return vehicle
end

function VehiclesDoesEntityExistWithServerUid(vehicle_id)
  if vehicles[vehicle_id] and vehicles[vehicle_id].network_id then
    local network_entity = NetworkGetEntityFromNetworkId(vehicles[vehicle_id].network_id)
    local entity_exists = DoesEntityExist(network_entity)
    local entity_actual_model = GetEntityModel(network_entity)
    local entity_supposed_model = GetHashKey(vehicles[vehicle_id].model)
    local state = Entity(network_entity).state

    if entity_exists and entity_actual_model == entity_supposed_model and state.flags and state.server_uid == vehicle_id then
      return true
    end
  end

  if vehicles[vehicle_id] and hasAnyVehicleFlags(vehicles[vehicle_id].flags, 'FLAG_CHOPPED') then
    return true
  end

  return false
end

-- Fast lookup table for anticheat bypass checks
-- Key format: "network_id:model:character_id"
local anticheat_bypass_cache = {}
-- Reverse lookup table for O(1) cleanup: network_id -> array of cache keys
local network_id_to_cache_keys = {}

-- Helper function to generate cache key
local function generateBypassKey(network_id, model, character_id)
  return tostring(network_id) .. ":" .. tostring(model) .. ":" .. tostring(character_id)
end

-- Helper function to add bypass entry to cache
local function addBypassEntry(network_id, model, character_id)
  local key = generateBypassKey(network_id, model, character_id)
  anticheat_bypass_cache[key] = true

  -- Maintain reverse lookup for O(1) cleanup
  if not network_id_to_cache_keys[network_id] then
    network_id_to_cache_keys[network_id] = {}
  end
  table.insert(network_id_to_cache_keys[network_id], key)
end

-- Helper function to remove bypass entries for a network_id (O(1) using reverse lookup)
local function removeBypassEntries(network_id)
  local keys = network_id_to_cache_keys[network_id]
  if keys then
    -- Remove all cache entries for this network_id
    for _, key in ipairs(keys) do
      anticheat_bypass_cache[key] = nil
    end
    -- Clear the reverse lookup entry
    network_id_to_cache_keys[network_id] = nil
  end
end

exports('ShouldByPassAnticheatCheck', function(network_id, model, character_id)
  local key = generateBypassKey(network_id, model, character_id)
  return anticheat_bypass_cache[key] == true
end)

-- Periodic cleanup to prevent anticheat cache from growing too large
-- Only cleans cache, NOT the vehicles table (which is needed for functionality)
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(600000) -- 10 minutes (less frequent to be safer)

    -- Clean up cache entries for entities that no longer exist in the world
    local cleaned_count = 0
    local network_ids_to_clean = {}

    -- First pass: identify network_ids that need cleanup
    for network_id, _ in pairs(network_id_to_cache_keys) do
      local entity = NetworkGetEntityFromNetworkId(network_id)
      if not DoesEntityExist(entity) then
        table.insert(network_ids_to_clean, network_id)
      end
    end

    -- Second pass: clean up using our efficient removeBypassEntries function
    for _, network_id in ipairs(network_ids_to_clean) do
      local keys = network_id_to_cache_keys[network_id]
      if keys then
        cleaned_count = cleaned_count + #keys
        removeBypassEntries(network_id)
      end
    end

    if cleaned_count > 0 then
      print('[blrp_vehicles] Cleaned up ' .. cleaned_count .. ' stale anticheat cache entries')
    end
  end
end)

RegisterNetEvent('blrp_vehicles:server:getVehicleData', function(server_uid, callback)
  if not vehicles[server_uid] then
    callback(false)
    return
  end

  callback(vehicles[server_uid])
end)

local function forceMission(vehicle)
  if not DoesEntityExist(vehicle) then
    return
  end

  local owner = NetworkGetEntityOwner(vehicle)

  tVehicles.forceMission(owner, { NetworkGetNetworkIdFromEntity(vehicle) })
end

RegisterNetEvent('blrp_vehicles:server:registerLockpickedVehicle', function(network_id, vehicle_data, player)
  if not player then
    player = source
  end

  local character = exports.blrp_core:character(player)
  local vehicle = NetworkGetEntityFromNetworkId(network_id)
  local state = Entity(vehicle).state

  local population_type = GetEntityPopulationType(vehicle)

  if population_type ~= 7 then
    forceMission(vehicle)
  end

  local uid = nil

  local flags_array = {}

  if not state.flags then
    uid = generateUidDeconflicted()

    table.insert(flags_array, 'FLAG_SERVER_MANAGED')
    table.insert(flags_array, 'FLAG_LOCAL')
    state.server_uid = uid
    state.owner_char_id = tonumber(character.get('id'))
    state.model = vehicle_data.model
  else
    flags_array = getVehicleFlags(state.flags)

    uid = state.server_uid
  end

  if not vehicles[uid] then
    local char_id = tonumber(character.get('id'))
    vehicles[uid] = {
      uid = uid,
      network_id = network_id,
      plate = vehicle_data.plate,
      owner_char_id = char_id,
      model = vehicle_data.model,
      is_local = true,
    }

    -- Add to anticheat bypass cache
    local model_hash = GetHashKey(vehicle_data.model)
    addBypassEntry(network_id, model_hash, char_id)
  end

  if vehicle_data.flag_stolen then
    local delay = 1000

    if vehicle_data.flag_stolen_delay then
      delay = math.random(20, 35) * 60 * 1000
    end

    SetTimeout(delay, function()
      exports.blrp_tablet:SetPlateStolen(vehicle_data.plate)
    end)
  end

  local player_coords = GetEntityCoords(GetPlayerPed(character.source))

  table.insert(flags_array, 'FLAG_LOCKPICKED')

  vehicles[uid].stolen_at_coords = player_coords

  vehicles[uid].flags = makeVehicleFlags(table.unpack(flags_array))
  state.flags = vehicles[uid].flags

  TriggerEvent('blrp_vehicles:lockpicked', network_id, character.source)

  if vehicle_data.slimjim then
    character.log('TOWING', 'Lockpicked vehicle with slimjim / plate = ' .. vehicle_data.plate .. ' / coords = ' .. player_coords)
  end

  exports.blrp_core:processCriminalityTowardVehicle(network_id, vehicle_data, player)
  print('[blrp_vehicles] Registering lockpicked vehicle', json.encode(vehicles[uid]))
end)

AddEventHandler('blrp_vehicles:server:registerHotwiredVehicle', function(network_id, vehicle_data, player)
  if not player then
    player = source
  end

  local character = exports.blrp_core:character(player)
  local vehicle = NetworkGetEntityFromNetworkId(network_id)
  local state = Entity(vehicle).state

  local population_type = GetEntityPopulationType(vehicle)

  if population_type ~= 7 then
    forceMission(vehicle)
  end

  local uid = nil

  local flags_array = {}

  if not state.flags then
    uid = generateUidDeconflicted()

    table.insert(flags_array, 'FLAG_SERVER_MANAGED')
    table.insert(flags_array, 'FLAG_LOCAL')
    state.server_uid = uid
    state.owner_char_id = tonumber(character.get('id'))
    state.model = vehicle_data.model
  else
    flags_array = getVehicleFlags(state.flags)

    uid = state.server_uid
  end

  if not vehicles[uid] then
    local char_id = tonumber(character.get('id'))
    vehicles[uid] = {
      uid = uid,
      network_id = network_id,
      plate = vehicle_data.plate,
      owner_char_id = char_id,
      model = vehicle_data.model,
      is_local = true,
    }

    -- Add to anticheat bypass cache
    local model_hash = GetHashKey(vehicle_data.model)
    addBypassEntry(network_id, model_hash, char_id)
  end

  table.insert(flags_array, 'FLAG_HOTWIRED')

  vehicles[uid].flags = makeVehicleFlags(table.unpack(flags_array))
  state.flags = vehicles[uid].flags

  TriggerEvent('blrp_vehicles:hotwired', network_id, character.source)
  exports.blrp_core:processCriminalityTowardVehicle(network_id, vehicle_data, player)
  print('[blrp_vehicles] Registering hotwired vehicle', json.encode(vehicles[uid]))
end)

AddEventHandler('blrp_vehicles:server:registerChoppedVehicle', function(network_id)
  local vehicle = NetworkGetEntityFromNetworkId(network_id)
  local state = Entity(vehicle).state

  local uid = nil

  local flags_array = {}

  if not state.flags then
    return
  else
    flags_array = getVehicleFlags(state.flags)

    uid = state.server_uid
  end

  table.insert(flags_array, 'FLAG_CHOPPED')

  vehicles[uid].flags = makeVehicleFlags(table.unpack(flags_array))
  state.flags = vehicles[uid].flags

  print('[blrp_vehicles] Registering chopped vehicle', json.encode(vehicles[uid]))
end)

local non_automobile_categories = {
  ['boats'] = true,
  ['emergencyboats'] = true,
  ['helicopters'] = true,
  ['planes'] = true,
  ['emergencyair'] = true,
}

--[[
  args {
    player, -- source
    vehicle_data, -- DB row or parameterized table of same
    heading, -- heading of resulting vehicle
    is_admin, -- apply FLAG_ADMIN
    prevent_storage, -- apply FLAG_PREVENT_STORAGE
    force_retrieved, -- apply FLAG_FORCE_RETRIEVED
  }
]]
function spawnGarageVehicle(arg)
  local player = arg.player

  if not player then
    return false
  end

  local vehicle_data = arg.vehicle_data

  if not vehicle_data or type(vehicle_data) ~= 'table' then
    return false
  end

  local character = exports.blrp_core:character(player)

  local vehicle_id = vehicle_data.id

  if vehicle_data.server_uid then
    vehicle_id = vehicle_data.server_uid
  end

  if VehiclesDoesEntityExistWithServerUid(vehicle_id) then
    character.notify('You already have this vehicle out')
    return false
  end

  local spawn_coords = character.getCoordinates()

  if vehicle_data.coords then
    spawn_coords = vehicle_data.coords
  end

  if vehicle_data.vehicle == 'dtanker2' or vehicle_data.vehicle == 'besra' or vehicle_data.vehicle == 'marquis' then
    spawn_coords = spawn_coords - vector3(5.0, 0, 0)
  end

  local vehicle_definition = config_vehicles_all[string.lower(vehicle_data.vehicle)]

  -- Throw away variables to make sure the script yields before continuing
  local _b = exports.blrp_core:ExemptSpawnNearCoords(GetHashKey(vehicle_data.vehicle), spawn_coords)

  local spawn_start_time = GetGameTimer()

  local network_id, class = tVehicles.spawnVehicle(character.source, { vehicle_data.vehicle, spawn_coords, (arg.heading or 0.0) })

  local spawn_duration = GetGameTimer() - spawn_start_time

  if not network_id then
    character.notify('Unable to retrieve vehicle. Please try again')
    return false
  end

  Citizen.Wait(100)

  local spawned_vehicle = NetworkGetEntityFromNetworkId(network_id)

  SetEntityRoutingBucket(spawned_vehicle, 0)

  local owner_char_id = vehicle_data.characterNumber or tonumber(character.get('id'))

  local flags_array = {}

  local addons = {}

  if vehicle_data.addons then
    addons = json.decode(vehicle_data.addons)
  end

  vehicles[vehicle_id] = {
    uid = vehicle_id,
    db_uid = vehicle_data.id,
    network_id = network_id,
    plate = vehicle_data.registration,
    owner_char_id = owner_char_id,
    business = vehicle_data.business,
    model = string.lower(vehicle_data.vehicle),
    class = class,
    vehicle_class = class,
    addons = addons,
  }

  -- Add to anticheat bypass cache
  local model_hash = GetHashKey(string.lower(vehicle_data.vehicle))
  addBypassEntry(network_id, model_hash, owner_char_id)

--   for _, addon_name in pairs(addons) do
--     exports.blrp_vehicles:InstallVehicleAddon(character.source, addon_name, vehicle_id, true)
--   end

  -- State bag implementation

  local state = Entity(spawned_vehicle).state

  table.insert(flags_array, 'FLAG_SERVER_MANAGED')
  state.server_uid = vehicle_id
  state.db_uid = vehicle_data.id
  state.owner_char_id = owner_char_id
  state.model = string.lower(vehicle_data.vehicle)
  state.vehicle_class = class

  if vehicle_data.business then
    state.business = vehicle_data.business
  end

  if arg.is_admin then
    table.insert(flags_array, 'FLAG_ADMIN')
  end

  if arg.boost_contract then
    table.insert(flags_array, 'FLAG_BOOSTED')
    table.insert(flags_array, 'FLAG_PREVENT_TRUNK')
    table.insert(flags_array, 'FLAG_PREVENT_GUNRACK')

    if arg.boost_contract.tier >= 3 then
      table.insert(flags_array, 'FLAG_BOOSTED_HIGHTIER')
    end

    if arg.boost_contract.tier >= 4 then
      table.insert(flags_array, 'FLAG_PREVENT_REFUEL')
    end
  end

  if vehicle_data.vinscratch then
    table.insert(flags_array, 'FLAG_VINSCRATCHED')
  end

  if arg.prevent_storage then
    table.insert(flags_array, 'FLAG_PREVENT_STORAGE')
  end

  if arg.force_retrieved then
    table.insert(flags_array, 'FLAG_FORCE_RETRIEVED')
  end

  if arg.has_key_item then
    table.insert(flags_array, 'FLAG_HAS_KEY_ITEM')
  end

  if tonumber(vehicle_data.neon or 0) ~= 0 then
    state.neons_enabled = true
    table.insert(flags_array, 'FLAG_HAS_NEONS')
  end

  if vehicle_data.fake_plate then
    table.insert(flags_array, 'FLAG_FAKE_PLATE')
    exports.blrp_tablet:HandlePlateSwitch(vehicle_data.fake_plate)
  end

  if vehicle_data.wax_expiration_time and vehicle_data.wax_expiration_time > os.time() then
    state.wax_expiration_time = vehicle_data.wax_expiration_time
  end

  if vehicle_data.nitrous_level then
    state.nitrous_equipped = true
    state.nitrous_level = vehicle_data.nitrous_level
  end

  if not arg.boost_contract and not arg.suppress_retrieve_message then
    character.notify('Your car is being retrieved')
  end

  -- Apply base flags
  vehicles[vehicle_id].flags = makeVehicleFlags(table.unpack(flags_array))
  state.flags = vehicles[vehicle_id].flags

  -- Apply custom flags from DB
  vehicles[vehicle_id].flags2 = (vehicle_data.flags_custom or 0)
  state.flags2 = vehicles[vehicle_id].flags2

  print('[blrp_vehicles] [NID: ' .. network_id .. ', Time: ' .. os.time() .. ', Gametimer: ' .. GetGameTimer() .. ']', json.encode(vehicles[vehicle_id]))
  print('[blrp_vehicles] [NID: ' .. network_id .. ', Time: ' .. os.time() .. ', Gametimer: ' .. GetGameTimer() .. ']', 'Spawn time: ' .. spawn_duration .. 'ms')

  return network_id
end

exports('SpawnGarageVehicle', spawnGarageVehicle)

local admin_vehicle_counter = 1

exports('SpawnBobcatVehicle', function(player, coords)
  local character = exports.blrp_core:character(player)
  local vehicle_id = 'a' .. os.date('%y%m%d%H%M') .. admin_vehicle_counter

  admin_vehicle_counter = admin_vehicle_counter + 1

  local plate = nil

  if not plate then
    plate = generatePlateDeconflicted()
  end

  local network_id = spawnGarageVehicle{
    player = character.source,
    vehicle_data = {
      coords = coords,
      server_uid = vehicle_id,
      vehicle = 'stockade4',
      registration = plate,
    },
    heading = 175.0,
    is_admin = true,
    has_key_item = true,
    suppress_retrieve_message = true,
  }

  local category = 'admin'
  local vehicle_definition = config_vehicles_all[vehicle_name]

  if vehicle_definition then
    category = vehicle_definition[4]
  end

  character.client('blrp_vehicles:client:applyCustomisation', network_id, category, {
    registration = plate,
    windows = 0,
    colour = 134,
    scolour = 0,
    ecolor = 0,
    ecolorextra = 0,
    platetype = 4,
    neon = 0,
    engineDamage = 1000,
    bodyDamage = 1000,
    fuelDamage = 1000,
    mods = json.encode({
      [48] = { mod = 10 },
    })
  }, true, false, true)

  tFuel.setFuelLevelPercent(character.source, { network_id, math.random(50, 80) })

  return network_id, vehicle_id, plate
end)

local PoliceLoadout = {
  { item = "police_armor", amount = 15 },
  { item = "ifak", amount = 15 },
  { item = "spikestrip", amount = 2 },
  { item = "carrepairkit", amount = 5 },
  { item = "tirerepairkit", amount = 3 },
  { item = "ammo_45acp", amount = 500 },
  { item = "ammo_40sw", amount = 500 },
  { item = "ammo_9mm_nato", amount = 500 },
  { item = "ammo_556nato", amount = 1000 },
  { item = "ammo_12ga", amount = 50 },
}

AddEventHandler('chatCommand', function(player, command, args)
  if command ~= 'spawn' then
    return
  end

  CancelEvent()

  local vehicle_name = args[1]
  local force_plate = args[2]

  local character = exports.blrp_core:character(player)

  if not GlobalState.is_dev and not character.hasPermissionCore('player.adminrevive') then
    return
  end

  local vehicle_id = 'a' .. os.date('%y%m%d%H%M') .. admin_vehicle_counter

  admin_vehicle_counter = admin_vehicle_counter + 1

  local plate = nil

  if force_plate and string.len(force_plate) > 0 and string.len(force_plate) <= 8 then
    plate = string.upper(force_plate)
  end

  if not plate then
    plate = generatePlateDeconflicted()
  end

  local network_id = spawnGarageVehicle{
    player = character.source,
    vehicle_data = {
      server_uid = vehicle_id,
      vehicle = vehicle_name,
      registration = plate,
    },
    heading = character.getHeading(),
    is_admin = true
  }

  local category = 'admin'
  local vehicle_definition = config_vehicles_all[vehicle_name]

  if vehicle_definition then
    category = vehicle_definition[4]
  end

  local plateType = 1 -- plate will be san andreas by default

  if GetResourceState('blrp_cayo') == 'started' and tZones.isInsideMatchedZone(character.source, { 'CayoExclusionImmediate' }) then
    plateType = 5 -- set plate to cayo if spawned inside cayo perico
  end

  character.client('blrp_vehicles:client:applyCustomisation', network_id, category, {
    registration = plate,
    windows = 0,
    colour = 0,
    scolour = 0,
    ecolor = 0,
    ecolorextra = 0,
    platetype = plateType,
    neon = 0,
    engineDamage = 1000,
    bodyDamage = 1000,
    fuelDamage = 1000,
  })

  tFuel.setFuelLevelPercent(character.source, { network_id, 100 })
  exports.blrp_vehicles:InstallVehicleAddon(character.source, 'car_radio', vehicle_id, true)

  local trunk_items = false
  local veh = NetworkGetEntityFromNetworkId(network_id)
  local state = Entity(veh).state
  local model = state.model
  if veh and DoesEntityExist(veh) then
    local class = exports.blrp_core:GetVehicleClassFromName(model)
    if class == 18 then -- emergency vehicles
      local contents = {}
      for _, entry in ipairs(PoliceLoadout) do
        contents[entry.item] = { amount = entry.amount }
      end
      exports.blrp_core:chest('vehicle:' .. vehicle_id .. ':tr').setContents(contents)
      trunk_items = true
    end
  end

  character.log('ADMIN', 'Admin spawned vehicle / vehicle = ' .. vehicle_name .. ' / uid = ' .. vehicle_id .. ' / plate = ' .. plate .. ' / Police trunk items spawned = ' .. tostring(trunk_items))
end)

RegisterNetEvent('blrp_vehicles:server:deleteNetworkedEntity', function(network_id)
  -- Clean up anticheat cache before deleting
  removeBypassEntries(network_id)
  DeleteEntity(NetworkGetEntityFromNetworkId(network_id))
end)

-- Entity deletion handler to clean up anticheat cache ONLY
-- DO NOT clean up vehicles table as it's needed for ongoing functionality
--AddEventHandler('entityRemoved', function(entity)
--  if GetEntityType(entity) ~= 2 then -- Only handle vehicles (type 2)
--    return
--  end
--
--  local network_id = NetworkGetNetworkIdFromEntity(entity)
--  if network_id == 0 then
--    return
--  end
--
--  -- Only clean up anticheat cache - vehicles table must remain intact
--  removeBypassEntries(network_id)
--end)

function generateUidDeconflicted()
  local uid = math.random(10000000, 99999999)

  if vehicles[uid] then
    return generateUidDeconflicted()
  end

  return uid
end

RegisterNetEvent('blrp_vehicles:server:searchTargetVin', function(network_id)
  local character = exports.blrp_core:character(source)
  local vehicle = NetworkGetEntityFromNetworkId(network_id)
  local model = GetEntityModel(vehicle)

  if not vehicle or vehicle <= 0 or not DoesEntityExist(vehicle) then
    character.notify('No VIN found.')
    return
  end

  local state = Entity(vehicle).state

  if not state.flags or not state.server_uid then
    character.notify('No VIN found.')
    return
  end

  if exports.blrp_vehicles:HasAnyVehicleFlags(state.flags, 'FLAG_FAKE_PLATE') then
    character.notify('The VIN is obstructed from your view')
    return
  end

  local vehicle_data = vehicles[state.server_uid]

  if not vehicle_data or not vehicle_data.plate then
    character.notify('No VIN found.')
    return
  end

  local log_model = vehicle_data.model or 'UNK'
  local log_char_id = vehicle_data.owner_char_id or 'UNK'

  character.log('ACTION', 'Checked VIN / plate = ' .. vehicle_data.plate .. ' / model = ' .. log_model .. ' / owner_char_id = ' .. log_char_id)
  character.client('blrp_core:client:addPoliceAlert', {
    allow_gps = false,
    allow_phone = false,
    badge = 'INFORMATION',
    badge_style = 'primary',
    bar_color = '#0B6EF6',
    can_accept = false,
    hide_location = true,
    icon = 'fa-regular fa-circle-info',
    is_response = true,
    msg = 'VIN: ' .. vehicle_data.plate,
    pos = vector3(0, 0, 0),
    show_for = 10000,
    sound = 'ToneP4',
    title = 'Dispatch Service',
    allow_dismiss = false,
  })
end)

RegisterNetEvent('blrp_vehicles:server:ensureState', function(network_id)
  local character = exports.blrp_core:character(source)

  if not network_id then
    return
  end

  for _, vehicle_data in pairs(vehicles) do
    if vehicle_data.network_id == network_id then
      local vehicle = NetworkGetEntityFromNetworkId(network_id)

      if not vehicle or vehicle <= 0 then
        character.notify('Invalid vehicle')
        return
      end

      local model = GetEntityModel(vehicle)

      if model ~= GetHashKey(vehicle_data.model) then
        character.notify('Model mismatch: ' .. model .. ' / ' .. GetHashKey(vehicle_data.model) .. ' / ' .. vehicle_data.model)
        return
      end

      local state = Entity(vehicle).state

      Entity(vehicle).state['server_managed'] = true
      Entity(vehicle).state['server_uid'] = vehicle_data.uid

      for state_key, state_value in pairs(vehicle_data) do
        Entity(vehicle).state[state_key] = state_value
      end

      character.notify('Vehicle state synced with server, please lock/unlock your vehicle again')
      character.log('VEHICLESYNC', 'Synchronized vehicle state bag / json = ' .. json.encode(vehicle_data))

      return
    end
  end
end)

AddEventHandler('chatCommand', function(player, command, args)
  if command ~= 'retrievevehicle' then
    return
  end

  CancelEvent()

  local character = exports.blrp_core:character(player)

  if not character.hasPermissionCore('vehicles.forceretrieve') then
    return
  end

  if not args[1] or not args[2] then
    character.client('chat:addMessage', {
			template = '<div class="chat-bubble" style="background-color: rgba(230, 0, 115, 0.6);"><i class="fas fa-exclamation-circle"></i> {0}</div>',
			args = { "Invalid syntax. /retrievevehicle <RO DL #> <Vehicle Plate>" }
		})

    return
  end

  local dlnumber = tonumber(args[1])

  if not dlnumber then
    character.client('chat:addMessage', {
			template = '<div class="chat-bubble" style="background-color: rgba(230, 0, 115, 0.6);"><i class="fas fa-exclamation-circle"></i> {0}</div>',
			args = { "Invalid DL number: " .. tostring(args[1]) }
		})

    return
  end

  local closest_garage_id = nil
  local closest_garage_distance = 99999
  local player_coords = character.getCoordinates()

  for garage_id, garage_data in pairs(config_garage_locations) do
    local distance = #(player_coords - garage_data.coords)

    if distance <= 5.0 and distance < closest_garage_distance then
      closest_garage_id = garage_id
      closest_garage_distance = distance
    end
  end

  if not closest_garage_id and exports.blrp_core:CoordsInRangeOfHouseGarage(player_coords) then
    closest_garage_id = 'z1'
  end

  if not closest_garage_id then
    character.notify('You are not near any garage')
    return
  end

  local vehicle_data = MySQL.Sync.fetchAll('SELECT * FROM vrp_user_vehicles WHERE registration = @registration AND EXISTS (SELECT id, dlnumber FROM characters WHERE id = vrp_user_vehicles.characterNumber AND dlnumber = @dlnumber)', {
    registration = args[2],
    dlnumber = dlnumber,
  })

  if #vehicle_data <= 0 then
    character.client('chat:addMessage', {
			template = '<div class="chat-bubble" style="background-color: rgba(230, 0, 115, 0.6);"><i class="fas fa-exclamation-circle"></i> {0}</div>',
			args = { 'Vehicle not found. Verify DL # and Registration' }
		})

    return
  end

  vehicle_data = vehicle_data[1]

  if vehicle_data.storedGarage and vehicle_data.storedGarage ~= closest_garage_id then
    if not character.hasGroup('staff') or not character.request('This vehicle is not actually stored here but you can pull it out as staff. Use only for staff stuff, not while on LEO. Proceed?', 30) then
      character.notify('The requested vehicle is not stored at this garage')
      return
    end
  end

  character.log('VEHICLERETRIEVE', 'Retrieved vehicle from garage via command / plate = ' .. vehicle_data.registration .. ' / coords = ' .. player_coords)

  local network_id = spawnGarageVehicle{
    player = character.source,
    vehicle_data = vehicle_data,
    force_retrieved = true,
  }

  character.client('blrp_vehicles:client:applyCustomisation', network_id, false, vehicle_data)
end)

function hasVehicleKey(server_uid, character)
  if not server_uid then
    return false
  end

  return character.hasItemQuantity('vehicle_key:meta:' .. server_uid, 1)
end

pVehicles.hasVehicleKey = function(server_uid)
  local character = exports.blrp_core:character(source)
  local vehicle = vehicles[server_uid]

  if not vehicle then
    return false
  end

  return hasVehicleKey(server_uid, character)
end

function hasVehicleSkeletonKey(model, player)
  return exports.blrp_core:character(player).hasItemQuantity('vehicle_key:meta:' .. string.upper(model) .. ':' .. server_start_date, 1)
end

pVehicles.hasVehicleSkeletonKey = function(server_uid)
  local character = exports.blrp_core:character(source)
  local vehicle = vehicles[server_uid]

  if not vehicle then
    return false
  end

  return character.hasItemQuantity('vehicle_key:meta:' .. string.upper(vehicle.model) .. ':' .. server_start_date, 1)
end

pVehicles.GetVehicleVINFromNetworkID = function(vehicle)
  local vehicle = NetworkGetEntityFromNetworkId(vehicle)
  local state = Entity(vehicle).state
  local vehicle_data = vehicles[state.server_uid]

  if not vehicle_data then
    return false
  end
  return vehicle_data.plate
end

exports('getVehicleVINFromNetworkID', pVehicles.GetVehicleVINFromNetworkID)

pVehicles.toggleNeonState = function(network_id)
  local vehicle = NetworkGetEntityFromNetworkId(network_id)

  if not vehicle or vehicle <= 0 then
    return
  end

  local state = Entity(vehicle).state
  local vehicle_data = vehicles[state.server_uid]

  if not vehicle_data or not hasAnyVehicleFlags(state.flags, 'FLAG_HAS_NEONS') then
    return
  end

  state.neons_enabled = not state.neons_enabled

  return state.neons_enabled
end

RegisterNetEvent('blrp_vehicles:toggleRiotRamBar', function(network_id, event_data)
  local character = exports.blrp_core:character(source)
  local entity = NetworkGetEntityFromNetworkId(network_id)
  local verb = 'Mounting'
  local new_mod = 0

  if tVehicles.getVehicleMod(character.source, { network_id, 2 }) == 0 then
    verb = 'Unmounting'
    new_mod = 1
  end

  if
    not character.progressPromise(verb .. ' Ram Bar', 10, {
      controlDisables = {
        disableMovement = true,
        disableCarMovement = true,
        disableMouse = false,
        disableCombat = true,
      },
      animation = {
        animDict = 'mini@repair',
        anim = 'fixing_a_ped',
        flags = 49,
      }
    }) or
    not DoesEntityExist(entity)
  then
    return
  end

  tVehicles.setVehicleMod(NetworkGetEntityOwner(entity), { network_id, 2, new_mod })
end)
