-- Wardrobe Proxied Server Functions
pWardrobe = {}
P.bindInstance('wardrobe', pWardrobe)

-- Get clothing store instance
tClothingStore = T.getInstance('blrp_clothingstore', 'clothingstore')

pWardrobe.setCustomization = function(outfit_name, force_user_id, player_model)
  local character = exports.blrp_core:character(source)
  local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

  if not wardrobe_cache[user_id] then
    loadWardrobe(user_id)
  end

  config_wardrobes = exports.blrp_clothingstore:GetConfigWardrobes()

  local wardrobe = wardrobe_cache[user_id]

  local outfit = wardrobe[outfit_name]

  -- Handle undress option
  if outfit_name == '> Undress' then
    if player_model == `mp_m_freemode_01` then
      outfit = config_wardrobes.undress_male
    else
      outfit = config_wardrobes.undress_female
    end
  end

  if not outfit then
    print('Outfit not found whilst trying to set customizations', outfit_name)
    return
  end

  -- Check item bound clothing. If wearing any, keep it on through outfit change
  local current_custom = tClothingStore.getCustomization(character.source, { false, outfit.save_hair })

  local blacklist = exports.blrp_clothingstore:GetBlacklist(player_model)

  if blacklist then
    for component, component_data in pairs(current_custom) do
      if type(component_data) == 'table' then
        local component_original = component

        component = tostring(component)

        if blacklist[component] then
          local blacklist_value = blacklist[component][tonumber(component_data[1])]

          if type(blacklist_value) == 'table' then
            blacklist_value = blacklist_value[tonumber(component_data[2])] or nil
          end

          if blacklist_value and string.match(blacklist_value, 'item:') then
            if not outfit[component_original] then
              outfit[component_original] = {
                0, 0, 0
              }
            end

            outfit[component_original][1] = component_data[1]
            outfit[component_original][2] = component_data[2]
          end
        end
      end
    end
  end

  local save_hair = outfit.save_hair

  --if
  --save_hair and
  --  tonumber(force_user_id) ~= tonumber(character.get('identifier')) and
  --  not character.request('Also apply hairstyle? This will override your hair')
  --then
  --  save_hair = false
  --end
  --
  --if save_hair and outfit['2'] then
  --  MySQL.scalar('SELECT skin FROM player_skins WHERE character_id = ?', { character.get('id') }, function(player_skin)
  --    if not player_skin then
  --      return
  --    end
  --
  --    player_skin = json.decode(player_skin)
  --
  --    player_skin.hair_1 = outfit['2'][1]
  --
  --    MySQL.update('UPDATE player_skins SET skin = ? WHERE character_id = ?', { json.encode(player_skin), character.get('id') })
  --  end)
  --end

  tClothingStore.setCustomization(character.source, { outfit, false, false, false, save_hair })
end

-- Save outfit to wardrobe
pWardrobe.saveOutfit = function(outfit_name, allow_dress, force_user_id, favorite, save_hair, folder_id, emoji)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end

    local wardrobe = wardrobe_cache[user_id]

    if not wardrobe then
        wardrobe = {}
    end

    -- Get current customization using the clothing store's method
    local custom_to_save = tClothingStore.getCustomization(character.source)

    if not custom_to_save then
        character.notify('Failed to get current customization')
        return
    end

    -- Convert folder_id to number if it's a string, or keep as nil if not provided
    if folder_id and folder_id ~= "" then
        folder_id = tonumber(folder_id)

        -- Validate that the folder exists
        if not folder_cache[user_id] then
            loadFolders(user_id)
        end

        if folder_id and folder_cache[user_id] and not folder_cache[user_id][folder_id] then
            character.notify('Invalid folder selected')
            print('[WARDROBE DEBUG] Invalid folder_id: ' .. tostring(folder_id) .. ' for user: ' .. user_id)
            folder_id = nil
        end
    else
        folder_id = nil
    end

    -- Add metadata
    custom_to_save.version = tonumber(current_version)
    custom_to_save.favorite = favorite or nil
    custom_to_save.save_hair = save_hair or nil
    custom_to_save.folder_id = folder_id
    custom_to_save.emoji = emoji or nil
    custom_to_save.created_at = os.time()

    wardrobe[outfit_name] = custom_to_save
    saveWardrobe(user_id, wardrobe)

    character.notify('Outfit saved: ' .. outfit_name)
    character.log('ACTION', 'Saved outfit: ' .. outfit_name .. (folder_id and (' to folder ID: ' .. folder_id) or ' as unassigned'))

    -- Refresh wardrobe interface
    TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
end

-- Create a new folder
pWardrobe.createFolder = function(folder_name, color, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not folder_name or folder_name == '' or string.len(folder_name) > 50 then
        character.notify('Invalid folder name')
        return
    end

    local folder_id = createFolder(user_id, folder_name, color)

    print('created folder_id', folder_id)
    if folder_id then
        character.notify('Folder created: ' .. folder_name)
        character.log('ACTION', 'Created folder: ' .. folder_name)

        -- Refresh wardrobe interface
        TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
    else
        character.notify('Failed to create folder (name may already exist)')
    end
end

-- Update a folder
pWardrobe.updateFolder = function(folder_id, folder_name, color, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not folder_id then
        character.notify('Invalid folder')
        return
    end

    if not folder_name or folder_name == '' or string.len(folder_name) > 50 then
        character.notify('Invalid folder name')
        return
    end

    local success = updateFolder(user_id, folder_id, folder_name, color or '#5865f2')

    if success then
        character.notify('Folder updated: ' .. folder_name)
        character.log('ACTION', 'Updated folder ID: ' .. folder_id .. ' to name: ' .. folder_name)

        -- Refresh wardrobe interface
        TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
    else
        character.notify('Failed to update folder')
    end
end

-- Delete a folder
pWardrobe.deleteFolder = function(folder_id, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not folder_id then
        character.notify('Invalid folder')
        return
    end

    local success = deleteFolder(user_id, folder_id)

    if success then
        character.notify('Folder deleted')
        character.log('ACTION', 'Deleted folder ID: ' .. folder_id)

        -- Refresh wardrobe interface
        TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
    else
        character.notify('Failed to delete folder')
    end
end

-- Move outfit to folder
pWardrobe.moveOutfitToFolder = function(outfit_name, folder_id, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end

    local wardrobe = wardrobe_cache[user_id]

    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end

    -- Update outfit folder
    wardrobe[outfit_name].folder_id = folder_id

    -- Save to database
    saveWardrobe(user_id, wardrobe)

    local folder_name = 'Unassigned'
    if folder_id and folder_cache[user_id] and folder_cache[user_id][folder_id] then
        folder_name = folder_cache[user_id][folder_id].name
    end

    character.notify('Moved outfit to: ' .. folder_name)
    character.log('ACTION', 'Moved outfit "' .. outfit_name .. '" to folder: ' .. folder_name)

    -- Refresh wardrobe interface
    TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
end

-- Toggle outfit favorite status
pWardrobe.toggleFavorite = function(outfit_name, allow_dress, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end
    
    local wardrobe = wardrobe_cache[user_id]
    
    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end
    
    local current_favorite = wardrobe[outfit_name].favorite or false
    wardrobe[outfit_name].favorite = not current_favorite
    
    saveWardrobe(user_id, wardrobe)

    local status = wardrobe[outfit_name].favorite and 'favorited' or 'unfavorited'
    -- character.notify('Outfit ' .. status .. ': ' .. outfit_name)
    character.log('ACTION', 'Toggled favorite for outfit: ' .. outfit_name)

    -- Refresh wardrobe interface
    TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
end

-- Edit outfit (name and emoji)
pWardrobe.editOutfit = function(old_name, new_name, emoji, allow_dress, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end

    local wardrobe = wardrobe_cache[user_id]

    if not wardrobe or not wardrobe[old_name] then
        character.notify('Outfit not found')
        return
    end

    -- If name is changing, check if new name already exists
    if old_name ~= new_name and wardrobe[new_name] then
        character.notify('An outfit with that name already exists')
        return
    end

    -- Update the outfit
    local outfit_data = wardrobe[old_name]
    outfit_data.emoji = emoji or nil

    -- If name changed, move the outfit data
    if old_name ~= new_name then
        wardrobe[new_name] = outfit_data
        wardrobe[old_name] = nil
    else
        wardrobe[old_name] = outfit_data
    end

    saveWardrobe(user_id, wardrobe)

    if old_name ~= new_name then
        character.notify('Outfit renamed to: ' .. new_name)
        character.log('ACTION', 'Renamed outfit from "' .. old_name .. '" to "' .. new_name .. '"')
    else
        character.notify('Outfit updated: ' .. new_name)
        character.log('ACTION', 'Updated outfit: ' .. new_name)
    end

    -- Refresh wardrobe interface
    TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
end

-- Rename outfit (legacy function for compatibility)
pWardrobe.renameOutfit = function(old_name, new_name, allow_dress, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end

    local wardrobe = wardrobe_cache[user_id]

    if not wardrobe or not wardrobe[old_name] then
        character.notify('Outfit not found')
        return
    end

    if wardrobe[new_name] then
        character.notify('An outfit with that name already exists')
        return
    end

    wardrobe[new_name] = wardrobe[old_name]
    wardrobe[old_name] = nil

    saveWardrobe(user_id, wardrobe)

    character.notify('Outfit renamed to: ' .. new_name)
    character.log('ACTION', 'Renamed outfit from "' .. old_name .. '" to "' .. new_name .. '"')

    -- Refresh wardrobe interface
    TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
end

-- Delete outfit
pWardrobe.deleteOutfit = function(outfit_name, allow_dress, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end
    
    local wardrobe = wardrobe_cache[user_id]
    
    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end
    
    wardrobe[outfit_name] = nil
    saveWardrobe(user_id, wardrobe)
    
    -- Reopen wardrobe to refresh
    TriggerEvent('blrp_wardrobe:server:openWardrobe', allow_dress, character.source, true, force_user_id)
    
    character.notify('Outfit deleted: ' .. outfit_name)
    character.log('ACTION', 'Deleted outfit: ' .. outfit_name)
end

-- Share outfit with closest player
pWardrobe.shareOutfit = function(outfit_name, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

  -- Find closest player
    local playerPed = GetPlayerPed(character.source)
    local playerCoords = GetEntityCoords(playerPed)
    local closestPlayer = nil
    local closestDistance = 4.0 -- Maximum distance to share

    for _, playerId in ipairs(GetPlayers()) do
        local targetPlayerId = tonumber(playerId)
        if targetPlayerId ~= character.source then
            local targetPed = GetPlayerPed(targetPlayerId)
            if targetPed and targetPed ~= 0 then
                local targetCoords = GetEntityCoords(targetPed)
                local distance = #(playerCoords - targetCoords)

                if distance < closestDistance then
                    closestDistance = distance
                    closestPlayer = targetPlayerId
                end
            end
        end
    end

    if not closestPlayer then
        character.notify('No players nearby to share with')
        return
    end

    local target_character = exports.blrp_core:character(closestPlayer)
    if not target_character then
        character.notify('Target player not found')
        return
    end
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end
    
    local wardrobe = wardrobe_cache[user_id]
    
    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end
    
    local outfit_data = wardrobe[outfit_name]
    
    -- Get target player's wardrobe
    local target_user_id = tonumber(target_character.get('identifier'))
    
    if not wardrobe_cache[target_user_id] then
        loadWardrobe(target_user_id)
    end
    
    local target_wardrobe = wardrobe_cache[target_user_id] or {}
    
    -- Create shared outfit name
    if not character then
      print('no character found when trying to share outfit')
      return
    end

    local shared_name = outfit_name .. ' (from ' .. character.get('firstname') .. ')'
    local counter = 1
    
    while target_wardrobe[shared_name] do
        shared_name = outfit_name .. ' (from ' .. character.get('firstname') .. ') ' .. counter
        counter = counter + 1
    end
    
    -- Copy outfit data
    local shared_outfit = {}
    for k, v in pairs(outfit_data) do
        shared_outfit[k] = v
    end
    shared_outfit.created_at = nil
    shared_outfit.folder = 'shared'
    
    target_wardrobe[shared_name] = shared_outfit
    saveWardrobe(target_user_id, target_wardrobe)
    
    character.notify('Outfit shared with ' .. target_character.get('firstname'))
    target_character.notify('Received outfit "' .. shared_name .. '" from ' .. character.get('firstname'))
    
    character.log('ACTION', 'Shared outfit "' .. outfit_name .. '" with ' .. target_character.get('firstname'))
    target_character.log('ACTION', 'Received outfit "' .. shared_name .. '" from ' .. character.get('firstname'))
end

-- Update outfit folder
pWardrobe.updateOutfitFolder = function(outfit_name, new_folder, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end
    
    local wardrobe = wardrobe_cache[user_id]
    
    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end
    
    wardrobe[outfit_name].folder = new_folder or 'default'
    saveWardrobe(user_id, wardrobe)
    
    character.notify('Outfit moved to folder: ' .. (new_folder or 'default'))
end

-- Upgrade outfit to current version
pWardrobe.upgradeOutfit = function(outfit_name, gender, allow_dress)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(character.get('identifier'))
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end
    
    local wardrobe = wardrobe_cache[user_id]
    
    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end
    
    -- Update outfit version
    wardrobe[outfit_name].version = tonumber(current_version)
    saveWardrobe(user_id, wardrobe)
    
    -- Reopen wardrobe to refresh
    TriggerEvent('blrp_wardrobe:server:openWardrobe', allow_dress, character.source, true)
    
    character.notify('Outfit updated to version ' .. current_version .. ' (' .. gender .. ')')
    character.log('ACTION', 'Upgraded outfit "' .. outfit_name .. '" to version ' .. current_version)
end

-- Replace outfit with current appearance
pWardrobe.replaceOutfit = function(outfit_name, allow_dress, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end

    local wardrobe = wardrobe_cache[user_id]

    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return
    end

    -- Get current customization using the clothing store's method
    local custom_to_save = tClothingStore.getCustomization(character.source)

    if not custom_to_save then
        character.notify('Failed to get current customization')
        return
    end

    -- Keep the original metadata but update the appearance
    local original_outfit = wardrobe[outfit_name]
    custom_to_save.version = original_outfit.version or tonumber(current_version)
    custom_to_save.favorite = original_outfit.favorite
    custom_to_save.save_hair = original_outfit.save_hair
    custom_to_save.folder_id = original_outfit.folder_id
    custom_to_save.emoji = original_outfit.emoji
    custom_to_save.created_at = original_outfit.created_at

    wardrobe[outfit_name] = custom_to_save
    saveWardrobe(user_id, wardrobe)

    character.notify('Outfit "' .. outfit_name .. '" replaced with current appearance')
    character.log('ACTION', 'Replaced outfit "' .. outfit_name .. '" with current appearance')

    -- Refresh wardrobe interface
    TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
end

-- Export outfit in odev format
pWardrobe.exportOutfit = function(outfit_name, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end

    local wardrobe = wardrobe_cache[user_id]

    if not wardrobe or not wardrobe[outfit_name] then
        character.notify('Outfit not found')
        return {success = false, message = 'Outfit not found'}
    end

    local outfit = wardrobe[outfit_name]

    -- Convert outfit to odev format
    local odev_data = "{\n"

    -- Add components (0-11)
    for i = 0, 11 do
        local component = outfit[tostring(i)]
        if component then
            local prefix = i == 1 and "-- " or ""
            odev_data = odev_data .. string.format("    %s[%d] = {%d, %d},\n", prefix, i, component[1], component[2])
        end
    end

    -- Add props (p0, p1, p2, p6, p7)
    local props = {'p0', 'p1', 'p2', 'p6', 'p7'}
    for _, prop in ipairs(props) do
        local prop_data = outfit[prop]
        if prop_data then
            odev_data = odev_data .. string.format("    ['%s'] = {%d, %d},\n", prop, prop_data[1], prop_data[2])
        end
    end

    odev_data = odev_data .. "}"

    return {success = true, data = odev_data}
end

-- Import outfit from odev format
pWardrobe.importOutfit = function(name, outfit_data, folder_id, allow_dress, force_user_id)
    local character = exports.blrp_core:character(source)
    local user_id = tonumber(force_user_id) or tonumber(character.get('identifier'))

    print('[blrp_wardrobe] Import called with name:', name, 'data length:', string.len(outfit_data or ''))

    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end

    local wardrobe = wardrobe_cache[user_id]

    if not wardrobe then
        wardrobe = {}
    end

    if wardrobe[name] then
        character.notify('An outfit with that name already exists')
        return
    end

    -- Parse odev format data
    local success, parsed_outfit = pcall(function()
        local outfit = {}

        -- Remove outer braces and clean up
        local clean_data = outfit_data:gsub("^%s*{", ""):gsub("}%s*$", "")

        -- Use pattern matching to find all [key] = {val1, val2} patterns
        -- This handles both single-line and multi-line formats
        for match in clean_data:gmatch("([^,{}]+%b{})") do
            -- Clean up the match
            local entry = match:gsub("^%s*", ""):gsub("%s*$", ""):gsub(",$", "")

            -- Remove comment prefix if present
            entry = entry:gsub("^%-%-", ""):gsub("^%s*", "")

            -- Extract key and values
            local key, val1, val2 = entry:match("%[([^%]]+)%]%s*=%s*{([^,]+),%s*([^}]+)}")

            if key and val1 and val2 then
                -- Remove quotes from key if present
                key = key:gsub("^['\"]", ""):gsub("['\"]$", "")

                -- Convert values to numbers
                local clean_val1 = val1:gsub("%s", "")
                local clean_val2 = val2:gsub("%s", "")
                local num1 = tonumber(clean_val1)
                local num2 = tonumber(clean_val2)

                if num1 and num2 then
                    outfit[key] = {num1, num2}
                end
            end
        end

        return outfit
    end)

    if not success then
        print('[blrp_wardrobe] Import parsing error:', parsed_outfit)
        character.notify('Invalid outfit data format: ' .. tostring(parsed_outfit))
        return
    end

    if not parsed_outfit or not next(parsed_outfit) then
        print('[blrp_wardrobe] No outfit data parsed from:', outfit_data)
        character.notify('No valid outfit data found')
        return
    end

    print('[blrp_wardrobe] Successfully parsed outfit with', #parsed_outfit, 'components')

    -- Add metadata
    parsed_outfit.version = tonumber(current_version)
    parsed_outfit.folder_id = folder_id
    parsed_outfit.created_at = os.time()

    wardrobe[name] = parsed_outfit
    saveWardrobe(user_id, wardrobe)

    character.notify('Outfit imported: ' .. name)
    character.log('ACTION', 'Imported outfit: ' .. name .. (folder_id and (' to folder ID: ' .. folder_id) or ' as unassigned'))

    -- Refresh wardrobe interface
    TriggerEvent('blrp_wardrobe:server:openWardrobe', true, character.source, true, force_user_id)
end
