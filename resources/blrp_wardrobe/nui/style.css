/* Modern Sleek Wardrobe Interface */

:root {
    /* Slack Dark Theme Colors */
    --primary: #1264a3;
    --primary-hover: #0b4f82;
    --primary-light: rgba(18, 100, 163, 0.15);
    --secondary: #616061;
    --success: #007a5a;
    --warning: #e01e5a;
    --danger: #e01e5a;

    /* Dark Backgrounds */
    --bg-primary: #1a1d21;
    --bg-secondary: #232629;
    --bg-tertiary: #2c2f33;
    --bg-quaternary: #35383c;

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #d1d2d3;
    --text-muted: #868686;

    /* Borders */
    --border: #3e4146;
    --border-light: #4a4d52;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.4);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -4px rgba(0, 0, 0, 0.4);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 8px 10px -6px rgba(0, 0, 0, 0.5);

    /* Border Radius */
    --radius: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    margin: 0;
    padding: 0;
    background: transparent;
    overflow: hidden;
    font-size: 14px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Emoji Styles */
.outfit-emoji {
    font-size: 1.1em;
    margin-right: 0.5rem;
    display: inline-block;
}

.emoji-selector {
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1rem;
    background: var(--bg-tertiary);
    max-width: 100%;
    overflow: hidden;
}

.selected-emoji-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border-radius: var(--radius);
    border: 1px solid var(--border);
}

.selected-emoji {
    font-size: 1.2em;
    color: var(--text-primary);
    min-height: 1.5em;
    display: flex;
    align-items: center;
}

.selected-emoji:empty::before {
    content: "No emoji selected";
    color: var(--text-muted);
    font-size: 0.875rem;
}

.emoji-search-container {
    margin-bottom: 1rem;
}

.emoji-search-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.emoji-search-input::placeholder {
    color: var(--text-muted);
}

.emoji-search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.emoji-grid-container {
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--bg-secondary);
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.25rem;
    padding: 0.5rem;
    width: 100%;
    box-sizing: border-box;
}

.emoji-option {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.2em;
    cursor: pointer;
    border-radius: var(--radius);
    transition: all 0.2s ease;
    background: var(--bg-tertiary);
    border: 1px solid transparent;
    box-sizing: border-box;
}

.emoji-option:hover {
    background: var(--primary-light);
    border-color: var(--primary);
    transform: scale(1.05);
}

.emoji-option.selected {
    background: var(--primary);
    border-color: var(--primary);
    color: white;
}

/* Custom scrollbar for emoji grid */
.emoji-grid-container::-webkit-scrollbar {
    width: 8px;
}

.emoji-grid-container::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius);
}

.emoji-grid-container::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: var(--radius);
    transition: background 0.2s ease;
}

.emoji-grid-container::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Firefox scrollbar */
.emoji-grid-container {
    scrollbar-width: thin;
    scrollbar-color: var(--border) var(--bg-tertiary);
}

/* Prevent horizontal overflow */
body {
    overflow-x: hidden;
}

.modal-content {
    max-width: 90vw;
    overflow-x: hidden;
}

/* Container */
.wardrobe-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.2s ease-out;
}

.wardrobe-main {
    width: min(95vw, 1100px);
    height: min(90vh, 750px);
    background: var(--bg-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    animation: slideUp 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: box-shadow 0.2s ease;
}

.wardrobe-main.dragging {
    box-shadow: var(--shadow-xl), 0 25px 50px -12px rgba(0, 0, 0, 0.6);
    transition: none;
}

/* Header */
.wardrobe-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border);
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    gap: 1rem;
    cursor: move;
    user-select: none;
}

.header-left {
    flex-shrink: 0;
}

.header-left h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
}

.header-left .icon {
    color: var(--primary);
    width: 24px;
    height: 24px;
}

.header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 1rem;
}

.header-center .form-control {
    margin: 0;
}

.header-center .form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.header-actions .btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    min-height: 32px;
}

.header-actions .btn i {
    width: 14px;
    height: 14px;
}

#close-wardrobe {
    background: var(--bg-secondary);
    border: none;
    color: var(--text-secondary);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

#close-wardrobe:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: scale(1.05);
}

/* Toolbar */
.wardrobe-toolbar {
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border);
    padding: 1rem 2rem;
    flex-shrink: 0;
}

/* Permission Message */
.permission-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--primary-light);
    border: 1px solid var(--primary);
    border-radius: var(--radius);
    color: var(--primary);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.permission-message i {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}



/* Folder Tags */
.folder-tags-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.folder-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    flex: 1;
}

.folder-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: 12px;
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    user-select: none;
    position: relative;
    white-space: nowrap;
    height: 28px;
}

.folder-tag:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-light);
    color: var(--text-primary);
}

.folder-tag.active {
    background: var(--bg-quaternary);
    border-color: var(--border);
    color: var(--text-primary);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
    transform: translateY(1px);
}

.folder-tag.active:hover {
    background: var(--bg-quaternary);
    border-color: var(--border-light);
}

.folder-tag .folder-count {
    padding: 0.1rem 0.375rem;
    border-radius: 8px;
    font-size: 0.65rem;
    font-weight: 600;
    margin-left: 0.25rem;
    line-height: 1;
}

.folder-tag.active .folder-count {
    color: white;
}

.folder-tag [data-lucide="folder"],
.folder-tag [data-lucide="folder"] svg,
.folder-tag [data-lucide="grid-3x3"],
.folder-tag [data-lucide="grid-3x3"] svg,
.folder-tag [data-lucide="folder-x"],
.folder-tag [data-lucide="folder-x"] svg {
    width: 8px !important;
    height: 8px !important;
    flex-shrink: 0 !important;
    min-width: 8px !important;
    min-height: 8px !important;
    max-width: 8px !important;
    max-height: 8px !important;
    font-size: 8px !important;
    transform: scale(1.84) !important;
}

/* Removed drag and drop styles */

/* Form Controls */
.form-control {
    position: relative;
    margin-bottom: 1rem;
}

.form-input, .form-select {
    width: 100%;
    padding: 1rem 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 400;
    transition: all 0.2s ease;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-input::placeholder {
    color: var(--text-muted);
}

/* Custom Checkbox Styling - Green instead of blue */
input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--border);
    border-radius: 4px;
    background: var(--bg-secondary);
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

input[type="checkbox"]:hover {
    border-color: var(--success);
    background: var(--bg-tertiary);
}

input[type="checkbox"]:checked {
    background: var(--success);
    border-color: var(--success);
}

input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
}

input[type="checkbox"]:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 165, 93, 0.2);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: var(--radius);
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-apply {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border);
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

.btn-apply:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Show apply button on outfit card hover */
.outfit-card:hover .btn-apply {
    opacity: 1;
    visibility: visible;
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-light);
}

.btn-danger {
    background: var(--danger);
    color: white;
    border: 1px solid var(--danger);
}

.btn-danger:hover:not(:disabled) {
    background: #c53030;
    border-color: #c53030;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-danger:disabled {
    background: var(--bg-secondary);
    color: var(--text-muted);
    border-color: var(--border);
    cursor: not-allowed;
    opacity: 0.6;
}

.btn-warning {
    background: var(--bg-secondary);
    color: var(--warning);
    border: 1px solid var(--border);
}

.btn-warning:hover {
    background: var(--bg-tertiary);
    color: var(--warning);
    border-color: var(--border-light);
}

.btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    min-width: 40px;
    height: 36px;
}

.outfit-actions .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    min-width: 36px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Content Area */
.outfit-container {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    background: var(--bg-tertiary);
}

/* Folder Section */
.folder-section {
    margin-bottom: 2rem;
}

.folder-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 1rem 1.5rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
}

.folder-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1rem;
    flex: 1;
}

.folder-count {
    padding: 0.125rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
}

.folder-edit-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    padding: 0.125rem;
    border-radius: 3px;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    margin-left: auto;
}

.folder-tag:hover .folder-edit-btn {
    opacity: 1;
}

.folder-edit-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.folder-edit-btn [data-lucide="edit-2"],
.folder-edit-btn [data-lucide="edit-2"] svg,
.folder-edit-btn i,
.folder-edit-btn i svg {
    width: 8px !important;
    height: 8px !important;
    transform: scale(1.84) !important;
}

/* Folder option styling */
.folder-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 4px;
    margin: 2px 0;
}

.folder-option:hover {
    background-color: var(--bg-tertiary);
}

.folder-option i {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

#outfits-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

#outfits-grid .outfit-card {
    width: auto;
}

/* Outfit Cards */
.outfit-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1rem 1.5rem;
    transition: all 0.2s ease;
    position: relative;
    overflow: visible;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
}

.outfit-card:hover {
    border-color: var(--primary);
    box-shadow: var(--shadow-md);
}

/* Favorite Star */
.favorite-star {
    position: absolute;
    top: -12px;
    right: -8px;
    font-size: 20px;
    z-index: 10;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.outfit-card.outdated {
    /* Removed orange border and background for outdated outfits */
    /* Visual indication now handled by exclamation mark in 3-dot menu */
}

.outfit-name {
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    margin-right: 1rem;
    display: flex;
    align-items: center;
}

.outfit-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.outfit-menu-container, .outfit-folder-container {
    position: relative;
}

.outfit-menu-btn, .outfit-folder-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    color: var(--text-muted);
    width: 32px;
    height: 32px;
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.15s ease;
    font-size: 14px;
}

.outfit-menu-btn:hover, .outfit-folder-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-light);
}

/* Dropdown Menus */
.outfit-menu, .outfit-folder-menu {
    position: fixed;
    background: var(--bg-quaternary);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    box-shadow: var(--shadow-xl);
    z-index: 999999;
    min-width: 200px;
    overflow: hidden;
    animation: slideDown 0.1s ease-out;
    user-select: none;
}

.outfit-menu button, .folder-option {
    width: 100%;
    text-align: left;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.1s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    font-weight: 400;
    white-space: nowrap;
}

.outfit-menu button:hover, .folder-option:hover {
    background: var(--primary);
    color: var(--text-primary);
}

.outfit-menu button:hover .icon {
    color: var(--text-primary);
}

.outfit-menu button:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.outfit-menu button:disabled:hover {
    background: transparent;
    color: var(--text-secondary);
}

.outfit-menu button:disabled:hover .icon {
    color: var(--text-muted);
}

.outfit-menu hr {
    border: none;
    border-top: 1px solid var(--border);
    margin: 0.25rem 0;
}

.outfit-menu .icon {
    width: 16px;
    height: 16px;
    color: var(--text-muted);
    flex-shrink: 0;
}

.outfit-menu .text-danger {
    color: var(--danger);
}

.outfit-menu .text-danger .icon {
    color: var(--danger);
}

.outfit-menu .text-danger:hover {
    background: var(--danger);
    color: var(--text-primary);
}

.outfit-menu .text-danger:hover .icon {
    color: var(--text-primary);
}

.outfit-menu .text-warning {
    color: var(--warning);
}

.outfit-menu .text-warning .icon {
    color: var(--warning);
}

.outfit-menu .text-warning:hover {
    background: var(--warning);
    color: var(--bg-primary);
}

.outfit-menu .text-warning:hover .icon {
    color: var(--bg-primary);
}

/* Removed favorite badge - using inline button instead */

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-muted);
}

.empty-state .icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 1.5rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.empty-state p {
    font-size: 0.875rem;
    margin: 0;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scrollbar */
.outfit-container::-webkit-scrollbar {
    width: 6px;
}

.outfit-container::-webkit-scrollbar-track {
    background: transparent;
}

.outfit-container::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 3px;
}

.outfit-container::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Color Picker */
.color-picker {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.color-option {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    position: relative;
}

.color-option:hover {
    transform: scale(1.1);
    border-color: var(--text-secondary);
}

.color-option.selected {
    border-color: var(--text-primary);
    transform: scale(1.1);
}

.color-option.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
}

.form-label {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

/* Ensure all labels are white */
label {
    color: var(--text-primary) !important;
    font-weight: 500;
}

/* Utility Classes */
.hidden { display: none !important; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.text-warning { color: var(--warning); }
.text-danger { color: var(--danger); }

/* Modal styles - Separate from wardrobe container */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20000;
    opacity: 0;
}

.modal-content {
    background: var(--bg-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    max-width: 450px;
    width: 90%;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transform: translateY(0);
}

.modal-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border);
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h2 {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
}

.modal-close-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.15s ease;
}

.modal-close-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border);
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* Modal animations - removed to prevent conflicts */

/* Import/Export specific styles */
.form-textarea {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    color: var(--text-primary) !important;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.form-textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-textarea[readonly] {
    background: var(--bg-quaternary);
    color: var(--text-primary) !important;
    cursor: default;
}

.export-instructions {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius);
    border-left: 4px solid var(--primary);
}

.export-instructions p {
    margin: 0 0 0.5rem 0;
    color: var(--text-secondary);
    font-weight: 600;
}

.export-instructions ul {
    margin: 0;
    padding-left: 1.25rem;
    color: var(--text-muted);
}

.export-instructions li {
    margin-bottom: 0.25rem;
}

/* Export Tabs */
.export-tabs {
    display: flex;
    background: var(--bg-quaternary);
    border-radius: var(--radius);
    padding: 0.25rem;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.export-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: transparent;
    border: none;
    border-radius: calc(var(--radius) - 0.25rem);
    color: var(--text-muted);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    justify-content: center;
}

.export-tab:hover {
    color: var(--text-secondary);
    background: var(--bg-primary);
}

.export-tab.active {
    background: var(--primary);
    color: var(--text-primary);
}

.export-tab i {
    width: 16px;
    height: 16px;
}
