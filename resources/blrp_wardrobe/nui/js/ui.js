// UI management functions
const WardrobeUI = {
    // Initialize the wardrobe interface
    initialize() {
        const html = `
            <div id="wardrobe-container" class="wardrobe-container" style="display: none;">
                <div class="wardrobe-main">
                    <!-- Header -->
                    <div class="wardrobe-header">
                        <div class="header-left">
                            <h2>
                                <i data-lucide="shirt" class="icon"></i>
                            </h2>
                        </div>
                        <div class="header-center">
                            <!-- Search Bar -->
                            <div class="form-control">
                                <input
                                    type="text"
                                    id="search-input"
                                    placeholder="Search outfits..."
                                    class="form-input"
                                >
                            </div>
                        </div>
                        <div class="header-right">
                            <!-- Action Buttons -->
                            <div class="header-actions">
                                <button id="undress-btn" class="btn btn-secondary" title="Undress">
                                    <i data-lucide="user-x"></i>
                                    Undress
                                </button>
                                <button id="clothing-store-btn" class="btn btn-secondary" title="Open Clothing Store" style="display: none;">
                                    <i data-lucide="shopping-bag"></i>
                                </button>
                                <button id="import-outfit-btn" class="btn btn-secondary" title="Import Outfit">
                                    <i data-lucide="download"></i>
                                    Import
                                </button>
                                <button id="save-outfit-btn" class="btn btn-primary">
                                    <i data-lucide="plus"></i>
                                    Save Current Outfit
                                </button>
                            </div>
                            <button id="close-wardrobe">
                                <i data-lucide="x"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Toolbar -->
                    <div class="wardrobe-toolbar">
                        <!-- Folder Tags -->
                        <div class="folder-tags-container">
                            <div id="folder-tags" class="folder-tags">
                                <!-- Folder tags will be populated here -->
                            </div>
                            <button id="new-folder-btn" class="btn btn-secondary btn-sm">
                                <i data-lucide="folder-plus"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Outfits Display -->
                    <div class="outfit-container">
                        <div id="outfits-grid">
                            <!-- Outfits will be populated here -->
                        </div>
                        
                        <div id="no-outfits" class="empty-state" style="display: none;">
                            <i data-lucide="shirt" class="icon"></i>
                            <h3>No outfits found</h3>
                            <p>Save your first outfit to get started!</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Outfit Modal -->
            <div id="save-outfit-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 id="save-modal-title">Save Current Outfit</h2>
                        <button id="cancel-save" class="modal-close-btn">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-control mb-4">
                            <input type="text" id="outfit-name-input" placeholder="Outfit name" class="form-input" maxlength="25">
                        </div>
                        <div class="form-control mb-4">
                            <label style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem; display: block;">
                                Save to folder:
                            </label>
                            <select id="outfit-folder-select" class="form-select">
                                <option value="">Unassigned</option>
                            </select>
                        </div>
                        <div class="form-control mb-4">
                            <label class="form-label">Emoji (Optional)</label>
                            <div class="emoji-selector">
                                <div class="selected-emoji-container">
                                    <span id="save-selected-emoji" class="selected-emoji" data-emoji="">No emoji selected</span>
                                    <button type="button" id="save-clear-emoji" class="btn btn-sm btn-secondary">Clear</button>
                                </div>
                                <div class="emoji-search-container">
                                    <input type="text" id="save-emoji-search" placeholder="Search emojis..." class="form-input emoji-search-input">
                                </div>
                                <div class="emoji-grid-container">
                                    <div class="emoji-grid" id="save-emoji-grid">
                                        <!-- Emojis will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <label class="flex items-center gap-2 mb-4" style="color: var(--text-secondary); font-size: 0.875rem;">
                            <input type="checkbox" id="save-hair-checkbox">
                            Save hair with this outfit
                        </label>
                        <div id="save-hair-info" class="mb-4" style="display: none; padding: 1rem; background: var(--primary-light); border-radius: var(--radius); color: var(--primary); font-size: 0.875rem;">
                            Your current hairstyle will always be applied with this outfit.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="cancel-save-alt" class="btn btn-secondary">Cancel</button>
                        <button id="confirm-save" class="btn btn-primary">Save</button>
                    </div>
                </div>
            </div>

            <!-- New Folder Modal -->
            <div id="new-folder-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Create New Folder</h2>
                        <button id="cancel-folder" class="modal-close-btn">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-control mb-4">
                            <input type="text" id="folder-name-input" placeholder="Folder name" class="form-input" maxlength="50">
                        </div>
                        <div class="form-control mb-4">
                            <label class="form-label">Folder Color</label>
                            <input type="hidden" id="folder-color-input" value="#5865f2">
                            <div id="folder-color-picker"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="cancel-folder-alt" class="btn btn-secondary">Cancel</button>
                        <button id="confirm-folder" class="btn btn-primary">Create</button>
                    </div>
                </div>
            </div>

            <!-- Edit Folder Modal -->
            <div id="edit-folder-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Edit Folder</h2>
                        <button id="cancel-edit-folder" class="modal-close-btn">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" id="edit-folder-id">
                        <div class="form-control mb-4">
                            <input type="text" id="edit-folder-name-input" placeholder="Folder name" class="form-input" maxlength="50">
                        </div>
                        <div class="form-control mb-4">
                            <label class="form-label">Folder Color</label>
                            <input type="hidden" id="edit-folder-color-input" value="#5865f2">
                            <div id="edit-folder-color-picker"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="delete-edit-folder" class="btn btn-danger" disabled>Delete Folder</button>
                        <div style="margin-left: auto; display: flex; gap: 0.75rem;">
                            <button id="cancel-edit-folder-alt" class="btn btn-secondary">Cancel</button>
                            <button id="confirm-edit-folder" class="btn btn-primary">Update</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Edit Outfit Modal -->
            <div id="edit-outfit-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Edit Outfit</h2>
                        <button id="cancel-edit" class="modal-close-btn">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-control mb-4">
                            <label class="form-label">Outfit Name</label>
                            <input type="text" id="edit-outfit-input" placeholder="Outfit name" class="form-input" maxlength="25">
                        </div>
                        <div class="form-control mb-4">
                            <label class="form-label">Emoji</label>
                            <div class="emoji-selector">
                                <div class="selected-emoji-container">
                                    <span id="selected-emoji" class="selected-emoji" data-emoji="">No emoji selected</span>
                                    <button type="button" id="clear-emoji" class="btn btn-sm btn-secondary">Clear</button>
                                </div>
                                <div class="emoji-search-container">
                                    <input type="text" id="emoji-search" placeholder="Search emojis..." class="form-input emoji-search-input">
                                </div>
                                <div class="emoji-grid-container">
                                    <div class="emoji-grid" id="emoji-grid">
                                        <!-- Emojis will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="cancel-edit-alt" class="btn btn-secondary">Cancel</button>
                        <button id="confirm-edit" class="btn btn-primary">Save Changes</button>
                    </div>
                </div>
            </div>

            <!-- Delete Outfit Modal -->
            <div id="delete-outfit-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Delete Outfit</h2>
                        <button id="cancel-delete" class="modal-close-btn">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p style="color: var(--text-secondary); margin-bottom: 1rem;">
                            Are you sure you want to delete "<span id="delete-outfit-name" style="color: var(--text-primary); font-weight: 600;"></span>"?
                        </p>
                        <p style="color: var(--text-danger); font-size: 0.875rem;">
                            This action cannot be undone.
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button id="cancel-delete-alt" class="btn btn-secondary">Cancel</button>
                        <button id="confirm-delete" class="btn btn-danger">Delete</button>
                    </div>
                </div>
            </div>

            <!-- Import Outfit Modal -->
            <div id="import-outfit-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Import Outfit</h2>
                        <button id="cancel-import" class="modal-close-btn">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-control">
                            <label for="import-outfit-name">Outfit Name</label>
                            <input type="text" id="import-outfit-name" placeholder="Enter outfit name..." class="form-input">
                        </div>
                        <div class="form-control">
                            <label for="import-outfit-data">Outfit Data (odev format)</label>
                            <textarea id="import-outfit-data" placeholder="Paste outfit data here..." class="form-textarea" rows="10"></textarea>
                        </div>
                        <div class="form-control">
                            <label for="import-outfit-folder">Folder</label>
                            <select id="import-outfit-folder" class="form-select">
                                <option value="">Unassigned</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="cancel-import-alt" class="btn btn-secondary">Cancel</button>
                        <button id="confirm-import" class="btn btn-primary">Import Outfit</button>
                    </div>
                </div>
            </div>

            <!-- Export Outfit Modal -->
            <div id="export-outfit-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 id="export-modal-title">Export Outfit</h2>
                        <button id="cancel-export" class="modal-close-btn">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <!-- Export Format Tabs -->
                        <div class="export-tabs">
                            <button id="compact-export-tab" class="export-tab active">
                                <i data-lucide="minimize-2"></i>
                                Compact
                            </button>
                            <button id="expanded-export-tab" class="export-tab">
                                <i data-lucide="maximize-2"></i>
                                Expanded
                            </button>
                        </div>

                        <div class="form-control">
                            <label for="export-outfit-data">Outfit Data (odev format)</label>
                            <textarea id="export-outfit-data" class="form-textarea" rows="15" readonly></textarea>
                        </div>
                        <div class="export-instructions">
                            <p><strong>Instructions:</strong></p>
                            <ul>
                                <li>Copy the data above using Ctrl+C</li>
                                <li>You can also import this data into another wardrobe</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="copy-export-data" class="btn btn-secondary">Copy to Clipboard</button>
                        <button id="close-export" class="btn btn-primary">Close</button>
                    </div>
                </div>
            </div>
        `;

        $('body').append(html);
        
        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        // Initialize emoji grids after UI is created
        this.initializeEmojiGrids();
    },

    // Show wardrobe
    show(data) {
        wardrobeData = data;
        currentOutfits = data.outfits || [];
        currentFolders = data.folders || [];

        // Handle permissions
        this.handlePermissions(data);

        // Populate folder options
        WardrobeFolders.updateFolderOptions();

        // Render outfits
        WardrobeOutfits.render();

        // Show interface with proper timing
        const container = $('#wardrobe-container');
        if (container.is(':visible')) {
            // Already visible, just update content
            return;
        }

        // Position wardrobe - center on first open, use last position otherwise
        const wardrobeMain = $('.wardrobe-main');
        if (!WardrobeEvents.hasBeenOpened || !WardrobeEvents.lastPosition) {
            // First time opening - center it
            wardrobeMain.css({
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)'
            });
            WardrobeEvents.hasBeenOpened = true;

            // Store the centered position after the element is visible
            setTimeout(() => {
                const rect = wardrobeMain[0].getBoundingClientRect();
                WardrobeEvents.lastPosition = {
                    left: rect.left,
                    top: rect.top
                };
            }, 50);
        } else {
            // Use last known position with boundary checking
            const wardrobeWidth = wardrobeMain.outerWidth();
            const wardrobeHeight = wardrobeMain.outerHeight();

            let left = WardrobeEvents.lastPosition.left;
            let top = WardrobeEvents.lastPosition.top;

            // Ensure the wardrobe stays within viewport bounds
            const minX = 0;
            const minY = 0;
            const maxX = window.innerWidth - wardrobeWidth;
            const maxY = window.innerHeight - wardrobeHeight;

            left = Math.max(minX, Math.min(maxX, left));
            top = Math.max(minY, Math.min(maxY, top));

            wardrobeMain.css({
                left: left + 'px',
                top: top + 'px',
                transform: 'none'
            });

            // Update stored position if it was adjusted
            WardrobeEvents.lastPosition = { left, top };
        }

        container.css('display', 'flex').hide().fadeIn(WardrobeConfig.animations.fadeIn);
    },

    // Handle permissions and show/hide UI elements
    handlePermissions(data) {
        const allowDress = data.allow_dress !== false;
        const allowUndress = data.allow_undress !== false;
        const nearClothingStore = data.near_clothing_store === true;

        // Show/hide save outfit button
        if (allowDress) {
            $('#save-outfit-btn').show();
        } else {
            $('#save-outfit-btn').hide();
        }

        // Show/hide undress button
        if (allowUndress) {
            $('#undress-btn').show();
        } else {
            $('#undress-btn').hide();
        }

        // Show/hide clothing store button
        if (nearClothingStore) {
            $('#clothing-store-btn').show();
        } else {
            $('#clothing-store-btn').hide();
        }

        // Show message if dress is disabled
        if (!allowDress) {
            this.showPermissionMessage();
        } else {
            this.hidePermissionMessage();
        }
    },

    // Show permission message
    showPermissionMessage() {
        const existingMessage = $('#permission-message');
        if (existingMessage.length === 0) {
            const message = `
                <div id="permission-message" class="permission-message">
                    <i data-lucide="info"></i>
                    <span>You can only undress at this location</span>
                </div>
            `;
            $('.wardrobe-toolbar').prepend(message);

            // Initialize Lucide icons for the new message
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }
    },

    // Hide permission message
    hidePermissionMessage() {
        $('#permission-message').remove();
    },

    // Initialize emoji grids with fallback
    initializeEmojiGrids() {
        // Basic emoji list as fallback
        const basicEmojis = [
            '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '☺️', '😚',
            '👔', '👗', '👕', '👖', '👚', '👙', '🩱', '🩲', '🩳', '👘', '🥻', '🦺', '👞', '👟', '👠', '👡', '🥿', '👢', '👒', '🎩',
            '🧢', '👑', '💍', '👜', '👛', '🎒', '🕶️', '🥽', '🧣', '🧤', '🧥', '⭐', '💎', '🔥', '❄️', '🌟', '💫', '✨', '🎯', '🎨',
            '🎭', '🎪', '🎵', '🎶', '🎤', '🎧', '🎸', '🎹', '🥁', '🎺', '🎷', '🎻', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖️', '🏵️', '🎗️',
            '🎀', '💰', '💳', '💸', '💵', '💴', '💶', '💷', '🎁', '🎉', '🎊', '🎈', '🎂', '🍰', '🧁', '🍭', '🍬', '🍫', '🍩', '🍪',
            '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮', '🐷', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒', '🐔',
            '🍎', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬',
            '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🛹', '🛼'
        ];

        // Populate both grids
        this.populateEmojiGrid('emoji-grid', basicEmojis, false);
        this.populateEmojiGrid('save-emoji-grid', basicEmojis, true);

        // Try to use EmojiManager if available
        if (typeof EmojiManager !== 'undefined' && EmojiManager.initialize) {
            setTimeout(() => {
                EmojiManager.initialize();
            }, 100);
        }
    },

    // Populate emoji grid with emojis
    populateEmojiGrid(gridId, emojis, isSaveModal) {
        const grid = document.getElementById(gridId);
        if (!grid) return;

        const className = isSaveModal ? 'emoji-option save-emoji-option' : 'emoji-option';
        grid.innerHTML = emojis.map(emoji =>
            `<span class="${className}" data-emoji="${emoji}" title="${emoji}">${emoji}</span>`
        ).join('');
    },

    // Close wardrobe
    close() {
        // Close any open menus first
        $('.outfit-menu').addClass('hidden');

        // Hide modals
        $('.modal-overlay').fadeOut(WardrobeConfig.animations.fadeOut);

        // Hide main interface
        $('#wardrobe-container').fadeOut(WardrobeConfig.animations.fadeOut, function() {
            // Send close event to game after animation completes
            WardrobeAPI.closeWardrobe();
        });
    }
};
