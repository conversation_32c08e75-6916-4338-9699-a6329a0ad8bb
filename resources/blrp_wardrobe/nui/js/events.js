// Event handlers and initialization
const WardrobeEvents = {
    // Drag state
    isDragging: false,
    dragOffset: { x: 0, y: 0 },
    lastPosition: null,
    hasBeenOpened: false,

    // Initialize all event handlers
    initialize() {
        this.bindGlobalEvents();
        this.bindToolbarEvents();
        this.bindModalEvents();
        this.bindNUICallbacks();
        this.bindDragEvents();
    },

    // Bind global events
    bindGlobalEvents() {
        // ESC key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                e.preventDefault();
                e.stopPropagation();
                
                if ($('#save-outfit-modal').is(':visible')) {
                    WardrobeOutfits.hideSaveModal();
                } else if ($('#new-folder-modal').is(':visible')) {
                    WardrobeFolders.hideCreateModal();
                } else if ($('#edit-folder-modal').is(':visible')) {
                    WardrobeFolders.hideEditModal();
                } else if ($('#edit-outfit-modal').is(':visible')) {
                    WardrobeOutfits.hideEditModal();
                } else if ($('#delete-outfit-modal').is(':visible')) {
                    WardrobeOutfits.hideDeleteModal();
                } else if ($('#import-outfit-modal').is(':visible')) {
                    WardrobeOutfits.hideImportModal();
                } else if ($('#export-outfit-modal').is(':visible')) {
                    WardrobeOutfits.hideExportModal();
                } else if ($('#wardrobe-container').is(':visible')) {
                    WardrobeUI.close();
                }
            }
        });
        
        // Prevent modal overlay clicks from affecting main wardrobe
        $(document).on('click', '.modal-overlay', function(e) {
            if (e.target === this) {
                e.preventDefault();
                e.stopPropagation();
                
                if ($(this).attr('id') === 'save-outfit-modal') {
                    WardrobeOutfits.hideSaveModal();
                } else if ($(this).attr('id') === 'new-folder-modal') {
                    WardrobeFolders.hideCreateModal();
                } else if ($(this).attr('id') === 'edit-folder-modal') {
                    WardrobeFolders.hideEditModal();
                } else if ($(this).attr('id') === 'edit-outfit-modal') {
                    WardrobeOutfits.hideEditModal();
                } else if ($(this).attr('id') === 'delete-outfit-modal') {
                    WardrobeOutfits.hideDeleteModal();
                } else if ($(this).attr('id') === 'import-outfit-modal') {
                    WardrobeOutfits.hideImportModal();
                } else if ($(this).attr('id') === 'export-outfit-modal') {
                    WardrobeOutfits.hideExportModal();
                }
            }
        });
        
        // Prevent modal content clicks from bubbling
        $(document).on('click', '.modal-content', function(e) {
            e.stopPropagation();
        });

        // NUI message handler
        window.addEventListener('message', function(event) {
            const data = event.data;

            switch (data.action) {
                case 'openWardrobe':
                    WardrobeUI.show(data.data);
                    break;
                case 'closeWardrobe':
                    WardrobeUI.close();
                    break;
                case 'refreshWardrobe':
                    // Handle refresh without showing/hiding the interface
                    if ($('#wardrobe-container').is(':visible')) {
                        wardrobeData = data.data;
                        currentOutfits = data.data.outfits || [];
                        currentFolders = data.data.folders || [];
                        WardrobeFolders.updateFolderOptions();
                        WardrobeOutfits.render();
                    }
                    break;
            }
        });
    },

    // Bind toolbar events
    bindToolbarEvents() {
        // Close wardrobe
        $(document).on('click', '#close-wardrobe', function() {
            WardrobeUI.close();
        });

        // Search input
        $(document).on('input', '#search-input', function() {
            searchQuery = $(this).val();
            WardrobeOutfits.render();
        });

        // Sort select
        $(document).on('change', '#sort-select', function() {
            sortBy = $(this).val();
            WardrobeOutfits.render();
        });

        // Save outfit button
        $(document).on('click', '#save-outfit-btn', function() {
            WardrobeOutfits.showSaveModal();
        });

        // Undress button
        $(document).on('click', '#undress-btn', function() {
            WardrobeAPI.undressPlayer();
            WardrobeUI.close();
        });

        // Clothing store button
        $(document).on('click', '#clothing-store-btn', function() {
            WardrobeAPI.openClothingStore();
        });

        // New folder button
        $(document).on('click', '#new-folder-btn', function() {
            WardrobeFolders.showCreateModal();
        });

        // Import outfit button
        $(document).on('click', '#import-outfit-btn', function() {
            WardrobeOutfits.showImportModal();
        });
    },

    // Bind modal events
    bindModalEvents() {
        // Save outfit modal
        $(document).on('click', '#confirm-save', function() {
            WardrobeOutfits.save();
        });

        $(document).on('click', '#cancel-save, #cancel-save-alt', function() {
            WardrobeOutfits.hideSaveModal();
        });

        // Save hair checkbox
        $(document).on('change', '#save-hair-checkbox', function() {
            if ($(this).is(':checked')) {
                $('#save-hair-info').slideDown(200);
            } else {
                $('#save-hair-info').slideUp(200);
            }
        });

        // New folder modal
        $(document).on('click', '#confirm-folder', function() {
            WardrobeFolders.create();
        });

        $(document).on('click', '#cancel-folder, #cancel-folder-alt', function() {
            WardrobeFolders.hideCreateModal();
        });

        // Edit folder modal
        $(document).on('click', '#confirm-edit-folder', function() {
            WardrobeFolders.update();
        });

        $(document).on('click', '#cancel-edit-folder, #cancel-edit-folder-alt', function() {
            WardrobeFolders.hideEditModal();
        });

        // Color picker events
        $(document).on('click', '.color-option', function() {
            const color = $(this).data('color');
            const inputId = $(this).data('input');

            // Update hidden input
            $(`#${inputId}`).val(color);

            // Update visual selection
            $(this).siblings('.color-option').removeClass('selected');
            $(this).addClass('selected');
        });

        // Edit outfit modal
        $(document).on('click', '#confirm-edit', function() {
            WardrobeOutfits.editOutfit();
        });

        $(document).on('click', '#cancel-edit, #cancel-edit-alt', function() {
            WardrobeOutfits.hideEditModal();
        });

        // Emoji picker events (using event delegation for dynamically added emojis)
        $(document).on('click', '.emoji-option', function() {
            const emoji = $(this).data('emoji');
            const isInSaveModal = $(this).hasClass('save-emoji-option');

            if (isInSaveModal) {
                $('#save-selected-emoji').text(emoji).data('emoji', emoji);
                // Clear selection from all save modal emojis
                $('.save-emoji-option').removeClass('selected');
            } else {
                $('#selected-emoji').text(emoji).data('emoji', emoji);
                // Clear selection from all edit modal emojis
                $('#emoji-grid .emoji-option').removeClass('selected');
            }

            // Add selection to clicked emoji
            $(this).addClass('selected');
        });

        // Clear emoji buttons
        $(document).on('click', '#clear-emoji', function() {
            $('#selected-emoji').text('').data('emoji', '');
            $('#emoji-grid .emoji-option').removeClass('selected');
        });

        $(document).on('click', '#save-clear-emoji', function() {
            $('#save-selected-emoji').text('').data('emoji', '');
            $('.save-emoji-option').removeClass('selected');
        });

        // Emoji search events
        $(document).on('input', '#emoji-search', function() {
            const query = $(this).val().toLowerCase();
            WardrobeEvents.filterEmojis('#emoji-grid', query);
        });

        $(document).on('input', '#save-emoji-search', function() {
            const query = $(this).val().toLowerCase();
            WardrobeEvents.filterEmojis('#save-emoji-grid', query);
        });

        // Delete outfit modal
        $(document).on('click', '#confirm-delete', function() {
            WardrobeOutfits.deleteOutfit();
        });

        $(document).on('click', '#cancel-delete, #cancel-delete-alt', function() {
            WardrobeOutfits.hideDeleteModal();
        });

        // Import outfit modal
        $(document).on('click', '#confirm-import', function() {
            WardrobeOutfits.importOutfit();
        });

        $(document).on('click', '#cancel-import, #cancel-import-alt', function() {
            WardrobeOutfits.hideImportModal();
        });

        // Export outfit modal
        $(document).on('click', '#copy-export-data', function() {
            WardrobeOutfits.copyExportData();
        });

        $(document).on('click', '#close-export, #cancel-export', function() {
            WardrobeOutfits.hideExportModal();
        });

        // Export format tabs
        $(document).on('click', '#compact-export-tab', function() {
            WardrobeOutfits.switchExportFormat('compact');
        });

        $(document).on('click', '#expanded-export-tab', function() {
            WardrobeOutfits.switchExportFormat('expanded');
        });

        // Enter key in inputs
        $(document).on('keypress', '#outfit-name-input', function(e) {
            if (e.which === 13) { // Enter key
                WardrobeOutfits.save();
            }
        });

        $(document).on('keypress', '#folder-name-input', function(e) {
            if (e.which === 13) { // Enter key
                WardrobeFolders.create();
            }
        });

        $(document).on('keypress', '#edit-folder-name-input', function(e) {
            if (e.which === 13) { // Enter key
                WardrobeFolders.update();
            }
        });

        $(document).on('keypress', '#edit-outfit-input', function(e) {
            if (e.which === 13) { // Enter key
                WardrobeOutfits.editOutfit();
            }
        });

        $(document).on('keypress', '#import-outfit-name', function(e) {
            if (e.which === 13) { // Enter key
                WardrobeOutfits.importOutfit();
            }
        });

        // Helper function to unescape HTML entities
        function unescapeHtml(text) {
            if (!text || typeof text !== 'string') {
                return text || '';
            }
            return text.replace(/&quot;/g, '"').replace(/&#39;/g, "'");
        }

        // Outfit action events using event delegation with more specific selectors
        $(document).off('click.outfit-actions').on('click.outfit-actions', '#outfits-grid .apply-outfit', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Try both .attr() and .data() methods to get the outfit name
            const rawOutfitName = $(this).attr('data-outfit') || $(this).data('outfit');
            const outfitName = unescapeHtml(rawOutfitName || '');

            if (outfitName) {
                WardrobeAPI.applyOutfit(outfitName);
                // WardrobeUI.close();
            }
        });

        $(document).off('click.outfit-favorites').on('click.outfit-favorites', '#outfits-grid .toggle-favorite', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const rawOutfitName = $(this).attr('data-outfit') || $(this).data('outfit');
            const outfitName = unescapeHtml(rawOutfitName || '');
            if (outfitName) {
                WardrobeAPI.toggleFavorite(outfitName);
            }
        });

        $(document).off('click.outfit-edit').on('click.outfit-edit', '#outfits-grid .edit-outfit', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const rawOutfitName = $(this).attr('data-outfit') || $(this).data('outfit');
            const outfitName = unescapeHtml(rawOutfitName || '');
            if (outfitName) {
                WardrobeOutfits.showEditModal(outfitName);
            }
        });

        $(document).off('click.outfit-share').on('click.outfit-share', '#outfits-grid .share-outfit', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const rawOutfitName = $(this).attr('data-outfit') || $(this).data('outfit');
            const outfitName = unescapeHtml(rawOutfitName || '');
            if (outfitName) {
                WardrobeAPI.shareOutfit(outfitName);
            }
        });

        $(document).off('click.outfit-upgrade').on('click.outfit-upgrade', '#outfits-grid .upgrade-outfit', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const rawOutfitName = $(this).attr('data-outfit') || $(this).data('outfit');
            const outfitName = unescapeHtml(rawOutfitName || '');
            if (outfitName) {
                // Auto-detect gender based on current player model instead of using dialog
                WardrobeAPI.upgradeOutfit(outfitName);
            }
        });

        $(document).off('click.outfit-delete').on('click.outfit-delete', '#outfits-grid .delete-outfit', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const rawOutfitName = $(this).attr('data-outfit') || $(this).data('outfit');
            const outfitName = unescapeHtml(rawOutfitName || '');
            if (outfitName) {
                WardrobeOutfits.showDeleteModal(outfitName);
            }
        });

        $(document).off('click.outfit-replace').on('click.outfit-replace', '#outfits-grid .replace-outfit', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const rawOutfitName = $(this).attr('data-outfit') || $(this).data('outfit');
            const outfitName = unescapeHtml(rawOutfitName || '');
            if (outfitName) {
                WardrobeAPI.replaceOutfit(outfitName);
            }
        });

        $(document).off('click.outfit-export').on('click.outfit-export', '#outfits-grid .export-outfit', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const rawOutfitName = $(this).attr('data-outfit') || $(this).data('outfit');
            const outfitName = unescapeHtml(rawOutfitName || '');
            if (outfitName) {
                WardrobeOutfits.showExportModal(outfitName);
            }
        });

        // Outfit menu toggle events
        $(document).on('click', '.outfit-menu-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const container = $(this).closest('.outfit-menu-container');
            const menu = container.find('.outfit-menu');
            const isCurrentlyHidden = menu.hasClass('hidden');

            // Clear any pending close timer
            if (window.menuCloseTimer) {
                clearTimeout(window.menuCloseTimer);
                window.menuCloseTimer = null;
            }

            // Close all other menus first
            $('.outfit-menu').addClass('hidden');

            // Toggle current menu
            if (isCurrentlyHidden) {
                // Calculate position for fixed positioning
                const buttonRect = $(this)[0].getBoundingClientRect();
                const menuWidth = 200; // min-width from CSS
                const menuHeight = 150; // estimated height

                let top = buttonRect.bottom + 8; // 8px margin
                let left = buttonRect.right - menuWidth;

                // Adjust if menu goes outside viewport
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                // If menu goes outside right edge, position it to the left
                if (left < 20) {
                    left = buttonRect.left;
                }

                // If menu goes outside bottom edge, position it above
                if (top + menuHeight > viewportHeight - 20) {
                    top = buttonRect.top - menuHeight - 8;
                }

                menu.css({
                    'top': top + 'px',
                    'left': left + 'px'
                }).removeClass('hidden');
            }
        });

        // Outfit folder button events
        $(document).on('click', '.outfit-folder-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const container = $(this).closest('.outfit-folder-container');
            const menu = container.find('.outfit-folder-menu');
            const isCurrentlyHidden = menu.hasClass('hidden');

            // Close all other folder menus
            $('.outfit-folder-menu').addClass('hidden');

            // Toggle current menu
            if (isCurrentlyHidden) {
                // Calculate position for fixed positioning
                const buttonRect = $(this)[0].getBoundingClientRect();
                const menuWidth = 200; // min-width from CSS
                const menuHeight = 100; // estimated height for folder menu

                let top = buttonRect.bottom + 8; // 8px margin
                let left = buttonRect.right - menuWidth;

                // Adjust if menu goes outside viewport
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                // If menu goes outside right edge, position it to the left
                if (left < 20) {
                    left = buttonRect.left;
                }

                // If menu goes outside bottom edge, position it above
                if (top + menuHeight > viewportHeight - 20) {
                    top = buttonRect.top - menuHeight - 8;
                }

                menu.css({
                    'top': top + 'px',
                    'left': left + 'px'
                }).removeClass('hidden');
            }
        });

        // Folder option click events
        $(document).on('click', '.folder-option', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Use both .attr() and .data() methods for reliable data retrieval
            const rawOutfitName = $(this).attr('data-outfit') || $(this).data('outfit');
            const outfitName = unescapeHtml(rawOutfitName || '');
            const folderId = $(this).attr('data-folder') || $(this).data('folder');

            console.log('Folder option clicked:');
            console.log('- Raw outfit name:', rawOutfitName);
            console.log('- Unescaped outfit name:', outfitName);
            console.log('- Folder ID:', folderId);

            if (outfitName) {
                // Move outfit to folder
                WardrobeAPI.moveOutfitToFolder(outfitName, folderId || null);

                // Close menu
                $(this).closest('.outfit-folder-menu').addClass('hidden');
            } else {
                console.log('ERROR: No outfit name found for folder change!');
            }
        });

        // Menu hover events to keep menus open
        $(document).on('mouseenter', '.outfit-menu, .outfit-folder-menu, .outfit-menu-container, .outfit-folder-container', function(e) {
            // Clear any pending close timer when hovering over menu or container
            if (window.menuCloseTimer) {
                clearTimeout(window.menuCloseTimer);
                window.menuCloseTimer = null;
            }
        });

        $(document).on('mouseleave', '.outfit-menu-container, .outfit-folder-container', function(e) {
            // Set a timer to close the menu after leaving the container
            const menu = $(this).find('.outfit-menu, .outfit-folder-menu');
            if (!menu.hasClass('hidden')) {
                window.menuCloseTimer = setTimeout(() => {
                    menu.addClass('hidden');
                    window.menuCloseTimer = null;
                }, 300); // 300ms delay before closing
            }
        });



        // Close menus when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.outfit-menu-container, .outfit-folder-container, .outfit-menu, .outfit-folder-menu').length) {
                $('.outfit-menu, .outfit-folder-menu').addClass('hidden');
                if (window.menuCloseTimer) {
                    clearTimeout(window.menuCloseTimer);
                    window.menuCloseTimer = null;
                }
            }
        });

        // Close menu after action buttons are clicked
        $(document).on('click', '.outfit-menu button', function(e) {
            // Close menu after action
            $(this).closest('.outfit-menu').addClass('hidden');
            if (window.menuCloseTimer) {
                clearTimeout(window.menuCloseTimer);
                window.menuCloseTimer = null;
            }
        });
    },

    // Filter emojis based on search query
    filterEmojis(gridSelector, query) {
        const grid = $(gridSelector);
        const emojis = grid.find('.emoji-option');

        if (!query || query.trim() === '') {
            // Show all emojis if no query
            emojis.show();
            return;
        }

        emojis.each(function() {
            const emoji = $(this).data('emoji');
            const title = $(this).attr('title') || '';

            // Simple search - check if emoji or title contains the query
            const matches = emoji.includes(query) ||
                           title.toLowerCase().includes(query) ||
                           WardrobeEvents.emojiMatchesKeyword(emoji, query);

            if (matches) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    },

    // Simple keyword matching for common emojis
    emojiMatchesKeyword(emoji, keyword) {
        const emojiKeywords = {
            '😀': ['happy', 'smile', 'grin'],
            '😃': ['happy', 'smile', 'joy'],
            '😄': ['happy', 'smile', 'laugh'],
            '😍': ['love', 'heart', 'eyes'],
            '😎': ['cool', 'sunglasses'],
            '👔': ['tie', 'shirt', 'formal', 'business'],
            '👗': ['dress', 'formal'],
            '👕': ['shirt', 'tshirt'],
            '👖': ['pants', 'jeans'],
            '👠': ['heel', 'shoe'],
            '👟': ['sneaker', 'shoe'],
            '🎩': ['hat', 'formal'],
            '👑': ['crown', 'king', 'queen'],
            '💍': ['ring', 'wedding'],
            '⭐': ['star', 'favorite'],
            '💎': ['diamond', 'gem'],
            '🔥': ['fire', 'hot', 'lit'],
            '❄️': ['snow', 'cold', 'winter'],
            '🌟': ['star', 'sparkle'],
            '✨': ['sparkle', 'magic']
        };

        const keywords = emojiKeywords[emoji] || [];
        return keywords.some(kw => kw.includes(keyword));
    },

    // Bind NUI callbacks
    bindNUICallbacks() {
        // Register NUI callbacks for server communication
        const callbacks = {
            closeWardrobe: () => ({ status: 'ok' }),
            saveOutfit: (data) => ({ status: 'ok' }),
            applyOutfit: (data) => ({ status: 'ok' }),
            toggleFavorite: (data) => ({ status: 'ok' }),
            editOutfit: (data) => ({ status: 'ok' }),
            renameOutfit: (data) => ({ status: 'ok' }),
            deleteOutfit: (data) => ({ status: 'ok' }),
            shareOutfit: (data) => ({ status: 'ok' }),
            upgradeOutfit: (data) => ({ status: 'ok' }),
            createFolder: (data) => ({ status: 'ok' }),
            updateFolder: (data) => ({ status: 'ok' }),
            deleteFolder: (data) => ({ status: 'ok' }),
            moveOutfitToFolder: (data) => ({ status: 'ok' }),
            undressPlayer: (data) => ({ status: 'ok' }),
            openClothingStore: (data) => ({ status: 'ok' })
        };

        // Note: NUI callbacks are handled by the game engine
        // This is just for reference of what callbacks are available
    },

    // Bind drag events for making the wardrobe draggable
    bindDragEvents() {
        // Start dragging when mousedown on header
        $(document).on('mousedown', '.wardrobe-header', (e) => {
            // Don't start drag if clicking on interactive elements
            if ($(e.target).closest('button, input, select, .header-actions').length > 0) {
                return;
            }

            this.isDragging = true;
            const wardrobeMain = $('.wardrobe-main');
            const rect = wardrobeMain[0].getBoundingClientRect();

            // Calculate offset from mouse to top-left of wardrobe
            this.dragOffset.x = e.clientX - rect.left;
            this.dragOffset.y = e.clientY - rect.top;

            // Add dragging class for visual feedback
            wardrobeMain.addClass('dragging');

            // Prevent text selection
            e.preventDefault();
        });

        // Handle dragging
        $(document).on('mousemove', (e) => {
            if (!this.isDragging) return;

            const wardrobeMain = $('.wardrobe-main');

            // Calculate new position
            let newX = e.clientX - this.dragOffset.x;
            let newY = e.clientY - this.dragOffset.y;

            // Get wardrobe dimensions
            const wardrobeRect = wardrobeMain[0].getBoundingClientRect();
            const wardrobeWidth = wardrobeRect.width;
            const wardrobeHeight = wardrobeRect.height;

            // Constrain to viewport bounds
            const minX = 0;
            const minY = 0;
            const maxX = window.innerWidth - wardrobeWidth;
            const maxY = window.innerHeight - wardrobeHeight;

            newX = Math.max(minX, Math.min(maxX, newX));
            newY = Math.max(minY, Math.min(maxY, newY));

            // Apply new position
            wardrobeMain.css({
                left: newX + 'px',
                top: newY + 'px',
                transform: 'none'
            });
        });

        // Stop dragging
        $(document).on('mouseup', () => {
            if (this.isDragging) {
                this.isDragging = false;
                const wardrobeMain = $('.wardrobe-main');
                wardrobeMain.removeClass('dragging');

                // Store the current position
                const rect = wardrobeMain[0].getBoundingClientRect();
                this.lastPosition = {
                    left: rect.left,
                    top: rect.top
                };
            }
        });

        // Also stop dragging if mouse leaves the window
        $(document).on('mouseleave', () => {
            if (this.isDragging) {
                this.isDragging = false;
                const wardrobeMain = $('.wardrobe-main');
                wardrobeMain.removeClass('dragging');

                // Store the current position
                const rect = wardrobeMain[0].getBoundingClientRect();
                this.lastPosition = {
                    left: rect.left,
                    top: rect.top
                };
            }
        });
    }
};

// Initialize everything when document is ready
$(document).ready(function() {
    // Initialize UI
    WardrobeUI.initialize();
    
    // Initialize events
    WardrobeEvents.initialize();
});
