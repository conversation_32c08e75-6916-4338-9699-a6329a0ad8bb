tHud = {}
T.bindInstance('hud', tHud)

local last_pause_state = nil
local in_air = false
local is_driving = false
local vehicle = false
local clock_override = false
local hunger = 0
local thirst = 0
local seatbeltState = false
local seatbeltFlash = false
hide_location = false
local ui_visible = true

Citizen.CreateThread(function()
  while GetResourceState('blrp_core') ~= 'started' do
    Wait(0)
  end

  seatbeltFlash = exports.blrp_core:LocalStorageGetBool('UI-seatbelt-flash')
end)

exports('SetHideLocation', function(_hide_location)
	hide_location = _hide_location
end)

tHud.getHideLocation = function()
	return hide_location
end

exports('GetHideLocation', function()
	return hide_location
end)

exports('SetUIVisible', function(ui_visible)
	SendNUIMessage({ showUi = ui_visible })
	ui_visible = ui_visible
end)

RegisterNetEvent('camera:hideUI', function(toggle)
	if toggle == nil then return end
  ui_visible = toggle
	SendNUIMessage({showUi = toggle})
end)

RegisterNetEvent('vrp:client:seatbeltStatusChanged', function(state)
	seatbeltState = state
end)

RegisterNetEvent('vrp:client:seatbeltFlashChanged', function(state)
	seatbeltFlash = state
	exports.blrp_core:LocalStorageSet('UI-seatbelt-flash', seatbeltFlash)
end)

RegisterNetEvent('cd_easytime:ForceUpdate', function(override)
	clock_override = override
end)

exports('SetWalking', function(walking)
	if walking then
		SendNUIMessage({ action = 'enable_walking' })
	else
		SendNUIMessage({ action = 'disable_walking' })
	end
end)

AddEventHandler('core:client:characterAttributeChanged', function(key, new_value)
	if key == 'noclip_active' then
		if new_value then
			SendNUIMessage({ action = 'enable_noclip' })
		else
			SendNUIMessage({ action = 'disable_noclip' })
		end
	end

	if key == 'esp_active' then
		if new_value then
			SendNUIMessage({ action = 'enable_esp' })
		else
			SendNUIMessage({ action = 'disable_esp' })
		end
	end

	if key == 'god_active' then
		if new_value then
			SendNUIMessage({ action = 'enable_god' })
		else
			SendNUIMessage({ action = 'disable_god' })
		end
	end

	if key == 'ghost_active' then
		if new_value then
			SendNUIMessage({ action = 'enable_ghost' })
		else
			SendNUIMessage({ action = 'disable_ghost' })
		end
	end
end)

local nitro_enabled = false

Citizen.CreateThread(function()
	SetTimeout(3000, function()
		if GetConvarInt('dev', -1) == 1 then
			SendNUIMessage({ action = 'enable_dev' })
		end
	end)

	while true do
		Wait(100)

		if is_driving then
			local speed = math.floor(GetEntitySpeed(vehicle) * 2.236936) -- MPH, KMH = 3.6
			SendNUIMessage({speed = speed, maxspeed = 250})

			if nitro_enabled and GetPedInVehicleSeat(vehicle, -1) == PlayerPedId() then
				SendNUIMessage({ action = 'update_nitro', nitroData = exports.blrp_nitro:GetHudDataPackage() })
			end
		end
	end
end)

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)

		if hide_location then
			SetPlayerBlipPositionThisFrame(-3410.0, 356.0)
			LockMinimapPosition(-3410.0, 356.0)
		else
			UnlockMinimapPosition()
		end
	end
end)

Citizen.CreateThread(function()
	while true do
		Wait(1000)

		local player_ped = PlayerPedId()
		local player_coords = GetEntityCoords(player_ped)
		local player_heading = GetEntityHeading(player_ped)

		is_driving = IsPedInAnyVehicle(player_ped, false)
    vehicle = GetVehiclePedIsIn(player_ped, false)

    local spectated_ped = exports.blrp_core:GetSpectatedPed()

    if spectated_ped then
      is_driving = IsPedInAnyVehicle(spectated_ped, false)
      vehicle = GetVehiclePedIsIn(spectated_ped, false)
    end

		if is_driving and Entity(vehicle).state.nitrous_equipped then
			nitro_enabled = true
		else
			nitro_enabled = false
		end

		-- Street label logic

		local zone_id = GetNameOfZone(player_coords)
		local zone = streetlabel_zones[zone_id] or zone_id
		local street_a, street_b = GetStreetNameAtCoord(table.unpack(player_coords))
		street_a = GetStreetNameFromHashKey(street_a)
		street_b = GetStreetNameFromHashKey(street_b)

		local locality_zone = exports.blrp_zones:IsInsideMatchedZone('CustomLocality', false, true)

		if locality_zone and locality_zone.data and locality_zone.data.locality_name then
			zone = locality_zone.data.locality_name
		end

		local direction = '--'

		for heading, _direction in pairs(streetlabel_directions) do
			if math.abs(player_heading - heading) < 22.5 then
				direction = _direction
			end
		end

		local street_str = ''

		if street_a == street_b then
			street_str = street_a
		elseif not street_b or street_b == '' then
			street_str = street_a
		else
			street_str = street_a .. ' / ' .. street_b
		end

    -- Handling for Vinewood <--> Grand Senora tunnel
    if street_b == 'Highland Way' then
      local street_b_temp = street_a

      street_a = street_b
      street_b = street_b_temp
    end

    if street_a == 'Highland Way' then
      if street_b ~= 'Senora Rd' and street_b ~= 'Power St' then
        street_str = street_a
        zone = 'Cooper Tunnel'
      end
    end

		local interior_name = ''
		local run_speed_modified = false

		pcall(function()
			interior_name = exports.blrp_core:GetInteriorNameAtPed()
			run_speed_modified = exports.blrp_core:GetRunSpeedModified()
		end)

		if interior_name then
			street_str = interior_name
		end

		if hide_location or string.match(street_str, 'Unknown') then
			direction = ''
			street_str = ''
			zone = ''
		end

		in_air = (IsPedInAnyHeli(player_ped) or IsPedInAnyPlane(player_ped) or (GetPedParachuteState(player_ped) ~= -1))
		
		if spectated_ped then
      		in_air = (IsPedInAnyHeli(spectated_ped) or IsPedInAnyPlane(spectated_ped) or (GetPedParachuteState(spectated_ped) ~= -1))
    	end

		SendNUIMessage({
			showSpeedo = is_driving,
			showNitro = nitro_enabled,

			---

			showParachute = HasPedGotWeapon(player_ped, `GADGET_PARACHUTE`),
			showAlt = in_air,

			---

			sprinting = run_speed_modified,

			---

			action = 'update_locality',
			direction = direction,
			name = street_str,
			locality = zone,

      ---

      seatbeltState = seatbeltState,
	  seatbeltFlash = seatbeltFlash,
      vehicleLocked = (GetVehicleDoorLockStatus(vehicle) >= 2)
		})
	end
end)

Citizen.CreateThread(function()
	while true do
		Wait(50)

		if in_air then
			local player_ped = PlayerPedId()
	    local player_coords = GetEntityCoords(player_ped)
      local spectated_ped = exports.blrp_core:GetSpectatedPed()
			local in_heli = IsPedInAnyHeli(player_ped)
			local in_plane = IsPedInAnyPlane(player_ped)
			local in_parachute = GetPedParachuteState(player_ped) ~= -1
			-- local altitude = math.ceil(player_coords.z / 0.3048) -- m -> ft -- Unreliable
			local altitude = math.ceil(GetEntityHeightAboveGround(player_ped) / 0.3048) -- m -> ft 
			
			if spectated_ped then
      			altitude = math.ceil(GetEntityHeightAboveGround(spectated_ped) / 0.3048) -- m -> ft 
    		end

			-- Altitude thresholds - default for helicopter
			local threshold_a = 150 -- Red altitude
			local threshold_b = 300 -- Ong altitude
			local threshold_c = 450 -- Yel altitude

			if in_plane then
				threshold_a = 500
				threshold_b = 650
				threshold_c = 700
			end

			if in_parachute then
				threshold_a = 250
				threshold_b = 500
				threshold_c = 1000
			end

			SendNUIMessage({
				action = "update_alt",
				altitude = altitude,
				height = altitude,
				threshold_a = threshold_a,
				threshold_b = threshold_b,
				threshold_c = threshold_c,
			})
		end
	end
end)

local last_me_check = 0
local me_data = nil

local last_hud_update = 0
local cached_data = {}

Citizen.CreateThread(function()
	while true do
		Wait(500)

		local now = GetGameTimer()
		local player_id = PlayerId()
		local player_ped = PlayerPedId()

		-- Basic health-related info (native and cheap)
		local coords = GetEntityCoords(player_ped)
		local health = GetEntityHealth(player_ped) - 100
		local armor = GetPedArmour(player_ped)
		local oxygen = math.max(0, GetPlayerUnderwaterTimeRemaining(player_id) * 10)
		local showOxygen = IsPedSwimmingUnderWater(player_ped)
		local isTalking = NetworkIsPlayerTalking(player_id)
		local clock_minutes = GetClockMinutes()
		local clock_hours = GetClockHours()
		local cached_busy_since = nil -- holds the raw timestamp
    local cached_is_busy = false -- boolean to track if busy

		-- Check export-heavy data less frequently
		if now - last_me_check > 1500 then
    	last_me_check = now

    	local success, result = pcall(function()
    		return exports.blrp_core:me()
    	end)

    	if success and result then
    		me_data = result

    		local dispatch_status = me_data.get('dispatch_status')
    		if dispatch_status and exports.blrp_core:DoesCodeCountAsBusy(dispatch_status.code or 'IS') then
    			cached_data.busy_since = dispatch_status.time
    			cached_data.is_busy = true
    		else
    			cached_data.busy_since = nil
    			cached_data.is_busy = false
    		end

    		if me_data.get('ai_flight_destination') then
    			cached_data.ai_flight_time = math.max(0, GlobalState.ai_flight_time - GlobalState.server_time)
    		else
    			cached_data.ai_flight_time = nil
    		end

    		cached_data.hunger = me_data.get('hunger')
    		cached_data.thirst = me_data.get('thirst')
    		cached_data.urine = me_data.get('urine')
    		cached_data.poop = me_data.get('poop')
    		cached_data.in_coma = me_data.isInComa() and not me_data.isInHospitalBed()
    	end
    end

    -- recalculate leo_busy_time every 500ms using cached busy_since
    if cached_data.is_busy and cached_data.busy_since then
    	cached_data.leo_busy_time = math.max(0, GlobalState.server_time - cached_data.busy_since)
    else
    	cached_data.leo_busy_time = nil
    end

    if cached_data.in_coma then
      health = -1
    end

		-- Get bleeding status from mythic_hospital
		local bleeding_level = 0
		local success, result = pcall(function()
			return exports.mythic_hospital:GetBleedingLevel()
		end)
		if success and result then
			bleeding_level = result
		end

		-- HUD update payload
		SendNUIMessage({
			action = "update_hud",
			hp = health,
			armor = armor,
			hunger = cached_data.hunger or 0,
			thirst = cached_data.thirst or 0,
			urine = cached_data.urine,
			poop = cached_data.poop,
			oxygen = oxygen,
			showOxygen = showOxygen,
			talking = isTalking,
			clock_minutes = clock_minutes,
			clock_hours = clock_hours,
			clock_override = clock_override,
			leo_busy_time = cached_data.leo_busy_time,
			ai_flight_time = cached_data.ai_flight_time,
		})

		-- Send bleeding data separately
		SendNUIMessage({
			action = "update_bleeding",
			visible = bleeding_level > 0,
			bleeding = bleeding_level
		})

		clock_override = false

		-- UI toggle on pause menu
		local pause_state = IsPauseMenuActive()
		if pause_state ~= last_pause_state then
			last_pause_state = pause_state
			SendNUIMessage({ showUi = not last_pause_state })
		end
	end
end)
-- Map stuff below
local xOff = -0.005
local yOff = -0.04

local res_w, res_h = GetActiveScreenResolution()

if res_w == 3440 and res_h == 1440 then
	xOff = -0.147
	yOff = -0.06
end

Citizen.CreateThread(function()
	local minimap = RequestScaleformMovie("minimap")

	-- baseline values from common:data/ui/frontend.xml
	SetMinimapComponentPosition('minimap', 'L', 'B', -0.0045 + xOff, 0.002 + yOff, 0.150, 0.188888)
	SetMinimapComponentPosition('minimap_mask', 'L', 'B', 0.020 + xOff, 0.032 + yOff, 0.111, 0.159)
	SetMinimapComponentPosition('minimap_blur', 'L', 'B', -0.03 + xOff, 0.022 + yOff, 0.266, 0.237)

	Wait(1000)
	SetBigmapActive(true, false)
	Wait(0)
	SetBigmapActive(false, false)

	while true do
		Wait(0)
		BeginScaleformMovieMethod(minimap, "SETUP_HEALTH_ARMOUR")
		ScaleformMovieMethodAddParamInt(3)
		EndScaleformMovieMethod()
	end
end)

CreateThread(function()
	while true do
		Wait(2000)

		if IsBigmapActive() then
			SetBigmapActive(false, false)
		end

		if is_driving and IsPedInAnyVehicle(PlayerPedId(), true) then
			local vehicle = GetVehiclePedIsUsing(PlayerPedId(), false)
			local fuel_volume = GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fPetrolTankVolume')
			local fuel_level = GetVehicleFuelLevel(vehicle)

			-- Passenger fuel sync
			if GetPedInVehicleSeat(vehicle, -1) ~= PlayerPedId() then
				local state = Entity(vehicle).state

				if state.fuel_synced then
					fuel_level = state.fuel_synced
				end
			end

			fuel_level = (fuel_level / fuel_volume)

			SendNUIMessage({
				action = "update_fuel",
				fuel = fuel_level,
				showFuel = true
			})
		end
	end
end)

function Voicelevel(val)
	SendNUIMessage({action = "voice_level", voice_level = val})
end

exports('Voicelevel', Voicelevel)

exports('SetTalking', function(talking)
	SendNUIMessage({ action = "voice_talking", talking = talking })
end)

exports('SetRadio', function(radio)
	SendNUIMessage({ action = "voice_radio", radio = radio })
end)

exports('SetMegaphone', function(megaphone)
  SendNUIMessage({ action = "voice_megaphone", megaphone = megaphone })
end)

local interrupt_race_fade = false

exports('SetRacingData', function(data)
	interrupt_race_fade = true

	if data.fade_out then
		interrupt_race_fade = false

		SetTimeout(30 * 1000, function()
			if not interrupt_race_fade then
				SendNUIMessage({ action = 'hideRacingBlock' })
			end
		end)
	else
		SendNUIMessage({ action = 'setRacingData', data = data })
	end
end)

AddEventHandler('menu:forceCloseMenu', function()
	SendNUIMessage({ action = 'hideRacingBlock' })
end)

exports('SetColdData', function(data)
  data.action = 'update_cold'

  SendNUIMessage(data)
end)

exports('SetHeatData', function(data)
  data.action = 'update_heat'

  SendNUIMessage(data)
end)

exports('SetRadiationData', function(data)
  data.action = 'update_radiation'

  SendNUIMessage(data)
end)

tHud.addCorn = function()
  SendNUIMessage({
    action = 'update_corn',
  })
end

local compass = {
  show = false,
  cardinal = {},
  intercardinal = {},
}

-- CONFIGURATION
compass.position = { x = 0.5, y = 0.02, centered = true }
compass.width = 0.25
compass.fov = 180
compass.followGameplayCam = true

compass.ticksBetweenCardinals = 9.0
compass.tickColour = { r = 255, g = 255, b = 255, a = 180 }
compass.tickSize = { w = 0.001, h = 0.004 }

compass.cardinal.textSize = 0.26
compass.cardinal.textOffset = 0.015
compass.cardinal.textColour = { r = 255, g = 255, b = 255, a = 255 }
compass.cardinal.tickShow = true
compass.cardinal.tickSize = { w = 0.001, h = 0.012 }
compass.cardinal.tickColour = { r = 255, g = 255, b = 255, a = 255 }

compass.intercardinal.show = true
compass.intercardinal.textShow = true
compass.intercardinal.textSize = 0.2
compass.intercardinal.textOffset = 0.012
compass.intercardinal.textColour = { r = 180, g = 180, b = 180, a = 230 }
compass.intercardinal.tickShow = true
compass.intercardinal.tickSize = { w = 0.001, h = 0.006 }
compass.intercardinal.tickColour = { r = 180, g = 180, b = 180, a = 200 }

-- DIRECTION LABELS
local function degreesToDirection(deg)
  local directions = {
    [0] = "N", [45] = "NE", [90] = "E", [135] = "SE",
    [180] = "S", [225] = "SW", [270] = "W", [315] = "NW", [360] = "N"
  }
  return directions[deg % 360] or ""
end

-- DRAW TEXT
local function drawText(text, x, y, opts)
  SetTextFont(4)
  SetTextScale(opts.size, opts.size)
  SetTextColour(opts.colour.r, opts.colour.g, opts.colour.b, opts.colour.a)
  if opts.outline then SetTextOutline() end
  if opts.centered then SetTextCentre(true) end
  BeginTextCommandDisplayText("STRING")
  AddTextComponentSubstringPlayerName(text)
  EndTextCommandDisplayText(x, y)
end

local function lerpAngle(a, b, t)
  local diff = (b - a + 540.0) % 360.0 - 180.0
  return (a + diff * t) % 360.0
end

-- COMPASS THREAD
CreateThread(function()
  if compass.position.centered then
    compass.position.x = compass.position.x - compass.width / 2
  end

  local lastHeading = 0.0

  while true do
    Wait(0)

    if not compass.show or not ui_visible then goto continue end

    local pxDegree = compass.width / compass.fov
    local playerHeadingDegrees

    if compass.followGameplayCam then
      local camRot = GetGameplayCamRot(2)
      local targetHeading = (-camRot.z + 360.0) % 360.0
      lastHeading = lerpAngle(lastHeading, targetHeading, 0.1)
      playerHeadingDegrees = lastHeading
    else
      playerHeadingDegrees = 360.0 - GetEntityHeading(PlayerPedId())
    end

    local tickDegree = playerHeadingDegrees - compass.fov / 2
    local tickDegreeRemainder = compass.ticksBetweenCardinals - (tickDegree % compass.ticksBetweenCardinals)
    local tickPosition = compass.position.x + tickDegreeRemainder * pxDegree
    tickDegree = tickDegree + tickDegreeRemainder

    -- Optional background bar
    DrawRect(0.5, compass.position.y, compass.width + 0.02, 0.03, 0, 0, 0, 40)

    while tickPosition < compass.position.x + compass.width do
      local dirText = degreesToDirection(tickDegree)

      if tickDegree % 90 == 0 then
        if compass.cardinal.tickShow then
          DrawRect(tickPosition, compass.position.y, compass.cardinal.tickSize.w, compass.cardinal.tickSize.h,
            compass.cardinal.tickColour.r, compass.cardinal.tickColour.g, compass.cardinal.tickColour.b, compass.cardinal.tickColour.a)
        end

        drawText(dirText, tickPosition, compass.position.y - compass.cardinal.textOffset, {
          size = compass.cardinal.textSize,
          colour = compass.cardinal.textColour,
          outline = true,
          centered = true
        })
      elseif (tickDegree % 45.0 == 0) and compass.intercardinal.show then
        if compass.intercardinal.tickShow then
          DrawRect(tickPosition, compass.position.y, compass.intercardinal.tickSize.w, compass.intercardinal.tickSize.h,
            compass.intercardinal.tickColour.r, compass.intercardinal.tickColour.g, compass.intercardinal.tickColour.b, compass.intercardinal.tickColour.a)
        end

        if compass.intercardinal.textShow then
          drawText(dirText, tickPosition, compass.position.y - compass.intercardinal.textOffset, {
            size = compass.intercardinal.textSize,
            colour = compass.intercardinal.textColour,
            outline = true,
            centered = true
          })
        end
      else
        DrawRect(tickPosition, compass.position.y, compass.tickSize.w, compass.tickSize.h,
          compass.tickColour.r, compass.tickColour.g, compass.tickColour.b, compass.tickColour.a)
      end

      tickDegree = tickDegree + compass.ticksBetweenCardinals
      tickPosition = tickPosition + pxDegree * compass.ticksBetweenCardinals
    end

    ::continue::
  end
end)

-- EXPORT
exports('SetCompassVisible', function(state)
  compass.show = state
end)

local circularCompass = {
  show = false,
  position = { x = 0.92, y = 0.86 },
  radius = 0.09,
  tickCount = 72,
  cardinal = {
    textSize = 0.35,
    colour = { r = 250, g = 200, b = 0, a = 255 }
  },
  tickColour = { r = 46, g = 46, b = 46, a = 200 },
  pinColour = { r = 46, g = 46, b = 46, a = 255 },
  pinSize = { w = 0.002, h = 0.015 }
}

local function drawText(text, x, y, opts)
  SetTextFont(4)
  SetTextScale(opts.size, opts.size)
  SetTextColour(opts.colour.r, opts.colour.g, opts.colour.b, opts.colour.a)
  if opts.outline then SetTextOutline() end
  SetTextCentre(true)
  BeginTextCommandDisplayText("STRING")
  AddTextComponentSubstringPlayerName(text)
  EndTextCommandDisplayText(x, y)
end

local function degreesToDirectionLabel(deg)
  local directions = {
    [270] = "N", [315] = "NE", [0] = "E", [45] = "SE",
    [90] = "S", [135] = "SW", [180] = "W", [225] = "NW"
  }
  return directions[deg]
end

local function lerpAngle(a, b, t)
  local diff = (b - a + 540.0) % 360.0 - 180.0
  return (a + diff * t) % 360.0
end

CreateThread(function()
  if not HasStreamedTextureDictLoaded("circcompass") then
    RequestStreamedTextureDict("circcompass", true)
    while not HasStreamedTextureDictLoaded("circcompass") do
      Wait(0)
    end
  end
end)

CreateThread(function()
  local lastHeading = 0.0
  while true do
    Wait(0)
    if not circularCompass.show or not ui_visible then goto continue end

    local ped = PlayerPedId()
    local camRot = GetGameplayCamRot(2)
    local targetHeading = (-camRot.z + 360.0) % 360.0
    lastHeading = lerpAngle(lastHeading, targetHeading, 0.1)
    local heading = lastHeading

    local centerX, centerY = circularCompass.position.x, circularCompass.position.y
    local radius = circularCompass.radius

	local aspectRatio = GetAspectRatio(false)
	DrawSprite(
	  "circcompass", "compass_circ",
	  centerX, centerY,
	  (radius * 3.0) / aspectRatio, radius * 3.0,
	  0.0,
	  255, 255, 255, 255
	)

    for i = 0, 359, 5 do
      local relAngle = (i - heading + 360) % 360
      local angleRad = math.rad(relAngle)
      local aspectRatio = GetAspectRatio(false)
	  local tickX = centerX + math.cos(angleRad) * radius / aspectRatio
      local tickY = centerY + math.sin(angleRad) * radius

      DrawRect(tickX, tickY, 0.001, 0.003,
        circularCompass.tickColour.r, circularCompass.tickColour.g, circularCompass.tickColour.b, circularCompass.tickColour.a)

      local dirLabel = degreesToDirectionLabel(i)
      if dirLabel then
        drawText(dirLabel, tickX, tickY - 0.012, {
          size = circularCompass.cardinal.textSize,
          colour = circularCompass.cardinal.colour,
          outline = true
        })
      end
    end

    -- Draw top pin
    DrawRect(centerX, centerY - radius, circularCompass.pinSize.w, circularCompass.pinSize.h,
      circularCompass.pinColour.r, circularCompass.pinColour.g, circularCompass.pinColour.b, circularCompass.pinColour.a)

    ::continue::
  end
end)

exports('SetCircularCompassVisible', function(state)
  circularCompass.show = state
end)

AddEventHandler('core:client:characterAttributeChanged', function(key, new_value)
  if key ~= 'clientAwareItems' then return end

  if new_value['compass'] and new_value['compass'] > 0 then
    compass.show = true
  else
    compass.show = false
  end

  if new_value['bzzz_camp_prop_compass'] and new_value['bzzz_camp_prop_compass'] > 0 then
    circularCompass.show = true
  else
    circularCompass.show = false
  end
end)