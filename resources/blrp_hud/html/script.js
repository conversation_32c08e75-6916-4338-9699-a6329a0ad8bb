Number.prototype.pad = function(size) {
    var s = String(this);
    while (s.length < (size || 2)) {s = "0" + s;}
    return s;
}

function parseTimeString(millis) {
  let prefix = '';

  if (millis < 0) {
    millis = Math.abs(millis);
    prefix = '-';
  }

  let minutes = parseInt(millis / 1000 / 60);

  millis = millis - (minutes * 60 * 1000);

  let seconds = parseInt(millis / 1000);

  millis = parseInt((millis - (seconds * 1000)) / 10);

  return prefix + minutes.pad() + ':' + seconds.pad() + ':' + millis.pad();
}

$(document).ready(function () {
  HealthIndicator = new ProgressBar.Circle("#HealthIndicator", {
    color: "rgb(0, 210, 91)",
    trailColor: "rgba(0, 255, 0, 0.5)",
    strokeWidth: 10,
    trailWidth: 10,
    duration: 50,
    easing: "easeInOut",
  });

  ArmorIndicator = new ProgressBar.Circle("#ArmorIndicator", {
    color: "rgb(255, 36, 36)",
    trailColor: "rgba(124, 30, 30, 0.5)",
    strokeWidth: 10,
    trailWidth: 10,
    duration: 50,
    easing: "easeInOut",
  });

  HungerIndicator = new ProgressBar.Circle("#HungerIndicator", {
    color: "rgb(255, 164, 59)",
    trailColor: "rgba(165, 116, 60, 0.5)",
    strokeWidth: 10,
    trailWidth: 10,
    duration: 250,
    easing: "easeInOut",
  });

  ThirstIndicator = new ProgressBar.Circle("#ThirstIndicator", {
    color: "rgb(0, 140, 255)",
    trailColor: "rgba(0, 85, 155, 0.5)",
    strokeWidth: 10,
    trailWidth: 10,
    duration: 250,
    easing: "easeInOut",
  });

  UrineIndicator = new ProgressBar.Circle("#UrineIndicator", {
    color: "rgb(222, 234, 40)",
    trailColor: "rgba(162, 178, 0, 0.5)",
    strokeWidth: 10,
    trailWidth: 10,
    duration: 250,
    easing: "easeInOut",
  });

  PoopIndicator = new ProgressBar.Circle("#PoopIndicator", {
    color: "rgb(179, 142, 58)",
    trailColor: "rgba(96, 69, 0, 0.5)",
    strokeWidth: 10,
    trailWidth: 10,
    duration: 250,
    easing: "easeInOut",
  });

  OxygenIndicator = new ProgressBar.Circle("#OxygenIndicator", {
    color: "rgb(0, 140, 255)",
    trailColor: "rgba(0, 85, 155, 0.5)",
    strokeWidth: 10,
    trailWidth: 10,
    duration: 250,
    easing: "easeInOut",
  });

  ColdIndicator = new ProgressBar.Circle("#ColdIndicator", {
    color: "rgb(40, 255, 255)",
    trailColor: "rgba(185, 232, 234, 0.5)",
    strokeWidth: 10,
    trailWidth: 10,
    duration: 250,
    easing: "easeInOut",
  });

  HeatIndicator = new ProgressBar.Circle("#HeatIndicator", {
    color: "rgb(255, 40, 40)",
    trailColor: "rgba(252, 213, 228, 0.5)",
    strokeWidth: 10,
    trailWidth: 10,
    duration: 250,
    easing: "easeInOut",
  });

    RadiationIndicator = new ProgressBar.Circle("#RadiationIndicator", {
    color: "rgb(221, 255, 0)",
    trailColor: "rgba(242, 255, 175, 0.5)",
    strokeWidth: 10,
    trailWidth: 10,
    duration: 250,
    easing: "easeInOut",
  });

  NitroIndicator = new ProgressBar.Circle("#NitroIndicator", {
    color: "rgb(230, 47, 0)",
    trailColor: "rgba(255, 99, 71, 0.5)",
    strokeWidth: 10,
    trailWidth: 10,
    duration: 1,
    easing: "easeInOut",
  });

  ParachuteIndicator = new ProgressBar.Circle("#ParachuteIndicator", {
    color: "rgba(222, 222, 222, 1)",
    trailColor: "rgba(184, 184, 184, 0.082)",
    strokeWidth: 10,
    trailWidth: 10,
    duration: 1,
    easing: "easeInOut",
  });
  ParachuteIndicator.animate(1.0);
  ParachuteIndicator.path.setAttribute("stroke", "rgba(255, 165, 0, 0.6)");

  Speedometer = new ProgressBar.Circle("#SpeedCircle", {
    color: "rgba(222, 222, 222, 1)",
    trailColor: "rgba(184, 184, 184, 0.082)",
    strokeWidth: 6,
    duration: 100,
    trailWidth: 6,
    easing: "easeInOut",
  });

  FuelIndicator = new ProgressBar.Circle("#FuelCircle", {
    color: "rgba(222, 222, 222, 1)",
    trailColor: "rgba(184, 184, 184, 0.082)",
    strokeWidth: 8,
    duration: 1000,
    trailWidth: 8,
    easing: "easeInOut",
  });

  VoiceIndicator = new ProgressBar.Circle("#VoiceIndicator", {
    color: "#4a4a4a",
    trailColor: "#4a4a4a",
    strokeWidth: 12,
    trailWidth: 12,
    duration: 250,
    easing: "easeInOut",
  });
  VoiceIndicator.animate(0.66);
  VoiceIndicator.path.setAttribute("stroke", "darkgrey");

  BleedingIndicator = new ProgressBar.Circle("#BleedingIndicator", {
    color: "rgb(220, 20, 60)",
    trailColor: "rgb(220, 20, 60)",
    strokeWidth: 50,
    trailWidth: 50,
    duration: 250,
    easing: "easeInOut",
  });
  BleedingIndicator.animate(1); // Make it a solid circle
});

let last_minute = -1;

function updateSeatBeltUI(data) {
  if (data.seatbeltState) {
    $("#SeatbeltCircle").css({'box-shadow': 'inset 0 0 0 3px green','animation': 'none'});
  } else {
    if (data.seatbeltFlash) {
      $("#SeatbeltCircle").css({'box-shadow': 'none', 'animation': 'flashSB 1s infinite alternate'});
    } else {
      $("#SeatbeltCircle").css({'box-shadow': 'inset 0 0 0 3px #FF000099','animation': 'none'});
    }
  }
}

let corned_until = 0;
let corn_end_time = 0;

window.addEventListener("message", function (event) {
  let data = event.data;

  if (data.action == "update_corn")
  {
    $("#HungerIcon")
      .removeClass('fa-hamburger')
      .addClass('fa-duotone')
      .addClass('fa-corn')
      .css('--fa-primary-color', '#66FF00')
      .css('--fa-secondary-color', '#FBED5E')
      .css('--fa-secondary-opacity', '1.0')
    ;

    corned_until = Date.now() + (30 * 1000);
  }

  if (data.action == "update_hud") {
    if(corned_until && corned_until < Date.now())
    {
      $("#HungerIcon")
        .removeClass('fa-duotone')
        .removeClass('fa-corn')
        .addClass('fa-hamburger')
      ;
    }

    HealthIndicator.animate(data.hp / 100);
    ArmorIndicator.animate(data.armor / 100);
    HungerIndicator.animate(data.hunger / 100);
    ThirstIndicator.animate(data.thirst / 100);
    OxygenIndicator.animate(data.oxygen / 100);

    if(data.urine != undefined) {
      $('#UrineIndicator').show();
      UrineIndicator.animate(data.urine / 100);
    } else {
      $('#UrineIndicator').hide();
    }

    if(data.poop != undefined) {
      $('#PoopIndicator').show();
      PoopIndicator.animate(data.poop / 100);
    } else {
      $('#PoopIndicator').hide();
    }

    let minutes = data.clock_minutes;
    let hours = data.clock_hours;

    if(minutes < 10) {
      minutes = '0' + minutes.toString();
    }

    if(hours < 10) {
      hours = '0' + hours.toString();
    }

    $('#Clock').text(hours + ':' + minutes);

    if(data.leo_busy_time != undefined) {
      $('#LEOBusyIndicator').show();
      $('#LEOBusyTimer').text(new Date(data.leo_busy_time * 1000).toISOString().slice(11, 19));
    } else {
      $('#LEOBusyIndicator').hide();
    }

    if(data.ai_flight_time != undefined) {
      $('#FlightIndicator').show();
      $('#FlightTimer').text(new Date(data.ai_flight_time * 1000).toISOString().slice(14, 19));
    } else {
      $('#FlightIndicator').hide();
    }
  }

  if (data.action == "update_nitro") {
    let nitro_data = data.nitroData

    if(nitro_data.in_use) {
      NitroIndicator.path.setAttribute('stroke', '#47D5FF');
      NitroIndicator.animate((100 - nitro_data.duration) / 100);
    } else if(nitro_data.cooldown < 100) {
      NitroIndicator.path.setAttribute('stroke', '#FBEC5D');
      NitroIndicator.animate(nitro_data.cooldown / 100);
    } else {
      NitroIndicator.path.setAttribute('stroke', '#FF6347');
      NitroIndicator.animate(nitro_data.level / 100);
    }
  }

  if (data.action == 'update_cold') {
    if(data.visible) {
      $("#ColdIndicator").show();
    } else {
      $("#ColdIndicator").hide();
    }

    if(data.warming) {
      $("#ColdIcon").removeClass('fa-snowflake').addClass('fa-fire');
    } else {
      $("#ColdIcon").removeClass('fa-fire').addClass('fa-snowflake');
    }

    ColdIndicator.animate(data.cold / 100);

    if(data.cold >= 90) {
      $("#ColdIcon").toggleClass("flash");
    }
  }

  if (data.action == 'update_heat') {
    if(data.visible) {
      $("#HeatIndicator").show();
    } else {
      $("#HeatIndicator").hide();
    }

    if(data.cooling) {
      $("#HeatIcon").removeClass('fa-fire').addClass('fa-snowflake');
    } else {
      $("#HeatIcon").removeClass('fa-snowflake').addClass('fa-fire');
    }

    HeatIndicator.animate(data.heat / 100);

    if(data.heat >= 90) {
      $("#HeatIcon").toggleClass("flash");
    }
  }

    if (data.action == 'update_radiation') {
    if (data.visible) {
      $("#RadiationIndicator").show();
    } else {
      $("#RadiationIndicator").hide();
    }
  
    if (data.decontaminating) {
      $("#RadiationIcon").removeClass('fa-radiation').addClass('fa-shower');
    } else {
      $("#RadiationIcon").removeClass('fa-shower').addClass('fa-radiation');
    }

    RadiationIndicator.animate(data.radiation / 100);

    if (data.radiation >= 90) {
      $("#RadiationIcon").toggleClass("flash");
    }
  }

  if (data.action == "update_locality") {
    $("#StreetDirection").text(data.direction);
    $("#StreetName").text(data.name);
    $("#StreetLocality").text(data.locality);
  }

  if (data.action == "update_alt") {
    if (data.altitude > 0) {
      $("#AltIndicator").text(data.altitude);

      if (data.altitude < 100) { // 1-2 digits
        $("#AltIndicator").css('font-size', '2.0vh');
        $("#AltIndicator").css('top', '-0.5vh');
      } else if (data.altitude < 1000) { // 3 digits
        $("#AltIndicator").css('font-size', '1.7vh');
        $("#AltIndicator").css('top', '0vh');
      } else if (data.altitude < 10000) { // 4 digits
        $("#AltIndicator").css('font-size', '1.3vh');
        $("#AltIndicator").css('top', '0.2vh');
      }

      if (data.height < data.threshold_a) {
        $('#AltIndicator').css('color', 'red');
      } else if (data.height < data.threshold_b) {
        $('#AltIndicator').css('color', 'orange');
      } else if (data.height < data.threshold_c) {
        $('#AltIndicator').css('color', 'yellow');
      } else {
        $('#AltIndicator').css('color', 'white');
      }
    } else if (data.altitude <= 0) {
      $("#AltIndicator").text("0");

      $("#AltIndicator").css('font-size', '2.0vh');
      $("#AltIndicator").css('top', '-0.5vh');
    }
  }

  if(data.action == "voice_visible") {
    if(data.visible) {
      $("#VoiceIndicator").show();
    } else {
      $("#VoiceIndicator").hide();
    }
  }

  if(data.action == 'voice_level') {
    VoiceIndicator.animate(data.voice_level / 100);
  }

  if(data.action == 'voice_talking') {
    if(data.talking) {
      VoiceIndicator.path.setAttribute("stroke", "#f4c441");
    } else {
      VoiceIndicator.path.setAttribute("stroke", "darkgrey");
    }
  }

  if(data.action == 'voice_radio') {
    if(data.radio) {
      $("#VoiceIcon").removeClass("fa-microphone");
      $("#VoiceIcon").addClass("fa-headset");
    } else {
      $("#VoiceIcon").removeClass("fa-headset");
      $("#VoiceIcon").addClass("fa-microphone");
    }
  }

  if(data.action == 'voice_megaphone') {
    if(data.megaphone) {
      $("#VoiceIcon").removeClass("fa-microphone");
      $("#VoiceIcon").addClass("fa-megaphone");
    } else {
      $("#VoiceIcon").removeClass("fa-megaphone");
      $("#VoiceIcon").addClass("fa-microphone");
    }
  }

  if (data.action == "enable_dev") {
    $("#DevelopmentIndicator").show();
  }

  // ESP

  if (data.action == "enable_esp") {
    $("#EspIndicator").show();
  }

  if (data.action == "disable_esp") {
    $("#EspIndicator").hide();
  }

  // Godmode

  if (data.action == "enable_god") {
    $("#GodmodeIndicator").show();
  }

  if (data.action == "disable_god") {
    $("#GodmodeIndicator").hide();
  }

  // Noclip

  if (data.action == "enable_noclip") {
    $("#NoclipIndicator").show();
  }

  if (data.action == "disable_noclip") {
    $("#NoclipIndicator").hide();
  }

  // Walking

  if (data.action == "enable_walking") {
    $("#WalkingIndicator").show();
  }

  if (data.action == "disable_walking") {
    $("#WalkingIndicator").hide();
  }

  // Sprinting

  if (data.sprinting !== undefined) {
    if (data.sprinting) {
      $("#SprintingIndicator").show();
    } else {
      $("#SprintingIndicator").hide();
    }
  }

  // Ghost mode

  if (data.action == "enable_ghost") {
    $("#GhostIndicator").show();
  }

  if (data.action == "disable_ghost") {
    $("#GhostIndicator").hide();
  }

  // Show oxygen if underwater
  if (data.showOxygen == true) {
    $("#OxygenIndicator").show();
  } else if (data.showOxygen == false) {
    $("#OxygenIndicator").hide();
  }

  // Show nitro if applicable
  if (data.showNitro == true) {
    $("#NitroIndicator").show();
  } else if (data.showNitro == false) {
    $("#NitroIndicator").hide();
  }

  // Hide armor if 0
  if (data.armor == 0) {
    $("#ArmorIndicator").fadeOut();
  } else if (data.armor > 0) {
    $("#ArmorIndicator").fadeIn();
  }

  if (data.showParachute == true) {
    $("#ParachuteIndicator").fadeIn();
  } else if (data.showParachute == false) {
    $("#ParachuteIndicator").fadeOut();
  }

  // Change color and icon if HP is 0 (dead)
  if (data.hp < 0) {
    HealthIndicator.animate(0);
    HealthIndicator.trail.setAttribute("stroke", "red");
    $("#hp-icon").removeClass("fa-heartbeat");
    $("#hp-icon").addClass("fa-skull");
  } else if (data.hp > 0) {
    HealthIndicator.trail.setAttribute("stroke", "green");
    $("#hp-icon").removeClass("fa-skull");
    $("#hp-icon").addClass("fa-heartbeat");
  }

  // Flash if thirst is low
  if (data.thirst < 10) {
    $("#ThirstIcon").toggleClass("flash");
  }

  // Flash if hunger is low
  if (data.hunger < 10) {
    $("#HungerIcon").toggleClass("flash");
  }

  // Flash if oxygen is low
  if (data.oxygen < 20) {
    $("#OxygenIcon").toggleClass("flash");
  }

  if (data.urine != undefined && data.urine > 80) {
    $("#UrineIcon").toggleClass("flash");
  }

  if (data.poop != undefined && data.poop > 80) {
    $("#PoopIcon").toggleClass("flash");
  }

  if (data.speed > 0) {
    $("#SpeedIndicator").text(data.speed);
    let multiplier = data.maxspeed * 0.1;
    let SpeedoLimit = data.maxspeed + multiplier;
    Speedometer.animate(Math.min(1.0, data.speed / SpeedoLimit));
    Speedometer.path.setAttribute("stroke", "white");
  } else if (data.speed == 0) {
    $("#SpeedIndicator").text("0");
    Speedometer.path.setAttribute("stroke", "none");
  }

  if (data.action == "update_fuel") {
    let finalfuel = data.fuel

    if (finalfuel >= 1.0) {
      FuelIndicator.animate(1.0);
    } else if (finalfuel < 1.0) {
      FuelIndicator.animate(finalfuel);
    }

    if (finalfuel < 0.2) {
      FuelIndicator.path.setAttribute("stroke", "red");
    } else if (finalfuel > 0.2) {
      FuelIndicator.path.setAttribute("stroke", "white");
    }
  }

  if (data.showSpeedo == true) {
    if(data.vehicleLocked) {
      $("#DoorLockIcon").removeClass('fa-lock-open').addClass('fa-lock');
      $("#LockCircle").css('box-shadow', 'inset 0 0 0 3px green');
    } else {
      $("#DoorLockIcon").addClass('fa-lock-open').removeClass('fa-lock');
      $("#LockCircle").css('box-shadow', 'inset 0 0 0 3px #FF000099');
    }

    updateSeatBeltUI(data);

    $("#VehicleContainer").fadeIn();
  } else if (data.showSpeedo == false) {
    $("#VehicleContainer").fadeOut();
  }

  if (data.showAlt == true) {
    $("#AltCircle").fadeIn();
  } else if (data.showAlt == false) {
    $("#AltCircle").fadeOut();
  }

  if (data.showFuel == true) {
    $("#FuelCircle").show();
  } else if (data.showFuel == false) {
    $("#FuelCircle").hide();
  }

  if (data.showUi == true) {
    $(".container").show();
  } else if (data.showUi == false) {
    $(".container").hide();
  }

  if (data.action == "toggle_hud") {
    $("body").fadeToggle()
  }

  if (data.action == 'setRacingData') {
    $("#raceInfo").show();

    let racing_data = data.data;

    let time_overall_str = '';

    if (racing_data.time_overall !== undefined) {
      $("#raceInfo_raceLTH").text(parseTimeString(racing_data.time_overall));
    }

    if (racing_data.time_lap !== undefined) {
      if (racing_data.time_overall === racing_data.time_lap && time_overall_str != '') {
        $("#raceInfo_raceLT").text(time_overall_str);
      } else {
        $("#raceInfo_raceLT").text(parseTimeString(racing_data.time_lap));
      }
    }

    if (racing_data.checkpoint_info !== undefined) {
      $("#raceInfo_raceCP").text(racing_data.checkpoint_info)
    }

    if (racing_data.lap_info !== undefined) {
      $("#raceInfo_raceLap").text(racing_data.lap_info)
    }

    if (racing_data.position_info !== undefined) {
      $("#raceInfo_racePOS").text(racing_data.position_info)
    }
  }

  if (data.action == 'hideRacingBlock') {
    $("#raceInfo").fadeOut();
  }

  if (data.action == 'update_bleeding') {
    if (data.visible) {
      $("#BleedingIndicator").show();

      // Remove any existing animation classes
      $("#BleedingIndicator").removeClass("flash-slow flash-medium flash-fast");

      // Add appropriate flash class based on bleeding level
      if (data.bleeding === 1) {
        $("#BleedingIndicator").addClass("flash-slow");
      } else if (data.bleeding === 2) {
        $("#BleedingIndicator").addClass("flash-medium");
      } else if (data.bleeding >= 3) {
        $("#BleedingIndicator").addClass("flash-fast");
      }
    } else {
      $("#BleedingIndicator").hide();
      $("#BleedingIndicator").removeClass("flash-slow flash-medium flash-fast");
    }
  }
});
