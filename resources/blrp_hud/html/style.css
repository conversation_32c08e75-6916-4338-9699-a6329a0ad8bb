body {
  width: 100%;
  overflow: hidden;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.container {
  width: 100%;
  height: 100%;
}

.IconContainer {
  position: absolute;
  bottom: 0vh;
  left: 0.1vh;
}

.BaseCircle {
  position: relative;
  width: 4vh !important;
  height: 4vh !important;
  float: left;
  margin: 0.3vh;
  border-radius: 50%;
  background: rgb(49, 49, 49);
}

.BaseOval {
  position: relative;
  width: 8vh !important;
  height: 4vh !important;
  float: left;
  margin: 0.3vh;
  border-radius: 2vh;
  background: rgb(49, 49, 49);
}

.Ad {
  background: rgb(180,105,255) !important;
}

#StreetLabel {
  position: relative;
  width: 100vh !important;
  height: 4vh !important;
  float: left;
  margin: 0.3vh;

  color: white;
  text-shadow: -1px 0 black, 0 1px black, 1px 0 black, 0 -1px black;
}

#StreetDirection {
  width: 5.5vh !important;
  position: absolute;
  font-size: 3vh;
  text-align: center;
}

#StreetName {
  position: absolute;
  left: 5.7vh;
  font-size: 1.6vh;
}

#StreetLocality {
  position: absolute;
  left: 5.7vh;
  top: 2vh;
  font-size: 1.6vh;
}

.fa-fw {
  color: white;
  position: absolute;
  font-size: 1.6vh;
  width: 4vh !important;
  left: 0 !important;
}

#LEOBusyIndicator {
  width: 11.0vh !important;
  box-shadow: inset 0 0 0 4px #ff8c00;
  padding-left: 11px;
}

#LEOBusyIcon {
  margin-left: 3px;
}

#LEOBusyTimer {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
  color: white;
  position: absolute;
  font-size: 1.6vh;
  width: 4vh !important;
  left: 3.6vh !important;
  bottom: 1.2vh !important;
}

#FlightIndicator {
  width: 8.5vh !important;
  box-shadow: inset 0 0 0 4px rgb(0, 189, 255);
  padding-left: 11px;
}

#FlightIcon {
  margin-left: 3px;
}

#FlightTimer {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
  color: white;
  position: absolute;
  font-size: 1.6vh;
  width: 4vh !important;
  left: 3.6vh !important;
  bottom: 1.2vh !important;
}

.fa-heartbeat,
.fa-shield-alt,
.fa-hamburger,
.fa-corn,
.fa-skull,
.fa-terminal,
.fa-bible,
.fa-eye,
.fa-paperclip,
.fa-parachute-box,
.fa-brain,
.fa-headset,
.fa-glass,
.fa-wine-bottle,
.fa-person-walking,
.fa-person-running,
.fa-snowflake,
.fa-fire,
.fa-hourglass-clock,
.fa-plane-departure,
.fa-ghost,
.fa-kidneys,
.fa-poop {
  bottom: 1.2vh;
}

.fa-tint {
  bottom: 1.2vh;
}

/* Bleeding indicator flash animations */
.flash-slow {
  animation: flash 2s infinite;
}

.flash-medium {
  animation: flash 1s infinite;
}

.flash-fast {
  animation: flash 0.5s infinite;
}

@keyframes flash {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.2; }
}

.fa-lungs {
  bottom: 1.3vh;
}

.fa-radiation {
  bottom: 1.25vh;
}

.fa-shower {
  bottom: 1.25vh;
}

.fa-microphone {
  bottom: 1.25vh;
}

.fa-megaphone {
  bottom: 1.25vh;
}

#AltCircle {
  width: 5vh !important;
  height: 5vh !important;
  position: absolute;
  font-size: 5vh;
  left: 28vh;
  bottom: 10vh;
  background: rgba(27, 27, 27, 0.336);
}

#AltIndicator {
  color: white;
  position: inherit;
  z-index: 1000;
  left: 1vh;
  top: -0.5vh;
  width: 3vh;
  font-size: 2vh;
  text-align: center;
  text-shadow: 0.2vh 0.2vh 0vh rgb(22, 22, 22);
}

#AltSub {
  color: white;
  position: inherit;
  z-index: 1000;
  left: 1vh;
  top: 0.6vh;
  width: 3vh;
  font-size: 1.0vh;
  text-align: center;
  text-shadow: 0.2vh 0.2vh 0vh rgb(22, 22, 22);
}

#SpeedCircle {
  width: 5vh !important;
  height: 5vh !important;
  position: absolute;
  font-size: 5vh;
  left: 28.0vh;
  bottom: 4.5vh;
  background: rgba(27, 27, 27, 0.336);
  transform: rotate(-180deg);
}

#SpeedIndicator {
  color: white;
  position: inherit;
  z-index: 1000;
  left: 1vh;
  top: 0.4vh;
  width: 3vh;
  font-size: 2vh;
  text-align: center;
  text-shadow: 0.2vh 0.2vh 0vh rgb(22, 22, 22);
  transform: rotate(-180deg);
}

#SpeedSub {
  color: white;
  position: inherit;
  z-index: 1000;
  left: 1vh;
  top: -1.6vh;
  width: 3vh;
  font-size: 1.0vh;
  text-align: center;
  text-shadow: 0.2vh 0.2vh 0vh rgb(22, 22, 22);
  transform: rotate(-180deg);
}

#FuelCircle {
  width: 3vh !important;
  height: 3vh !important;
  position: absolute;
  left: 33.6vh;
  bottom: 4.5vh;
  background: rgba(27, 27, 27, 0.336);
  transform: rotate(-180deg);
}

#LockCircle {
  width: 3vh !important;
  height: 3vh !important;
  position: absolute;
  left: 37.6vh;
  bottom: 4.5vh;
  background: rgba(27, 27, 27, 0.336);
  transform: rotate(-180deg);
}

#SeatbeltCircle {
  width: 3vh !important;
  height: 3vh !important;
  position: absolute;
  left: 41.6vh;
  bottom: 4.5vh;
  background: rgba(27, 27, 27, 0.336);
}

#SeatbeltIndicator {
  height: 2.0vh;
  position: absolute;
  top: 16.7%;
  left: 16.7%;
}

#Clock {
  color: white;
  position: absolute;
  z-index: 1000;
  left: 0.95vh;
  bottom: 1.8vh;
  width: 3vh;
  font-size: 1.3vh;
  text-align: center;
  text-shadow: -1px 0 black, 0 1px black, 1px 0 black, 0 -1px black;
}

.fa-gas-pump, .fa-lock, .fa-lock-open {
  color: white;
  font-size: 1vh;
  position: inherit;
  left: 1vh;
  bottom: 1vh;
  transform: rotate(-180deg);
}

.fa-text {
  color: white;
  font-size: 1vh;
  position: inherit;
  left: 1vh;
  bottom: 1vh;
}

.flash {
  -webkit-animation: flash 1s;
}

@keyframes flash {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
