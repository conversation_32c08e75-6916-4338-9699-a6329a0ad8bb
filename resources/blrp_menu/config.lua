Config = {}

Config.Control = 244

local function handcuffDownFilter()
  return not exports.blrp_core:me().isInComa() and not exports.blrp_core:IsHandcuffed()
end

local function givekeysDownFilter()
  return exports.blrp_core:me().isInComa()
end

Config.MenuItems = {
  {
    filter = handcuffDownFilter,

    definition = {
      id = 'vehicle',
      title = 'Vehicle',
      icon = 'fa-solid fa-car-mirrors',
      items = {
        { title = 'Race Menu', icon = 'fa-solid fa-flag-checkered', command = 'racemenu', shouldClose = true },
        { title = 'Open Trunk', icon = 'fa-solid fa-hand', event_client = 'blrp_menu:accessTrunk', shouldClose = true },
        { title = 'Give Keys', icon = 'fa-solid fa-key', event_client = 'blrp_menu:giveKeys', shouldClose = true },
        { title = 'Repair', icon = 'fa-solid fa-toolbox', event_client = 'blrp_menu:repairVehicle', shouldClose = true },
        { title = 'Get In Trunk', icon = 'fa-solid fa-car', command = 'inside', shouldClose = true },
        { title = 'Toggle Neons', icon = 'fa-solid fa-keyboard-brightness', event_client = 'blrp_menu:toggleNeons', filter = 'isInVehicle', shouldClose = true },
      }
    }
  },

  {
    filter = givekeysDownFilter,

    definition = {
      id = 'Keys',
      title = 'Give keys',
      icon = 'fa-solid fa-key',
      event_client = 'blrp_menu:giveKeys',
      shouldClose = true
    }
  },

  {
    filter = handcuffDownFilter,

    definition = {
      id = 'citizen',
      title = 'Citizen',
      icon = 'fa-solid fa-user',
      items = {
        { title = 'Open Notepad', icon = 'fa-solid fa-clipboard', event_server = 'server:selfOpen_notepad', shouldClose = true },
        { title = 'Player Settings', icon = 'fa-solid fa-cog', command = 'playersettings', shouldClose = true },
        { title = 'Clothing >', icon = 'fa-solid fa-shirt',
          items = {
            { title = 'Helmet', icon = 'fa-solid fa-helmet-safety', command = 'helmet' },
            { title = 'Glasses', icon = 'fa-solid fa-glasses', command = 'glasses' },
            { title = 'Vest', icon = 'fa-solid fa-vest', command = 'vest' },
            { title = 'Shoes', icon = 'fa-solid fa-shoe-prints', command = 'shoes' },
            { title = 'Pants', icon = 'pants', command = 'pants' }, -- NEED SOMETHING FOR THIS
            { title = 'Shirt', icon = 'fa-solid fa-shirt', command = 'shirt' },
            { title = 'Decals', icon = 'fa-solid fa-plus', command = 'decals' },
            { title = 'Mask', icon = 'fa-solid fa-masks-theater', command = 'mask' },
          }
        },
        { title = 'Rob', icon = 'fa-solid fa-mask', event_client = 'blrp_menu:robPlayer', shouldClose = true },
      }
    }
  },

  {
    filter = handcuffDownFilter,

    definition = {
      id = 'emotes',
      title = 'Emotes',
      icon = 'fa-solid fa-face-smile',
      command = 'openemotemenu',
      shouldClose = true
    }
  },

  {
    filter = function()
      return handcuffDownFilter() and exports.blrp_core:me().hasGroup('Security')
    end,

    definition = {
      id = 'Security',
      title = 'Security',
      icon = 'fa-solid fa-user-police',
      items = {
        { title = 'Citizen >', icon = 'fa-solid fa-user',
          items = {
            { title = 'Toggle Shackles', icon = 'fa-solid fa-handcuffs', event_client = 'blrp_menu:toggleShackles', shouldClose = true },
            { title = 'Escort', icon = 'fa-solid fa-people-simple', event_client = 'blrp_menu:escortTarget', shouldClose = true },
          }
        },
        { title = 'Vehicle >', icon = 'fa-solid fa-car-side',
          items = {
            { title = 'Put In', icon = 'fa-solid fa-circle-arrow-down-right', event_client = 'blrp_menu:copPutInCar', shouldClose = true },
            { title = 'Pull Out', icon = 'fa-solid fa-circle-arrow-up-left', event_client = 'blrp_menu:pullOutVeh', shouldClose = true },
            { title = 'Impound', icon = 'fa-solid fa-car-garage', event_client = 'blrp_menu:impoundVehicle', shouldClose = true },
          }
        },
        { title = 'Dispatch >', icon = 'fa-solid fa-walkie-talkie',
        items = {
          { title = 'Downbad Operations',  icon = 'fa-solid fa-tower-broadcast', event_client = 'blrp_menu:policeRadioSet', radio_channel = 1350 },
          { title = 'Downbad Concierge', icon = 'fa-solid fa-tower-broadcast', event_client = 'blrp_menu:policeRadioSet', radio_channel = 1351 },
          { title = 'Downbad Security', icon = 'fa-solid fa-tower-broadcast', event_client = 'blrp_menu:policeRadioSet', radio_channel = 1352 },
        }
      },
      { title = 'Cuff', icon = 'fa-solid fa-handcuffs', event_client = 'blrp_menu:toggleRestraints', shouldClose = true },
      },
    },
  },

  {
    filter = function()
      return handcuffDownFilter() and exports.blrp_core:me().hasGroup('LEO')
    end,

    definition = {
      id = 'police',
      title = 'Police',
      icon = 'fa-solid fa-user-police',
      items = {
        { title = 'Citizen >', icon = 'fa-solid fa-user',
          items = {
            { title = 'Repair Cop Items', icon = 'fa-solid fa-toolbox', event_client = 'blrp_menu:repairCopItems', shouldClose = true },
            { title = 'Toggle Shackles', icon = 'fa-solid fa-handcuffs', event_client = 'blrp_menu:toggleShackles', shouldClose = true },
            { title = 'Escort', icon = 'fa-solid fa-people-simple', event_client = 'blrp_menu:escortTarget', shouldClose = true },
            { title = 'Search', icon = 'fa-solid fa-magnifying-glass', event_client = 'blrp_menu:searchTargetPlayer', shouldClose = true },
            { title = 'Revoke Keys', icon = 'fa-solid fa-key-skeleton-left-right', event_client = 'blrp_menu:revokeKeys', shouldClose = true },
            { title = 'Toggle Bed', icon = 'fa-solid fa-bed', event_client = 'blrp_menu:toggleBedState', shouldClose = true },
          }
        },
        { title = 'Vehicle >', icon = 'fa-solid fa-car-side',
          items = {
            { title = 'Put In', icon = 'fa-solid fa-circle-arrow-down-right', event_client = 'blrp_menu:copPutInCar', shouldClose = true },
            { title = 'Pull Out', icon = 'fa-solid fa-circle-arrow-up-left', event_client = 'blrp_menu:pullOutVeh', shouldClose = true },
            { title = 'Search', icon = 'fa-solid fa-magnifying-glass', event_client = 'blrp_menu:searchTargetVehicle', shouldClose = true },
            { title = 'Check Vin', icon = 'fa-solid fa-hashtag', event_client = 'blrp_menu:searchTargetVin', shouldClose = true },
            { title = 'Impound', icon = 'fa-solid fa-car-garage', event_client = 'blrp_menu:impoundVehicle', shouldClose = true },
          }
        },
        { title = 'Status >', icon = 'fa-solid fa-user-police',
          items = {
            { title = 'Busy',  icon = 'fa-solid fa-clock', command = 'bu UNAVAILABLE FOR DISPATCH', shouldClose = true },
            { title = 'Pursuit',  icon = 'fa-solid fa-siren-on', command = 'bu PURSUIT', shouldClose = true },
            { title = 'Admin Duties',  icon = 'fa-solid fa-clipboard', command = 'am', shouldClose = true },
            { title = 'In Service',  icon = 'fa-solid fa-check', command = 'is', shouldClose = true },
            { title = 'Meeting',  icon = 'fa-solid fa-handshake', command = 'mt', shouldClose = true },
            { title = 'Operation',  icon = 'fa-solid fa-bomb', command = 'op', shouldClose = true },
            { title = 'Processing',  icon = 'fa-solid fa-handcuffs', command = 'pr', shouldClose = true },
          }
        },
        { title = 'Dispatch >', icon = 'fa-solid fa-walkie-talkie',
        items = {
          { title = 'Patrol South', icon = 'fa-solid fa-tower-broadcast', event_client = 'blrp_menu:policeRadioSet', radio_channel = 10 },
          { title = 'Patrol North', icon = 'fa-solid fa-tower-broadcast', event_client = 'blrp_menu:policeRadioSet', radio_channel = 11 },
          { title = 'Ops 1', icon = 'fa-solid fa-tower-broadcast', event_client = 'blrp_menu:policeRadioSet', radio_channel = 12 },
          { title = 'Ops 2', icon = 'fa-solid fa-tower-broadcast', event_client = 'blrp_menu:policeRadioSet', radio_channel = 13 },
          { title = 'Ops 3', icon = 'fa-solid fa-tower-broadcast', event_client = 'blrp_menu:policeRadioSet', radio_channel = 14 },
          { title = 'Ops 4', icon = 'fa-solid fa-tower-broadcast', event_client = 'blrp_menu:policeRadioSet', radio_channel = 15 },
          { title = 'Ops 5', icon = 'fa-solid fa-tower-broadcast', event_client = 'blrp_menu:policeRadioSet', radio_channel = 16 },
          { title = 'Ops 6', icon = 'fa-solid fa-tower-broadcast', event_client = 'blrp_menu:policeRadioSet', radio_channel = 17 },
        }
      },
      { title = 'Cuff', icon = 'fa-solid fa-handcuffs', event_client = 'blrp_menu:toggleRestraints', shouldClose = true },
      },
    },
  },

  {
    filter = function()
      return handcuffDownFilter() and IsPedInAnyVehicle(PlayerPedId(), true)
    end,

    definition = {
      title = 'Doors >',
      icon = 'fa-solid fa-door-closed',
      items = {
        { title = 'Front Left', icon = 'fa-solid fa-door-open', event_client = 'blrp_menu:toggleDoor', door_id = 0 },
        { title = 'Hood', icon = 'fa-solid fa-door-open', event_client = 'blrp_menu:toggleDoor', door_id = 4 },
        { title = 'Front Right', icon = 'fa-solid fa-door-open', event_client = 'blrp_menu:toggleDoor', door_id = 1 },
        { title = 'Back Right', icon = 'fa-solid fa-door-open', event_client = 'blrp_menu:toggleDoor', door_id = 3 },
        { title = 'Trunk', icon = 'fa-solid fa-door-open', event_client = 'blrp_menu:toggleDoor', door_id = 5 },
        { title = 'Back Left', icon = 'fa-solid fa-door-open', event_client = 'blrp_menu:toggleDoor', door_id = 2 },
      }
    },
  },

  {
    filter = function()
      return handcuffDownFilter() and exports.blrp_core:me().hasGroup('LSFD')
    end,

    definition = {
      title = 'Medic',
      icon = 'fa-solid fa-file-medical',
      items = {
        { title = 'Escort', icon = 'fa-solid fa-people-simple', event_client = 'blrp_menu:emsEscort', shouldClose = true },
        { title = 'Revive', icon = 'fa-solid fa-person-arrow-up-from-line', event_client = 'blrp_menu:reviveTarget', shouldClose = true },
        { title = 'CPR', icon = 'fa-solid fa-person-praying', event_client = 'blrp_menu:performCpr', shouldClose = true },
        { title = 'Treatment >', icon = 'fa-solid fa-stethoscope',
          items = {
            { title = 'Field Treatment', icon = 'fa-solid fa-star-of-life', event_client = 'blrp_menu:fieldTreatment', shouldClose = true },
            { title = 'Toggle Bed', icon = 'fa-solid fa-bed', event_client = 'blrp_menu:toggleBedState', shouldClose = true },
            { title = 'Check Pulse', icon = 'fa-solid fa-magnifying-glass', event_client = 'blrp_menu:checkTargetPulse', shouldClose = true },
            { title = 'Check Injuries', icon = 'fa-solid fa-clipboard-list-check', event_client = 'blrp_menu:checkTargetInjuries', shouldClose = true },
          }
        },
        { title = 'Status >', icon = 'fa-solid fa-users-medical',
          items = {
            { title = 'Busy',  icon = 'fa-solid fa-siren-on', command = 'bu UNAVAILABLE FOR DISPATCH', shouldClose = true },
            { title = 'Reception',  icon = 'fa-solid fa-clipboard', command = 're', shouldClose = true },
            { title = 'Fire Response',  icon = 'fa-solid fa-fire', command = 're', shouldClose = true },
            { title = 'In Service',  icon = 'fa-solid fa-check', command = 'is', shouldClose = true },
          }
        },
        { title = 'Put Into Vehicle', icon = 'fa-solid fa-circle-arrow-down-right', event_client = 'blrp_menu:emsPutInVehicle', shouldClose = true },
        { title = 'Pull From Vehicle', icon = 'fa-solid fa-circle-arrow-up-left', event_client = 'blrp_menu:pullOutVeh', shouldClose = true },
        { title = 'Impound', icon = 'fa-solid fa-car-garage', event_client = 'blrp_menu:impoundVehicle', shouldClose = true },
      }
    },
  },

  {
    filter = function()
      return handcuffDownFilter() and (exports.blrp_core:me().hasGroup('Tow Truck') or exports.blrp_core:me().hasGroup('Sheriff Tow Truck'))  -- You can modify the condition here as needed
    end,

    definition = {
      title = 'Tow',
      icon = 'fa-solid fa-car-garage',
      items = {
        { title = 'Tow Vehicle', icon = 'fa-solid fa-car-garage', event_client = 'blrp_vehicles:client:toggleTow', shouldClose = true },
        { title = 'Request Tow Job', icon = 'fa-solid fa-clipboard-check', event_client = 'blrp_vehicles:client:requestTowDispatch', shouldClose = true },
        { title = '', icon = '', event_client = '', shouldClose = false },  -- Blank One For PlaceHolder
      }
    },
  },

  {
    filter = function()
      local vehicleModels = {
        [`taxi`] = { livery = nil },
        [`dynasty`] = { livery = {3, 10} },
        [`imperialpas`] = { livery = {3, 4, 5} },
        [`eudora`] = { livery = {10, 12} },
        [`broadway`] = { livery = 11 },
      }

      local vehicle = GetVehiclePedIsIn(PlayerPedId())
      local vehicleModel = GetEntityModel(vehicle)

      if vehicleModels[vehicleModel] ~= nil then
        local requiredLivery = vehicleModels[vehicleModel].livery
        local liveryMod = GetVehicleMod(vehicle, 48)  -- 48 is the livery mod slot
        local isValidLivery = false

        if requiredLivery then
          if type(requiredLivery) == "table" then
            for _, validLivery in ipairs(requiredLivery) do
              if liveryMod == validLivery then
                isValidLivery = true
                break
              end
            end
          elseif liveryMod == requiredLivery then
            isValidLivery = true
          end
        else
          isValidLivery = true
        end

        return IsPedInAnyVehicle(PlayerPedId()) and
               GetPedInVehicleSeat(vehicle, -1) == PlayerPedId() and
               isValidLivery and
               exports.blrp_core:me().hasGroup('Taxi')
      end

      return false
    end,

    definition = {
      title = 'Taxi',
      icon = 'fa-solid fa-taxi',
      items = {
        { title = 'Start Taxi Job', icon = 'fa-thin fa-taxi', event_client = 'core:client:jobs:taxi:startjob', shouldClose = true },
        { title = 'Stop Taxi Job', icon = 'fa-thin fa-car', event_client = 'core:client:jobs:taxi:stopjob', shouldClose = true },
        { title = '', icon = '', event_client = '', shouldClose = false },  -- Blank One For PlaceHolder
      }
    },
  },

  {
    filter = function()
      return handcuffDownFilter() and exports.blrp_core:me().hasGroup('Mechanic')
    end,

    definition = {
      title = 'Mechanic',
      icon = 'fa-solid fa-wrench',
      items = {
        { title = 'Diagnose', icon = 'fa-solid fa-flask', event_client = 'vRP:VehicleDiagnostic', shouldClose = true },
        { title = 'Repair Engine', icon = 'fa-solid fa-engine', event_client = 'blrp_menu:repairEngine', shouldClose = true },
        { title = 'Repair Body', icon = 'fa-solid fa-car-side', event_client = 'blrp_menu:repairBody', shouldClose = true },
        { title = 'Repair Fuel', icon = 'fa-solid fa-gas-pump', event_client = 'blrp_menu:repairFuel', shouldClose = true },
      }
    }
  },

  {
    filter = function()
      return  (IsPedInAnyVehicle(PlayerPedId()) and
        GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId()),-1 ) == PlayerPedId() and
        GetEntityModel(GetVehiclePedIsIn(PlayerPedId())) == `dickgreenwd`)
    end,

    definition = {
      id = 'attachlight',
      title = 'Attach Light',
      icon = 'fa-solid fa-siren-on',
      event_client = 'blrp_menu:addLight',
      shouldClose = true,
    }
  },

  {
    filter = function()
      return  (IsPedInAnyVehicle(PlayerPedId()) and
        GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId()),-1 ) == PlayerPedId() and
        GetEntityModel(GetVehiclePedIsIn(PlayerPedId())) == `rrcontender`)
    end,

    definition = {
      id = 'tamode',
      title = 'TA Mode',
      icon = 'fa-solid fa-siren-on',
      items = {
        { title = 'Left', icon = 'fa-solid fa-arrow-left', event_client = 'blrp_vehicles:client:rrcontenderta', direction = 'left', shouldClose = true },
        { title = 'Right', icon = 'fa-solid fa-arrow-right', event_client = 'blrp_vehicles:client:rrcontenderta', direction = 'right', shouldClose = true },
        { title = 'Both', icon = 'fa-solid fa-arrow-right-arrow-left', event_client = 'blrp_vehicles:client:rrcontenderta', direction = 'both', shouldClose = true },
      }
    }
  },

  {
    filter = function()
      return exports.housing:NearbyHouse() and not IsPedInAnyVehicle(PlayerPedId())
    end,

    definition = {
      title = 'Property',
      icon = 'fa-solid fa-buildings',
      items = {
        { title = 'Enter', icon = 'fa-solid fa-right-to-bracket', event_server = 'blrp_housing:enterHouse', shouldClose = true, filter = 'housingEnterOptionFilter', filter_pass_value_to_event = true },
        { title = 'Locked Down', icon = 'fa-solid fa-house-lock', event_client = '', shouldClose = true, filter = 'housingIsLockedDownFilter' },

        { title = 'Lock', icon = 'fa-solid fa-lock', event_server = 'server:houseToggleLock', shouldClose = true, filter = 'housingLockOptionFilter', filter_pass_value_to_event = true },
        { title = 'Too Far From Door', icon = 'fa-solid fa-person-to-door', event_client = '', shouldClose = true, filter = 'housingInsideFarOptionFilter' },

        { title = 'Unlock', icon = 'fa-solid fa-unlock', event_server = 'server:houseToggleLock', shouldClose = true, filter = 'housingUnlockOptionFilter', filter_pass_value_to_event = true },
        { title = 'Locked Down', icon = 'fa-solid fa-house-lock', event_client = '', shouldClose = true, filter = 'housingIsLockedDownFilter' },

        {
          title = 'Furniture >',
          icon = 'fa-solid fa-couch',
          filter = 'housingFurnitureOptionFilter',
          items = {
            { title = 'Place Furniture', icon = 'fa-solid fa-couch', event_server = 'blrp_housing:server:openFurnitureMenu', shouldClose = true, filter = 'housingFurnitureOptionFilter', filter_pass_value_to_event = true },
            { title = 'Buy Furniture', icon = 'fa-solid fa-cart-shopping', event_server = 'blrp_furniturestore:server:openStore', shouldClose = true, filter = 'housingFurnitureOptionFilter', filter_pass_value_to_event = true },
            { title = 'Edit mode: ON', icon = 'fa-solid fa-pen-to-square', event_client = 'blrp_housing:client:toggleFurnitureEditMode', shouldClose = true, filter = 'housingEditModeIsOn' },
            { title = 'Edit mode: OFF', icon = 'fa-solid fa-pen-to-square', event_client = 'blrp_housing:client:toggleFurnitureEditMode', shouldClose = true, filter = 'housingEditModeIsOff' },
          }
        },

        { title = 'Knock', icon = 'fa-solid fa-hand-fist', event_server = 'blrp_housing:knock', shouldClose = true, filter = 'housingKnockOptionFilter', filter_pass_value_to_event = true },
        { title = 'Trick or Treat', icon = 'fa-solid fa-jack-o-lantern', event_server = 'blrp_housing:trickOrTreat', shouldClose = true, filter = 'trickOrTreatFilter', filter_pass_value_to_event = true },

        { title = 'Remove Lockdown', icon = 'fa-solid fa-building-circle-check', event_server = 'core:server:properties:toggleLockdown', shouldClose = true, filter = 'housingUnlockdownFilter' },
        { title = 'Initiate Lockdown', icon = 'fa-solid fa-building-lock', event_server = 'core:server:properties:toggleLockdown', shouldClose = true, filter = 'housingLockdownFilter' },

        { title = 'Eject Guests', icon = 'fa-solid fa-eject', event_server = 'blrp_housing:server:ejectPlayers', shouldClose = true, filter = 'housingEjectOptionFilter', filter_pass_value_to_event = true },

        { title = 'Leave', icon = 'fa-solid fa-right-from-bracket', event_server = 'blrp_housing:leaveHouse', shouldClose = true, filter = 'housingLeaveOptionFilter', filter_pass_value_to_event = true },
        { title = 'Too Far From Door', icon = 'fa-solid fa-person-to-door', event_client = '', shouldClose = true, filter = 'housingInsideFarOptionFilter' },
      }
    }
  }
}

local house_door_distance = 3.0

function isInVehicle()
  return IsPedInAnyVehicle(PlayerPedId(), true)
end

function housingEditModeIsOn()
  return exports.housing:interactFurnitureEditMode()
end

function housingEditModeIsOff()
  return not exports.housing:interactFurnitureEditMode()
end

-- Eject - inside only - checks owner / co-owner / warehouse manage
function housingEjectOptionFilter()
  local nearby_house, inside_house, door_coords = exports.housing:NearbyHouse()

  if not inside_house or #(GetEntityCoords(PlayerPedId()) - door_coords) > house_door_distance then
    return false
  end

  local character_id = exports.blrp_core:me().get('id') or -1

  character_id = tonumber(character_id)

  if
    not nearby_house.business_id and
    (
      character_id == tonumber(nearby_house.owner_character_id) or
      character_id == tonumber(nearby_house.coowner_character_id)
    )
  then
    return nearby_house.id
  end

  if
    nearby_house.business_id
  then
    for business_name, business_perms in pairs(exports.blrp_core:me().get('business_perms') or {}) do
      if business_perms._business_id == nearby_house.business_id and business_perms.warehousemanage then
        return nearby_house.id
      end
    end
  end

  return false
end

-- Lockdown option (LEO)
function housingLockdownFilter()
  local found_allowed_group = false

  for _, group in pairs({
    'police_rank5',
    'sheriff_rank5',
    'sahp_rank2',
  }) do
    if exports.blrp_core:me().hasOrInheritsGroup(group) then
      found_allowed_group = true
      break
    end
  end

  if not found_allowed_group then
    return false
  end

  local nearby_house, inside_house, door_coords = exports.housing:NearbyHouse()

  return (not inside_house and nearby_house.is_locked_down == 0) and nearby_house.id
end

-- Un-lockdown option (LEO)
function housingUnlockdownFilter()
  local found_allowed_group = false

  for _, group in pairs({
    'police_rank5',
    'sheriff_rank5',
    'sahp_rank2',
  }) do
    if exports.blrp_core:me().hasOrInheritsGroup(group) then
      found_allowed_group = true
      break
    end
  end

  if not found_allowed_group then
    return false
  end

  local nearby_house, inside_house, door_coords = exports.housing:NearbyHouse()

  return (not inside_house and nearby_house.is_locked_down == 1) and nearby_house.id
end

-- Option showing too far from inside door (prevent leaving / locking from far)
function housingInsideFarOptionFilter()
  local nearby_house, inside_house, door_coords = exports.housing:NearbyHouse()

  return inside_house and #(GetEntityCoords(PlayerPedId()) - door_coords) > house_door_distance
end

-- Option showing outside door locked down by police
function housingIsLockedDownFilter()
  local nearby_house, inside_house = exports.housing:NearbyHouse()

  return nearby_house.is_locked_down == 1
end

-- Lock - inside and outside - checks that they are not too far from the inside door
function housingLockOptionFilter()
  local nearby_house, inside_house, door_coords = exports.housing:NearbyHouse()

  local inside_check = (inside_house and #(GetEntityCoords(PlayerPedId()) - door_coords) < house_door_distance) or not inside_house

  return
    (
      nearby_house.is_locked == 0 and
      nearby_house.is_locked_down == 0
    ) and
    inside_check and
    nearby_house.id
end

-- Leave - inside only - checks that they are not too far from the inside door
function housingLeaveOptionFilter()
  local nearby_house, inside_house, door_coords = exports.housing:NearbyHouse()

  return (inside_house and #(GetEntityCoords(PlayerPedId()) - door_coords) < house_door_distance) and nearby_house.id
end

-- Unlock - inside and outside - checks that they are not too far from the inside door
function housingUnlockOptionFilter()
  local nearby_house, inside_house, door_coords = exports.housing:NearbyHouse()

  local inside_check = (inside_house and #(GetEntityCoords(PlayerPedId()) - door_coords) < house_door_distance) or not inside_house

  return (nearby_house.is_locked == 1 and nearby_house.is_locked_down == 0 and inside_check) and nearby_house.id
end

-- Furniture filter
function housingFurnitureOptionFilter()
  local nearby_house, inside_house = exports.housing:NearbyHouse()

  return inside_house and nearby_house.id
end

-- Enter option
function housingEnterOptionFilter()
  local nearby_house, inside_house = exports.housing:NearbyHouse()

  return (not inside_house and nearby_house.is_locked_down == 0) and nearby_house.id
end

-- Knock option
function housingKnockOptionFilter()
  local nearby_house, inside_house = exports.housing:NearbyHouse()

  return not inside_house and nearby_house.id
end

function trickOrTreatFilter()
  local currentTime = GlobalState.server_time

  local nearby_house, inside_house = exports.housing:NearbyHouse()

  local isHalloween = (currentTime > 1730296800 and currentTime < 1730555940)
  return isHalloween and not inside_house and nearby_house.id
end
