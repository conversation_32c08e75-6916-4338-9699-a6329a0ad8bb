beginRegisterCategory('police')

registerItem('axion_camera', 'Police Body Camera', 0.2)

registerItem('coil_camera', 'Coil Body Camera', 0.2, {
  {
    name = 'Use',
    callback = function(character, item_id)
      if not character.hasAnyPermissionCore({ 'police.service', 'doc.service', 'emergency.service' }) then
        character.notify('You don\'t know how to use this')
        return
      end

      if not character.progressPromise('Messing with bodycam', 3, {
        animation = {
          animDict = 'clothingtie',
          anim = 'try_tie_neutral_b',
          flags = 49,
        },
      }) then
        return
      end
      TriggerClientEvent('sullybodycam:toggle', character.source)
    end
  }
}, {},{
  override_image = 'axion_camera'
})

registerItem('hand_radio', 'Radio', 0.5)
registerItem('fingerprint_reader', 'Fingerprint Reader', 1.0)
registerItem('sticker_lspd', 'LSPD Sticker', 0.1)
registerItem('sticker_nysp', 'NYSP Sticker', 0.1)
registerItem('sticker_bcso', 'BCSO Sticker', 0.1)

local leo_vehicles = nil

registerItem('finger_scanner', 'Mobile Fingerprint Scanner', 1.0, {
  {
    name = 'Use',
    callback = function(character, item_id)
      if not leo_vehicles then
        leo_vehicles = {}

        local vehicles_all = exports.blrp_vehicles:GetConfigVehiclesAll()

        for vehicle_code, vehicle_data in pairs(vehicles_all) do
          if ({
            ['police'] = true,
            ['sheriff'] = true,
            ['leospeciality'] = true,
            ['leointerceptor'] = true,
            ['policefleet'] = true,
            ['leocommand'] = true,
            ['gtf'] = true,
            ['emergencyboats'] = true,
          })[vehicle_data[4]] then
            table.insert(leo_vehicles, GetHashKey(vehicle_code))
          end
        end
      end

      local nearby_vehicle = tVehicles.getNearbyOwnedVehicle(character.source, { leo_vehicles, 10.0, true })

      if
        (not nearby_vehicle or nearby_vehicle <= 0) and
        not tZones.isInsideMatchedZone(character.source, { 'PoliceStationZone' })
      then
        character.notify('No connection to network. Out of range of LEO vehicle / police station')
        return
      end

      local target_source = character.targetClosePlayer(3)
      local target_character = core.character(target_source)

      if not scanFingerprint(target_character.source, character.source) then
        return
      end

      character.log('POLICE', 'Used mobile fingerprint scanner on ' .. target_character.get('fullname') .. ' ' .. target_character.get('identifier') .. ' ' .. target_character.get('id'))

      TriggerEvent('core:server:enqueueAlert', {
        service_name = 'PoliceSupervisors',
        can_accept = false,
        coords = vector3(0, 0, 0),
        badge = 'Dispatch',
        badge_style = 'primary',
        title = 'Information',
        msg = character.get('fullname') .. ' mobile scanned fingerprint for ' .. target_character.get('fullname'),
        icon = 'fa-solid fa-fingerprint',
        show_for = 10000,
        allow_gps = false,
        hide_location = true,
        location_override = '',
        is_response = true,
      })
    end,
    groups = {
      'LEO',
    }
  }
}, {}, {
  client_aware = true,
})

function calculateBAC(drinks, isFemale)
  local weight = 90
  if isFemale then
    weight = weight * 0.85 -- reduce weight by 15% for females
  end

  local BAC = (drinks / (weight * 0.5 * 0.7)) - (0.015 * 1)

  if tonumber(BAC) < 0 then BAC = '0.00' end

  return string.format("%.3f", BAC)
end

exports('calculateBAC', calculateBAC)

registerItem('breathalyzer', 'Preliminary Breath Tester', 1.0, {
  {
    name = 'Use',
    callback = function(character, item_id)
      local target_source = character.targetClosePlayer(3)
      local target_character = core.character(target_source)

      character.log('POLICE', 'Used PBT on ' .. target_character.get('fullname') .. ' ' .. target_character.get('identifier') .. ' ' .. target_character.get('id'))

      local model = GetEntityModel(GetPlayerPed(target_source))
      local isFemale = (model == GetHashKey('mp_f_freemode_01'))

      local drinks = math.min(tSurvivalAlcohol.getIntoxication(target_source) / 10)
      local BAC = calculateBAC(drinks, isFemale)
      if tonumber(BAC) < 0 then BAC = '0.00' end
      if not target_character.request("Someone is trying to perform a breath test on you, do you blow in to the PBT?", 30) then
        character.notify("They refused to blow in to the PBT")
        return
      end
      character.playSoundAround(3, 'detector_b')
      if not character.progressPromise('Taking Breath Sample', 4, {
        animation = {
          animDict = 'weapons@first_person@aim_rng@generic@projectile@shared@core',
          anim = 'idlerng_med',
          flags = 49,
        },
        prop = {
          model = 'prop_inhaler_02',
          bone = 6286,
          coords = {
            x = 0.1,
            y = 0.0,
            z = -0.01,
          },
          rotation = {
            x = 50.0,
            y = 45.0,
            z = 180.0,
          },
        },
      }) then
        return
      end

      character.playSoundAround(3, 'detector_c', 0.5)
      Citizen.Wait(500)
      character.notify("Returned BAC: ".. BAC)
    end
  }
})

-- Evidence

registerItem('bullet_casing', 'Bullet Casing', 0.01)
registerItem('bullet_fragment', 'Bullet Fragment', 0.01)
registerItem('veh_scrap', 'Vehicle Scrap', 0.01)
registerItem('hair_strand', 'Hair Strand', 0.01)
registerItem('empty_vial', 'Empty Vial', 0.01)
registerItem('blood_vial', 'Filled Vial (Blood)', 0.01, {
  {
    name = 'Label',
    callback = function(character, item_id, item_id_original, meta)
      if not meta then
        return
      end

      character.hideInventory(true)

      local label = character.prompt('Enter label for vial', false, true)

      if not label or label == '' then
        character.notify('Invalid label')
        return
      end

      modifyItemMeta(character.source, item_id, 'evidence_bin_label', label)
    end
  },
  {
    name = 'Drink',
    callback = function(character, item_id)
      if not I.doIfNotActionLocked(character) then
        return
      end

      tActionLocks.setTimeout(character.source, {"physical", true, 4})

      I.effectDrink(character, false, false, 'prop_cs_vial_01', false, false, false)

      character.notify('You drink the blood. It tastes awfully ferrous')
      character.take(item_id, 1)
    end
  }
})

registerItem('gsr_kit', 'GSR Test Kit', 0.02, {
  {
    name = 'Use',
    callback = function(character, item_id)
      local target_source = character.targetClosePlayer(3)

      if not target_source then
        character.notify('No people nearby')
        return
      end

      if not character.take(item_id, 1) then
        return
      end


      character.hideInventory(true)

      local gsr_positive, gsr_level = tCore.gsrTest(target_source)

      character.animate({ { 'mp_common', 'givetake1_a' } }, false)

      if not character.hasGroup('LEO') or not gsr_positive then

        character.notify('GSR Test Result: NEGATIVE')
        local label = character.prompt('Enter label for test kit', false, true)

        character.give('gsr_kit_used', 1, {
          ['evidence_bin_label'] = label,
          ['drug_scan_result'] = 'NEGATIVE',
        })
        return
      end

      local label = character.prompt('Enter label for test kit', false, true)
      character.give('gsr_kit_used', 1, {
        ['evidence_bin_label'] = label,
        ['drug_scan_result'] = 'POSITIVE'.. string.upper(gsr_level),
      })
      character.notify('GSR Test Result: POSITIVE - ' .. string.upper(gsr_level))
    end
  }
})

registerItem('gsr_kit_used', 'GSR Test Kit (used)', 0.02, {
  {
    name = 'Label',
    callback = function(character, item_id, item_id_original, meta)
      character.hideInventory(true)

      local label = character.prompt('Enter label for test kit', false, true)

      if not label or label == '' then
        character.notify('Invalid label')
        return
      end

      modifyItemMeta(character.source, item_id, 'evidence_bin_label', label)
    end,
  },
}, nil, {
  unstackable = true,
})

registerItem('reagent_tester', 'Substance Test Kit', 0.05, {
  {
    name = 'Open',
    callback = function(character, item_id)
      character.hideInventory(true)

      SetTimeout(250, function()
        local chest_name = 'substanceTest-' .. character.get('id') .. '-' .. os.time()

        character.openChest(chest_name, 0.5, function()
          local drug_item_id, drug_item_data = core.chest(chest_name).getSingleItem()

          if not drug_item_id then
            return
          end

          local item_definition = GItems.getItemDefinition(drug_item_id)
          local sub_color = '#FFFFFF'
          local sub_name = 'None'

          if item_definition.sub_color and item_definition.sub_name then
            sub_color = item_definition.sub_color
            sub_name = item_definition.sub_name
          end

          if not character.take(item_id, 1, false) then
            return
          end

          if not character.progressPromise('Analyzing Sample', 4, {
            animation = {
              animDict = 'mp_arresting',
              anim = 'a_uncuff',
              flags = 49,
            },
          }) then
            sub_color = '#FFFFFF'
            sub_name = 'None - Inconclusive Test'
          end

          character.give('used_reagent_tester', 1, {
            sub_color = sub_color,
            sub_name = sub_name
          })
        end, {
          whitelisted_categories = { 'testable_drugs', 'drugs' },
          max_slots = 1,
        })
      end)
    end
  }
})

registerItem('used_reagent_tester', 'Substance Test Kit (Used)', 0.05, {
  {
    name = 'Label',
    callback = function(character, item_id)
      character.hideInventory(true)

      local label = character.prompt('Enter label', '', true, 30)

      if label == '' then
        label = nil
      end

      modifyItemMeta(character.source, item_id, 'storage_label', label)

      if not label then
        character.notify('Removed label')
      else
        character.notify('Labelled test kit: ' .. label)
      end
    end
  }
}, false, {
  unstackable = true
})

registerItem('spikestrip', 'Spike Strip', 8.0, {
  {
    name = 'Use',
    callback = function(character, item_id)
      if not character.hasGroup('LEO') then
        character.notifyError('You stab yourself with the spike strips while playing around')
        character.varyHealth(-20)
        return
      end

      character.client('c_setSpike')
    end
  }
})

registerItem('police_armor', 'Police Armor', 1.0, {
  {
    name = 'Use',
    callback = function(character, item_id)
      if not character.hasGroup({
        'LEO',
        'LEO_OffDuty',
        'LSFD',
        'Security',
      }) then
        character.notify('This does not fit you')
        return
      end

      I.putOnArmor(character, item_id, 100)
    end
  }
}, {}, {
  carry_limit = 5,
  dont_disarm = true,
  categories_secondary = {
    'duffelbag_blacklist',
  },
})

registerItem('evidence_bin', 'Evidence Bin (INV)', 0.01, {
  {
    name = 'Open',
    callback = function(character, item_id)
      if not character.hasGroup('LEO') then
        character.notify("Even though you're probably a criminal, you can't bring yourself to open this")
        return
      end

      local bin_id = item_id:gsub('evidence_bin:meta:', '')

      if not string.match(item_id, ':meta:') then
        character.notify('The bin is jammed shut - try getting a new one')
        return
      end

      character.client('blrp_inventory:hide')

      SetTimeout(250, function()
        character.openChest('ev' .. bin_id .. 'bin', 1000.0, function() end, {
          whitelisted_items = {
            'bullet_casing',
            'bullet_fragment',
            'veh_scrap',
            'hair_strand',
            'empty_vial',
            'blood_vial',
            'printed_paper',
            'm_document',
            'gopostal_letter_open',
            'gopostal_parcel_open',
            'gopostal_freight_open',
          }
        })
      end)
    end
  },

  {
    name = 'Label',
    callback = function(character, item_id)
      if not character.hasGroup('LEO') then
        character.notify("You can't find a pen")
        return
      end

      character.client('blrp_inventory:hide')

      SetTimeout(100, function()
        local label = character.prompt('Enter label for evidence bin', false, true)

        if not label or label == '' then
          character.notify('Invalid label')
          return
        end

        if not string.match(item_id, ':meta:') then
          character.notify('The bin is jammed shut - try getting a new one')
          return
        else
          local bin_id = item_id:gsub('evidence_bin:meta:', '')

          character.take(item_id, 1, false)
          character.give(item_id, 1, {
            evidence_bin_id = bin_id,
            evidence_bin_label = label
          }, false, false)
        end

        character.notify('You scribble the label on the lid')
      end)
    end
  }
})

registerItem('evidence_bin_general', 'Evidence Bin', 0.01, {
  {
    name = 'Open',
    callback = function(character, item_id)
      local bin_id = item_id:gsub('evidence_bin_general:meta:', '')

      if not string.match(item_id, ':meta:') then
        character.notify('The bin is jammed shut - try getting a new one')
        return
      end

      local meta = character.hasGetItemMeta(item_id)
      local time_exempt = false
      local whitelisted_items = nil

      if meta.imprint_time and (os.time() - meta.imprint_time) <= (30 * 60) then
        time_exempt = true

        -- This prevents crims getting their hands on evidence bins and then using them as infinite storage units
        if not character.hasGroup('LEO') then
          whitelisted_items = {}
        end
      end

      if not character.hasGroup('LEO') and not time_exempt then
        character.notify('You cannot find a way to open this')
        return
      end

      character.client('blrp_inventory:hide')

      SetTimeout(250, function()
        character.openChest('ev' .. bin_id .. 'bin2', 1000.0, function() end, {
          allow_transfer_from_storage = (
            character.hasPermissionCore('lspd.command') or
            character.hasPermissionCore('sheriff.command') or
            character.hasGroup('LEO INV') or
            time_exempt
          ),
          whitelisted_items = whitelisted_items
        })
      end)
    end
  },

  {
    name = 'Label',
    callback = function(character, item_id)
      if not character.hasGroup('LEO') then
        character.notify("You can't find a pen")
        return
      end

      character.client('blrp_inventory:hide')

      SetTimeout(100, function()
        local label = character.prompt('Enter label for evidence bin', false, true)

        if not label or label == '' then
          character.notify('Invalid label')
          return
        end

        if not string.match(item_id, ':meta:') then
          character.notify('The bin is jammed shut - try getting a new one')
          return
        else
          local bin_id = item_id:gsub('evidence_bin_general:meta:', '')

          character.take(item_id, 1, false)
          character.give(item_id, 1, {
            evidence_bin_id = bin_id,
            evidence_bin_label = label
          }, false, false)
        end

        character.notify('You scribble the label on the lid')
      end)
    end
  }
})

registerItem('pd_radio_tool', 'Radio Tool', 0.1, {
  [1] = {
    name = 'Dispatch Alerts',
    callback = function(character, item_id)
      if not character.hasPermissionCore('police.service') then
        return
      end

      local alert_status = T.getInstance('blrp_core', 'alerts').toggleSound(character.source)

      if alert_status then
        character.notify('Dispatch alert sound enabled')
      else
        character.notify('Dispatch alert sound disabled')
      end
    end
  },
  [2] = {
    name = 'Dispatch Blip',
    callback = function(character, item_id)
      if not character.hasPermissionCore('police.service') then
        return
      end

      local blip_hidden = not (character.get('blip_hidden') or false)

      character.set('blip_hidden', blip_hidden)

      if blip_hidden then
        character.notify('Dispatch blip hidden')
        character.log('ACTION', 'Set dispatch blip to hidden')
      else
        character.notify('Dispatch blip visible')
        character.log('ACTION', 'Set dispatch blip to visible')
      end
    end
  }
})

registerItem('cru_radio_tool', 'CRU Radio Tool', 0.1, {
  [1] = {
    name = 'Dispatch Alerts',
    callback = function(character, item_id)
      if not character.hasPermissionCore('cru.employee') then
        return
      end

      local alert_status = T.getInstance('blrp_core', 'alerts').toggleSound(character.source)

      if alert_status then
        character.notify('Dispatch alert sound enabled')
      else
        character.notify('Dispatch alert sound disabled')
      end
    end
  },
  [2] = {
    name = 'Dispatch Blip',
    callback = function(character, item_id)
      if not character.hasPermissionCore('cru.employee') then
        return
      end

      local blip_hidden = not (character.get('blip_hidden') or false)

      character.set('blip_hidden', blip_hidden)

      if blip_hidden then
        character.notify('Dispatch blip hidden')
        character.log('ACTION', 'Set dispatch blip to hidden')
      else
        character.notify('Dispatch blip visible')
        character.log('ACTION', 'Set dispatch blip to visible')
      end
    end
  }
})

local fak_options = {
  [1] = {
    name = 'Use',
    callback = function(character, item_id)
      local is_leo = character.hasGroup('LEO')

      local seconds = 4

      if not is_leo then
        seconds = 10
      end

      local item_name = GItems.getItemName(item_id)

      if
        not character.progressPromise('Bandaging with ' .. item_name, seconds, {
          interrupt_when_shot = true,
          interrupt_when_ragdoll = true,
          animation = {
            animDict = 'missheistdockssetup1clipboard@idle_a',
            anim = 'idle_a',
            flags = 49,
          },
          controlDisables = {
            disableMovement = false,
            disableSprint = true,
            disableCarMovement = false,
            disableMouse = false,
            disableCombat = true,
          },
          prop = {
            model = 'prop_ld_health_pack',
          },
        }) or
        not character.take(item_id, 1)
      then
        return
      end

      if is_leo then
        character.varyHealthOverTime(30, 2)
        character.log('HEALTH', 'Gained health with item', {
          item_id = item_id,
          amount = '30',
        })
      else
        character.notify('Your lack of LEO training causes you not to use the ' .. item_name .. ' to its fullest potential')
        character.client('mythic_hospital:items:bandage')
      end
    end
  },
  [2] = {
    name = 'Administer Treatment',
    callback = function(character, item_id)
      local close_source = character.targetClosePlayer(5)

      if not close_source then
        return
      end

      local target = core.character(close_source)
      local is_leo = character.hasGroup('LEO')

      local seconds = 4

      if not is_leo then
        seconds = 10
      end

      local item_name = GItems.getItemName(item_id)

      if
        not character.progressPromise('Bandaging with ' .. item_name, seconds, {
          interrupt_when_shot = true,
          interrupt_when_ragdoll = true,
          animation = {
            animDict = 'missheistdockssetup1clipboard@idle_a',
            anim = 'idle_a',
            flags = 49,
          },
          prop = {
            model = 'prop_ld_health_pack',
          },
        }) or
        not character.take(item_id, 1)
      then
        return
      end

      if is_leo then
        character.notify('You treated someone with an ' .. item_name)
        target.varyHealth(20)
        target.notify('You were treated with an ' .. item_name)
        target.log('HEALTH', 'Gained health with item', {
          item_id = item_id,
          amount = '20',
        })
      else
        character.notify('Your lack of LEO training causes you not to use the ' .. item_name .. ' to its fullest potential')
        target.varyHealth(8)
        target.notify('You were treated with an ' .. item_name)
        target.log('HEALTH', 'Gained health with item', {
          item_id = item_id,
          amount = '8',
        })
      end
    end
  },
  [3] = {
    name = 'Inspect',
    callback = function(character, item_id)
      character.notify('The label reads: Go FAK yourself - Made in North Yankton')
    end
  }
}

registerItem('afak', 'AFAK', 0.25, fak_options, {}, { carry_limit = 8, dont_disarm = true, categories_secondary = { 'duffelbag_blacklist' } })
registerItem('ifak', 'IFAK', 0.25, fak_options, {}, { carry_limit = 8, dont_disarm = true, categories_secondary = { 'duffelbag_blacklist' } })

registerItem('prop_bcso_badge', 'BCSO Badge', 0.1, {
  {
    name = 'Use',
    callback = function(character, item_id)
      TriggerClientEvent('blrp_core:flashBadge:client:animation', character.source, item_id)
    end
  }
}, false, {
  optional_rob = true,
})

registerItem('prop_fibb_badge', 'FIB Badge', 0.1, {
  {
    name = 'Use',
    callback = function(character, item_id)
      TriggerClientEvent('blrp_core:flashBadge:client:animation', character.source, item_id)
    end
  }
}, false, {
   optional_rob = true,
 })

registerItem('prop_lspd2_badge', 'LSPD Badge', 0.1, {
  {
    name = 'Use',
    callback = function(character, item_id)
      TriggerClientEvent('blrp_core:flashBadge:client:animation', character.source, item_id)
    end
  }
}, false, {
   optional_rob = true,
 })

registerItem('prop_sasp_badge', 'SAHP Badge', 0.1, {
  {
    name = 'Use',
    callback = function(character, item_id)
      TriggerClientEvent('blrp_core:flashBadge:client:animation', character.source, item_id)
    end
  }
}, false, {
   optional_rob = true,
 })

registerItem('prop_nysp_badge', 'NYSP Badge', 0.1, {
  {
    name = 'Use',
    callback = function(character, item_id)
      TriggerClientEvent('blrp_core:flashBadge:client:animation', character.source, item_id)
    end
  }
}, false, {
   optional_rob = true,
 })

registerItem('prop_lspd_badge', 'LSPD Detective Badge', 0.1, {
  {
    name = 'Use',
    callback = function(character, item_id)
      TriggerClientEvent('blrp_core:flashBadge:client:animation', character.source, item_id)
    end
  }
}, false, {
   optional_rob = true,
 })

registerItem('police_gear_bag', 'Police Gear Bag', 20.0, {
  {
    name = 'Load into trunk',
    callback = function(character, item_id)

      if not character.hasGroup('LEO') then
        character.notifyError('You try to open the zipper but it appears to be stuck.')
        return
      end

      local network_id = tVehicles.findNearbyVehicleNetworkId(character.source)

      if not network_id then
        return
      end

      local vehicle = NetworkGetEntityFromNetworkId(network_id)
      local state = Entity(vehicle).state
      local model = state.model

      if exports.blrp_core:GetVehicleClassFromName(model) ~= 18 then
        character.notifyError('The police equipment doesn\'t seem to fit in here.')
        return
      end

      if not character.request('Do you want to load the equipment in to the trunk of the '..model..'?') then
        return
      end

      local starter_items = {
        ['police_armor'] = 15,
        ['ifak'] = 15,
        ['ammo_45acp'] = 500, -- Heavy pistol ammo
        ['ammo_40sw'] = 500, -- Pistol (glock, combat pistol) ammo
        ['ammo_9mm_nato'] = 500, -- SP ammo
        ['ammo_556nato'] = 1000, -- rifle ammo
        ['ammo_12ga'] = 50, -- shotgun shells
       -- ['ammo_10mm'] = 200, -- smg ammo
        ['spikestrip'] = 2,
        ['prop_barrier_work05'] = 5,
        ['wbody|WEAPON_FIREEXTINGUISHER'] = 1,
        ['wammo|WEAPON_STUNGUN'] = 50,
        ['gsr_kit'] = 15,
        ['reagent_tester'] = 15,
        ['bandage'] = 20,
        ['p_cone_red'] = 2,
        ['p_cone_blue'] = 2,
        ['p_cone'] = 5,
        ['nitrile_gloves'] = 2,
        ['empty_vial'] = 20,
        ['carrepairkit'] = 5,
        ['tirerepairkit'] = 3,
      }

      local newItemWeight = 0
      for type_name, type_count in pairs(starter_items) do
        newItemWeight = newItemWeight + (GItems.getItemWeight(type_name) * type_count)
      end

      local storage_config = exports.blrp_vehicles:getStorageConfig()
      local trunk_max_weight = storage_config.vehicles[state.model] or storage_config.default_weight
      local chest_name = 'vehicle:' .. state.server_uid .. ':tr'
      local current_weight = core.chest(chest_name).getCurrentWeight()


      if current_weight + newItemWeight > trunk_max_weight then
        character.notifyError('The trunk is too full to fit the gear in.')
        return
      end

      core.chest(chest_name).fetchContents(function(items)
        if not items then items = {} end

        for type_name, type_count in pairs(starter_items) do
          if not items[type_name] then
            items[type_name] = {
              amount = 0
            }
          end

          items[type_name].amount = items[type_name].amount + type_count

        end

        if character.take(item_id, 1) then
          core.chest(chest_name).setContents(items)
          character.notify('Loaded equipment in to the trunk of the '..model..'.')
          character.log('ACTION', 'Loaded a police gear bag in to vehicle', {
            server_uid = state.server_uid,
          })
        else
          character.notifyError('Failed to load equipment')
        end

      end)
    end,
  }
}, {}, {
  carry_limit = 1
})

registerItem('academy_gear_bag', 'LEO Academy Gear Bag', 5.0, {
  {
    name = 'Load into trunk',
    callback = function(character, item_id)
      if not character.hasGroup('LEO') then
        character.notifyError('You try to open the zipper but it appears to be stuck.')
        return
      end

      local network_id = tVehicles.findNearbyVehicleNetworkId(character.source)

      if not network_id then
        return
      end

      local vehicle = NetworkGetEntityFromNetworkId(network_id)
      local state = Entity(vehicle).state
      local model = state.model

      if exports.blrp_core:GetVehicleClassFromName(model) ~= 18 then
        character.notifyError('The police equipment doesn\'t seem to fit in here.')
        return
      end

      if not character.request('Do you want to load the equipment into the trunk of the ' .. model .. '?') then
        return
      end

      local starter_items = {
        ['police_armor'] = 3,
        ['ifak'] = 5,
        ['wbody|WEAPON_STUNGUN'] = 1,
        ['wbody|WEAPON_NIGHTSTICK'] = 1,
        ['wbody|WEAPON_KNIFE'] = 1,
        ['wammo|WEAPON_STUNGUN'] = 15,
        ['hand_radio'] = 1,
        ['tablet'] = 1,
        ['finger_scanner'] = 1,
        ['gsr_kit'] = 10,
      }

      if character.hasGroup('Sheriff_Internal') or character.hasGroup('SAHP_Internal') then
        starter_items['wbody|WEAPON_HEAVYPISTOL'] = 1
        starter_items['ammo_45acp'] = 100
      elseif character.hasGroup('LSPD_Internal') then
        starter_items['wbody|WEAPON_SERVICEPISTOL_9MM'] = 1
        starter_items['ammo_9mm_nato'] = 100
      end

      local chest = Chest:new('vehicle:' .. state.server_uid .. ':tr')

      if chest:getItemsWeight() + GItems.getTotalWeight(starter_items) > exports.blrp_vehicles:getStorageCapacity(state.model) then
        character.notifyError('The trunk is too full to fit the gear in.')
        return
      end

      if not character.take(item_id, 1) then
        return
      end

      for item_id, item_amount in pairs(starter_items) do
        chest:put(item_id, item_amount, false, character.source)
      end

      character.notify('Loaded equipment into the trunk of the ' .. model .. '.')
      character.log('ACTION', 'Loaded a police gear bag into vehicle', {
        server_uid = state.server_uid,
      })
    end,
  }
}, {}, {
  override_image = 'police_gear_bag'
})

registerItem('ems_gear_bag', 'LSFD Gear Bag', 15.0, {
  {
    name = 'Load into trunk',
    callback = function(character, item_id)

      if not character.hasGroup('LSFD') then
        character.notifyError('You try to open the zipper but it appears to be stuck.')
        return
      end

      local network_id = tVehicles.findNearbyVehicleNetworkId(character.source)

      if not network_id then
        return
      end

      local vehicle = NetworkGetEntityFromNetworkId(network_id)
      local state = Entity(vehicle).state
      local model = state.model

      if exports.blrp_core:GetVehicleClassFromName(model) ~= 18 then
        character.notifyError('The medical equipment doesn\'t seem to fit in here.')
        return
      end

      if not character.request('Do you want to load the equipment in to the trunk of the '..model..'?') then
        return
      end

      local starter_items = {
        ['police_armor'] = 5,
        ['medkit'] = 50,
        ['firstaid'] = 50,
        ['vicodin'] = 50,
        ['hydrocodone'] = 50,
        ['gauze'] = 50,
        ['suture'] = 50,
        ['dressing'] = 50,
        ['nitrile_gloves'] = 5,
        ['scuba_kit_fr'] = 5,
        ['p_cone'] = 10,
        ['carrepairkit'] = 5,
        ['tirerepairkit'] = 5,
        ['shark_repelent'] = 5,
      }

      local newItemWeight = 0
      for type_name, type_count in pairs(starter_items) do
        newItemWeight = newItemWeight + (GItems.getItemWeight(type_name) * type_count)
      end

      local storage_config = exports.blrp_vehicles:getStorageConfig()
      local trunk_max_weight = storage_config.vehicles[state.model] or storage_config.default_weight
      local chest_name = 'vehicle:' .. state.server_uid .. ':tr'
      local current_weight = core.chest(chest_name).getCurrentWeight()


      if current_weight + newItemWeight > trunk_max_weight then
        character.notifyError('The trunk is too full to fit the gear in.')
        return
      end

      core.chest(chest_name).fetchContents(function(items)
        if not items then items = {} end

        for type_name, type_count in pairs(starter_items) do
          if not items[type_name] then
            items[type_name] = {
              amount = 0
            }
          end

          items[type_name].amount = items[type_name].amount + type_count

        end

        if character.take(item_id, 1) then
          core.chest(chest_name).setContents(items)
          character.notify('Loaded equipment in to the trunk of the '..model..'.')
          character.log('ACTION', 'Loaded a LSFD gear bag in to vehicle', {
            server_uid = state.server_uid,
          })
        else
          character.notifyError('Failed to load equipment')
        end

      end)
    end,
  }
}, {}, {
  carry_limit = 1
})

registerItem('police_regear_bag', 'Police Regear Bag', 15.0, {
  {
    name = 'Load into trunk',
    callback = function(character, item_id)

      if not character.hasGroup('LEO') then
        character.notifyError('You try to open the zipper but it appears to be stuck.')
        return
      end

      local network_id = tVehicles.findNearbyVehicleNetworkId(character.source)

      if not network_id then
        return
      end

      local vehicle = NetworkGetEntityFromNetworkId(network_id)
      local state = Entity(vehicle).state
      local model = state.model

      if exports.blrp_core:GetVehicleClassFromName(model) ~= 18 then
        character.notifyError('The police equipment doesn\'t seem to fit in here.')
        return
      end

      if not character.request('Do you want to load the equipment in to the trunk of the '..model..'?') then
        return
      end

      local starter_items = {
        ['police_armor'] = 15,
        ['ifak'] = 15,
        ['ammo_45acp'] = 500, -- Heavy pistol ammo
        ['ammo_9mm_nato'] = 500, -- SP ammo
        ['ammo_556nato'] = 500, -- rifle ammo
        ['ammo_12ga'] = 50, -- shotgun shells
        ['wammo|WEAPON_STUNGUN'] = 50,
        ['bandage'] = 20,
        ['carrepairkit'] = 5,
        ['tirerepairkit'] = 3,
      }

      if character.hasGroup('SAHP_Internal') then
        starter_items['ammo_762nato'] = 500
      end

      local newItemWeight = 0
      for type_name, type_count in pairs(starter_items) do
        newItemWeight = newItemWeight + (GItems.getItemWeight(type_name) * type_count)
      end

      local storage_config = exports.blrp_vehicles:getStorageConfig()
      local trunk_max_weight = storage_config.vehicles[state.model] or storage_config.default_weight
      local chest_name = 'vehicle:' .. state.server_uid .. ':tr'
      local current_weight = core.chest(chest_name).getCurrentWeight()


      if current_weight + newItemWeight > trunk_max_weight then
        character.notifyError('The trunk is too full to fit the gear in.')
        return
      end

      core.chest(chest_name).fetchContents(function(items)
        if not items then items = {} end

        for type_name, type_count in pairs(starter_items) do
          if not items[type_name] then
            items[type_name] = {
              amount = 0
            }
          end

          items[type_name].amount = items[type_name].amount + type_count

        end

        if character.take(item_id, 1) then
          core.chest(chest_name).setContents(items)
          character.notify('Loaded equipment in to the trunk of the '..model..'.')
          character.log('ACTION', 'Loaded a police gear bag in to vehicle', {
            server_uid = state.server_uid,
          })
        else
          character.notifyError('Failed to load equipment')
        end

      end)
    end,
  }
}, {}, {
  carry_limit = 1,
  override_image = 'police_gear_bag'
})

-- Knox GPS thing
registerItem('gps_tracker', function(item_id, meta)
  local name = 'GPS Tracker'

  if meta.frequency then
    name = name .. ' (' .. meta.frequency .. ')'
  end

  return name
end, 0.5, {
  {
    name = 'Install',
    callback = function(character, item_id, item_id_base, meta)
      character.hideInventory(true)

      exports.blrp_vehicles:InstallPhysicalTracker(character, item_id, item_id_base, meta)
    end
  },
  {
    name = 'Set Frequency',
    callback = function(character, item_id)
      character.hideInventory(true)

      local frequency = character.prompt('Enter frequency (whole numbers only)', '', true, 30)

      if not frequency or frequency == '' or not tonumber(frequency) then
        character.notify('Invalid Frequency')
        return
      end

      character.notify('Locator frequency set to ' .. frequency)
      modifyItemMeta(character.source, item_id, 'frequency', frequency)
    end
  }
}, false, {
  unstackable = true,
})

registerItem('gps_locator', function(item_id, meta)
  local name = 'GPS Locator'

  if meta.frequency then
    name = name .. ' (' .. meta.frequency .. ')'
  end

  return name
end, 0.5, {
  {
    name = 'Activate Remotely',
    callback = function(character, item_id, item_id_base, meta)
      if not meta.frequency then
        character.notify('You must set a frequency')
        return
      end

      character.notify('Tracking activation sent...')

      -- Random delay
      Citizen.Wait(math.random(15, 25) * 1000)

      local vehicles = exports.blrp_vehicles:GetVehicles(function(vehicle)
        return tostring(vehicle.tracker_frequency) == tostring(meta.frequency)
      end)

      -- No vehicles on this frequency
      if #vehicles == 0 then
        character.notify('No tracker response received')
        return
      end

      local trackers_enabled = 0

      for _, vehicle_data in pairs(vehicles) do
        local vehicle = NetworkGetEntityFromNetworkId(vehicle_data.network_id)

        if vehicle and vehicle > 0 then
          local state = Entity(vehicle).state

          if
            state.server_uid == vehicle_data.uid and
            exports.blrp_vehicles:HasAnyVehicleFlags(state.flags, 'FLAG_TRACKER_ATTACHED') and
            not exports.blrp_vehicles:HasAnyVehicleFlags(state.flags, 'FLAG_TRACKER_DISABLED', 'FLAG_TRACKER_ENABLED')
          then
            exports.blrp_vehicles:StartVehicleTracker(vehicle_data.network_id, 15, 'frequency:' .. vehicle_data.tracker_frequency, 47)
            trackers_enabled = trackers_enabled + 1
            character.log('VEHICLE-TRACKER', 'Activated vehicle tracker', {
              vehicle_plate = vehicle_data.plate,
              vehicle_id = vehicle_data.uid,
              frequency = vehicle_data.tracker_frequency,
            })
          end
        end
      end

      if trackers_enabled == 0 then
        character.notify('No tracker response received')
        return
      end

      character.notify('Tracker activated on ' .. trackers_enabled .. ' vehicles')
    end
  },
  {
    name = 'Deactivate remotely',
    callback = function(character, item_id, item_id_base, meta)
      if not meta.frequency then
        character.notify('You must set a frequency')
        return
      end

      local vehicles = exports.blrp_vehicles:GetVehicles(function(vehicle)
        return tostring(vehicle.tracker_frequency) == tostring(meta.frequency)
      end)

      character.notify('Tracking deactivation sent')

      for _, vehicle_data in pairs(vehicles) do
        local vehicle = NetworkGetEntityFromNetworkId(vehicle_data.network_id)

        if vehicle and vehicle > 0 then
          local state = Entity(vehicle).state

          if
            state.server_uid == vehicle_data.uid and
            exports.blrp_vehicles:HasAnyVehicleFlags(state.flags, 'FLAG_TRACKER_ENABLED')
          then
            exports.blrp_vehicles:StopVehicleTracker(vehicle_data.network_id)
            character.log('VEHICLE-TRACKER', 'Deactivated vehicle tracker', {
              vehicle_plate = vehicle_data.plate,
              vehicle_id = vehicle_data.uid,
              frequency = vehicle_data.tracker_frequency,
            })
          end
        end
      end
    end,
    groups = {
      'LEO',
    },
  },
  {
    name = 'Set Frequency',
    callback = function(character, item_id)
      character.hideInventory(true)

      local frequency = character.prompt('Enter frequency (whole numbers only)', '', true, 30)

      if not frequency or frequency == '' or not tonumber(frequency) then
        character.notify('Invalid Frequency')
        return
      end

      character.notify('Locator frequency set to ' .. frequency)
      modifyItemMeta(character.source, item_id, 'frequency', frequency)
    end
  }
}, false, {
  unstackable = true,
})

endRegisterCategory()
