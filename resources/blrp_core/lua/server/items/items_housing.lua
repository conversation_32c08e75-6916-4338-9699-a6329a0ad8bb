beginRegisterCategory('excluded')

registerItem('furniture_token', 'Furniture Token', 0.5, {
  {
    name = 'Claim',
    callback = function(character, item_id)
      character.client('blrp_inventory:hide')

      local meta = character.hasGetItemMeta(item_id)

      if not meta or not meta.name_display or not meta.name_model then
        return
      end

      local name_display = meta.name_display
      local name_model = meta.name_model

      local current_property_cl = tHousing.getCurrentHouse(character.source)

      if not current_property_cl then
        character.notify('You must be inside a property to redeem this')
        return
      end

      local current_property = G_houses[current_property_cl.property_id]

      if not current_property then
        character.notify('Error claiming furniture')
        return
      end

      if
        not character.request('Are you sure you want to claim this token to the current property? If you do, the item will be removed from your player inventory') or
        not character.take(item_id, 1)
      then
        return
      end

      local binding = 'character_id'
      local binding_value = current_property.owner_character_id

      -- Claim to business
      if current_property.business_id then
        binding = 'business_id'
        binding_value = current_property.business_id
      end

      local insert_id = MySQL.insert.await('INSERT INTO core_character_furniture (' .. binding .. ', name_model, name_display, tex_var) VALUES (?, ?, ?, ?)', {
        binding_value, name_model, name_display, (meta.tex_var or nil)
      })

      CoreAnticheat.EntityListener.AddAllowedModel(name_model)
      --acAddAllowedModel(name_model)

      character.log('ACTION', 'Redeemed furniture token', {
        item_id = item_id,
        name_display =  name_display,
        name_model =  name_model,
        furniture_id = insert_id,
      })
      character.notify('The furniture company has delivered 1x ' .. name_display .. ' to your house')
    end
  }
}, false, {
  claimable = true,
})

beginRegisterCategory('housing')

-- Misc
registerFurnitureTokenItem('h_prop_loggerman', 'Logger Man', 1.0, false, false, { furniture_token_model = 'tr_loggerman' })

registerFurnitureTokenItem('tr_manfredsprunk', 'Sprunk Picture', 1.0, {
  {
    name = 'Inspect',
    callback = function(character, item_id)
      character.notify('A limited edition print of BCSO Deputy Manfred Renard and his love for Sprunk')
    end
  }
})

registerFurnitureTokenItem('tr_manfredredwood', 'Redwood Picture', 1.0, {
  {
    name = 'Inspect',
    callback = function(character, item_id)
      character.notify('A limited edition print of BCSO Deputy Manfred Renard and his love for Redwood Cigarettes')
    end
  }
})

registerFurnitureTokenItem('prop_bl_hse_celltowa_a', 'BCSO Celltowa Poster', 0.5)

-- Halloween 2021
registerFurnitureTokenItem('h_prop_devilboy', 'Devil Boy', 1.0, false, false, { furniture_token_model = 'h_prop_devilguy' })
registerFurnitureTokenItem('h_prop_ghostboy', 'Ghost Boy', 1.0, false, false, { furniture_token_model = 'h_prop_ghost' })
registerFurnitureTokenItem('h_prop_pumpkincat', 'Pumpkin Cat', 1.0, false, false, { furniture_token_model = 'h_prop_pumpkincat' })
registerFurnitureTokenItem('h_prop_scream', 'Scream', 1.0, false, false, { furniture_token_model = 'h_prop_scream' })
registerFurnitureTokenItem('h_prop_spooky', 'Spooky', 1.0, false, false, { furniture_token_model = 'h_prop_spook' })

-- Christmas 2021
registerFurnitureTokenItem('h_prop_beaver', 'Yankton Beaver', 1.0, false, false, { furniture_token_model = 'dc_plg_04_beaver001' })

-- Casino lucky wheel

for plushie_id, plushie_name in pairs({
  ['ch_prop_arcade_claw_plush_01a'] = 'Purple Plushie',
  ['ch_prop_arcade_claw_plush_02a'] = 'Green Plushie',
  ['ch_prop_arcade_claw_plush_03a'] = 'Blue Plushie',
  ['ch_prop_arcade_claw_plush_04a'] = 'Brown Plushie',
  ['ch_prop_arcade_claw_plush_05a'] = 'Yellow Plushie',
  ['ch_prop_arcade_claw_plush_06a'] = 'Red Plushie',
  ['ch_prop_princess_robo_plush_07a'] = 'Princess Plushie',
  ['ch_prop_shiny_wasabi_plush_08a'] = 'Wasabi Plushie',
  ['ch_prop_master_09a'] = 'Sensei Plushie',
  ['prop_bd_plush'] = 'BD Plushie',
  ['prop_bl_sexdoll'] = "Jeery's Wife",
  ['ch_prop_arcade_claw_plush_06n'] = 'Tuner Shop Plushie',
  ['h4_prop_h4_art_pant_01a'] = 'Jewelled Panther',
  ['prop_bl_plush_cat'] = 'Cat Plushie',
}) do
  local recipe = false

  if plushie_id == 'ch_prop_arcade_claw_plush_06n' then
    recipe = { ['paper'] = 1 }
  end

  registerFurnitureTokenItem(plushie_id, plushie_name, 0.2, {
    {
      name = 'Use',
      callback = function(character, item_id)
        character.animate({
          { 'impexp_int-0', 'mp_m_waremech_01_dual-0', 1 },
        }, true, true)

        local xOff = 0.0
        local zRot = 0.0

        if plushie_id == 'prop_bl_sexdoll' then
          zRot = -90.0
        end

        if plushie_id == 'h4_prop_h4_art_pant_01a' then
          xOff = -0.2
        end

        tProps.addPlayerProp(character.source, { item_id, 24817, xOff, 0.46, 0.0, -180.0, -90.0, zRot })
      end
    }
  }, recipe, {
    furniture_claim_idx = 2
  })
end

registerItem('prop_bl_piggybank', 'Piggy Bank', 0.2, {
  [1] = {
    name = 'Open',
    callback = function(character, item_id)
      character.hideInventory(true)

      SetTimeout(250, function()
        character.openChest(item_id, 0.0, false, {
          whitelisted_items = {
            'cash',
          }
        })
      end)
    end
  },
  [2] = {
    name = 'Label',
    callback = function(character, item_id, item_id_original, meta)
      character.hideInventory(true)

      local label = character.prompt('Enter label', false, true, 25)

      if not label or label == '' then
        character.notify('Invalid label')
        return
      end

      modifyItemMeta(character.source, item_id, 'storage_label', label)
      character.notify(GItems.getItemName(item_id) .. ' labelled')
    end
  },
  [3] = {
    name = 'Hold',
    callback = function(character, item_id)
      character.animate({
        { 'impexp_int-0', 'mp_m_waremech_01_dual-0', 1 },
      }, true, true)

      tProps.addPlayerProp(character.source, { 'prop_bl_piggybank', 24817, -0.1, 0.46, 0.0, -180.0, -90.0, 0.0 })
    end
  },
}, {}, {
  unstackable = true,
})

-- Assorted stuff

for token_item_id, token_item_name in pairs({
  -- BadCon 2022
  ['badcon_houseprop_01'] = 'Comic Poster A',
  ['badcon_houseprop_02'] = 'Comic Poster B',
  ['badcon_houseprop_03'] = 'Comic Poster C',
  ['badcon_houseprop_04'] = 'Comic Poster D',
  ['badcon_houseprop_05'] = 'Pokemon Poster',
  ['badcon_houseprop_06'] = 'Pokemon Poster 2',
  ['badcon_houseprop_07'] = 'DBZ Poster',
  ['badcon_houseprop_08'] = 'DBZ Poster 2',
  ['badcon_houseprop_09'] = 'Sailor Moon Poster',
  ['badcon_houseprop_10'] = 'Sailor Moon Poster 2',
  ['badcon_houseprop_11'] = 'Anime Poster',
  ['badcon_houseprop_12'] = 'Anime Poster 2',
  ['badcon_houseprop_13'] = 'Comic Poster E',
  ['badcon_houseprop_14'] = 'Comic Poster F',
  ['badcon_houseprop_15'] = 'Comic Poster G',
  ['badcon_houseprop_16'] = 'Comic Poster H',
  ['dragonball_set_houseprop'] = 'Dragonball Set',

  -- Halloween 2022
  ['prop_hween22_blackspider'] = 'Black Spider',
  ['prop_hween22_cauldronsmall'] = 'Small Cauldron',
  ['prop_hween22_cauldronbig'] = 'Big Cauldron',
  ['prop_hween22_cuteghost'] = 'Cute Ghost',
  ['prop_hween22_jackolantern'] = "Jack O' Lantern",

  -- Xmas 2022
  ['vw_prop_casino_art_lampf_01a'] = 'Female Artistic Lamp',
  ['vw_prop_casino_art_lampm_01a'] = 'Male Artistic Lamp',
  ['vw_prop_casino_art_bowling_01a'] = 'RSR Bowling Pin',
  ['vw_prop_casino_art_bowling_01b'] = 'Funky Art Bowling Pin',
  ['vw_prop_casino_art_bowling_02a'] = 'Eye See You Bowling Pin',
  ['vw_prop_casino_art_guitar_01a'] = 'Love Fist Electric Guitar',
  ['vw_prop_casino_art_deer_01a'] = 'Mounted Deer Head',
  ['vw_prop_casino_art_cherries_01a'] = 'Cherry Statue',
  ['vw_prop_casino_art_car_01a'] = 'Roosevelt Scale Model',
  ['vw_prop_casino_art_car_02a'] = 'Mamba Scale Model',
  ['vw_prop_casino_art_car_03a'] = 'Hermes Scale Model',
  ['vw_prop_casino_art_car_04a'] = 'Adder Scale Model',
  ['vw_prop_casino_art_car_05a'] = 'Z Type Scale Model',
  ['vw_prop_casino_art_car_06a'] = 'Cheetah Scale Model',
  ['vw_prop_casino_art_car_07a'] = 'Infernus Scale Model',
  ['vw_prop_casino_art_car_08a'] = 'Torismo Scale Model',
  ['vw_prop_casino_art_car_09a'] = 'Jester Scale Model',
  ['vw_prop_casino_art_car_10a'] = 'Deluxo Scale Model',
  ['vw_prop_casino_art_car_11a'] = 'X80 Proto Scale Model',
  ['vw_prop_casino_art_car_12a'] = 'JB 700 Scale Model',
  ['sf_prop_sf_art_car_01a'] = 'Taxi Scale Model',
  ['sf_prop_sf_art_car_02a'] = 'Euros Scale Model',
  ['sf_prop_sf_art_car_03a'] = 'Youga Classic Scale Model',
  ['sf_prop_sf_art_s_board_01a'] = 'Hanging Skateboards 1',
  ['sf_prop_sf_art_s_board_02a'] = 'Hanging Skateboards 2',
  ['sf_prop_sf_art_s_board_02b'] = 'Hanging Skateboards 3',
  ['vw_prop_casino_art_mod_06a'] = 'Up-N-Atom Enjoyer',
}) do
  registerFurnitureTokenItem(token_item_id, token_item_name, 1.0)
end

-- Cayo Perico 2022

registerFurnitureTokenItem('bl_h4_int_04_cannon1', 'Miniature Cannon', 0.3)
registerFurnitureTokenItem('bl_h4_int_04_drinks_globe', 'Free Standing World Globe', 5.0)
registerFurnitureTokenItem('bl_h4_prop_rug_panther', 'Panther Rug', 5.0)

for tequila_item_id, tequila_item_name in pairs({
  ['h4_prop_h4_t_bottle_02a'] = 'Cayo Perico Diamond Tequila',
  ['h4_prop_h4_t_bottle_02b'] = 'Cayo Perico Tequila',
}) do
  registerFurnitureTokenItem(tequila_item_id, tequila_item_name, 0.2, {
    {
      name = 'Drink',
      callback = function(character, item_id)
        if not I.doIfNotActionLocked(character) then
          return
        end

        if not character.take(item_id, 1, false) then
          return
        end

        tActionLocks.setTimeout(character.source, { 'physical', true, 3 })

        character.notify('Drinking ' .. GItems.getItemName(item_id))

        tSurvivalAlcohol.addIntoxication(character.source, { 15 })
        character.varyNeed('thirst', -60)

        local extra = {}

        I.effectDrink(character, extra.duration, extra.sequence, item_id, extra.bone, -0.2, extra.xRot, extra.zRot)
      end
    },
  }, false, {
    furniture_claim_idx = 2
  })
end

-- Misc

for tint_idx, color in pairs({
  [0] = 'Pink',
  [1] = 'Blue',
  [2] = 'Purple',
  [3] = 'Yellow',
  [4] = 'Black',
  [5] = 'Red',
}) do
  registerFurnitureTokenItem('prop_bl_plush_bunny_' .. tint_idx, color .. ' Bunny Plushie', 0.2, {
    {
      name = 'Use',
      callback = function(character, item_id)
        character.animate({
          { 'impexp_int-0', 'mp_m_waremech_01_dual-0', 1 },
        }, true, true)

        tProps.addPlayerProp(character.source, { 'prop_bl_plush_bunny', 24817, 0.0, 0.46, 0.0, -180.0, -90.0, 0.0, false, tint_idx })
      end
    }
  }, recipe, {
    furniture_claim_idx = 2
  })
end

endRegisterCategory()
