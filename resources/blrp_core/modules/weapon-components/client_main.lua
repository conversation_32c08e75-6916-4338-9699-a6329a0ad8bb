AddEventHandler('core:client:weapon-components:addEquippedMods', function(weapon_name, components)
  local current_weapon_hash = GetSelectedPedWeapon(PlayerPedId())

  local tint_component = nil

  for _, attached_component_name in pairs(components) do
    for component_name, component_data in pairs(config_weapon_components) do
      if string.match(attached_component_name, component_name) then
        if component_data.tint then
          tint_component = component_data
        else
          local component_hash = component_data.weapon_hashes[current_weapon_hash]

          if component_hash then
            GiveWeaponComponentToPed(PlayerPedId(), current_weapon_hash, component_hash)

            -- How to deal with skin that covers the body and slide by only using a single component
            if string.match(attached_component_name, 'sk') then
              local slide_skin_hash = component_data.weapon_hashes[GetHashKey(weapon_name .. '_SLIDE')]

              if slide_skin_hash then
                GiveWeaponComponentToPed(PlayerPedId(), current_weapon_hash, slide_skin_hash)
              end
            end
          end
        end
      end
    end
  end

  SetPedWeaponTintIndex(PlayerPedId(), current_weapon_hash, 0)

  if tint_component then
    if tint_component.weapons[weapon_name] then
      SetPedWeaponTintIndex(PlayerPedId(), current_weapon_hash, tint_component.tint)
    end
  end
end)
