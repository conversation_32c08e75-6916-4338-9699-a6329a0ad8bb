local current_id = 1

local activeJobs = {
  id, -- Job ID
  player, -- Player who's job it is (source)
  char_id,
  origin, -- Which trucking location the job originated
  destination, -- The destination of the job
  route, -- Which route the job is using
  ped_net_id, -- net id of the spawned ped
  paper_item_id, -- the meta string of the waybill
  trailer, -- net id of the trailer (for cleanup when logging out)
  trailerHash, -- hash of the trailer
  waybillGiven, -- tracks if waybill has been given on job (to avoid double ups)
}

local function hasActiveJob(location)
  local currentTime = GetGameTimer() -- Get the current game time
  local timeLimit = 10 * 60 * 1000 -- 10 minutes in milliseconds

  for job_id, job_data in pairs(activeJobs) do
    if job_data.origin == location then
      local jobTime = job_data.startTime
      if jobTime and currentTime - jobTime <= timeLimit then
        return job_id -- Job is active and above the time limit
      end
    end
  end

  return false -- No active job or all jobs are outside the time limit
end

local restrictedTruckers = {}

AddEventHandler('playerDropped', function()
  local character = core.character(source)
  local char_id = character.get('id')
  if restrictedTruckers[char_id] then
    character.log('TRUCKING JOB', 'Logged out while on a job timeout between jobs')
  end
end)
pTrucker = { }
P.bindInstance('trucker', pTrucker)

tTrucker = T.getInstance('blrp_core', 'trucker')

pTrucker.registerJobTimeout = function(char_id, time, toggle)
  local character = core.character(source)
  local originalTime = time
  time = time * 60000
  char_id = tonumber(char_id)

  if restrictedTruckers[char_id] == nil and toggle then
    restrictedTruckers[char_id] = {}
  end

  if toggle then
    character.log("TRUCKING JOB", tostring(originalTime).." minute delay until next job.")
    restrictedTruckers[char_id].endTime = GetGameTimer() + time
  else
    restrictedTruckers[char_id] = nil
  end
end

pTrucker.reassignJob = function(job_id)
  local player = activeJobs[job_id].player
  local character = core.character(activeJobs[job_id].player)
  tTrucker.clearTruckingJob(player, {}) -- client side cleanup
  pTrucker.cleanupTruckerJob(job_id, false, false, "job re-assigned") -- server side cleanup
  character.log("TRUCKING JOB", "Job "..job_id.." re-assigned due to job already taken.")

  Citizen.CreateThread(function()
    local foundAnotherDriver = {
      "Sorry Trucker, we found another driver for the job.",
      "Unfortunately, the job has been assigned to another driver.",
      "I'm afraid the driving assignment is no longer available, as we found another driver.",
      "Sorry, the job has been taken by another driver.",
      "Regrettably, we had to assign the job to another driver."
    }

    local randomResponse = foundAnotherDriver[math.random(1, #foundAnotherDriver)]
    local message = randomResponse .. " We'll reach out to you when another job comes up."
    TriggerEvent('gcPhone:sendMessage_Anonymous', '698-4929', character.get('phone'), message)
    character.set('truckerOfferedLoad', false)
    Citizen.Wait(10000)
    TriggerEvent('core:server:trucking:clockin', character.source)
  end)
end

pTrucker.loadDelay = function(job_id)
  local character = core.character(activeJobs[job_id].player)
  character.log("TRUCKING JOB", "Job "..job_id.." delayed due to job already taken.")

  local message = "Sorry mate, another load is in the way.  We'll get your trailer ready for you in no time."
  TriggerEvent('gcPhone:sendMessage_Anonymous', '698-4929', character.get('phone'), message)
end

pTrucker.isJobTaken = function(job_id)
  local character = core.character(source)
  local job_taken_id = false
  local trailerHasLeftSpawn = true  -- Default to true, meaning the site is clear.

  -- Check if another player has taken the job
  local job_taken_check = hasActiveJob(activeJobs[job_id].origin)
  if job_taken_check ~= false and activeJobs[job_taken_check].player ~= source then
    job_taken_id = job_taken_check  -- Store the job that is taken
  end

  local job = job_taken_id and activeJobs[job_taken_id]
  if job and job.trailerNetworkID and job.origin then
    local trailer = NetworkGetEntityFromNetworkId(job.trailerNetworkID)
    if DoesEntityExist(trailer) then
      local trailerCoords = GetEntityCoords(trailer)
      local spawnCoords = vector3(trucking_config.locations[job.origin].coords.x, trucking_config.locations[job.origin].coords.y, trucking_config.locations[job.origin].coords.z)
      -- Check if the trailer is still at the spawn location
      local isAtSpawn = #(trailerCoords - spawnCoords) < 5.0

      -- If the trailer is still at spawn, we set trailerHasLeftSpawn to false
      if isAtSpawn then
        trailerHasLeftSpawn = false
      end
    end
  end

  -- Return both values: job_taken_id (false if no job is taken) and whether the trailer has left the spawn.
  return job_taken_id, trailerHasLeftSpawn
end


function table.contains(table, value)
  for _, v in ipairs(table) do
    if v == value then
      return true
    end
  end
  return false
end


core.event("core:server:trucking:clockin", function(player)
  if not player then
    player = source
  end
  local character = core.character(player)
  local jobRestricted = tTrucker.isJobRestricted(player, {})
  local serverJobRestricted = restrictedTruckers[character.get('id')]

  if serverJobRestricted then
    local waitTime = serverJobRestricted.endTime - GetGameTimer()
    character.log('TRUCKING JOB', math.floor(waitTime / 60000) .. ' min wait time detected for ' .. character.get('identifier'))

    Citizen.CreateThread(function()
      if waitTime > 0 then
        Citizen.Wait(waitTime)
        restrictedTruckers[character.get('id')] = nil
        TriggerEvent('core:server:trucking:clockin', character.source)
      else
        restrictedTruckers[character.get('id')] = nil
        serverJobRestricted = nil
      end
    end)
  end

  if jobRestricted or serverJobRestricted then
    TriggerEvent('gcPhone:sendMessage_Anonymous', '698-4929', character.get('phone'), 'Hey trucker!   We\'ll be in touch with more work soon!')
    return
  end

  if not character.hasGroup('Truck Driver') then
    -- clock in check
    TriggerEvent('gcPhone:sendMessage_Anonymous', '698-4929', character.get('phone'), 'Hey champ!  We\'ve got some more work for you, head to a Job Centre and clock in if you\'re interested!')
    return
  end

  local foundJob = false
  for k, v in pairs(activeJobs) do
    if activeJobs[k].char_id == character.get('id') then
      if activeJobs[k].destination and activeJobs[k].trailerNetworkID then
        local trailer = NetworkGetEntityFromNetworkId(activeJobs[k].trailerNetworkID)
        if DoesEntityExist(trailer) and GetEntityModel(trailer) == activeJobs[k].trailerHash then
          local trailerCoords = GetEntityCoords(trailer)
          local playerCoords = GetEntityCoords(GetPlayerPed(source))
          if #(playerCoords - trailerCoords) < 100 then
            activeJobs[k].player = player
            local curJob = activeJobs[k]
            foundJob = true
            tTrucker.updateJob(player, { k,
                                        curJob.route,
                                        curJob.origin,
                                        curJob.destination,
                                        curJob.trailer,
                                        curJob.trailerHash, curJob.trailerNetworkID, true })
            TriggerEvent('core:server:trucking:setdestination', activeJobs[k].destination, k, activeJobs[k].route, player)
          else
            -- Clean up trucker job - Too far from trailer, likely clocked out and back in to fix an issue. 
            pTrucker.cleanupTruckerJob(k, false, false, "clocked out") -- server side cleanup.
          end
        end
      end

    end
  end

  if not foundJob then
    local randomOffer = {
      "Hey there Trucker! We've got a new load for you. Reply with 'Yes' if you're ready to take it!",
      "Greetings Truck Driver! A new load just arrived. Reply 'Yes' if you're ready to take it!",
      "Hello Trucker! Are you ready for a new load? Respond with 'Yes' if you are!",
      "Howdy there, Trucking champ! We just got a new load. Let us know if you're ready to take it by replying with 'Yes'!",
      "Attention Truck Driver! We have a new load ready for pickup. Respond with 'Yes' if you're ready to take it!"
    }
    local randomResponse = randomOffer[math.random(1, #randomOffer)]
    TriggerEvent('gcPhone:sendMessage_Anonymous', '698-4929', character.get('phone'), randomResponse)

    character.set('truckerOfferedLoad', true)
  end
end)

core.event('core:server:trucking:getjob', function(player, phone, message)
  if not player then
    player = source
  end
  local character = core.character(player)

  message = string.lower(message)

  if string.match(message, 'yes') or string.match(message, 'yeah') or string.match(message, 'yep') then
    if character.get('truckerOfferedLoad') == true then
      local dispatchPhrases = {
        "Cheers Mate, we'll dispatch you the job shortly!",
        "Thanks a lot, mate. The job is yours, and we'll dispatch you shortly!",
        "Cheers, buddy! You're the driver we need, and we'll send you to the job soon.",
        "Awesome news, mate! You're on the job, and we'll dispatch you shortly.",
        "Congrats, pal! You got the job, and we'll send you on your way shortly!",
        "Thank you, my friend! You're the one for the job, and we'll dispatch you in no time."
      }

      local randomResponse = dispatchPhrases[math.random(1, #dispatchPhrases)]
      TriggerEvent('core:server:trucking:assignjob', character.source)
      TriggerEvent('gcPhone:sendMessage_Anonymous', '698-4929', character.get('phone'), randomResponse)
      character.set('truckerOfferedLoad', false)
      return
    else
      local noWork = {
        "Sorry Trucker, I don't have any work for you at the moment",
        "Unfortunately, we don't have any work for you right now, sorry!",
        "I'm afraid there are no jobs for you at the moment, apologies",
        "Sorry, there are no driving assignments available for you currently",
        "Regrettably, we don't have any work for you at this time, sorry!"
      }
      local randomResponse = noWork[math.random(1, #noWork)]
      TriggerEvent('gcPhone:sendMessage_Anonymous', '698-4929', character.get('phone'), randomResponse)
    end
  end

end)

core.event('core:server:trucking:assignjob', function(player)
  if not player then
    player = source
  end
  local character = core.character(player)
  math.randomseed(GetGameTimer())

  local charLevel = character.getAptitudeLevel('trucking')
  local Routes = {}

  -- Create new table with routes based on character's trucking level
  for _, entry in ipairs(trucking_config.routes) do
    if entry.level <= charLevel then
        table.insert(Routes, entry)
    end
  end

  local route = Routes[math.random(1, #Routes)] -- Generate random route
  local origin_location = route.origins[math.random(1, #route.origins)]

  current_id = current_id + 1
  local job_id = current_id

  activeJobs[job_id] = {
    id = job_id,
    char_id = character.get('id'),
    player = player,
    origin = origin_location,
    route = route,
  }

  local curJob = activeJobs[job_id]
  tTrucker.updateJob(player, { job_id,
  curJob.route,
  curJob.origin,
  nil,
  nil,
  nil, nil, false })

  character.client('blrp_core:client:trucking:assignjob', origin_location, job_id)
  character.client('blrp_core:client:addPoliceAlert', {
    allow_gps = true,
    allow_phone = false,
    badge = 'Trucking Dispatch',
    badge_style = 'primary',
    bar_color = '#0B6EF6',
    blip_color = 5,
    blip_duration = 180,
    blip_id = 67,
    can_accept = false,
    hide_location = true,
    icon = 'fa-light fa-truck-container-empty',
    is_response = false,
    msg = 'Trailer ready for collection',
    pos = vector3(trucking_config.locations[origin_location].coords.x, trucking_config.locations[origin_location].coords.y, trucking_config.locations[origin_location].coords.x),
    show_for = 10000,
    sound = 'ToneP4',
    title = 'Dispatch Service',
    allow_dismiss = true,
  })
end)

core.event('core:server:trucking:setdestination', function(destination, job_id, route_id, player)
  if not player then
    player = source
  end
  local character = core.character(player)

  character.set('truckDestination', destination)
  activeJobs[job_id].destination = destination
  activeJobs[job_id].route = route_id
  character.client('blrp_core:client:addPoliceAlert', {
    allow_gps = true,
    allow_phone = false,
    badge = 'Trucking Dispatch',
    badge_style = 'primary',
    bar_color = '#0B6EF6',
    blip_color = 5,
    blip_duration = 180,
    blip_id = 479,
    can_accept = false,
    hide_location = true,
    icon = 'fa-regular fa-truck-container',
    is_response = false,
    msg = 'Collect the trailer and head to the destination!',
    pos = vector3(trucking_config.locations[destination].coords.x, trucking_config.locations[destination].coords.y, trucking_config.locations[destination].coords.x),
    show_for = 10000,
    sound = 'ToneP4',
    title = 'Dispatch Service',
    allow_dismiss = true,
  })
end)

function SpawnWaybillPed(locationID, job_id, character)
  local destination = trucking_config.locations[locationID]
  local ped_net_id = spawnPed(character.source, {
    ped_model = 's_m_m_dockwork_01',
    spawn_coords = vector3(destination.pedCoords.x, destination.pedCoords.y, destination.pedCoords.z),
    spawn_heading = destination.pedCoords.w,

    -- This does not currently sync with target restarts
    eye_interaction = {
      handler = function(_player, _ped, _net_id, _event_data)
        getWaybillFromPed(_player, _ped, _net_id)
      end
    }

  })

  Ped(ped_net_id).freeze(true)
  Ped(ped_net_id).set('trucker_job_id', job_id)

  activeJobs[job_id].ped_net_id = ped_net_id
  print("[TRUCKER] Spawning Ped for job:", job_id, 'Network ID:', ped_net_id, activeJobs[job_id].ped_net_id)
end

core.event('core:server:trucking:ArrivedAtJob', function(locationID, job_id)
  local character = core.character(activeJobs[job_id].player)
  if not character.get('truckDestination') == locationID then
    --print("truck destination isn\'t the same as client")
    return
  end

  SpawnWaybillPed(locationID, job_id, character)

end)

Citizen.CreateThread(function()
  -- ped loop
  while true do
    Citizen.Wait(10000)
    for job_id, job_data in pairs(activeJobs) do

      -- Time job out after defined time
      local currentTime = GetGameTimer() -- Get the current game time
      local timeLimit = 10 * 60 * 1000 -- 10 minutes in milliseconds
      local jobTime = activeJobs[job_id].startTime
      local trailer = NetworkGetEntityFromNetworkId(job_data.trailerNetworkID)
      local jobCoords = vector3(trucking_config.locations[job_data.origin].coords.x, trucking_config.locations[job_data.origin].coords.y, trucking_config.locations[job_data.origin].coords.z)
      local distanceFromJob = #(GetEntityCoords(trailer) - jobCoords)
      if jobTime and distanceFromJob > 10 then
        activeJobs[job_id].startTime = nil -- cancel timeout once job has left the spawn
      end
      if jobTime and currentTime - jobTime >= timeLimit then
        if distanceFromJob < 5 then
          local player = activeJobs[job_id].player
          local character = core.character(activeJobs[job_id].player)
          pTrucker.deletePed(job_id)
          tTrucker.clearTruckingJob(activeJobs[job_id].player, {}) -- client side cleanup
          pTrucker.cleanupTruckerJob(job_id, true, false, "non pickup") -- server side cleanup
          character.log("TRUCKING JOB", "Job timed out due to non pickup.")

          Citizen.CreateThread(function()
            Citizen.Wait(60000)
            TriggerEvent('core:server:trucking:clockin', player) -- give a new job
          end)
          return
        end
      end

      if activeJobs[job_id].ped_net_id and not activeJobs[job_id].waybillGiven then
        local ped = NetworkGetEntityFromNetworkId(activeJobs[job_id].ped_net_id)
        if not DoesEntityExist(ped) or GetEntityModel(ped) ~= `s_m_m_dockwork_01` then
          local character = core.character(activeJobs[job_id].player)
          if character then
            SpawnWaybillPed(activeJobs[job_id].origin, job_id, character)
            print("Respawning Waybill ped for Job ID:", job_id)
          else
            print("Attempted respawn Waybill ped, character not found for Job ID:", job_id)
          end

        end
        if DoesEntityExist(ped) and GetEntityModel(ped) == `s_m_m_dockwork_01` and not activeJobs[job_id].waybillGiven then
          Ped(activeJobs[job_id].ped_net_id).playAnim(activeJobs[job_id].player, "taxi_hail", "hail_taxi")
        end
      end
    end
  end
end)

function stringsplit(inputstr, sep)
  if sep == nil then
    sep = "%s"
  end
  local t = {}
  i = 1
  for str in string.gmatch(inputstr, "([^" .. sep .. "]+)") do
    t[i] = str
    i = i + 1
  end
  return t
end

AddEventHandler('playerDropped', function(reason)
  local _player = source
  for k, v in pairs(activeJobs) do
    if v.player == _player then
      if v.ped_net_id then
        --print("delete the ped", v.ped_net_id)
        Ped(v.ped_net_id).delete()
        v.ped_net_id = nil
      end
    end
  end
end)

function getWaybillFromPed(_player, _ped, _ped_net_id)
  local character = core.character(_player)

  local job_id = Ped(_ped_net_id).get('trucker_job_id')
  local job = activeJobs[job_id]

  if not job then
    character.notify('Do you know this person?')
    return
  end

  if not job.char_id == character.get('id') then
    character.notify('Do you know this person?')
    return
  end

  if job.waybillGiven then
    character.notify('They already gave you your waybill!')
    return
  end

  character.animate({ { 'mp_common', 'givetake1_a' } }, true)

  Ped(_ped_net_id).playAnim(_player, 'mp_common', 'givetake1_a')

  character.notify('The man gave you a Waybill noting what you are carrying!')
  local paper_id = tonumber('99' .. math.random(1000000, 9000000))
  --HTML Template for Waybill
  local shipperName = trucking_config.locations[activeJobs[job_id].origin].name
  local currentTime = os.time()

  -- Format the current time as a human-readable string
  local timeDateShipped = os.date("%B %d, %Y %I:%M:%S %p", currentTime)
  local receiverName = trucking_config.locations[activeJobs[job_id].destination].name
  local contentsDescription = activeJobs[job_id].route.commodities[1].name .. " manufactured by " .. activeJobs[job_id].route.commodities[1].manufacturer

  -- Define the HTML template with placeholders for the variable values
  local paper_content = [[
[nobg]<table style="border-collapse: collapse; margin: auto;">
<tr><td colspan="3" style="text-align: center; border: 1px solid black;"><h1>Pacific Shipping Solutions</h1></br><h6>We'll make your cargo feel like it's on a luxury cruise, minus the umbrella drinks</h6></td></tr>
<tr><td colspan="3" style="text-align: center; border: 1px solid black;"><h4>Waybill / Bill of Lading</h4></td></tr>
<tr><td colspan="3" style="text-align: center; border: 1px solid black;"><p>%s</p></td></tr>
<tr>
<td style="border: 1px solid black;">
<h3>Shipper's Information:</h3>
<table style="border-collapse: collapse;">
<tr><td><p>Shipper's Name:</p></td><td><p>%s</p></td></tr>
</table>
</td>
<td style="border: 1px solid black;">
<h3>Receiver's Information:</h3>
<table style="border-collapse: collapse;">
<tr><td><p>Receiver's Name:</p></td><td><p>%s</p></td></tr>
</table>
</td>
</tr>
<tr><td colspan="3" style="border: 1px solid black;"><h2>Description of Contents:</h2></td></tr>
<tr><td colspan="3" style="border: 1px solid black;"><p>%s</p></td></tr>
</table>
]]

  -- Use string.format to replace the placeholders with the variable values
  local htmlWithValues = string.format(paper_content, timeDateShipped, shipperName, receiverName, contentsDescription)

  activeJobs[job_id].paper_item_id = 'printed_paper:meta:' .. paper_id
  TriggerEvent('blrp_paper:server:givePaperDirectSafe', _player, paper_id, 'Job Waybill', htmlWithValues, 1)

  SetTimeout(1000, function()
    Ped(_ped_net_id).wanderAroundArea(_player, character.getCoordinates(), 12, 13, 12)
    _wait(1000 * 30)
    Ped(_ped_net_id).delete()
  end)

  job.waybillGiven = true

end

pTrucker.cleanupTruckerJob = function(job_id, text, give_new_job, called_from)
  if not text then
    text = false
  end
  if activeJobs[job_id] then
    local character = core.character(activeJobs[job_id].player)
    character.log("TRUCKING JOB", "Job cleaned up", { called_from = called_from or 'unknown' })
    local items = character.getAllItems()
    local waybillFound = false
    if text then
      TriggerEvent('gcPhone:sendMessage_Anonymous', '698-4929', character.get('phone'), "The boss isn't happy!  We'll let you know when there is more work.")
      character.varyAptitudeXp('trucking', -1000) -- lower reputation if a load is destroyed
    end


    if activeJobs[job_id].paper_item_id then
      for idname, props in pairs(items) do
        if string.find(idname, activeJobs[job_id].paper_item_id) then
          waybillFound = true
        end
      end
    end

    local trailer = NetworkGetEntityFromNetworkId(activeJobs[job_id].trailerNetworkID)
    if activeJobs[job_id].trailerNetworkID and trailer and DoesEntityExist(trailer) and GetEntityModel(trailer) == activeJobs[job_id].trailerHash then
      print("[TRUCKER] Deleting trailer for:", job_id, 'Network ID:', activeJobs[job_id].trailerNetworkID)
      DeleteEntity(trailer)
      tTrucker.clearTruckingJob(activeJobs[job_id].player, {}) -- client side cleanup
    end

    if give_new_job then
      local player_copy = activeJobs[job_id].player
      Citizen.CreateThread(function()
        Citizen.Wait(30000)
        TriggerEvent('core:server:trucking:clockin', player_copy) -- give a new job
      end)
    end

    if waybillFound then
      if character.request('Would you like to hand in your waybill?', 20) then
        character.take(activeJobs[job_id].paper_item_id, 1, true)
      end
    end
    if activeJobs[job_id].ped_net_id then
      Ped(activeJobs[job_id].ped_net_id).delete()
      activeJobs[job_id].ped_net_id = nil
    end
    activeJobs[job_id] = nil
  else
    local character = core.character(source)
    if character then
      character.log("TRUCKING JOB", "Job cleaned up", { called_from = called_from or 'unknown' })
      if text then
        TriggerEvent('gcPhone:sendMessage_Anonymous', '698-4929', character.get('phone'), "The boss isn't happy!  We'll let you know when there is more work.")
        character.varyAptitudeXp('trucking', -1000) -- lower reputation if a load is destroyed
      end
    end
  end
end

pTrucker.updateJob = function(job_id, route, origin, destination, trailer, trailerhash, net_id, trailerSpawned)
  activeJobs[job_id].route = route
  activeJobs[job_id].origin = origin
  activeJobs[job_id].destination = destination
  activeJobs[job_id].trailer = trailer
  activeJobs[job_id].trailerHash = trailerhash
  activeJobs[job_id].trailerNetworkID = net_id
  if trailerSpawned then
    activeJobs[job_id].startTime = GetGameTimer()
  end
end

pTrucker.deletePed = function(job_id)
  if not activeJobs[job_id] then
    return
  end

  if activeJobs[job_id].ped_net_id then
    Ped(activeJobs[job_id].ped_net_id).delete()
    activeJobs[job_id].ped_net_id = nil
  end
end

pTrucker.findJobFromTrailer = function(trailer_netid)
  local trailerFound = nil
  for jobID, jobData in pairs(activeJobs) do
    if jobData.trailerNetworkID == trailer_netid then
      trailerFound = jobID
    end
  end
  return trailerFound
end

pTrucker.returnJobData = function(jobs_id)
  if activeJobs[jobs_id] then
    return activeJobs[jobs_id]
  end
  return false
end

pTrucker.truckerJobSuccess = function(job_id)
  local character = core.character(source)

  local job = activeJobs[job_id]

  if not job then
    return
  end

  local jobCoords = trucking_config.locations[job.destination].coords.xyz

  local distance_from_coords = character.distanceFrom(jobCoords)
  local distance_between_jobs = #(trucking_config.locations[job.origin].coords.xyz - jobCoords)

  if distance_from_coords > 20 then
    character.log("TRUCKING JOB", "too far from destination to be paid")
    return
  end

  local truckingLevel = character.getAptitudeLevel('trucking')

  character.varyAptitudeXp('trucking', distance_between_jobs) -- add distance to varyAptitudeXp

  -- Clamp pre-bonus payout $0 - $4250 - Results in max payout of $6375 at max level
  local payout = math.clamp(math.ceil(distance_between_jobs * 0.65), 0, 4250)

  local modifiedPayout = math.ceil(payout * (1 + (truckingLevel / 20)))
  local bonus = modifiedPayout - payout
  local amount = modifiedPayout

  if character.get('has_felonies') then
    -- felon nerf reduction as trucker levels up
    if truckingLevel < 3 then
      amount = math.floor(amount * 0.70)
    elseif truckingLevel < 6 then
      amount = math.floor(amount * 0.75)
    elseif truckingLevel < 8 then
      amount = math.floor(amount * 0.80)
    elseif truckingLevel < 10 then
      amount = math.floor(amount * 0.85)
    elseif truckingLevel == 10 then
      amount = math.floor(amount * 0.92)
    end
  end

  local felonPenalty = modifiedPayout - amount

  character.giveBankMoney(amount)
  exports.blrp_banking:LogTransaction(character, 'Deposit', {
    account_from = character.get('personalbanknumber'),
    amount = amount,
    note = 'Trucking Payment'
  })
  character.notify("$" .. amount .. " was deposited into your bank for completing your delivery.", true)
  character.log('TRUCKING JOB', "Received $" .. amount .. " for completing trucker mission. Trucking Level: " .. truckingLevel .. " XP $ Bonus: $" .. bonus .. " Felon penalty: $" .. felonPenalty)
end

AddEventHandler('core:aptitudes:levelUp', function(player, aptitude, new_level, old_level, xp)
  if aptitude ~= 'trucking' then
    return
  end

  local character = exports.blrp_core:character(player)
  character.log('TRUCKING JOB', "Levelled up to " .. new_level .. " total xp: " .. xp)
  local levelMessage = trucking_config.messages[new_level] or trucking_config.messages[99]

  TriggerEvent('gcPhone:sendMessage_Anonymous', '698-4929', character.get('phone'), levelMessage)

end)

AddEventHandler('core:aptitudes:levelDown', function(player, aptitude, new_level, old_level, xp)
  if aptitude ~= 'trucking' then
    return
  end

  local character = exports.blrp_core:character(player)
  character.log('TRUCKING JOB', "Levelled down to " .. new_level .. " total xp: " .. xp)

  TriggerEvent('gcPhone:sendMessage_Anonymous', '698-4929', character.get('phone'), 'Hey trucker!  Some sloppy work from you recently. We\'re gonna have to cut your pay until you figure it out!')

end)

--[[
local tSurvival = T.getInstance('blrp_core', 'survival')
AddEventHandler('chatMessage', function(source, n, message)
    local args = stringsplit(message, " ")
    if (args[1] == "/gototrucker") then
        CancelEvent()
        if (args[2] ~= nil) then
            local location = tonumber(args[2])
            tSurvival.teleportCoords(source, {trucking_config.locations[location].coords, trucking_config.locations[location].coords.w})
        else
            local event = 'chatMessage'
            local eventTarget = source
            local messageSender = "SYSTEM"
            local messageSenderColor = {200, 0, 0}
            local message = "Usage: /gototrucker <location id>"
            TriggerClientEvent(event, eventTarget, messageSender, messageSenderColor, message)
        end
    end
end)]]
