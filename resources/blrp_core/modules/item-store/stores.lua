local is_dev = GlobalState.is_dev

--[[
  Item definition as follows:

  {
    'item_id', -- item ID
    50, -- price
    function(character, item_id) end, -- restrictor function, returns true/false based on some condition to show the item to the specific user
  }
]]

local vbmarket_service_charge = 0.02
local vbmarket_base_items = {
  { 'vb_sticker', 50 },
  { 'prop_coffeecup_vb', 250 },
  { 'vb_keychain', 100 },
  { 'prop_bl_piggybank', 1000 },
}

local vbmarket_gift_bags = {
  { 'giftbag_condolences', 1000 },
  { 'giftbag_getwell', 1000 },
  { 'giftbag_greybag', 1000 },
  { 'giftbag_white', 1000 },
  { 'giftbag_thankyou', 1000 },
  { 'giftbag_haveaniceday', 1000 },
  { 'giftbag_anniversary', 1000 },
  { 'giftbag_birthday', 1000 },
  { 'giftbag_christmas', 1000 },
  { 'giftbag_fb', 1000 },
  { 'giftbag_ty', 1000 },
  { 'giftbag_ch', 1000 },
  { 'giftbag_green', 1000 },
  { 'giftbag_lav', 1000 },
  { 'giftbag_fire', 1000 },
  { 'giftbag_fyellow', 1000 },
  { 'giftbag_hpink', 1000 },
  { 'giftbag_msmiley', 1000 },
  { 'giftbag_nlighting', 1000 },
  { 'giftbag_sadblue', 1000 },
  { 'giftbag_spark', 1000 },
}

local vbmarket_siphon_block = {
  ['vb_sticker'] = true,
  ['prop_coffeecup_vb'] = true,
  ['vb_keychain'] = true,
  ['prop_bl_piggybank'] = true,
}

local function cayoDoubloonRestrictor(stage)
  return function(character, item_id)
    return os.time() > GlobalState.c24q2_times[stage]
  end
end

local function cayoMuseumRestrictor(character, item_id)
  return (character.getUserData('c24quest:Museum:items', false, true) or {})[item_id]
end

local function cayoBadfestRestrictor()
  local time = os.time()

  return (time > **********) and (time < **********)
end

item_stores = {
  -- Cayo 2024
  -- 1 USD = 41 CPD
  {
    uid = 'cp24-bf24',
    name = 'Badfest 2024',
    items = {
      { 'bf24_ltr_a', 1000, cayoBadfestRestrictor },
      { 'bf24_patch_a', 750, cayoBadfestRestrictor },
      { 'bf24_patch_b', 750, cayoBadfestRestrictor },
      { 'bf24_patch_c', 750, cayoBadfestRestrictor },

      { 'clth_bf24_tank_a', 7000, cayoBadfestRestrictor },
      { 'clth_bf24_tank_b', 7000, cayoBadfestRestrictor },
      { 'clth_bf24_tank_c', 7000, cayoBadfestRestrictor },
      { 'clth_bf24_tank_d', 7000, cayoBadfestRestrictor },
      { 'clth_bf24_tank_e', 7000, cayoBadfestRestrictor },
      { 'clth_bf24_tank_f', 7000, cayoBadfestRestrictor },
      { 'clth_bf24_tank_g', 7000, cayoBadfestRestrictor },
      { 'clth_bf24_denim', 7000, cayoBadfestRestrictor },
      { 'clth_bf24_btn_c_a', 7000, cayoBadfestRestrictor },
      { 'clth_bf24_btn_c_b', 7000, cayoBadfestRestrictor },
      { 'clth_bf24_btn_o_a', 7000, cayoBadfestRestrictor },
      { 'clth_bf24_btn_o_b', 7000, cayoBadfestRestrictor },
    },
    allow_debit = true,
    siphon_perc = 1,
    siphon_accountNumber = 9018197,
  },

  {
    uid = 'cp24-q2-sell-doubloons',
    name = 'Cayo Doubloon Sales',
    items = {
      { 'prop_c24_googshellp', 100, cayoDoubloonRestrictor(2) }, -- Week 1 / 2
      { 'h4_prop_h4_t_bottle_01a', 100, cayoDoubloonRestrictor(3) }, -- Week 3 / 4
      { 'prop_c24_fishtrophy', 100, cayoDoubloonRestrictor(4) }, -- Week 5 / 6
      { 'clth_c24_shark', 100, cayoDoubloonRestrictor(5) }, -- Week 7 / 8
    },
    currency = 'c24_doubloon',
    allow_selling = true,
    selling_coefficient = 1.0,

    purchase_callback = function(player, store_uid, item_id, quantity, price, total_cost)
      core.character(player).setUserData('c24:completionist:' .. item_id, 1, true)
    end,
  },

  {
    uid = 'cp24-q2-sell',
    name = 'Cayo Artifact Sales',
    items = {
      { 'c24_doubloon', 33000 }, -- Gold doubloon

      -- Scuba rewards
      { 'c24_sf_a', 5000, cayoMuseumRestrictor }, -- Blue Starfish
      { 'c24_sf_b', 5000, cayoMuseumRestrictor }, -- Chunky Starfish
      { 'c24_sf_c', 5000, cayoMuseumRestrictor }, -- Starfish
      { 'c24_sd', 2500, cayoMuseumRestrictor }, -- Sand Dollar
      { 'c24_pg', 2500, cayoMuseumRestrictor }, -- Pewter Goblet
      { 'c24_kg', 2500, cayoMuseumRestrictor }, -- Keg O' Grog
      { 'c24_mb', 2500, cayoMuseumRestrictor }, -- Mysterious Bones

      -- Metal detecting rewards
      { 'c24_bc_a', 1200, cayoMuseumRestrictor }, -- A.M. Bottlecap
      { 'c24_bc_b', 1200, cayoMuseumRestrictor }, -- Dusche Gold Bottlecap
      { 'c24_bc_c', 1200, cayoMuseumRestrictor }, -- e-Cola Bottlecap
      { 'c24_bc_d', 1200, cayoMuseumRestrictor }, -- Logger Bottlecap
      { 'c24_bc_e', 1200, cayoMuseumRestrictor }, -- Orang-O-Tang Bottlecap
      { 'c24_bc_f', 1200, cayoMuseumRestrictor }, -- Patriot Bottlecap
      { 'c24_bc_g', 1200, cayoMuseumRestrictor }, -- Pisswasser Bottlecap
      { 'c24_bc_h', 1200, cayoMuseumRestrictor }, -- Sprunk Bottlecap
      { 'c24_bc_i', 1200, cayoMuseumRestrictor }, -- Stronzo Bottlecap
      { 'c24_cb', 5000, cayoMuseumRestrictor }, -- Cannonball
    },
    currency = 'cash_peso',
    allow_selling = true,
    selling_coefficient = 1.0,
  },

  {
    uid = 'c24-blath',
    name = 'Blath Alain Supplies',
    items = {
			{ 'c24_icepack', 2638 },
      { 'clth_lanyard_bacv', 1000 },
      { 'clth_lanyard_bacm', 1000 },
    },
    currency = 'cash_peso',
  },

  {
    uid = 'cp24-general',
    name = 'Cayo General Store',
    items = {
      { 'wbody|WEAPON_KNIFE', 32000 },
			{ 'shark_repelent', 2500 },
		  { 'scuba_kit', 61500 },
      { 'carrepairkit', 9300 },
      { 'c24_kc_a', 22550 },
      { 'c24_kc_b', 22550 },
      { 'c24_ltr_a', 61500 },
      { 'food_pineapple', 7100 },
      { 'food_lulo', 350 },
      { 'food_cacay', 40 },
      { 'food_cp_pepper', 1200 },
      { 'cp23_coffee', 3800 },
      { 'cp23_fresh', 10250 },
      { 'cp23_fan', 410 },
      { 'empty_petrolcan', 10269 },
      { 'fs_bonitostrips', 880 },
    },
    currency = 'cash_peso',
  },

  {
    uid = 'cp24-q3-flowers',
    name = 'Cayo Flower Store',
    items = {
      { 'prop_c24_flowers', 400000 },
    },
    currency = 'cash_peso',
  },

  {
    uid = 'cp24-kush',
    name = 'Cayo Kush Seed Store',
    items = {
      { 'c24_kush_seed', 33861 },
    },
    currency = 'cash_peso',
  },

  {
    uid = 'cp24-hydration',
    name = 'Epsilon Hydration Station',
    items = {
      { 'c24_epwater', 99 },
    },
    allow_debit = true,
  },

  {
    uid = 'cp24-q1-supplies',
    name = 'CP24Q1 Supply Store',
    items = {
      { 'c24_panth_rep', 2255 },
      { 'ammo_3006', 90, },
    },
    currency = 'cash_peso',
  },

  {
    uid = 'cp24-q1-pelts',
    name = 'CP24Q1 Pelt Store',
    items = {
      { 'bl_h4_prop_rug_panther', 20 },
    },
    currency = 'c24_panth_hide',
  },

  {
    uid = 'cp24-q1-fangs',
    name = 'CP24Q1 Fang Store',
    items = {
      { 'c24_kc_panth', 5 },
    },
    currency = 'c24_panth_fang',
  },
  -- /Cayo 2024

  -- Yankton 2024
  {
    uid = 'xm24-gen',
    name = 'Lawton General Store',
    items = {
      { 'hand_warmer', 250 },
      { 'carrepairkit', 150 },
      { 'tirerepairkit', 150 },
      { 'water', 40 },
      { 'coffee', 40 },
      { 'bandage', 25 },
      { 'wbody|WEAPON_METALDETECTOR', 2750 },
      { 'garden_shovel', 25 },
      { 'xm24_inst_gravy', 150 },
      { 'xm23_kc_bf', 750 },
      { 'xm23_flintsteel', 600 },
      { 'xm24_galwater', 450 },
      { 'clth_xm23_jbib_a', 4500 },
      { 'clth_xm23_jbib_b', 4500 },
      { 'clth_xm23_jbib_c', 4500 },
    },
  },

  {
    uid = 'xm24-liq',
    name = 'Lawton Liquor Store',
    items = {
      { 'beer', 40 },
      { 'vodka', 50 },
      { 'don_pereon', 50 },
      { 'food_spec_dankwhiskey', 50 },
      { 'food_spec_tequila', 50 },
      { 'crack_whiskey', 1250 },
      { 'cwwhiskey_mentholmint', 1750 },
      { 'xm24_whiskey_a', 12500 },
      { 'xm24_whiskey_b', 28500 },
      { 'xm24_whiskey_c', 64999 },
    },
  },

  {
    uid = 'xm24gal_1a',
    name = 'xmaxx Gallery - Display 1',
    items = {
      { 'vw_prop_art_wall_segment_02a', 650000 },
    },
  },

  {
    uid = 'xm24gal_1b',
    name = 'xmaxx Gallery - Display 1',
    items = {
      { 'vw_prop_casino_art_guitar_01a', 175000 },
      { '24_bl_xmsidetable', 9999 },
    },
  },

  {
    uid = 'xm24gal_1c',
    name = 'xmaxx Gallery - Display 1',
    items = {
      { 'vw_prop_casino_art_cherries_01a', 400000 },
      { 'vw_prop_casino_art_deer_01a', 100000 },
    },
  },

  {
    uid = 'xm24gal_2',
    name = 'xmaxx Gallery - Display 2',
    items = {
      { 'vw_prop_casino_art_mod_06a', 500000 },
    },
  },

  {
    uid = 'xm24gal_3a',
    name = 'xmaxx Gallery - Display 3',
    items = {
      { 'v_corp_facebeanbagc', 1 },
    },
    allow_selling = true,
    currency = 'xm24_furvouch',
  },

  {
    uid = 'xm24gal_3b',
    name = 'xmaxx Gallery - Display 3',
    items = {
      { 'v_corp_facebeanbagd', 1 },
      { 'prop_el_guitar_02', 1 },
    },
    allow_selling = true,
    currency = 'xm24_furvouch',
  },

  {
    uid = 'xm24gal_4',
    name = 'xmaxx Gallery - Display 4',
    items = {
      { '24_bl_xmcofftable', 5000 },
      { 'v_ret_ta_book1', 299998 },
      { 'vw_prop_book_stack_02c', 30000 },
    },
  },

  {
    uid = 'xm24gal_5',
    name = 'xmaxx Gallery - Display 5',
    items = {
      { '24_bl_xmsidetable2', 1 },
      { 'sf_prop_sf_art_pogo_01a', 4 },
    },
    allow_selling = true,
    currency = 'xm24_furvouch',
  },

  {
    uid = 'xm24gal_6',
    name = 'xmaxx Gallery - Display 6',
    items = {
      { 'vw_prop_casino_art_bowling_02a', 45750 },
      { 'vw_prop_casino_art_bowling_01b', 72500 },
      { 'vw_prop_casino_art_bowling_01a', 109885 },
      { 'vw_prop_casino_art_basketball_02a', 20000 },
      { 'vw_prop_vw_colle_sasquatch', 25000 },
      { 'sf_prop_sf_bong_01a', 65000 },
      { 'vw_prop_casino_art_car_04a', 75000 },
      { 'vw_prop_casino_art_car_06a', 69999 },
      { 'vw_prop_casino_art_car_09a', 122853 },
      { 'vw_prop_casino_art_car_10a', 49999 },
      { 'vw_prop_casino_art_car_11a', 102000 },
      { '24_bl_xmrug', 7500 },
    },
  },

  {
    uid = 'xm24gal_7',
    name = 'xmaxx Gallery - Display 7',
    items = {
      { 'sf_prop_sf_art_dog_01a', 2 },
      { 'sf_prop_sf_art_dog_01b', 2 },
      { 'sf_prop_sf_art_dog_01c', 2 },
      { 'xm3_prop_xm3_bong_01a', 1 },
      { 'vw_prop_casino_art_grenade_01d', 2 },
      { 'vw_prop_casino_art_grenade_01a', 3 },
      { 'vw_prop_casino_art_grenade_01b', 2 },
    },
    allow_selling = true,
    currency = 'xm24_furvouch',
  },

  {
    uid = 'xm24gal_8',
    name = 'xmaxx Gallery - Display 8',
    items = {
      { 'vw_prop_vw_wallart_67a', 3 },
      { 'vw_prop_vw_wallart_40a', 3 },
      { 'vw_prop_vw_wallart_94a', 1 },
      { 'vw_prop_vw_wallart_93a', 1 },
    },
    allow_selling = true,
    currency = 'xm24_furvouch',
  },

  {
    uid = 'xm24gal_9',
    name = 'xmaxx Gallery - Display 9',
    items = {
      { 'vw_prop_art_wall_segment_01a', 1500000 },
    },
  },

  {
    uid = 'xm24gal_10',
    name = 'xmaxx Gallery - Display 10',
    items = {
      { '24_bl_xmsketchpad', 4000 },
      { '24_bl_xmpmag', 10000 },
      { 'xs_prop_trinket_mug_01a', 5000 },
    },
  },

  {
    uid = 'xm24gal_11',
    name = 'xmaxx Gallery - Display 11',
    items = {
      { 'sf_prop_sf_art_s_board_01a', 150000 },
      { 'vw_prop_vw_wallart_34a', 60000 },
      { 'vw_prop_vw_wallart_32a', 95000 },
    },
  },

  {
    uid = 'xm24gal_12',
    name = 'xmaxx Gallery - Display 12',
    items = {
      { 'v_ret_ps_toiletbag', 5000 },
      { 'v_res_mbathpot', 5000 },
      { 'ex_mp_h_din_table_01', 40001},
    },
  },

  {
    uid = 'xm24gal_13',
    name = 'xmaxx Gallery - Display 13',
    items = {
      { 'vw_prop_casino_art_lampf_01a', 150000 },
      { 'vw_prop_casino_art_lampm_01a', 150000 },
      { '24_bl_xmcoffeetable', 12000 },
      { '24_bl_xmsofa_01', 6500 },
      { '24_bl_xmsofa_02', 6500 },
      { '24_bl_xmplant', 2500 },
      { 'h4_prop_int_plants_04', 750 },
    },
  },

  {
    uid = 'xm24vend1',
    name = 'Lawton Square Food Vendor',
    items = {
      { 'xm24_egg', 200 },
      { 'xm24_macncheese', 300 },
      { 'xm24_bruschetta', 250 },
      { 'xm24_challah', 300 },
      { 'xm24_empanada', 400 },
      { 'xm24_beancass', 300 },
      { 'xm24_cannedfood', 1000 },
      { 'xm24_tofu', 500 },
      { 'food_spec_poutine', 300 },
      { 'xm24_oatbev', 300 },
      { 'xm24_spritz', 350 },
      { 'xm24_sprunk_c', 250 },
      { 'xm24_coquito', 800 },
      { 'xm24_mmule', 600 },
    },
  },

  {
    uid = 'xm24vend2',
    name = 'Lawton Square Gingerbread Vendor',
    items = {
      { 'xm24_gingerbr_a', 400 },
      { 'xm24_gingerbr_b', 550 },
      { 'xm24_gingerbr_c', 300 },
      { 'xm24_gingerbr_d', 350 },
      { 'xm24_gingerbr_e', 350 },
      { 'xm24_gingerbr_f', 800 },
      { 'xm24_gingerbr_g', 450 },
    },
    allow_debit = true,
  },

  {
    uid = 'xm24vend3',
    name = 'Lawton Square Treat Vendor',
    items = {
      { 'xm24_strawsanta', 100 },
      { 'xm24_truffle', 250 },
      { 'xm24_lamington', 300 },
      { 'xm24_snowc', 149 },
      { 'xm24_xmcrack', 200 },
      { 'xm24_cranslime', 999 },
      { 'xm24_rainbowcookie', 400 },
      { 'xm24_banoffee', 350 },
    },
  },

  {
    uid = 'xm24pawn-sell',
    name = "Klepto's Pawn - Sell Junk",
    items = {
      { 'xm23_md_spike', 300 },
      { 'xm23_md_whistle', 200 },
      { 'xm23_md_fork', 300 },
      { 'xm23_md_tooth', 350 },
      { 'xm23_md_buckshot', 150 },
      { 'xm23_md_locket', 2000 },
      { 'xm23_md_iron', 700 },
      { 'xm23_md_medallion', 1000 },
      { 'cash_xm23_coin', 750 },
      { 'xmas2021_th_tankard', 550 },
      { 'xmas2021_th_silvercross', 1100 },
      { 'xmas2021_th_coin', 550 },
      { 'xm22g_drone', 2500 },
      { 'xm22g_pickled_eggs', 5000 },
      { 'xm23_shork', 20 },
    },
    selling_only = true,
    currency = 'cash_klepto',
  },

  {
    uid = 'xm24pawn-buy',
    name = "Klepto's Pawn - Buy Klepto",
    items = {
      { 'xm24_p_globe', 25000 },
      { 'prop_t_telescope_01b', 100000 },
      { 'prop_fragtest_cnst_06b', 65000 },
      { 'v_res_vacuum', 15500 },
      { 'xm24_p_moncounter', 19000 },
      { 'prop_boxing_glove_01', 22500 },
      { 'xm_prop_crates_weapon_mix_01a', 75000 },
      { 'xm3_prop_xm3_crate_ammo_01a', 75000 },
      { 'xm24_p_ammobox', 20000 },
      { 'prop_gun_case_01', 30000 },
    },
    currency = 'cash_klepto',
  },

  -- Camp Morningwood
  {
    uid = 'camp-247',
    name = "Lago Zancudo 24/7",
    items = {
      { 'lighter', 100 },
      { 'water', 60 },
      { 'lemonlimonad', 60 },
      { 'clam_shovel', 4500 },
      { 'camp_smorekit', 1500 },
      { 'camp_nicpouches', 600 },
      { 'food_organichoney', 80 },
      { 'rooting_knife', 1500 },
      { 'carrepairkit', 175 },
      { 'tirerepairkit', 175 },
      { 'bogberry_seed', 34 },
      { 'bandage', 25 },
      { 'cigarette', 15 },
      { 'pussycatmag', 5 },
      { 'pussycatmag2', 5 },
      { 'mead_barrel', 1000 },
    },
    siphon_tax = 0.08,
  },

  {
    uid = 'cm25-blath',
    name = 'Blath Alain Supplies',
    items = {
      { 'clth_lanyard_bacv', 1000 },
      { 'clth_lanyard_bacm', 1000 },
    },
    allow_debit = true,
  },

  -- Easter 2024
  {
    uid = 'ea25-bronze',
    name = 'Bronze Egg Store',
    items = {
      { 'kc_carrot_2', 1 },
      { 'ea25_ltr_a', 1 },
      { 'prop_bl_easterbasket_darkpink', 1 },
      { 'prop_bl_easterbasket_green', 1 },
      { 'prop_bl_easterbasket_lightblue', 1 },
      { 'prop_bl_easterbasket_lightgreen', 1 },
      { 'prop_bl_easterbasket_lightpurple', 1 },
      { 'prop_bl_easterbasket_orange', 1 },
      { 'prop_bl_easterbasket_pink', 1 },
      { 'prop_bl_easterbasket_purple', 1 },
      { 'prop_bl_easterbasket_red', 1 },
      { 'prop_bl_easterbasket_white', 1 },
      { 'prop_bl_easterbasket_yellow', 1 },
    },
    currency = 'prop_bl_easteregg_2025_bronze',
  },

  {
    uid = 'ea25-silver',
    name = 'Silver Egg Store',
    items = {
      { 'clth_ea25_feet_a', 2 },
      { 'clth_ea25_feet_b', 2 },
      { 'clth_ea25_feet_c', 2 },
      { 'clth_ea25_feet_d', 2 },
      { 'clth_ea25_feet_e', 2 },
      { 'clth_ea25_feet_f', 2 },
      { 'clth_ea25_feet_g', 2 },
      { 'clth_ea25_feet_h', 2 },
      { 'clth_ea25_feet_i', 2 },
      { 'clth_ea25_feet_j', 2 },
    },
    currency = 'prop_bl_easteregg_2025_silver',
  },

  {
    uid = 'ea25-gold',
    name = 'Gold Egg Store',
    items = {
      { 'prop_bl_ea25_ch_01', 2 },
      { 'prop_bl_ea25_ch_02', 2 },
      { 'prop_bl_ea25_ch_03', 2 },
      { 'prop_bl_ea25_ch_04', 2 },
      { 'prop_bl_ea25_ch_05', 2 },
      { 'prop_bl_ea25_ch_06', 2 },
      { 'prop_bl_ea25_ch_07', 2 },
      { 'prop_bl_ea25_ch_08', 2 },
      { 'prop_bl_ea25_ch_09', 2 },
      { 'prop_bl_ea25_ch_10', 2 },
      { 'prop_bl_ea25_ch_11', 2 },
      { 'prop_bl_ea25_ch_12', 2 },
      { 'prop_bl_ea25_ch_13', 2 },
      { 'prop_bl_ea25_ch_14', 2 },
      { 'prop_bl_ea25_ch_15', 2 },
      { 'prop_bl_ea25_ch_16', 2 },
      { 'prop_bl_ea25_ch_17', 2 },
      { 'prop_bl_ea25_ch_18', 2 },
      { 'prop_bl_ea25_ch_19', 2 },
      { 'prop_bl_ea25_ch_20', 2 },
      { 'prop_bl_ea25_ch_21', 2 },
      { 'prop_bl_ea25_ch_22', 2 },
      { 'prop_bl_ea25_ch_23', 2 },
      { 'prop_bl_ea25_ch_24', 2 },
    },
    currency = 'prop_bl_easteregg_2025_gold',
  },
  -- /Easter 2024

  {
    uid = 'casino-security',
    name = 'Casino Security',
    items = {
      { 'metal_detector', 1000 },
    },
  },

  {
    uid = 'salc-scratchers',
    name = 'Scratch Tickets',
    items = {
      { 'scratcher_hog', 250 },
      { 'scratcher_rg', 500 },
      { 'scratcher_lc', 750 },
      { 'scratcher_csf', 1000 },
      { 'scratcher_cb', 1500 },
    },

    allow_debit = true,
    siphon_tax = 0.08,

    siphon_account = 1000011,
    siphon_percent = 1,
    siphon_hidden = true,
  },

  {
    uid = 'weedseeds',
    name = 'Weed Seed Guy',
    items = {
      { 'seed_weed_a', 200 },
    },
  },

  {
    uid = 'badpix',
    name = 'BadPix',
    items = {
      { 'badpix_camera', 10270 },
      { 'badpix_film', 5000 },
      { 'photo_album', 500 },
    }
  },

  {
    uid = '247-all',
    name = '24/7 Market',
    items = {
      -- Utility items
      { 'lighter', 100 },
      { 'personal_phone', 10 },
      { 'hand_radio', 250 },
      { 'tablet', 10 },

      -- Drinks

      { 'water', 40 },
      { 'coffee', 40 },
      { 'icetea', 40 },
      { 'lemonlimonad', 40 },
      { 'inv_pack_mini_1', 100 },

      -- Food
      { 'scooby_snacks', 5 },
      { 'pdonut', 50 },
      { 'sandwich', 40 },
      { 'taco_kit', 25 },

      -- Misc
      { 'cigarette', 20 },
      { 'carrepairkit', 150 },
      { 'key_chain', 5 },
      { 'pussycatmag', 5 },
      { 'pussycatmag2', 5 },
      { 'xxscondom', 10 },
    },

    allow_debit = true,
    siphon_tax = 0.08,
  },

  {
    uid = 'liquor-all',
    name = 'Liquor Store',
    items = {
      { 'beer', 40 },
      { 'vodka', 50 },
      { 'don_pereon', 50 },
      { 'food_spec_dankwhiskey', 50 },
      { 'food_spec_tequila', 50 },
      { 'food_jaeger', 50, },
    },
    allow_debit = true,
    siphon_tax = 0.08,
  },

  {
    uid = 'liquor-gsl',
    name = 'Liquor Store GSL',
    items = {
      { 'food_mexicanbeer', 40, },
      { 'food_purp45', 40, },
      { 'beer', 40 },
      { 'vodka', 50 },
      { 'don_pereon', 50 },
      { 'food_spec_dankwhiskey', 50 },
      { 'food_spec_tequila', 50 },
      { 'food_jaeger', 50, },
    },
    allow_debit = true,
    siphon_tax = 0.08,
  },

  {
    uid = 'digi-all',
		name = 'Digital Den',
    items = {
      { 'personal_phone', 600 },
      { 'personal_phone_red', 600 },
      { 'personal_phone_orange', 600 },
      { 'personal_phone_yellow', 600 },
      { 'personal_phone_green', 600 },
      { 'personal_phone_darkgreen', 600 },
      { 'personal_phone_teal', 600 },
      { 'personal_phone_blue', 600 },
      { 'personal_phone_purple', 600 },
      { 'personal_phone_pink', 600 },
      { 'personal_phone_brown', 600 },
      { 'tablet', 10 },
      { 'hand_radio', 250 },
      { 'hand_gps', 100 },
      { 'compass', 1000 },
      { 'prop_bl_terminal', 1000 },
      { 'car_radio', 7500 },
      { 'digiden_wire_harness', 6000 },
      { 'wired_earbuds', 100 },
      { 'prepaid_simcard', 1000},
      { 'wired_headphones', 125 },
      { 'wireless_airbuds', 500 },
      { 'hdmi_cable', 250 },
      { 'gold_hdmi_cable', 2500 },
      { 'bluetooth_hose', 1250 },
    },
    purchase_limit_per_store = {
      ['car_radio'] = 1
    },
    allow_debit = true,
    siphon_tax = 0.08,
	},

  {
    uid = 'digi-paleto',
		name = 'Digital Den Paleto Bay',
    items = {
      { 'personal_phone', 600 },
      { 'personal_phone_red', 600 },
      { 'personal_phone_orange', 600 },
      { 'personal_phone_yellow', 600 },
      { 'personal_phone_green', 600 },
      { 'personal_phone_darkgreen', 600 },
      { 'personal_phone_teal', 600 },
      { 'personal_phone_blue', 600 },
      { 'personal_phone_purple', 600 },
      { 'personal_phone_pink', 600 },
      { 'personal_phone_brown', 600 },
      { 'tablet', 10 },
      { 'hand_radio', 250 },
      { 'hand_gps', 100 },
      { 'prop_bl_terminal', 1000 },
      { 'car_radio', 7500 },
      { 'digiden_wire_harness', 6000 },
      { 'wired_earbuds', 100 },
      { 'prepaid_simcard', 1000},
      { 'wired_headphones', 125 },
      { 'wireless_airbuds', 500 },
      { 'hdmi_cable', 250 },
      { 'gold_hdmi_cable', 2500 },
      { 'usb_magstripe', 1250 },
      { 'bluetooth_hose', 1250 },
    },
    purchase_limit_per_store = {
      ['car_radio'] = 1
    },
    allow_debit = true,
    siphon_tax = 0.08,
	},

  {
    uid = 'digi-vb',
		name = 'Digital Den Vespucci',
    items = {
      { 'personal_phone', 600 },
      { 'personal_phone_red', 600 },
      { 'personal_phone_orange', 600 },
      { 'personal_phone_yellow', 600 },
      { 'personal_phone_green', 600 },
      { 'personal_phone_darkgreen', 600 },
      { 'personal_phone_teal', 600 },
      { 'personal_phone_blue', 600 },
      { 'personal_phone_purple', 600 },
      { 'personal_phone_pink', 600 },
      { 'personal_phone_brown', 600 },
      { 'laptop_green', 5000 },
      { 'laptop_blue', 5000 },
      { 'laptop_violet', 5000 },
      { 'laptop_yellow', 5000 },
      { 'laptop_black', 5000 },
      { 'laptop_red', 5000 },
      { 'tablet', 10 },
      { 'hand_radio', 250 },
      { 'hand_gps', 100 },
      { 'prop_bl_terminal', 1000 },
      { 'car_radio', 7500 },
      { 'digiden_wire_harness', 6000 },
      { 'wired_earbuds', 100 },
      { 'prepaid_simcard', 1000},
    },
    purchase_limit_per_store = {
      ['car_radio'] = 1
    },
    allow_debit = true,
    siphon_tax = 0.08,
	},

  {
    uid = 'prison-electronics',
    name = 'Bolingbroke Communication Store',
    items = {
      { 'hand_radio', 250 },
      { 'personal_phone', 10 },
      { 'tablet', 10 },
    },
    siphon_tax = 0.08,
  },

  {
    uid = 'grapeseed-market',
    name = 'Grapeseed Market',
    items = {
      { 'jute_seed', 40 },
      { 'tomato_seed', 2 },
      { 'corn_seed', 2 },
      { 'plant_pot', 50 },
    },

    purchase_limit_per_store = {
      ['tomato_seed'] = 45,
      ['corn_seed'] = 45,
    },
    siphon_tax = 0.08,
  },

  {
    uid = 'legion-coffee',
    name = 'Legion Square Coffee',
    items = {
      { 'pdonut', 45 },
      { 'coffee', 35 },
    },
    siphon_tax = 0.08,
  },

  {
    uid = 'prison-cafeteria',
    name = 'Prison Cafeteria',
    items = {
      { 'bad_meat', 0 },
  		{ 'malk', 0 },
  		{ 'water_prison', 45 },
  		{ 'coffee', 40 },
  		{ 'icetea', 40 },
  		{ 'lemonlimonad', 40 },
  		{ 'food_spec_ramen', 1 },
  		{ 'food_spec_vegsoup', 1 },
  		{ 'food_spec_nuts', 1 },
  		{ '12plytoiletpaper', 1 },
  		{ 'scooby_snacks', 5 },
  		{ 'pdonut', 50 },
  		{ 'sandwich', 40 },
  		{ 'vicodin', 75 },
  		{ 'bandage', 25 },
  		{ 'gauze', 50 },
  		{ 'cigarette', 20 },
      { 'bread', 10 },
      { 'shampoo', 20 },
    },
  },

  {
    uid = 'canteen-lspd',
		name = 'LSPD Canteen',
		items = {
      { 'coffee', 40 },
      { 'food_meatballs', 120 },
      { 'food_meat_pie', 120 },
      { 'food_hot_fries', 120 },
      { 'food_spec_veganburger', 50 },
      { 'water', 40 },
      { 'food_spec_fruitsalad', 50 },
      { 'food_spec_trailmix', 50 },
      { 'food_spec_chxsalad', 50 },
      { 'drink_beetshot', 150 },
      { 'food_grilledcheese', 120 },
      { 'food_croissant', 150 },
      { 'powerade_blue', 40 },
      { 'food_pepperminthotchocolate', 40 },
      { 'food_brocolicheddarsoup', 40 },
		},
		allow_debit = true,
	},

  {
    uid = 'prison-snacks',
		name = 'Police Snacks',
		items = {
      { 'jar_of_ranch', 40 },
      { 'uncrustaburger', 120 },
      { 'corn_nuggets', 120 },
      { 'black_cherry_slush', 80 },
      { 'dog_treats', 100},
		},
		allow_debit = true,
	},

  {
    uid = 'leo-lspd-supervisor',
    name = 'LSPD Supervisor Shop',
    permission = 'lspd.supervisor',
    items = {
      { 'sticker_lspd', 100 },
      { 'det_cord', 11000 },
      { 'wbody|WEAPON_GRENADELAUNCHER_SMOKE', 5000 },
      { 'ammo_gren_smoke', 1000 },
      { 'wbody|WEAPON_BEANBAG', 10000 },
      { 'wammo|WEAPON_BEANBAG', 100 },
      { 'drugkit', 50 },
      { 'pd_radio_tool', 75 },
      { 'p_cone_blue', 50 },
      { 'p_cone_red', 50 },
      { 'academy_gear_bag', 5000 },
    },
    allow_debit = true,
  },

  {
    uid = 'leo-bcso-supervisor',
    name = 'BCSO Supervisor Shop',
    permission = 'sheriff.supervisor',
    items = {
      { 'sticker_bcso', 100 },
      { 'det_cord', 11000 },
      { 'wbody|WEAPON_SPECIALCARBINE', 20000 },
      { 'wbody|WEAPON_REVOLVER_MK2', 10000 },
      { 'wbody|WEAPON_BEANBAG', 10000 },
      { 'wammo|WEAPON_BEANBAG', 100 },
      { 'sprunk_towel', 100 },
      { 'wbody|WEAPON_GRENADELAUNCHER_SMOKE', 5000 },
      { 'ammo_gren_smoke', 1000 },
      { 'drugkit', 50 },
      { 'p_cone_blue', 50 },
      { 'p_cone_red', 50 },
      { 'academy_gear_bag', 5000 },
      { 'corn_supp', 15 },
    },
    allow_debit = true,
  },

  {
    uid = 'leo-doc-supervisor',
    name = 'DOC Supervisor Shop',
    permission = 'doc.supervisor',
    items = {
      { 'wbody|WEAPON_BEANBAG', 10000 },
      { 'wammo|WEAPON_BEANBAG', 100 },
      { 'yoga_mat_doc', 10 },
    },
    allow_debit = true,
  },

  {
    uid = 'pharmacy-odeas',
		name = "O'deas Pharmacy",
		items = {
      { 'bandage', 25 },
      { 'vicodin', 75 },
      { 'gauze', 50 },
      { 'nitrile_gloves', 150 },
      { 'ammonia', 100 },
      { 'kq_acetone', 25 },
      { 'boiling_flask', 200 },
      { 'wax_paper',200},
      { 'sulfuric_acid', 200 },
      { 'hydrobromic_acid', 10},
      { 'pthalein', 10},
      { 'd_water', 10 },
      { 'wbody|WEAPON_FLASHLIGHT_UV', 1500 },
      { 'lemon_oil', 150, },
      { 'linseed_oil', 150, },
    },
    siphon_tax = 0.08,
	},

  {
    uid = 'pharmacy-all',
		name = 'Dollar Pills',
		items = {
      { 'birthdaycard', 120 },
      { 'flowers', 80 },
      { 'getwellcard', 100 },
      { 'roses', 120 },
      { 'Teddybear', 100 },
      { 'tissuebox', 100 },
      { 'bandage', 25 },
      { 'vicodin', 75 },
      { 'gauze', 50 },
      { 'paper', 2 },
      { 'pill_binder', 2 },
      { 'pill_capsule', 2 },
      { 'baggy', 2 },
      { 'empty_vial', 2 },
      { 'candle_blue', 10 },
      { 'candle_red', 10 },
      { 'candle_green', 10 },
      { 'candle_yellow', 10 },
      { 'sudafed', 200 },
      { 'nitrile_gloves', 150 },
      { 'makeup_brush', 100 },
      { 'makeup_palette', 250 },
      { 'makeup_wipes', 150 },
      { 'medical_thermometer', 250 },
    },
    purchase_limit_per_store = {
      ['sudafed'] = 1
    },
    siphon_tax = 0.08,
	},

  {
    uid = 'casino-exchange',
		name = 'Diamond Casino Chip Exchange',
		items = {
			{ 'poker_chip_dummy', 1 },
		},
    allow_selling = true,
    selling_coefficient = 1.0,
	},

  {
    uid = 'casino-shop',
    name = 'Diamond Casino Store',
    items = {
      { 'card_deck', 2000 },
    },
  },

  {
    uid = 'casino-cards',
    name = 'Diamond Casino Cards',
    items = {
      { 'card_deck_dc', 1 },
    },
  },

  {
    uid = 'casino-employees',
    name = 'Diamond Casino Employees',
    permission = 'casino.employee',
    items = {
      { 'dice_blue', 10 },
      { 'dice_red', 10 },
      { 'cigar_casino', 10 },
      { 'champagne_casino', 100 },
      { 'chip_golden', 1000 },
      { 'chip_diamond', 1000 },
      { 'whiskey_casino', 10 },
      { 'drink_kraken_rum', 10 },
      { 'drink_casino_champagne', 50 },
      { 'drink_sapphire_moon', 50 },
      { 'drink_seltzer_water', 50 },
      { 'drink_ice_coffee', 50 },
      { 'drink_strawberry_water', 50 },
      { 'food_shrimp_cocktail', 50 },
      { 'drink_royal_martini', 50 },
      { 'food_c_strawberries', 25 },
      { 'food_c_pretzel', 50 },
      { 'food_salad', 50 },
      { 'food_Brisket', 100 },
    },
  },

  {
    uid = 'obsidian-supplies',
    name = 'Obsidian Tattoos Supplies',
    permission = 'obsidian.employee',
    items = {
      { 'tattoo_gun', 250 },
      { 'tattoo_ink', 250 },
			{ 'wayward_bear', 20 },
			{ 'wayward_sticker', 20 },
			{ 'wayward_tattoo_cream', 20 },
      { 'ww_ltr_a', 1 },
      { 'ww_kc_a', 1 },
    },
  },

  {
    uid = 'toilet-supplies',
    name = 'Toilet Supplies',
		permission = 'dumppump.employee',

    items = {
      { 'prop_portaloo', 1000 },
      { '0plytoiletpaper', 10 },
      { 'toilet_seal', 100 },
      { 'dnp_lighter', 10 },
      { 'dnp_keychain', 10 },
    },
    allow_debit = true,
  },

  {
    uid = 'd8-supplies',
    name = 'D8 Supplies',
		permission = 'dynasty8.employee',

    items = {
      { 'd8_keychain', 10 },
    },
    allow_debit = true,
  },

  {
    uid = 'chemical-gloves',
    name = 'Chemical Gloves',
    items = {
      { 'chemical_gloves', 15000 },
    },
  },

  {
    uid = 'canteen-dbsec',
		name = 'Downbad Security Canteen',
		items = {
      { 'coffee', 35 },
      { 'water', 35 },
  		{ 'redgull', 40 },
      { 'food_spec_veganburger', 50 },
      { 'food_spec_fruitsalad', 50 },
      { 'food_spec_plainnuts', 8 },
      { 'food_spec_veganbar', 15 },
      { 'food_spec_vegansal', 65 },
		},
		allow_debit = true,
	},

  {
    uid = 'ems-food',
    name = 'Hospital Food',
    permission = 'emergency.shop',

    items = {
      { 'donut', 40 },
  		{ 'kebab', 8 },
  		{ 'pdonut', 50 },
  		{ 'water', 40 },
  		{ 'sandwich', 40 },
  		{ 'coffee', 40 },
  		{ 'tea', 40 },
  		{ 'orangejuice', 40 },
  		{ 'icetea', 40 },
  		{ 'redgull', 40 },
  		{ 'lemonlimonad', 40 },
  		{ 'food_spec_lillipop', 15 },
      { 'food_greenjello', 10 },
      { 'food_tapiocapudding', 10 },
      { 'food_roastbeefsandwich', 10 },
      { 'food_chickennoodlesoup', 15 },
      { 'food_orangeslice', 10 },
      { 'food_eggnog', 60 },
      { 'food_applepie_slice', 60 },
      { 'food_holidaydinner', 100 },
    },
    allow_debit = true,
  },

  {
    uid = 'ems-drugs',
    name = 'LSFD Drug Supply',
    permission = 'emergency.shop',
    items = {
      { 'drugkit', 50 },
  		{ 'methylphenidate', 0 },
  		{ 'narconon', 0 },
  		{ 'lithium', 0 },
      { 'bupropion', 0},
      { 'lidocaine', 0},
      { 'labetalol', 0},
      { 'lofexidine', 0},
      { 'valium', 0},
      { 'naloxone', 0},
  		{ 'levacetylmethadol', 0 },
    },
  },

  {
    uid = 'ems-equipment',
    name = 'LSFD Equipment',
    permission = 'emergency.shop',
    items = {
      { 'ems_gear_bag', 10000 },
      { 'carrepairkit', 150 },
      { 'tirerepairkit', 150 },
      { 'coil_camera', 20 },
  		{ 'pills', 20 },
  		{ 'gauze', 50 },
  		{ 'bandage', 25 },
  		{ 'firstaid', 2 },
  		{ 'medkit', 2 },
  		{ 'vicodin', 75 },
  		{ 'hydrocodone', 2 },
  		{ 'morphine', 2 },
  		{ 'dressing', 2 },
  		{ 'suture', 2 },
  		{ 'syringe', 2 },
  		{ 'empty_vial', 2 },
  		{ 'lighter', 100 },
  		{ 'binoculars', 150 },
  		{ 'hand_radio', 250 },
  		{ 'hand_gps', 100 },
  		{ 'personal_phone', 10 },
      { 'tablet', 10 },
  		{ 'scalpel', 2 },
  		{ 'wheelchair_prop', 100 },
      { 'police_armor', 900 },
		  { 'scuba_kit_fr', 300 },
      { 'manilla_folder', 25 },
      { 'ashes_urn', 1000 },
      { 'p_cone', 150 },
      { 'sm_prop_smug_crate_m_medical', 5000 },
      { 'sm_prop_smug_crate_s_medical', 500 },
			{	'imp_prop_impexp_campbed_01', 100},
      { 'prop_barrier_work06', 500 },
      { 'spray_remover', 1250 },
      { 'nitrile_gloves', 150 },
			{ 'ziptie', 150 },
			{ 'lsfd_toy_helmet', 2 },
			{ 'lsfd_sticker_badge', 2 },
      { 'mobile_printer', 750 },
      { 'paper', 1 },
      { 'medical_thermometer', 250 },
      { 'rx_bag', 25 },
      { 'iv_tubing', 25 },
      { 'tpn_ivbag', 25 },

      { 'wbody|WEAPON_NIGHTSTICK', 100 },
      { 'wbody|WEAPON_FLASHLIGHT', 25 },
      { 'wbody|WEAPON_FIREEXTINGUISHER', 500 },
      { 'wbody|WEAPON_FLARE', 100 },
    },
    allow_debit = true,
    suppress_ux_check = true,
  },

  {
    uid = 'ems-supervisor',
    name = 'LSFD Supervisor Shop',
    permission = 'emergency.supervisor',
    items = {
      { 'wbody|WEAPON_STUNGUN', 1000 },
      { 'wammo|WEAPON_STUNGUN', 1 },
      { 'wbody|WEAPON_FLAREGUN', 800 },
      { 'ammo_flaregun', 1500 },
      { 'p_cone_red', 50 },
      { 'fire_axe', 5000 },
    },
    allow_debit = true,
  },

  {
    uid = 'ems-tmu',
    name = 'TMU Equipment',
    permission = 'emergency.shop',
    items = {
      { 'wbody|WEAPON_HEAVYPISTOL', 1800 },
      { 'ammo_45acp', 1 },
    },
  },

  {
    uid = 'zancudo-equipment',
    name = 'Zancudo Equipment',
    permission = 'fortzancudo.employee',
    items = {
      { 'vb_keychain', 0, },
      { 'wbody|WEAPON_KNIFE', 0 },
      { 'wbody|WEAPON_FLASHLIGHT', 0 },
      { 'wbody|WEAPON_FLASHLIGHT_UV', 0 },
      { 'wbody|WEAPON_HEAVYPISTOL', 0 },
      { 'ammo_45acp', 0},
      { 'wbody|WEAPON_TACTICALRIFLE', 0 },
      { 'wbody|WEAPON_PUMPSHOTGUN_MK2', 0 },
      { 'ammo_12ga', 0 },
      { 'ammo_556nato', 0},
  		{ 'police_armor', 0 },
      { 'ifak', 0 },
  		{ 'bandage', 0 },
  		{ 'gauze', 0 },
      { 'carrepairkit', 0 },
      { 'tirerepairkit', 0 },
  		{ 'binoculars', 0 },
  		{ 'hand_radio', 0 },
  		{ 'hand_gps', 0 },
      { 'compass', 0 },
      { 'ziptie', 0 },
		  { 'scuba_kit_fr', 0 },
      { 'component_flashlight', 0 },
      { 'tool_slimjim', 0 },
      { 'nitrile_gloves', 0 },
      { 'wbody|WEAPON_FLARE', 0 },
      { 'cm25_canteen', 0 },
      { 'cm25_paracord', 0 },
      { 'cm25_thread', 0 },
      { 'cm25_cloth', 0 },
      { 'clth_chain_mil_a', 0 },
    },
    allow_debit = true,
  },

  {
    uid = 'zancudo-equipment-cpt',
    name = 'Zancudo Captain Equipment',
    permission = { 'Fort Zancudo', 'extra_b' },
    items = {
      { 'wbody|WEAPON_MILSPECKNIFE', 0 },
      { 'wbody|WEAPON_MILITARYRIFLE', 0 },
      { 'ammo_556nato', 0},
      { 'wbody|WEAPON_HEAVYRIFLE', 0 },
      { 'ammo_762nato', 0},
      { 'wbody|WEAPON_COMBATMG', 0 },
      { 'wbody|WEAPON_GRENADELAUNCHER_SMOKE', 0 },
      { 'ammo_gren_smoke', 0},
      { 'wbody|WEAPON_REVOLVER_MK2', 0 },
      { 'ammo_500sw', 0},
      { 'wbody|WEAPON_GRENADE', 0 },
      { 'afak', 0 },
      { 'cm25_sublvlkey', 0 },
      { 'cm25_wristunit', 0 },
      { 'bzzz_mre_box_b', 0 },
    },
    allow_debit = true,
  },

  {
    uid = 'zancudo-equipment-mre',
    name = 'Zancudo MREs',
    permission = { 'Fort Zancudo', 'extra_a' },
    items = {
      { 'cm25_mre_a', 0 },
      { 'cm25_mre_b', 0 },
      { 'cm25_mre_c', 0},
      { 'cm25_mre_d', 0 },
      { 'cm25_mre_e', 0 },
      { 'cm25_mre_f', 0},
      { 'cm25_mre_g', 0 },
      { 'cm25_mre_h', 0},
      { 'cm25_mre_i', 0 },
      { 'cm25_mre_j', 0 },
      { 'cm25_mre_d_a', 0 },
      { 'cm25_mre_d_b', 0 },
      { 'cm25_mre_d_c', 0 },
      { 'cm25_mre_d_d', 0 },
    },
    allow_debit = true,
  },

  {
    uid = 'leo-equipment',
    name = 'LEO Equipment',
    permission = 'police.shop',
    items = {
      { 'police_gear_bag', 25000 },
      { 'police_regear_bag', 25000 },
      -- Weapons, guns, and associated ammo
      { 'wbody|WEAPON_NIGHTSTICK', 100 },
      { 'wbody|WEAPON_FLASHLIGHT', 750 },
      { 'wbody|WEAPON_FLASHLIGHT_UV', 1500 },
      { 'wbody|WEAPON_STUNGUN', 1000 },
      { 'wammo|WEAPON_STUNGUN', 1 },
      { 'wbody|WEAPON_COMBATPISTOL', 1250 },
      { 'ammo_40sw', 1 },
      { 'wbody|WEAPON_SERVICEPISTOL_9MM', 1250 },
      { 'ammo_9mm_nato', 1 },
      { 'wbody|WEAPON_HEAVYPISTOL', 1800 },
      { 'ammo_45acp', 1 },
      { 'wbody|WEAPON_PUMPSHOTGUN_MK2', 2500 },
      { 'wbody|WEAPON_PUMPSHOTGUN2', 2500 },
      { 'ammo_12ga', 1 },
      { 'wbody|WEAPON_SMG', 7500 },
      { 'wbody|WEAPON_UMP45', 7500 },
      { 'wbody|WEAPON_TACTICALRIFLE', 15000 },
      { 'wbody|WEAPON_CARBINERIFLE', 15000 },
     -- { 'wbody|WEAPON_CARBINERIFLE_MK2', 25000 },
      { 'ammo_556nato', 1 },
      { 'ammo_762nato', 1 },
      { 'ammo_500sw', 1 },
      { 'wbody|WEAPON_FIREEXTINGUISHER', 500 },
      { 'wbody|WEAPON_KNIFE', 250 },

      -- Medical supplies, armor
  		{ 'police_armor', 900 },
      { 'ifak', 500 },
  		{ 'bandage', 25 },
  		{ 'gauze', 50 },

      -- Training Weapons
      { 'wbody|WEAPON_CARBINERIFLET', 10000 },
      { 'wbody|WEAPON_CARBINERIFLEMK2T', 10000 },
      { 'ammo_rubberb', 1 },

      -- Everything else
      { 'carrepairkit', 150 },
      { 'tirerepairkit', 150 },
  		{ 'coil_camera', 20 },
  		{ 'binoculars', 150 },
  		{ 'hand_radio', 250 },
  		{ 'hand_gps', 100 },
      { 'compass', 1000 },
  		{ 'gsr_kit', 10 },
  		{ 'empty_vial', 2 },
      { 'p_evidence_marker', 10 },
      { 'metal_detector', 1000 },
		  { 'scuba_kit_fr', 300 },
      { 'spikestrip', 5000 },
      { 'component_flashlight', 300 },
      { 'manilla_folder_classified', 50 },
      { 'p_cone', 150 },
      { 'tool_slimjim', 1000 },
      { 'prop_barrier_work05', 500 },
      { 'spray_remover', 1250 },
      { 'finger_scanner', 5000 },
      { 'tablet', 10 },
      { 'nitrile_gloves', 150 },
      { 'reagent_tester', 25 },
      { 'breathalyzer', 2500 },
      { 'wbody|WEAPON_FLARE', 100 },
      { 'wbody|WEAPON_SPEEDGUN', 5000 },
      { 'mobile_printer', 750 },
      { 'paper', 1 },
      { 'bzzz_world_of_lamps_uv', 7500 },
    },
    allow_debit = true,
    suppress_ux_check = true,
  },

  {
    uid = 'ranger-equipment',
    name = 'Ranger Equipment',
    permission = 'ranger.shop',
    items = {
      -- Weapons, guns, and associated ammo
      { 'wbody|WEAPON_FIREEXTINGUISHER', 500 },
      { 'wbody|WEAPON_KNIFE', 250 },
      { 'wbody|WEAPON_FLASHLIGHT', 750 },

      -- Medical supplies, armor
  		{ 'bandage', 25 },
  		{ 'gauze', 50 },

      -- Everything else
      { 'carrepairkit', 150 },
      { 'tirerepairkit', 150 },
  		{ 'binoculars', 150 },
  		{ 'hand_radio', 250 },
  		{ 'hand_gps', 100 },
		  { 'scuba_kit_fr', 300 },
      { 'tablet', 10 },
    },
    allow_debit = true,
    suppress_ux_check = true,
  },

  {
    uid = 'supervisor-ranger-shop',
    name = 'Ranger Supervisor Shop',
    permission = { 'LEO Ranger', 'extra_a' },
    items = {
      -- Special ranger props
      { 'prop_ranger_tent', 10000 }
    },
    allow_debit = true,
    suppress_ux_check = true,
  },

  {
    uid = 'leo-tints',
    name = 'LEO Tints',
    permission = 'police.shop',
    items = {
      { 'wtint_sp_0', 250 },
      --{ 'wtint_sp_2', 250 }, -- Gold Skin
      { 'wtint_sp_3', 250 },
      { 'wtint_sp_4', 250 },
      { 'wtint_sp_5', 250 },
      { 'wtint_sp_7', 250 },
      { 'wtint_sp_9', 250 },
      { 'wtint_sp_10', 250 },
      { 'wtint_sp_11', 250 },
      { 'wtint_sp_12', 250 },
      { 'wtint_sp_14', 250 },
      { 'wtint_sp_16', 250 },
      { 'wtint_sp_17', 250 },
      { 'wtint_sp_18', 250 },
      { 'wtint_sp_19', 250 },
    },
    allow_debit = true,
    suppress_ux_check = true,
  },

  {
    uid = 'leo-nysp-equipment',
    name = 'NYSP Equipment',
    permission = 'police.shop',
    items = {
      { 'sticker_nysp', 100 },
      { 'wbody|WEAPON_BEANBAG', 10000 },
      { 'wammo|WEAPON_BEANBAG', 100 },
      { 'p_cone_blue', 50 },
      { 'p_cone_red', 50 },
      { 'drugkit', 50 },
    },
    allow_debit = true,
  },

  {
    uid = 'leo-sahp-equipment',
    name = 'SAHP Equipment',
    permission = 'police.shop',
    items = {
      { 'wbody|WEAPON_HEAVYRIFLE', 15000 },
      { 'p_cone_blue', 50 },
      { 'p_cone_red', 50 },
      { 'afak', 500 },
    },
    allow_debit = true,
  },

  {
    uid = 'leo-canteen-sandy',
    name = 'BCSO Canteen',
    items = {
      { 'beer_patriot_free', 40 },
      { 'food_boiled_corn', 50 },
      { 'food_cannedcorn', 50 },
      { 'food_lunchable', 50 },
      { 'food_instant_grits', 50 },
      { 'powerade_yellow', 40 },
      { '5g_blocker', 50 },
      { 'food_spec_sprunk', 45 },
      { 'drink_sprunkl', 100 },
      { 'drink_sprunkextrem', 150 },
      { 'food_vegemitetoast', 120 },
    },
    allow_debit = true,
  },

  {
    uid = 'doj-canteen-court',
    name = 'Canteen',
    items = {
      { 'food_grilledcheese', 120 },
      { 'food_croissant', 150 },
      { 'coffee', 40 },
      { 'powerade_blue', 40 },
      { 'food_pepperminthotchocolate', 40 },
      { 'manilla_folder', 25, },
    },
    allow_debit = true,
  },

  {
    uid = 'lightandlove',
    name = 'Light & Love',
    items = {
      { 'll_pack_a', 2500 },
      { 'll_joint_a', 600 },
      { 'll_bud_b', 450 },
      { 'll_grinder_a', 200 },
      { 'll_lighter_a', 350 },
      { 'll_food_a', 300 },
      { 'll_food_b', 300 },
      { 'll_oil_b', 400 },
      { 'll_balm', 650 },
      { 'll_oil_a', 350 },
      { 'll_paper_a', 10 },
      { 'll_paper_d', 10 },
      { 'll_paper_b', 10 },
      { 'll_paper_c', 10 },
      { 'll_paper_e', 10 },
    },
  },

  {
    uid = 'chemical-supply',
    name = 'Lab Chemical Supplies',
    items = {
      { 'ing_benzalkonium', 2},
      { 'ing_chloro', 2 },
      { 'ing_hydroacid', 2},
      { 'ing_butan', 2},
      { 'ing_benzamide', 2},
      { 'ing_diethyl', 2},
      { 'ing_dimethyl', 2},
      { 'ing_dexlof', 2},
      { 'ing_levlof', 2},
      { 'ing_anhydrous', 2},
      { 'ing_cstear', 2},
      { 'd_water', 2},
      { 'boiling_flask', 100},
      { 'distilling_flask', 100},
      { 'chemical_gloves', 15000},
      { 'respirator', 10000}
    },
    allow_debit = true,
  },

  {
    uid = 'drug-equipment-supply',
    name = 'Drug Equipment',
    items = {
      { 'nasal_inj', 2 },
      { 'pill_capsule', 2 },
      { 'injection_syringe', 2},
      { 'vial', 2}
    },
    allow_debit = true,
  },


	{
    uid = 'ldorganics',
		name = 'LD Organics',
		items = {
			{ 'ld_chapstick', 500 },
      { 'ld_zippo', 250 },
      { 'ld_grinder', 150 },
      { 'ld_paper', 5 },

      { 'ld_bud_a', 150 },  -- Enema cleanse equivalent
      { 'ld_bud_b', 100 },  -- Medical Joint equivalent
      { 'ld_bud_c', 150 },  -- Ficus Joint equivalent

      { 'ld_joint_d', 400 }, -- Preroll
		},

    purchase_limit_per_store = {
      ['ld_chapstick'] = 5,
    },
    siphon_tax = 0.08,
	},
	{
    uid = 'stpeters-supply',
		name = 'St Peters Pub Supply',
		permission = 'rockyroadtowing.employee',
		items = {
      { 'xm23_s5_pint', 50 },
      {'xm23_s5_bottle', 50},
		},
	},
	{
    uid = 'wanderlust-supply',
		name = 'Wanderlust Supply',
		permission = 'wanderlust.employee',
		items = {
      { 'book_mrbones', 1000 },
      { 'wl_frostvein', 1000 },
      { 'book_bloodink', 1000 },
      { 'book_likesnow', 1000 },
      { 'book_ghostridge', 1000 },
      { 'book_yamrmask', 1000 },
      { 'book_mrbones_b', 1000 },
      { 'book_becomingher', 1000 },
      { 'book_doublecrossed', 1000 },
      { 'book_nightfallrising', 1000 },
      { 'wl_giftbag_a', 1 },
		},
	},
  {
    uid = 'yellowjack-supply',
		name = 'Yellow Jack',
		permission = 'yellowjack.employee',
		items = {
      { 'beer_logger', 50 },
      { 'beer_patriot', 50 },
      { 'food_black_talon', 50 },
      { 'food_energydrink', 1 },
      { 'stor_yj', 1 },
      { 'beer_patriot_pump', 50 },
      { 'black_talon_app', 50 },
      { 'drink_yj_beer', 65 },
      { 'yj_napkin', 1 },
		},
    purchase_callback = itemPurchasedYellowjack,
	},

	{
    uid = 'vanillaunicorn-supply',
		name = 'Vanilla Unicorn Fridge',
		permission = 'unicorn.employee',

		items = {
      { 'food_champagne', 50 },
      { 'stor_vu', 1 },
      { 'lighter_vanillazippo', 100},
      {'lighter_vanillaunicorn', 50},
      { 'vu_loveglove', 10 },
      { 'vu_keychain', 1 },
		},
	},

  {
    uid = 'dbsecurity-supply',
		name = 'Downbad Security Supply',
		permission = 'security.service',

		items = {
      { 'wbody|WEAPON_STUNGUN', 1000 },
      { 'wammo|WEAPON_STUNGUN', 1 },
      { 'wbody|WEAPON_FLASHLIGHT', 25 },
      { 'bandage', 25 },
      { 'p_cone', 150 },
      { 'police_armor', 900 },
		},

    allow_debit = true,
	},

	{
    uid = 'belmont-supply',
    name = 'The Belmont',
    permission = 'belmont.employee',
    items = {
			{ 'belmont_cigar', 10 },
			{ 'belmont_cigarcuter', 10 },
      { 'belmont_cigarbox', 10 },
      { 'food_stronzobeer', 50 },
      { 'food_sambuca', 50 },
    },
  },

	{
    uid = 'redwoods-supply',
    name = 'Redwoods Cigarettes',
    items = {
      { 'rw_au_pack', 800 },
			{ 'rw_pack', 500 },
      {'redwood_carton', 4000},
      {'redwood_gcarton', 7000},
      {'redwood_tray1', 5000},
      {'redwood_tray2', 5000},
			{ 'rw_jr_pack', 250 },
      { 'rw_matches', 200 },
			{ 'rw_chew', 300 },
			{ 'rw_paper', 4 },
			{ 'rw_zippo', 1000 },
      { 'tobacco_leaves', 50 },
    },
    allow_debit = true,
  },
  {
    uid = 'zoobies-supply',
    name = 'Zoobies Supply',
    permission = 'zoobies.employee',
    items = {
      { 'zoobies_papers', 2 },
		--	{ 'bd_thcdonut', 150 },
      {'z_prcrate',100},
      {'z_sscrate',100},
      {'zoobies_grinder', 35},
     -- {'bd_thc_brownie',150},
      {'zoobies_prerolls',1},
			{ 'stor_zoob', 1 },
      {'zoobies_lighter', 35},
      {'seed_kingston', 50},
    },
  },
  {
    uid = 'kushkorner-supply',
    name = 'KushKorner Supply',
    permission = 'kushkorner.employee',
    items = {
      {'kk_papers', 2 },
      {'kk_prcrate',100},
      {'kk_sscrate',100},
      {'kk_grinder', 35},
      {'kk_prerolls',1},
			{'stor_kk', 1 },
      {'kk_lighter2', 35},
    },
  },
  {
    uid = 'kushkorner-seeds',
    name = 'KushKorner Seed Supply',
    permission = { 'The Kush Korner', 'management' },
    items = {
      {'seed_panic', 50},
    },
  },
  {
    uid = 'spellbound-supply',
    name = 'Spellbound Occult Supply',
    permission = 'spellbound.employee',
    items = {
      {'togo_spellbound_bag',1},
      {'spellbound_card_deck',3250},
      {'sb_runebag', 2500},
      {'sb_pendulum',2500},
      {'sb_bofshadows',100},
      {'quarts_crystal',100},
      {'amethyst_crystal', 100},
      {'lapis_lazuli_crystal',100},
			{'emerald_crystal',100},
      {'tigers_eye_crystal',100},
      {'carnelian_crystal',100},
      {'garnet_crystal',100},
      {'sixx_on_the_beach_incense',100},
      {'nag_champa_incense',100},
      {'dragons_blood_incense',100},
      {'sandlewood_incense',100},
      {'sage',250},
      {'karmic_sachet',250},
    },
  },

  {
    uid = 'gty-supply',
    name = 'Go Truck Yourself Supply',
    permission = 'gty.employee',
    items = {
     {'repair_parts', 15},
     {'tirerepairkit', 150},
     {'carrepairkit', 150},
     {'gty_airfresh', 1},
     {'gty_keychain', 1 },
     {'gty_cert', 10 },
    },
  },
  {
    uid = 'gty-supply-m',
    name = 'Go Truck Yourself Management Supply',
    permission = { 'Go Truck Yourself', 'management' },
    items = {
     {'p_cone', 50},
     {'gty_coupon', 1},
    },
  },

  {
    uid = 'alphamail-supply',
    name = 'AlphaMail Supply',
    permission = 'alphamail.employee',
    items = {
     {'alphamail_boxdispenser', 50},
    },
  },

  {
    uid = 'pawn-supply',
    name = 'Pawn Shop Store',
    permission = 'sftpawn.employee',

    items = {
      { 'pokemon_binder', 500 },
      { 'prop_bl_ss_e', 500 },
    },
  },

  {
    uid = 'pawn-tcg',
    name = 'Pawn Shop TCG Store',
    permission = 'sftpawn.employee',

    items = {
    },
  },

  {
    uid = 'prison-food-supply',
    name = 'Food Tray',
    items = {
      { 'food_prison_mushedpeas', 0 },
      { 'food_prison_pizzasoup', 0 },
      { 'food_prison_chunkymalk', 0 },
      { 'food_prison_blandnoodles', 0 },
      { 'food_prison_soggysalad', 0 },
      { 'food_prison_coldmixedveggies', 0 },
      { 'food_prison_unsaltedrice', 0 },
    },
  },

  {
    uid = 'tequilala-supply',
    name = 'TequiLaLa',
    permission = 'tequilala.employee',

    items = {
      { 'drink_paledeath', 150 },
      { 'drink_tequilya', 170 },
      { 'drink_greenfairy', 100 },
      { 'drink_off_da_block', 125 },
    },
    allow_debit = true,
  },

  {
    uid = 'homebrew-food-supply',
    name = 'Homebrew Cafe Food Supply',
    permission = 'homebrew.employee',
    items = {
      { 'stor_hb', 1 },
      { 'food_chippies', 5 },
      { 'hb_choc_kiss', 5 },
      { 'homebrew_zippo', 100 },
    },
  },

  {
    uid = 'homebrew-drink-supply',
    name = 'Homebrew Cafe Drink Supply',
    permission = 'homebrew.employee',
    items = {
      { 'drink_bwood_blast', 25 },
      { 'beer_pride_brew', 50 },
      { 'drink_raggarum', 50 },
      { 'drink_pina_peyote', 50 },
      { 'drink_vinny_cocoa', 25 },
      { 'drink_ka_citrus', 25 },
      { 'hb_calypso_isl', 25 },
      { 'hb_calypso_ob', 25 },
      { 'hb_calypso_org', 25 },
      { 'hb_calypso_sth', 25 },
      { 'hb_calypso_str', 25 },
      { 'hb_charles_angels', 50 },
      { 'hb_magnum', 50 },
    },
  },

  {
    uid = 'msg-imports',
    name = 'Mike\'s Sporting Goods - Imports',
    permission = { 'Mikes Sporting Goods', 'extra_a' },
    items = {
      { 'comp_sk_patr', 1000 },
      { 'comp_sk_camo_bs', 1000 },
      { 'comp_sk_camo_wl', 1000 },
      --{ 'food_mikenuts', 10 },
      { 'key_chain_msg', 1 },
      { 'wbody|WEAPON_HUNTINGRIFLE', 1000 },
      { 'wbody|WEAPON_HEAVYSHOTGUN', 1000 },
      { 'prop_mikes_chair', 1000 },
      { 'prop_bl_ss_b', 500 },
      { 'prop_bl_ss_c', 500 },
			{ 'shark_repelent', 50 },
      { 'tirerepairkit', 150 },
      { 'sift_pan', 100 },
      { 'blrp_tacklebox', 1000 },

      ----

      { 'hunting_map', 1 },
      { 'fishing_guide', 1 },
  		{ 'bandage', 25 },
      { 'beer', 40 },
  		{ 'binoculars', 150 },
      { 'carrepairkit', 150 },
      { 'cigarette', 20 },
  		{ 'hand_gps', 100 },
      --{ 'water', 40 },
      { 'wbody|WEAPON_FLASHLIGHT', 25 },
      { 'wbody|WEAPON_KNIFE', 250 },
      { 'wbody|WEAPON_FLAREGUN', 800 },
      { 'ammo_flaregun', 1500 },
      { 'ammo_3006', 1 },
		  { 'scuba_kit', 300 },
  		{ 'hand_radio', 250 },
      { 'beach_ring', 500 },
      { 'clth_hunting_vest', 300 },

      ----

      { 'duffle_bag', 1000 },
      { 'camo_duffle_bag', 1000 },
    },
    allow_debit = true,
    siphon_tax = 0.08,
    suppress_ux_check = true,
  },

  {
    uid = 'msg-exports',
    name = 'Mike\'s Sporting Goods - Exports',
    permission = 'mikessporting.employee',
    items = {
      { 'boar_tusk', 4000 },
      { 'deer_antlers', 3000 },
      { 'rabbit_foot', 6000 },
      { 'rat_tail', 7000 },
    },
    selling_only = true,
    selling_coefficient = 1.0,
  },

  {
    uid = 'lst-nos',
    name = 'Los Santos Tuners - NOS',
    permission = { 'Los Santos Tuners', 'extra_a' },
    items = {
      { 'nos_bottle', 5000 },
      { 'nos_kit', 20000 },
    },
    allow_debit = true,
    siphon_tax = 0.16, -- Double tax because Tuner shop end sale price is 2x all of these items
  },

  {
    uid = 'lst-general',
    name = 'Los Santos Tuners - Parts',
    permission = 'lstuners.employee',

    items = {
      { 'repair_parts', 15 },
      { 'tirerepairkit', 150 },
      { 'lst_engine', 6400 },
      { 'lst_suspension', 1600 },
      { 'lst_suspensionsport', 1600 },
      { 'lst_transmission', 4800 },
      { 'lst_brake', 4800 },
      { 'lst_turbo', 6000 },
      { 'lst_wb_kit', 48000 },
      { 'lst_wire_harness', 150 },
      { 'car_radio', 7500 },
      { 'obd_scanner', 5000 },
    },
    allow_debit = true,
    siphon_tax = 0.16, -- Double tax because Tuner shop end sale price is 2x all of these items
  },

  {
    uid = 'thepit-supply',
    name = 'The Pit Shop',
    permission = 'midnightclub.employee',

    items = {
      { 'repair_parts', 15, },
      { 'tirerepairkit', 150 },
    },
    allow_debit = true,
  },


  {
    uid = 'thevault-supply',
    name = 'The Vault Shop',
    permission = 'the-vault.employee',

    items = {
      { 'vault_keychain', 1, },
      { 'vault_zippo', 35, },
    },
    allow_debit = true,
  },

  {
    uid = 'leoswat-supplys',
    name = 'swat Shop',
    permission = 'swat.employee',

    items = {
      { 'wbody|WEAPON_MILITARYRIFLE', 25000, },
      { 'ammo_556nato', 1 },
      { 'ammo_9mm_nato', 1 },
      { 'ammo_45acp', 1 },

      -- Med stuff
      { 'police_armor', 900 },
      { 'ifak', 500 },
      { 'bandage', 25 },
    },
    allow_debit = true,
  },

  {
    uid = 'leogtf-supplys',
    name = 'gtf Shop',
    permission = 'gtf.employee',

    items = {
      { 'wbody|WEAPON_CARBINERIFLE_MK2', 25000, },
      { 'ammo_556nato', 1 },
      { 'ammo_9mm_nato', 1 },
      { 'ammo_45acp', 1 },

      -- Med stuff
      { 'police_armor', 900 },
      { 'ifak', 500 },
  		{ 'bandage', 25 },

      -- Raid Stuff
      { 'wbody|WEAPON_GRENADELAUNCHER_SMOKE', 5000 },
      { 'ammo_gren_smoke', 1000 },
      { 'det_cord', 11000 },

    },
    allow_debit = true,
  },

  {
    uid = 'rockyroadtowing-supply',
    name = 'Rocky Road Towing - Equipment',
    permission = 'rockyroadtowing.employee',

    items = {
      { 'carrepairkit', 150 },
      { 'tirerepairkit', 150 },
      { 'key_chain', 5 },
      { 'hand_radio', 250 },
      { 'hand_gps', 100 },
      { 'personal_phone', 10 },
      { 'tablet', 10 },
      { 'wbody|WEAPON_FIREEXTINGUISHER', 500, },
      { 'prop_bl_terminal', 1000 },
  		{ 'binoculars', 150, },
    },
  },

  {
    uid = 'rockyroadtowing-2',
    name = 'Rocky Road Towing - Manager Shop',

    items = {
      { 'rockyroad_keychain', 5 },
      { 'rr_airfresh', 5 },
      { 'tool_slimjim', 1000 },
    },
  },

  {
    uid = 'boathouse-supply',
    name = 'The Boathouse Supply Closet',
    permission = 'boathouse.employee',
    items = {
      { 'airfresh_boathouse', 150 },
      { 'pink_slip', 500 },
      { 'key_chain_boathouse', 150 },
      { 'obd_scanner', 5000 },
    },
  },

  {
    uid = 'joe-supply',
    name = 'Joe\'s Spray Paint Supply',
    permission = 'joescorner.employee',

    items = {
      { 'rag', 25 },
      { 'spraypaint_black', 1000 },
      { 'spraypaint_white', 1000 },
      { 'spraypaint_red', 1000 },
      { 'spraypaint_orange', 1000 },
      { 'spraypaint_yellow', 1000 },
      { 'spraypaint_green', 1000 },
      { 'spraypaint_blue', 1000 },
      { 'spraypaint_purple', 1000 },
    },

    purchase_limit_per_store = {
      ['spraypaint_black'] = 5,
      ['spraypaint_white'] = 5,
      ['spraypaint_red'] = 5,
      ['spraypaint_orange'] = 5,
      ['spraypaint_yellow'] = 5,
      ['spraypaint_green'] = 5,
      ['spraypaint_blue'] = 5,
      ['spraypaint_purple'] = 5,
    },
  },

  {
    uid = 'coolbeans-supply',
		name = 'Cool Beans Supply',
    permission = 'coolbeans.employee',

		items = {
      {'stor_cb',1},
      { 'coolbeans_napkin', 1 },
      { 'prop_coffeecup_bcso', 100 },
      { 'prop_coffeecup_lsfd', 100 },
      { 'prop_coffeecup_lspd', 100 },
      { 'prop_coffeecup_chihuahua', 100 },
      { 'prop_coffeecup_doc', 100 },
      { 'prop_coffeecup_vagos', 100 },
      { 'prop_coffeecup_vu', 100 },
      { 'prop_coffeecup_305', 100 },
      { 'prop_coffeecup_aod', 100 },
      { 'prop_coffeecup_lost', 100 },
      { 'prop_coffeecup_mikes', 100 },
      { 'prop_coffeecup_otf', 100 },
      { 'prop_coffeecup_tq', 100 },
      { 'prop_coffeecup_yj', 100 },
      { 'prop_coffeecup_fdf', 100 },
      { 'prop_coffeecup_bch', 100 },
      { 'prop_coffeecup_lst', 100 },
      { 'prop_coffeecup_ballas', 100 },
		},

    suppress_ux_check = true,
	},

  {
    uid = 'coolbeans-supply2',
		name = 'More Cups',
    permission = 'coolbeans.employee',

		items = {
      { 'prop_coffeecup_bd', 100 },
      { 'prop_coffeecup_mcf', 100 },
      { 'prop_coffeecup_pearls', 100 },
      { 'prop_coffeecup_cb_a', 100 },
      { 'prop_coffeecup_doj', 100 },
      { 'prop_coffeecup_lsc', 100 },
      { 'prop_coffeecup_pdm', 100 },
      { 'prop_coffeecup_blath_alainn', 100 },
      { 'prop_coffeecup_burgershot', 100 },
      { 'prop_coffeecup_tdls', 100 },
      { 'prop_coffeecup_dkn', 100 },
      { 'prop_coffeecup_gty', 100 },
      { 'prop_coffeecup_misfitz', 100 },
      { 'prop_coffeecup_rebels', 100 },
      { 'prop_coffeecup_rocky_road', 100 },
		},

    suppress_ux_check = true,
	},

  {
    uid = 'coolbeans-supply3',
		name = 'Extra Cups',
    permission = 'coolbeans.employee',

		items = {
      { 'prop_coffeecup_boos_catfe', 100 },
      { 'prop_coffeecup_cartel', 100 },
      { 'prop_coffeecup_condemned_mc', 100 },
      { 'prop_coffeecup_esa', 100 },
      { 'prop_coffeecup_finders_keepers', 100 },
      { 'prop_coffeecup_ikonz', 100 },
      { 'prop_coffeecup_mm', 100 },
      { 'prop_coffeecup_thompson__associates', 100 },
      { 'prop_coffeecup_vice_motorsports', 100 },
      { 'prop_coffeecup_the_westwoods', 100 },
      { 'prop_coffeecup_waywards_tattoos', 100 },
      { 'prop_coffeecup_weazel_news', 100 },
		},

    suppress_ux_check = true,
	},
  ---------------------------------------
  ------------- 24/7 Market -------------
  ---------------------------------------

  {
    uid = '247market-supply',
    name = '24/7 Supply',
    permission = 'yellersmarket.employee',
    items = {
      { '247_plasticbag', 1 },
      { 'original_rum',30 },
      { 'peach_rum',30 },
      { 'coconut_rum',600 },
      { 'drink_cartel_vodka', 5, },
      { 'drink_aces_champagne', 5, },
      { 'drink_eagles_mark', 5, },
      { 'drink_famnotic', 5, },
      { 'drink_gronac', 5, },
      { 'drink_vatron', 5, },
    },
    allow_debit = true,
  },

  {
    uid = '247drinks-autoorder',
    name = '247 Specialty Stock',
    items = {
      { 'drink_cartel_vodka', 225, },
      { 'drink_aces_champagne', 750, },
      { 'drink_eagles_mark', 225, },
      { 'drink_famnotic', 150, },
      { 'drink_gronac', 375, },
      { 'drink_vatron', 300, },
    },
    allows_restock = true,
  },

  {
    uid = '247market-canned',
    name = 'Canned Goods',
    tax_name = '24/7 Market',
    items = {
      { 'food_olives', 1 },
      { 'food_salsa', 1 },
      { 'food_hominy', 1 },
      { 'food_coconutcream', 1 },
      { 'food_pineapplejuice', 1 },
      { 'food_peas', 1 },
      { 'food_porkandbeans', 1},
    },
    allows_restock = true,
    --purchase_callback = itemPurchasedMarket,

    siphon_tax = 0.08,
  },

  {
    uid = '247market-condiments',
    name = 'Condiments',
    tax_name = '24/7 Market',

    items = {
      { 'food_soysauce', 1 },
      { 'food_vegetableoil', 1 },
      { 'food_vanilla', 1 },
      { 'food_cocoa', 1 },
      { 'food_ketchup', 1 },
      { 'food_mayonnaise', 1 },
      { 'food_vinegar', 1 },
      { 'food_vic_hotsauce', 75 },
      { 'food_butter', 1 },
      { 'food_jerk_seasoning', 5 },
      { 'food_chilipowder', 1 },
      { 'food_salt', 1 },
      { 'curry_mix', 1 },
      { 'food_paprika', 1 },
      { 'food_tartarsauce', 1 },
      { 'food_bbqsauce', 2 },
      { 'food_spices', 1 },
    },
    allows_restock = true,
    --purchase_callback = itemPurchasedMarket,

    siphon_tax = 0.08,
  },

  {
    uid = '247market-dessert',
    name = 'Dessert Ingredients',
    tax_name = '24/7 Market',

    items = {
      { 'food_chocolate', 1 },
      { 'food_wchocolate', 1},
      { 'food_sugar', 1 },
      { 'food_frosting', 1 },
      { 'food_marshmellow', 1 },
      { 'food_cmints',1},
      { 'food_cherry', 1 },
      { 'food_chocolatesauce', 1 },
      { 'food_cremedecoca', 1 },
      { 'food_whippedcream', 1 },
      { 'food_sprinkles', 1 },
    },
    allows_restock = true,
    --purchase_callback = itemPurchasedMarket,

    siphon_tax = 0.08,
  },

  {
    uid = '247market-bar',
    name = 'Bar Fixtures',
    tax_name = '24/7 Market',

    items = {
      { 'food_bitters', 1 },
      { 'food_grenadine_syrup', 1 },
      { 'food_lemonjuice', 1 },
      { 'food_heavycream', 1 },
      { 'food_simplesyrup', 1 },
      { 'food_soda_flavoring', 1 },
      { 'food_dsoda', 1},
      { 'honey', 1 },
      { 'caramel_syrup', 1 },
    },
    allows_restock = true,
    --purchase_callback = itemPurchasedMarket,

    siphon_tax = 0.08,
  },

  {
    uid = '247market-dry',
    name = 'Dry Foods',
    tax_name = '24/7 Market',

    items = {
      { 'food_flour_mix', 1 },
      { 'food_noodles', 1 },
      { 'food_oolongteabag', 1 },
      { 'food_black_teabag', 1 },
      { 'food_tortilla', 1 },
      { 'food_bread', 1 },
      { 'food_cinnamon', 1 },
      { 'food_tostadas', 1 },
      { 'food_matchapowder', 1},
      { 'food_hibiscustealeafs', 1 },
      { 'food_spec_nuts', 1 },
      { 'food_pretzel', 1 },
      { 'food_expressobeans', 1 },
      { 'food_gelatine', 1 },
      { 'food_buns', 2 },
      { 'food_breading', 1 },
      { 'food_buns_hotdog', 2 },
      { 'food_malt', 5 },
      { 'yeast', 10 },
      { 'pestle_mortar', 250 },
      { 'coffee_beans', 1 },
      { 'food_pasta', 1 },
      { 'food_cornstarch', 10 },
      { 'food_scookie', 2},
      { 'food_crackers', 2 },
    },
    allows_restock = true,
   -- purchase_callback = itemPurchasedMarket,

    siphon_tax = 0.08,
  },

  {
    uid = '247market-prepared',
    name = 'Pre-prepared Ingredients',
    tax_name = '24/7 Market',

    items = {
      { 'food_orangeslice', 5 },
      -- { 'food_raw_fish', 1 },
      { 'food_cooked_rice', 1 },
      { 'food_cooked_onions', 1 },
      { 'food_onions', 2 },
      { 'food_cooked_beans', 1 },
      { 'food_chopped_potato', 2 },
      { 'food_grated_cheese', 1 },
      { 'food_cheese', 2 },
      { 'food_frozenstrawberries', 1 },
      { 'food_boba', 1},
      { 'food_frozen_waffle', 2 },
      { 'food_shrimp', 1 },
      { 'food_ice', 1 },
      { 'food_crab', 65 },
      { 'food_oysters', 100 },
      { 'food_lobster', 125 },
      { 'food_peach_slice', 5},
      { 'food_apple_slice', 5 },
      { 'food_cream_cheese', 1 },
    },
    allows_restock = true,
   -- purchase_callback = itemPurchasedMarket,

    siphon_tax = 0.08,
  },

  {
    uid = '247market-alcodrinks',
    name = 'Alcoholic Drinks',
    items = {
      { 'beer', 40, },
      { 'vodka', 50, },
      { 'don_pereon', 50, },
      { 'food_spec_dankwhiskey', 50, },
      { 'food_spec_rum', 50, },
      { 'food_vermouth', 50, },
      { 'food_baileys', 50, },
      { 'food_gin', 50, },
      { 'food_triplesec', 50, },
      { 'food_kahlua', 50, },
      { 'food_jaeger', 50, },
      { 'drink_kiwiliq', 50, },
      { 'drink_strawliq', 50, },
      { 'drink_pschnapps', 50, },
    },
    allows_restock = true,
    siphon_tax = 0.08,
  },

  {
    uid = '247market-nonalcodrinks',
    name = 'Non-alcoholic Drinks',
    tax_name = '24/7 Market',
    items = {
      { 'water', 40 },
      { 'milk', 1 },
      { 'food_strawberrymilk', 1 },
      { 'food_tomatojuice', 1 },
      { 'cheap_gocagola', 40 },
      { 'coffee', 40 },
      { 'food_clubsoda', 1 },
      { 'orangejuice', 40 },
      { 'lemonlimonad', 40 },
      { 'icetea', 40 },
      { 'food_cadiawater', 1 },
      { 'food_spec_sprunk', 45 },
      { 'eggnog', 20 },
    },
    allows_restock = true,
    --purchase_callback = itemPurchasedMarket,

    siphon_tax = 0.08,
  },

  {
    uid = '247market-produce',
    name = 'Fresh Produce',
    tax_name = '24/7 Market',
    items = {
      { 'food_seaweed', 1 },
      { 'food_ginger', 1 },
      { 'food_lettuce', 1 },
      { 'food_tomato', 10 },
      { 'food_jalapeno', 1 },
      { 'food_avocado', 1 },
      { 'food_cilantro', 1 },
      { 'food_celery', 1 },
      { 'food_mint', 1 },
      { 'food_lime', 1 },
      { 'food_blueberry', 1 },
      { 'food_blackberry', 1 },
      { 'food_banana', 1 },
      { 'food_plantain', 5 },
      { 'food_arbol', 1 },
      { 'food_habpeppers', 1 },
      { 'food_garlic', 1 },
      { 'food_raw_corn', 10 },
      { 'food_okra', 5 },
      { 'ackee', 1 },
      { 'food_carrot', 1 },
      { 'cucumber', 1 },
      { 'tamarind', 1 },
      { 'food_lemon_slice', 1 },
      { 'food_apple', 1 },
    },
    allows_restock = true,
    --purchase_callback = itemPurchasedMarket,

    siphon_tax = 0.08,
  },

  {
    uid = '247market-meat',
    name = 'Fresh Meat',
    tax_name = '24/7 Market',

    items = {
      { 'food_raw_meat', 15 },
      { 'food_raw_goat', 15 },
      { 'food_oxtail', 15 },
      { 'taco_ingredient', 3 },
      { 'food_raw_meat_patty', 15 },
      { 'food_raw_bacon', 15 },
      { 'food_raw_pork', 15 },
      { 'food_raw_wagyu', 150 },
      { 'food_raw_chicken', 15 },
      { 'food_egg', 5 },
    },
    allows_restock = true,
    --purchase_callback = itemPurchasedMarket,

    siphon_tax = 0.08,
  },

  {
    uid = '247market-sales',
    name = '24/7 Market Sales',
    items = {
      { 'peach', 7, },
      { 'grape', 6, },
      { 'food_tomato', 5, },
      { 'food_raw_corn', 7, },
      { 'fish_bass_lg', 80, },
      { 'fish_bass_sm', 59, },
      { 'fish_crappie', 75, },
      { 'fish_grouper', 81, },
      { 'fish_lakecarp', 80, },
      { 'fish_mackerel', 68, },
      { 'fish_salmon', 86, },
    },
    selling_only = true,
    selling_coefficient = 1.0,
    selling_coefficient_felon = 0.70,
  },

  -- Life Invader
  {
    uid = 'lifeinvader-store',
    name = 'Life Invader',
    items = {
      { 'paper', 2 },
    },
    siphon_tax = 0.08,
  },

  {
    uid = 'burgershot-supply',
		name = 'Burgershot Food Supply',
    permission = 'burgershot.employee',

		items = {
      { 'stor_bs', 1 },
      { 'bs_napkin', 1 },
      -- { 'bs_mystery_pack', 1500 },
		},

    purchase_callback = itemPurchasedBurgershot,
	},

  {
    uid = 'catfe-supply',
    name = 'Catfe Supply',
    permission = 'catfe.employee',
    items = {
      { 'stor_cat', 1, },
      { 'boos_napkin', 1, },
    },
  },

  {
    uid = 'paletodiner-supply',
    name = 'Paleto Diner Supply',
    permission = 'paletodiner.employee',
    items = {
      { 'stor_diner', 1, },
      { 'diner_napkin', 1, },
    },
  },

  {
    uid = 'pearls-supply',
    name = 'pearls Supply',
    permission = 'pearls.employee',
    items = {
      { 'pearls_napkin', 1, },
      { 'stor_pearls', 1, },
    },
  },

  {
    uid = 'weazelnews-supply',
    name = 'Weazel News Supply',
    permission = 'weaznews.employee',
    items = {
      { 'clth_lanyard_wn', 1500, },
    },
  },

  {
    uid = 'chihuahua-supply',
    name = 'Chihuahua Hotdogs Food Supply',
    permission = 'chihuahua.employee',
    items = {
      { 'stor_chi', 1, },
      { 'chihuahua_napkin', 1 },
      { 'chihuahua_keychain', 1 },
    },
  },

  {
    uid = 'cartel-supply',
    name = 'The Ranch Feed Supply',
    permission = 'neverland.employee',
    items = {
      { 'hunting_bait_7', 20, },
      { 'hunting_bait_8', 20, },
      { 'hunting_bait_9', 20, },
      { 'food_tofu', 10, },
    },
  },

  {
    uid = 'pdm-supply',
    name = 'PDM Supply Closet',
    permission = 'pdm.employee',

    items = {
      { 'airfresh_pdm', 150, },
      { 'pink_slip', 500, },
      { 'key_chain_pdm', 150, },
      { 'obd_scanner', 5000, },
    },
  },

  {
    uid = 'lsc-supply',
    name = 'LSC Supply Closet',
    permission = 'lsc.employee',

    items = {
      { 'airfresh_lsc', 150, },
      { 'pink_slip', 500, },
      { 'key_chain_lsc', 150, },
      { 'obd_scanner', 5000, },
    },
  },

  {
    uid = 'Vice-supply',
    name = 'Vice Supply Closet',
    permission = 'vms.employee',

    items = {
      { 'obd_scanner', 5000, },
      { 'pink_slip', 500 },
      { 'airfresh_vice', 1, },
      { 'key_chain_vice', 1, },
    },
  },

  {
    uid = 'hayes-supply',
    name = 'Mechanic Shop',
    permission = 'mechanic.repair',

    items = {
      { 'repair_parts', 15, },
      { 'bufties_wax', 1000, },
      { 'tirerepairkit', 150 },
    },
  },

  {
    uid = 'ballas-supply',
    name = 'Ballas Stock',
    permission = 'ballas.employee',
    items = {
      { 'backhoods_pack', 50, },
      { 'backhoods_pack_cherry', 50, },
      { 'backhoods_pack_honey', 50, },
      { 'backhoods_pack_mojito', 50, },
      { 'backhoods_pack_whiskey', 50, },
    },
  },

  {
    uid = 'youtool-supply',
    name = 'You Tool',
    items = {
      { 'dynamite', 25, },
  		{ 'binoculars', 150, },
  		{ 'garden_shovel', 25, },
  		{ 'electric_torch', 100, },
  		{ 'garden_leaf_blower', 100, },
  		{ 'watering_can', 15, },
  		{ 'oil_empty_drum', 5, },
  		{ 'grain_scoop', 15, },
  		{ 'pruning_shears', 15, },
      { 'sticky_tape', 20 },
      { 'generator', 15000, },
      { 'pressure_cooker', 1000, },
      { 'stor_hween_a', 100, },
      { 'manilla_folder', 25, },
      { 'chalk_whi', 100 },
      { 'container_toybox', 2500, },
      { 'flux_limestone', 380, },
      { 'prop_skid_chair_01', 5000, },
      { 'fermenting_barrel', 200, },
      { 'prop_bl_ss_a', 7000, },
      { 'wbody|WEAPON_FIREEXTINGUISHER', 500, },
      { 'rope', 150, },
      { 'angle_grinder_blade', 10000, },
      { 'tire_iron', 2500, },
      { 'jack_stand', 5000, },
      { 'waybill_printer', 2000 },

      --Spraypaint stuff
      { 'pigment_purple', 250, },
      { 'pigment_blue', 250, },
      { 'pigment_green', 250, },
      { 'pigment_yellow', 250, },
      { 'pigment_orange', 250, },
      { 'pigment_red', 250, },
      { 'pigment_white', 250, },
      { 'pigment_black', 250, },
    },
    allow_debit = true,
    siphon_tax = 0.08,
    suppress_ux_check = true,
  },

  {
    uid = 'pillbox-gifts',
    name = 'Pillbox Gift Shop',
    items = {
      { 'balloon_elephant', 10000, },
      { 'birthdaycard', 120, },
			{ 'flowers', 80, },
			{ 'flowers_tulips', 80, },
			{ 'getwellcard', 100, },
			{ 'roses', 120, },
			{ 'Teddybear', 100, },
      { 'teddybear_pink', 2000, },
      { 'polarbear', 5000, },
			{ 'tissuebox', 100, },
      { 'book_selfhelp', 350, },
      { 'card_newbaby', 100, },
      { 'bandage', 25, },
    },
  },
  --[[
  {
    uid = 'pillbox-lab',
    name = 'Chemistry Equipment',
    items = {
      { 'boiling_flask', 100, },
      {'distilling_flask', 100, },
      {'ing_bromine',100,},
      {'ing_methylamine',100,},
      {'hydrobromic_acid',100},
      {'safrole',100},
      {'ing_pcarbonate',100},
      {'sulfuric_acid',200},
      { 'boiling_flask', 200},
      {'ing_chydroxide',100},
      {'water',100},
    },
  },
   ]]
  {
    uid = 'bayview-all',
    name = 'Bayview Lodge General Store',
    items = {
  		{ 'hunting_bait_2', 165, },
  		{ 'hunting_bait_4', 165, },
  		{ 'hunting_bait_5', 165, },
  		{ 'hunting_bait_6', 165, },
      { 'hunting_map', 1, },
  		{ 'bandage', 25, },
      { 'beer', 40, },
  		{ 'binoculars', 150, },
      { 'carrepairkit', 150, },
      { 'cigarette', 20, },
      { 'food_spec_trailmix', 50, },
  		{ 'hand_gps', 100, },
      { 'water', 40, },
      { 'wbody|WEAPON_FLASHLIGHT', 25, },
      { 'wbody|WEAPON_KNIFE', 270, },
      { 'wbody|WEAPON_HEAVYSHOTGUN', 5100, },
      { 'ammo_3006', 2, },
      { 'fishing_guide', 1 },
      { 'fs_brokeassreel', 300 },
      { 'fs_cheapmono', 85 },
      { 'fs_valuecastrod', 1100 },
      { 'fs_no1', 55 },
      { 'fs_fishfinder', 1000 },
      { 'fs_bread', 25 },
      { 'fs_maggots', 25 },
    },
    siphon_tax = 0.08,
  },

  {
    uid = '247-market',
    name = '24/7 Market',
    tax_name = '24/7 Market',

    items = {
      -- Utility items
      { 'lighter_247', 200 },


      -- Drinks

      { 'water', 40 },
      { 'coffee', 40 },
      { 'icetea', 40 },
      { 'lemonlimonad', 40 },
      { 'inv_pack_mini_1', 100 },

      -- Food
      { 'scooby_snacks', 5 },
      { 'pdonut', 50 },
      { 'sandwich', 40 },
      { 'taco_kit', 25 },

      -- Misc
      { 'cigarette', 20 },
      { 'carrepairkit', 150 },
      { 'key_chain', 5 },
      { 'prop_porn_mag_02', 150 },
      { 'prop_porn_mag_03', 150 },
      { 'xxscondom', 10 },
    },

    purchase_callback = itemPurchasedMarket,

    siphon_tax = 0.08,
  },

  {
    uid = 'bayhardware',
    name = 'Bay Hardware',

    items = {
      { 'ziptie', 150 },
      { 'wbody|WEAPON_HAMMER', 75, },
      { 'wbody|WEAPON_WRENCH', 100, },
      { 'wbody|WEAPON_FIREEXTINGUISHER', 500, },
      { 'screwdriver', 25, },
      { 'garden_trowel', 25, },
      { 's_suitcase2', 2000},
      { 'twine', 1, },
      { 'paint_thinner', 1000, },
      { 'bank_drill', 3500, },
      { 'wire_cutters', 1000, },
      { 'wbody|WEAPON_CROWBAR', 1750, },
      { 'solvent', 250, },
      { 'pliers', 250, },
    },

    allow_debit = true,
    siphon_tax = 0.08,
  },

  {
    uid = 'calicojacks',
    name = 'Calico Jacks',
    items = {
		  { 'scuba_kit', 300, },
      { 'gold_coin', 90, },
		  { 'common_artifact', 120, },
		  { 'rare_artifact', 170, },
    },
    allow_selling = true,
  },

  {
    uid = 'metal-merchant',
    name = 'Metal Merchant',
    items = {
      { 'gold_ingot', 70, },
      { 'aluminum', 35, },
      { 'steel', 47, },
      { 'iron', 50, },
      { 'titanium', 65, },
      { 'platinum', 125, },
    },
    selling_only = true,
    selling_coefficient = 1.0,
    selling_coefficient_felon = 0.70,
  },

  {
    uid = 'fence',
    name = 'Fence',
    items = {
      { 'catalytic_converter', 3400, },
      { 'car_battery', 800, },
    },
    selling_only = true,
    selling_coefficient = 1.0,
    chop_level_modify = true,
  },

  {
    uid = 'ammunation-all',
    name = 'Ammunation',
    items = {
      { 'wbody|WEAPON_PISTOL', 950 },
      { 'wbody|WEAPON_PISTOL_MK2', 1550 },
      { 'ammo_40sw', 1 },
      { 'wbody|WEAPON_VINTAGEPISTOL', 1100 },
      { 'ammo_32acp', 1 },
      { 'wbody|WEAPON_SNSPISTOL', 975 },
      { 'ammo_380acp', 1 },
      { 'wbody|WEAPON_REVOLVER', 2200 },
      { 'ammo_44mag', 1 },
      { 'wbody|WEAPON_DOUBLEACTION', 6000 },
      { 'ammo_38lc', 1 },
      { 'ammo_50ae', 1 },
      { 'wbody|WEAPON_PUMPSHOTGUN', 20000 },
      { 'ammo_12ga', 1 },
      { 'wbody|WEAPON_FLASHLIGHT', 25 },
      { 'wbody|WEAPON_GOLFCLUB', 100 },
      { 'wbody|WEAPON_BAT', 150 },
      { 'wbody|WEAPON_POOLCUE', 100 },
      { 'wbody|WEAPON_KNIFE', 250 },
      { 'component_flashlight', 300 },
      { 'weapon_enable_kit', 500 },
      { 'tool_wpn_disable', 5000 },

      { 'wtint_n_0', 200 },
      { 'wtint_n_1', 200 },
      -- { 'wtint_n_2', 200 }, -- Gold
      { 'wtint_n_3', 200 },
      { 'wtint_n_4', 200 },
      { 'wtint_n_5', 200 },
      { 'wtint_n_6', 200 },
      { 'wtint_n_7', 200 },

      { 'wtint_mk2_0', 200 },
      { 'wtint_mk2_1', 200 },
      { 'wtint_mk2_2', 200 },
      { 'wtint_mk2_3', 200 },
      { 'wtint_mk2_4', 200 },
      { 'wtint_mk2_5', 200 },
      { 'wtint_mk2_6', 200 },
      { 'wtint_mk2_7', 200 },
      { 'wtint_mk2_8', 200 },
      { 'wtint_mk2_9', 200 },
      { 'wtint_mk2_10', 200 },
      { 'wtint_mk2_11', 200 },
      { 'wtint_mk2_12', 200 },
      { 'wtint_mk2_13', 200 },
      { 'wtint_mk2_14', 200 },
      { 'wtint_mk2_15', 200 },
      { 'wtint_mk2_16', 200 },
      { 'wtint_mk2_17', 200 },
      { 'wtint_mk2_18', 200 },
      { 'wtint_mk2_19', 200 },
      { 'wtint_mk2_20', 200 },
      { 'wtint_mk2_21', 200 },
      { 'wtint_mk2_22', 200 },
      -- { 'wtint_mk2_23', 200 }, -- Gold
      { 'wtint_mk2_24', 200 },
      { 'wtint_mk2_25', 200 },
      { 'wtint_mk2_26', 200 },
      { 'wtint_mk2_27', 200 },
      { 'wtint_mk2_28', 200 },
      { 'wtint_mk2_29', 200 },
      { 'wtint_mk2_30', 200 },
      { 'wtint_mk2_31', 200 },
    },
    suppress_ux_check = true,
    siphon_tax = 0.08,
  },

  {
    uid = 'fireworks',
    name = 'Fireworks',
    items = fireworks_store_items,
    suppress_ux_check = true,
  },

  {
    uid = 'tannery',
    name = 'Tannery',
    items = {
      { 'deer_hide', 125, },
      { 'boar_hide', 225, },
      { 'rabbit_hide', 325, },
      { 'rat_hide', 525, },
    },
    selling_only = true,
    selling_coefficient = 1.0,
    selling_coefficient_felon = 0.70,
  },

  {
    uid = 'meat-sales',
    name = 'Meat Sales',
    items = {
      { 'deer_meat', 75, },
      { 'mlion_meat', 175, },
      { 'boar_meat', 175, },
      { 'coyote_meat', 225, },
      { 'rabbit_meat', 375, },
      { 'rat_meat', 475, },
      { 'animal_bones', 225, },
      { 'fishmeat_salmon', 10, },
      { 'fishmeat_slimy', 2, },
      { 'fishmeat_white', 10, },
    },
    selling_only = true,
    selling_coefficient = 1.0,
    selling_coefficient_felon = 0.70,
  },

  {
    uid = 'illegal-hunting-sell',
    name = 'Poaching Sales',
    items = {
      { 'mlion_hide', 325, },
      { 'coyote_hide', 425, },
      { 'mlion_head', 2000, },
      { 'coyote_fang', 3000, },
    },
    selling_only = true,
    selling_coefficient = 1.0,
    selling_coefficient_felon = 1.0,
  },

  {
    uid = 'illegal-hunting-buy',
    name = 'Poaching Sales',
    items = {
      { 'hunting_bait_1', 75, },
      { 'hunting_bait_3', 75, },
      { 'fs_hugecutbait', 55 },
    },
  },

  {
    uid = 'fence-items',
    permission = { 'SFT Pawn', 'management' },
    name = 'Fence Sales',
    items = {
      { 'lighter_golden', 15000, },
      { 'diamond_grillz', 30000, },
      { 'gold_chalice', 100000, },
      { 'golden_moose', 165000, },
      { 'faberge_egg', 300000, },
    },
    selling_only = true,
    selling_coefficient = 1.0,
  },

  {
    uid = 'illegal-weapons',
    name = 'Gang Tools',
    items = {
      { 'wbody|WEAPON_SWITCHBLADE', 500, },
      { 'wbody|WEAPON_PISTOL50', 1500, },
      { 'wbody|WEAPON_BATTLEAXE', 750, },
      { 'wbody|WEAPON_BOTTLE', 225, },
      { 'wbody|WEAPON_DAGGER', 2000, },

      --[[
      {
        'wbody|WEAPON_PISTOLXM3',
        5000,
        function(character)
          return false
        end
      }
      ]]
    },
  },

  {
    uid = 'illegal-items',
    name = 'Sandy Scumbag Supplies',
    items = {
      { 'meth_kit', 500, },
			{ 'weapon_disable_kit', 250, },
			{ 'speedbomb', 150000, },
    },
  },

  {
    uid = 'vineyard',
    name = 'Wine Sales',
    items = {
      { 'bitter_wine', 220, },
      { 'wine', 650, },
    },
    selling_only = true,
    selling_coefficient = 1.0,
    selling_coefficient_felon = 0.70,
  },

  {
    uid = 'mechanic-parts',
    name = 'Mechanic Shop',
    permission = 'mechanic.repair',
    items = {
      { 'repair_parts', 15 },
      { 'tirerepairkit', 150 },
    },
  },

  {
    uid = 'locksmith',
    name = 'Locksmith',
    items = {
      { 'lockpick', 150, },
      { 'safe_dialer', 200 },
    },
  },

  {
    uid = 'vangelico',
    name = 'Vangelico Jewelry',
    items = {
      { 'sapphire', 38, },
		  { 'ruby', 60, },
		  { 'diamond', 105, },
		  { 'blood_diamond', 500, },
    },
    allow_selling = true,
  },

  {
    uid = 'vangelico-jewelry',
    name = 'Vangelico Jewelry',
    items = {
      { 'clth_ring_m_c', 3500 },
      { 'clth_ring_m_h', 4000 }, -- Bronze

      { 'clth_ring_m_d', 5500 },
      { 'clth_ring_m_i', 6000 }, -- Tungsten

      { 'clth_ring_m_a', 8500 },
      { 'clth_ring_m_j', 9000 }, -- Silver

      { 'clth_ring_m_b', 10000 },
      { 'clth_ring_m_g', 11000 },
      { 'clth_ring_m_k', 12000 }, -- Gold

      { 'clth_ring_m_e', 7500 },
      { 'clth_ring_m_f', 8000 }, -- Other tungsten

      { 'clth_ring_f_c', 5000 },
      { 'clth_ring_f_b', 5500 },
      { 'clth_ring_f_f', 6000 }, -- Zirconia

      { 'clth_ring_f_d', 10000 },
      { 'clth_ring_f_a', 11000 },
      { 'clth_ring_f_e', 12000 },
      { 'clth_ring_f_g', 15000 }, -- Diamond

      { 'clth_vangelico_1a', 3500000 },
      { 'clth_vangelico_2a', 30000 },
      { 'clth_vangelico_2b', 30000 },
      { 'clth_vangelico_2c', 30000 },
      { 'clth_vangelico_3a', 55000 },
      { 'clth_vangelico_3b', 55000 },
      { 'clth_vangelico_3c', 55000 },
      { 'clth_vangelico_4a', 45000 },
      { 'clth_vangelico_4b', 45000 },
      { 'clth_vangelico_5a', 85000 },
      { 'clth_vangelico_5b', 85000 },
      { 'clth_vangelico_6a', 65000 },
      { 'clth_vangelico_6b', 125000 },
      { 'clth_vangelico_6c', 75000 },
      { 'clth_vangelico_6d', 109000 },
      { 'clth_vangelico_6e', 93000 },
      { 'clth_vangelico_6f', 65000 },
      { 'clth_vangelico_6g', 125000 },
      { 'clth_vangelico_6h', 75000 },
      { 'clth_vangelico_6i', 109000 },
      { 'clth_vangelico_6j', 93000 },
      { 'clth_vangelico_7a', 30000 },
      { 'clth_vangelico_7b', 60000 },
      { 'clth_vangelico_7c', 25000 },
      { 'clth_vangelico_7d', 52000 },
      { 'clth_vangelico_7e', 44000 },
      { 'clth_vangelico_7f', 30000 },
      { 'clth_vangelico_7g', 60000 },
      { 'clth_vangelico_7h', 25000 },
      { 'clth_vangelico_7i', 52000 },
      { 'clth_vangelico_7j', 44000 },
      { 'clth_vangelico_8a', 135000 },
      { 'clth_vangelico_8b', 120000 },
      { 'clth_vangelico_8c', 135000 },
      { 'clth_vangelico_8d', 120000 },
      { 'clth_vangelico_8e', 110000 },
      { 'clth_vangelico_8f', 110000 },
      { 'clth_vangelico_9a', 450000 },
      { 'clth_vangelico_9b', 450000 },
      { 'clth_vangelico_9c', 450000 },
      { 'clth_vangelico_9d', 450000 },
      { 'clth_vangelico_10a', 750000 },
      { 'clth_vangelico_11a', 95000 },
      { 'clth_vangelico_12a', 300000 },
      { 'clth_vangelico_12b', 300000 },
      { 'clth_vangelico_13a', 150000 },
      { 'clth_vangelico_13b', 150000 },
      { 'clth_vangelico_14a', 150000 },
      { 'clth_vangelico_14b', 150000 },
      { 'clth_vangelico_15a', 175000 },
      { 'clth_vangelico_15b', 120000 },
      { 'clth_vangelico_15c', 200000 },
      { 'clth_vangelico_16a', 125000 },
      { 'clth_vangelico_16b', 75000 },
      { 'clth_vangelico_16c', 90000 },
      { 'clth_vangelico_16d', 75000 },
    },
    allow_debit = true,
  },

  -- Trojan Armor storefront
  {
    uid = 'trojan-storefront',
    name = 'Trojan Armor',
    items = {},
    stockable = true,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },
  {
    uid = 'trojan-supply',
    name = 'Trojan Armor Supply',
    permission = 'trojan.employee',
    items = {
      { 'key_chain_trojan', 1, },
    },
  },

  -- cinco de Mayo shop
  {
    uid = 'lilmec-supply',
    name = 'Cinco De Mayo',

    items = {
      { 'giftbag_Cinco_de_mayo', 1000, },
      { 'mexico_lighter', 1000, },
      { 'pinata_smash_keychain', 1000, },
      { 'sombrero_keychain', 1000, },
      { 'cinco_liv_vigerozx', 150000, },
      { 'prop_cinco_de_mayo_neon', 100000, },
      { 'bl_cinco_painting', 130000, },
    },
    allow_debit = true,
  },

  -- B1 LSIA Shop
  {
    uid = 'b1lsia-store',
    name = 'B1 LSIA',

    items = {
      { 'b1postr01_25', 10000, },
      { 'b1postr02_25', 10000, },
      { 'b1_scm', 30000, },
      { 'b1_keychain', 5000, },
      { 'b1_ltr_a', 5000, },
      { 'binoculars', 300}
    },
    allow_debit = true,
  },

  -- Mug shop
  {
    uid = 'mugs',
    name = 'Crucial Fix Mugs',
    items = {
      { 'dr_mug_247_grey', 2500 },
      { 'dr_mug_247_white', 2500 },
      { 'dr_mug_badger', 2500 },
      { 'dr_mug_beanmachine_grey', 2500 },
      { 'dr_mug_beanmachine_white', 2500 },
      { 'dr_mug_blondedlossantos_grey', 2500 },
      { 'dr_mug_blondedlossantos_white', 2500 },
      { 'dr_mug_burgershot_grey', 2500 },
      { 'dr_mug_burgershot_white', 2500 },
      { 'dr_mug_celltowa', 2500 },
      { 'dr_mug_channelx_grey', 2500 },
      { 'dr_mug_channelx_white', 2500 },
      { 'dr_mug_chihuahua_grey', 2500 },
      { 'dr_mug_chihuahua_white', 2500 },
      { 'dr_mug_climaticcandidchannel_grey', 2500 },
      { 'dr_mug_climaticcandidchannel_white', 2500 },
      { 'dr_mug_cluckinbell_grey', 2500 },
      { 'dr_mug_cluckinbell_white', 2500 },

      { 'dr_mug_cnt_grey', 2500 },
      { 'dr_mug_cnt_white', 2500 },
      { 'dr_mug_crucialfix_grey', 2500 },
      { 'dr_mug_crucialfix_white', 2500 },
      { 'dr_mug_dynasty8_green', 2500 },
      { 'dr_mug_dynasty8_grey', 2500 },
      { 'dr_mug_eastlosfm', 2500 },
      { 'dr_mug_flylofm_grey', 2500 },
      { 'dr_mug_flylofm_white', 2500 },
      { 'dr_mug_fruit_grey', 2500 },
      { 'dr_mug_fruit_white', 2500 },

      { 'dr_mug_globe_oil', 2500 },
      { 'dr_mug_gopostal', 2500 },
      { 'dr_mug_krapea_grey', 2500 },
      { 'dr_mug_krapea_white', 2500 },
      { 'dr_mug_lifeinvader_grey', 2500 },
      { 'dr_mug_lifeinvader_white', 2500 },
      { 'dr_mug_lombbank', 2500 },
      { 'dr_mug_lossantosrockradio_grey', 2500 },
      { 'dr_mug_lst', 2500 },
      { 'dr_mug_ltd', 2500 },
      { 'dr_mug_morsmutual_grey', 2500 },
      { 'dr_mug_morsmutual_white', 2500 },
      { 'dr_mug_nonstoppopfm', 2500 },
      { 'dr_mug_penris_grey', 2500 },

      { 'dr_mug_penris_white', 2500 },
      { 'dr_mug_postop_grey', 2500 },
      { 'dr_mug_postop_white', 2500 },
      { 'dr_mug_radiolossantos_grey', 2500 },
      { 'dr_mug_radiolossantos_white', 2500 },
      { 'dr_mug_radiomirrorpark_grey', 2500 },
      { 'dr_mug_radiomirrorpark_white', 2500 },
      { 'dr_mug_raine_grey', 2500 },
      { 'dr_mug_raine_white', 2500 },
      { 'dr_mug_rebelradio_grey', 2500 },
      { 'dr_mug_rebelradio_white', 2500 },
      { 'dr_mug_ron', 2500 },
      { 'dr_mug_space103_2_grey', 2500 },

      { 'dr_mug_space103_2_white', 2500 },
      { 'dr_mug_std', 2500 },
      { 'dr_mug_theblueark_grey', 2500 },
      { 'dr_mug_theblueark_white', 2500 },
      { 'dr_mug_thelab_grey', 2500 },
      { 'dr_mug_thelab_white', 2500 },
      { 'dr_mug_thelowdown91_1_grey', 2500 },
      { 'dr_mug_thelowdown91_1_white', 2500 },

      { 'dr_mug_tinkle_grey', 2500 },
      { 'dr_mug_tinkle_white', 2500 },
      { 'dr_mug_vinewoodboulevardradio_grey', 2500 },
      { 'dr_mug_vinewoodboulevardradio_white', 2500 },
      { 'dr_mug_westcoastclassics_grey', 2500 },
      { 'dr_mug_westcoastclassics_white', 2500 },
      { 'dr_mug_worldwidefm', 2500 },
      { 'dr_mug_xero', 2500 },
      --{ 'dr_mug_whiz', 2500 }, -- to add to BCSB loot
      { 'dr_mug_vomfeuer_grey', 2500 },
      { 'dr_mug_vomfeuer_white', 2500 },
    },
    suppress_ux_check = true,
    allow_debit = true,
    siphon_tax = 0.08,
  },

  -- tubes
  {
    uid = 'tubes',
    name = 'Tubes for days',
    items = {
      { 'banana', 15000 },
      { 'inflatable', 15000 },
      { 'parasailing', 15000 },
      { 'ski', 15000 },
      { 'circle', 15000 },
    },
    suppress_ux_check = true,
    allow_debit = true,
    siphon_tax = 0.08,
  },

  -- Aracde
  {
    uid = 'arcade-exchange',
		name = 'Arcade token Exchange',
		items = {
			{ 'arcade_token', 1 },
		},
    allow_debit = true,
    siphon_tax = 0.08,
	},

  {
    uid = 'arcade-shop',
		name = 'Arcade Ticket Shop',
		items = {
			{ 'food_blowpopcherry', 100 },
      { 'food_ringpop', 100 },
      { 'food_blowpopapple', 100 },
      { 'food_nerds', 100 },
      { 'food_dots', 100 },
      { 'food_smarties', 100 },
      { 'food_sourcrawlers', 100 },
      { 'food_sourheads', 100 },
      { 'food_candy_chain', 100 },
      { 'banana', 15000 },
		},
    currency = 'arcade_ticket',
	},

  -- Vespucci Beach Market boilerplates
  {
    uid = 'vbmarket-A1',
    name = 'Vespucci Beach Market A1',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-A2',
    name = 'Vespucci Beach Market A2',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-A3',
    name = 'Vespucci Beach Market A3',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-A4',
    name = 'Vespucci Beach Market A4',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-B1',
    name = 'Vespucci Beach Market B1',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-B2',
    name = 'Vespucci Beach Market B2',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-B3',
    name = 'Vespucci Beach Market B3',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-B4',
    name = 'Vespucci Beach Market B4',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-C1',
    name = 'Vespucci Beach Market C1',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-C2',
    name = 'Vespucci Beach Market C2',
    items = vbmarket_gift_bags, -- TODO: CHANGE THIS IF THE ADMIN SHOP EVER MOVES
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-C3',
    name = 'Vespucci Beach Market C3',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-C4',
    name = 'Vespucci Beach Market C4',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-D1',
    name = 'Vespucci Beach Market D1',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-D2',
    name = 'Vespucci Beach Market D2',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-D3',
    name = 'Vespucci Beach Market D3',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-D4',
    name = 'Vespucci Beach Market D4',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-E1',
    name = 'Vespucci Beach Market E1',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-E2',
    name = 'Vespucci Beach Market E2',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-E3',
    name = 'Vespucci Beach Market E3',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-E4',
    name = 'Vespucci Beach Market E4',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-F1',
    name = 'Vespucci Beach Market F1',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-F2',
    name = 'Vespucci Beach Market F2',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-F3',
    name = 'Vespucci Beach Market F3',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'vbmarket-F4',
    name = 'Vespucci Beach Market F4',
    items = vbmarket_base_items,
    stockable = true,
    stockable_service_percent = vbmarket_service_charge,
    stockable_siphon_block = vbmarket_siphon_block,
    siphon_tax = 0.08,
    allow_debit = true,
    allow_dirty_cash = true,
  },

  {
    uid = 'fs-fish-sales',
    name = 'Fishmonger',
    items = {
      { 'fs_minnow', 175 },
      { 'fs_crab', 87 },
      { 'fs_tuna', 250 },
      { 'fs_haddock', 175 },
      { 'fs_pollock', 175 },
      { 'fs_trout', 175 },
      { 'fs_stripedbass', 200 },
      { 'fs_salmon', 200 },
      { 'fs_northernpike', 225 },
      { 'fs_garfish', 185 },
      { 'fs_crappie', 175 },
      { 'fs_rainbowtrout', 175 }
    },
    selling_only = true,
    selling_coefficient = 1.0,
    selling_coefficient_felon = 0.70,
  },

  {
    uid = 'fs-illegal-fish-sales',
    name = 'Fish Poaching',
    items = {
      { 'fs_turtle', 300 },-- illegal
      { 'fs_dolphin', 350 },-- illegal
      { 'fs_shark', 350 },-- illegal
    },
    selling_only = true,
    selling_coefficient = 1.0,
    selling_coefficient_felon = 1.0,
  },

  {
    uid = 'fs-fishing-supplies',
    name = 'Mikes Fishing Equipment',
    items = {
      { 'fs_fishfinder', 500 },
      { 'fs_rockbottomreel', 650 },
      { 'fs_brokeassreel', 100 },
      { 'fs_linesnifferreel', 350 },
      { 'fs_fishrusreel', 225 },
      { 'fs_thunderreel', 750 },
      { 'fs_zillareel', 2000 },
      { 'fs_kingbraid', 75 },
      { 'fs_cheapmono', 25 },
      { 'fs_bitesizemono', 35 },
      { 'fs_mobeymono', 45 },
      { 'fs_noodlebraid', 55 },
      { 'fs_lightningbraid', 65 },
      { 'fs_no1', 35 },
      { 'fs_no2', 35 },
      { 'fs_no3', 35 },
      { 'fs_no6', 35 },
      { 'fs_no10', 55 },
      { 'fs_towhook', 75 },
      { 'fs_valuecastrod', 700 },
      { 'fs_elementalrod', 1400 },
      { 'fs_nerorod', 2100 },
      { 'fs_brutasrod', 2800 },
      { 'fs_zeusrod', 3500 },
      { 'fs_magnumxlrod', 4200 },
    },
    allow_debit = true,
    siphon_tax = 0.08,
  },

  {
    uid = 'fs-fishing-bait',
    name = 'Bait Store',
    items = {
      { 'fs_nightworms', 1 },
      { 'fs_redworms', 1 },
      { 'fs_leech', 1 },
      { 'fs_waxworms', 1 },
      { 'fs_mealworms', 1 },
      { 'fs_bread', 1 },
      { 'fs_maggots', 1 },
    },
    allow_debit = true,
    siphon_tax = 0.08,
  },

  {
    uid = 'mikes-export-m',
    name = 'Mikes Management Export',
    permission = { 'Mikes Sporting Goods', 'management' },
    items = {
      { 'deer_meat', 83, },
      { 'boar_meat', 193, },
      { 'rabbit_meat', 413, },
      { 'rat_meat', 523, },
      { 'animal_bones', 248, },
      { 'fishmeat_salmon', 11, },
      { 'fishmeat_slimy', 3, },
      { 'fishmeat_white', 11, },
      { 'deer_hide', 138, },
      { 'boar_hide', 248, },
      { 'rabbit_hide', 358, },
      { 'rat_hide', 578, },
      { 'fs_crab', 90, },
      { 'fs_minnow', 180, },
      { 'fs_haddock', 180, },
      { 'fs_pollock', 180, },
      { 'fs_trout', 180, },
      { 'fs_rainbowtrout', 180, },
      { 'fs_crappie', 180, },
      { 'fs_garfish', 190, },
      { 'fs_stripedbass', 205, },
      { 'fs_salmon', 205, },
      { 'fs_northernpike', 230, },
      { 'fs_tuna', 255, },
    },
    selling_only = true,
    selling_coefficient = 1.0,
  },

  {
    uid = 'masquerade-open-mask',
    name = 'Masquerade mask',
    items = {
      { 'clth_mask_o_a', 1500 }, -- Black mask
      { 'clth_mask_o_b', 1500 }, -- Black Bandana Mask
      { 'clth_mask_o_c', 1500 }, -- Bag mask
    },
    allow_debit = true,
  },
}

-- Masquerade - mask items

local mask_store = {
  uid = 'masquerade',
  name = "Masquerade",
  items = {},
  stockable = true,
  storage_base_allow_siphon_all = true,
}

local function maskStoreCallback(character, item_id)
  local mask_config = clth_mask_items[item_id]

  if not mask_config or not mask_config.groups or mask_config.prevent_purchase then
    return false
  end

  if not is_dev and not character.hasGroup(mask_config.groups) then
    return false
  end

  local has_permission = false

  for _, group in pairs(mask_config.groups) do
    if businessCanAccess(character.source, group, 'masks') then
      has_permission = true
      break
    end
  end

  return is_dev or has_permission
end

local mask_item_ids = {}

for mask_item_id, mask_config in pairs(clth_mask_items) do
  table.insert(mask_item_ids, mask_item_id)
end

table.sort(mask_item_ids)

for _, mask_item_id in pairs(mask_item_ids) do
  local mask_config = clth_mask_items[mask_item_id]

  if mask_config.groups and not mask_config.prevent_purchase then
    table.insert(mask_store.items, {
      mask_item_id,
      5000,
      maskStoreCallback
    })
  end
end

table.insert(item_stores, mask_store)

-- /Frosty's Fabrics

-- Vangelico - chain items

local chain_store = {
  uid = 'vangelico-chains',
  name = "Vangelico Chains",
  items = {},
  stockable = true,
  storage_base_allow_siphon_all = true,
}

local function chainStoreCallback(character, item_id)
  local chain_config = clth_chain_items[item_id]

  if not chain_config or not chain_config.groups or chain_config.prevent_purchase then
    return false
  end

  if not is_dev and not character.hasGroup(chain_config.groups) then
    return false
  end

  local has_permission = false

  for _, group in pairs(chain_config.groups) do
    if businessCanAccess(character.source, group, 'chains') then
      has_permission = true
      break
    end
  end

  return is_dev or has_permission
end

local chain_item_ids = {}

for chain_item_id, chain_config in pairs(clth_chain_items) do
  table.insert(chain_item_ids, chain_item_id)
end

table.sort(chain_item_ids)

for _, chain_item_id in pairs(chain_item_ids) do
  local chain_config = clth_chain_items[chain_item_id]

  if chain_config.groups and not chain_config.prevent_purchase then
    table.insert(chain_store.items, {
      chain_item_id,
      chain_config.store_price or 25000,
      chainStoreCallback
    })
  end
end

table.insert(item_stores, chain_store)

-- /Vangelico - chain items

local price_cache = {}

Citizen.CreateThread(function()
  -- Block until dynamic items are registered
  while not dynamic_items_registered do
    Citizen.Wait(0)
  end

  for _, store in pairs(item_stores) do
    if (not store.items or #store.items == 0) and not store.stockable then
      print('^1item store defined as non-stockable and with no items', store.uid, '^7')
    end

    if store.items and #store.items > 25 and not store.suppress_ux_check then
      print('^3item store defined with > 25 items. Consider refactoring for better UX', store.uid, '^7')
    end

    for _, item in pairs(store.items or {}) do
      local item_id, price = table.unpack(item)

      if not price_cache[item_id] then
        price_cache[item_id] = {}
      end

      local store_mode = 'buy'

      if store.allow_selling then
        store_mode = 'buy / sell'
      end

      if store.selling_only then
        store_mode = 'sell'
      end

      table.insert(price_cache[item_id], {
        store.uid, price, store_mode
      })
    end
  end

  for item_id, items in pairs(price_cache) do
    if #items > 1 then
      local sell_prices = {}
      local buy_prices = {}

      for _, item in pairs(items) do
        if string.match(item[3], 'sell') then
          table.insert(sell_prices, item[2])
        else
          table.insert(buy_prices, item[2])
        end
      end

      if #sell_prices > 0 then
        local flag = false

        for _, buy_price in pairs(buy_prices) do
          for _, sell_price in pairs(sell_prices) do
            if sell_price > buy_price then
              flag = true
            end
          end
        end

        if flag then
          print_r('^1item for sale with multiple prices. Review data.', items, '^7')
        end
      end
    end
  end
end)
