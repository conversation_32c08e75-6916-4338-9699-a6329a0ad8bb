local timer = 1 --in minutes - Set the time during the player is outlaw
local wanted_time = 15 -- in seconds how long between any wanted alert

local has_suppressor = false
local suppressors = {
  `COMPONENT_AT_AR_SUPP`,
  `COMPONENT_AT_AR_SUPP_02`,
  `COMPONENT_AT_PI_SUPP`,
  `COMPONENT_AT_PI_SUPP_02`,
  `COMPONENT_AT_SR_SUPP`,
  `COMPONENT_AT_SR_SUPP_03`,
  `COMPONENT_CERAMICPISTOL_SUPP`,
  `COMPONENT_SERVICEPISTOL_SUPP`,
  `COMPONENT_AT_VP897_SUPP`,
  `COMPONENT_PISTOLXM3_SUPP`,
}

local ignored_hashes = {
  [0] = true, -- WEAPON_INVALID
  [`WEAPON_UNARMED`] = true,
  [`WEAPON_OBJECT`] = true,
  [`WEAPON_PLASMAP`] = true,
  [`WEAPON_ANIMAL`] = true,
  [`WEAPON_COUGAR`] = true,
  [`WEAPON_SNOWBALL`] = true,
  [`WEAPON_PETROLCAN`] = true,
  [`WEAPON_BALL`] = true,
  [`WEAPON_FLAREGUN`] = true,
  [`WEAPON_PAINTBALL`] = true,
  [`WEAPON_FIREEXTINGUISHER`] = true,
  [`WEAPON_SNIPERRIFLE`] = true,
  [`WEAPON_STUNGUN`] = true,
  [`WEAPON_HOTDOG`] = true,
  [`WEAPON_LIGHTSABER`] = true,
  [`WEAPON_LIGHTSABER_BLUE`] = true,
  [`WEAPON_LIGHTSABER_PURPLE`] = true,
  [`WEAPON_LIGHTSABER_RED`] = true,
  [`WEAPON_LIGHTSABER_YELLOW`] = true,
  [`WEAPON_METALDETECTOR`] = true,
  [`WEAPON_RAZORBACK`] = true,
  [`WEAPON_RAYSHOTGUN`] = true,
  [`WEAPON_NEEDLER`] = true,
  [`WEAPON_NEEDLER2`] = true,
  [`WEAPON_STONE_HATCHET`] = true,
  [`WEAPON_TOMAHAWK`] = true,
  [`WEAPON_HOBBYHORSE`] = true,
  [`WEAPON_DRAGON_KATANA_BLUE`] = true,
  [`WEAPON_KATANA_2`] = true,
  [`WEAPON_CANDYCANE`] = true,
  [`WEAPON_CANDYBOMB`] = true,
  [`WEAPON_FLARE`] = true,
  [`WEAPON_MOLOTOV`] = true,
}

local automatic_guns = {
  [`WEAPON_SMG_MK2`] = 'WEAPON_SMG_MK2',
  [`WEAPON_APPISTOL`] = 'WEAPON_APPISTOL',
  [`WEAPON_MICROSMG`] = 'WEAPON_MICROSMG',
  [`WEAPON_SMG`] = 'WEAPON_SMG',
  [`WEAPON_UMP45`] = 'WEAPON_UMP45',
  [`WEAPON_ASSAULTSMG`] = 'WEAPON_ASSAULTSMG',
  [`WEAPON_MINISMG`] = 'WEAPON_MINISMG',
  [`WEAPON_MACHINEPISTOL`] = 'WEAPON_MACHINEPISTOL',
  [`WEAPON_TECPISTOL`] = 'WEAPON_TECPISTOL',
  [`WEAPON_COMBATPDW`] = 'WEAPON_COMBATPDW',
  [`WEAPON_ASSAULTRIFLE`] = 'WEAPON_ASSAULTRIFLE',
  [`WEAPON_ASSAULTRIFLE2`] = 'WEAPON_ASSAULTRIFLE2',
  [`WEAPON_ASSAULTRIFLE_MK2`] = 'WEAPON_ASSAULTRIFLE_MK2',
  [`WEAPON_CARBINERIFLE`] = 'WEAPON_CARBINERIFLE',
  [`WEAPON_CARBINERIFLE_MK2`] = 'WEAPON_CARBINERIFLE_MK2',
  [`WEAPON_CARBINERIFLET`] = 'WEAPON_CARBINERIFLET',
  [`WEAPON_CARBINERIFLEMK2T`] = 'WEAPON_CARBINERIFLEMK2T',
  [`WEAPON_ADVANCEDRIFLE`] = 'WEAPON_ADVANCEDRIFLE',
  [`WEAPON_SPECIALCARBINE`] = 'WEAPON_SPECIALCARBINE',
  [`WEAPON_SPECIALCARBINE_MK2`] = 'WEAPON_SPECIALCARBINE_MK2',
  [`WEAPON_BULLPUPRIFLE`] = 'WEAPON_BULLPUPRIFLE',
  [`WEAPON_COMPACTRIFLE`] = 'WEAPON_COMPACTRIFLE',
  [`WEAPON_MILITARYRIFLE`] = 'WEAPON_MILITARYRIFLE',
  [`WEAPON_MG`] = 'WEAPON_MG',
  [`WEAPON_GUSENBERG`] = 'WEAPON_GUSENBERG',
  [`WEAPON_COMBATMG`] = 'WEAPON_COMBATMG',
  [`WEAPON_COMBATMG_MK2`] = 'WEAPON_COMBATMG_MK2',
  [`WEAPON_BULLPUPRIFLE_MK2`] = 'WEAPON_BULLPUPRIFLE_MK2',
  [`WEAPON_MINIGUN`] = 'WEAPON_MINIGUN',
  [`WEAPON_SERVICEPISTOL_AUTO`] = 'WEAPON_SERVICEPISTOL_AUTO',
}

local last_gunshot_time = 0

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1000)

    local ped = PlayerPedId()
    local selected_weapon = GetSelectedPedWeapon(ped)

    if ignored_hashes[selected_weapon] then
      has_suppressor = false
    else
      local suppressor_found = false

      for _, suppressor_hash in ipairs(suppressors) do
        if HasPedGotWeaponComponent(ped, selected_weapon, suppressor_hash) then
          suppressor_found = true
        end
      end

      has_suppressor = suppressor_found
    end
  end
end)

local wanted_time_left = 0

Citizen.CreateThread(function() -- time decrease thread
  while true do
    Citizen.Wait(1000)
    if wanted_time_left > 0 then
      wanted_time_left = wanted_time_left - 1
    end
  end
end)

-- Fight detection
--[[
Citizen.CreateThread(function()
  while true do
    Wait(0)
    if IsPedInMeleeCombat(PlayerPedId()) then
      if
        not core.me().hasGroup('LEO') and
        wanted_time_left < 1 and
        (not ignored_hashes[GetSelectedPedWeapon(PlayerPedId())] or GetSelectedPedWeapon(PlayerPedId()) == `WEAPON_UNARMED`)
      then
        local plyPos = GetEntityCoords(PlayerPedId(), true)
        local startHealth = GetEntityHealth(PlayerPedId())

        Citizen.Wait(2000)

        local endHealth = GetEntityHealth(PlayerPedId())

        if startHealth ~= endHealth then
          if tZones.isInsideMatchedZone({ 'prisonPen' }) then
            TriggerEvent('blrp_core:client:prepareAlert', {
              is_response = true,
              service_name = 'DOC',
              can_accept = false,
              coords = plyPos,
              badge = 'Priority 4',
              badge_style = 'primary',
              title = "Fight in progress",
              icon = 'fad fa-phone',
              show_for = 5000,
              allow_gps = false,

              radius = true,
              radius_fadetime = 30,
              radius_radius = 10.0,
              radius_shift = 3,
            })
          end

          TriggerEvent('blrp_core:client:prepareAlert', {
            is_response = true,
            service_name = 'Police',
            can_accept = false,
            coords = plyPos,
            badge = 'Priority 4',
            badge_style = 'primary',
            title = "Fight in progress",
            icon = 'fad fa-phone',
            show_for = 5000,
            allow_gps = false,

            radius = true,
            radius_fadetime = 30,
            radius_radius = 90.0,
            radius_shift = 70,
          })

          wanted_time_left = wanted_time
        end
      end
    end
  end
end)
]]--

function gunshotDetectionLoop()
  if not IsPedShooting(PlayerPedId()) or wanted_time_left > 1 then
    return
  end

  local current_weapon = GetSelectedPedWeapon(PlayerPedId())
  local player_coords = GetEntityCoords(PlayerPedId(), true)

  if ignored_hashes[current_weapon] then
    return
  end

  last_gunshot_time = GetGameTimer()

  local nearby_house, inside_house, door_coords = exports.housing:NearbyHouse()

  if
    has_suppressor or
    core.me().hasGroup('LEO') or
    core.me().hasGroup('Fort Zancudo') or
    exports.blrp_zones:IsInsideMatchedZone('NoShotsCalls') or
    exports.blrp_zones:IsInsideMatchedZone('ApartmentZone') or
    (inside_house and nearby_house.business_id) or -- Inside a warehouse
    (inside_house and math.random(1, 100) > 33) -- 66% chance to suppress gunshot calls in a house
  then
    return
  end

  -- Don't send shots alert if they're using a hunting rifle in the LEGAL hunting area or Jenkins Farm
  if current_weapon == `WEAPON_HEAVYSHOTGUN` or current_weapon == `WEAPON_HUNTINGRIFLE` then
    if #(player_coords.xy - vector2(2157.053, 4983.559)) < 65.0 then
      return
    end

    if exports.blrp_zones:IsInsideMatchedZone('legalHuntingArea') then
      local hours = GlobalState.time_hours
      local minutes = GlobalState.time_minutes

      if (hours == 5 and minutes >= 30) or (hours == 20 and minutes <= 30) or (hours > 5 and hours < 20) then
        return
      end

      if math.random(1, 100) >= 15 then
        return
      end
    end
  end

  local message = 'Gunshots heard'

  if IsPedInAnyVehicle(PlayerPedId(), false) then
    message = 'Drive-by gunshots heard'
  end

  if automatic_guns[current_weapon] and math.random(1, 100) <= 25 then
    message = 'Automatic gunshots heard'
  end

  TriggerEvent('blrp_core:client:prepareAlert', {
    is_response = true,
    service_name = 'Police',
    can_accept = false,
    coords = player_coords,
    badge = 'Priority 4',
    badge_style = 'primary',
    title = message,
    icon = 'fad fa-phone',
    show_for = 5000,
    allow_gps = false,

    radius = true,
    radius_fadetime = 30,
    radius_radius = 120.0,
    radius_shift = 70,
  })

  wanted_time_left = wanted_time
end

-- Gunshot detection
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    gunshotDetectionLoop()
  end
end)

---------------------------------------
-- GSR Stuff start
---------------------------------------

tCore.gsrTest = function()
  if last_gunshot_time == 0 then
    return false, ''
  end

  local diff = (GetGameTimer() - last_gunshot_time) / 1000 / 60

  if diff > 15 then
    return false, ''
  end

  if diff > 7 then
    return true, 'Weak'
  end

  return true, 'Strong'
end
