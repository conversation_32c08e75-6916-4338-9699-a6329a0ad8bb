local fleeca_number = 0
local min_cops = 5
local keycards_given = 0
local last_keycard_given = 0

-- Citizen.CreateThread(function()
--   while true do
--     local can_rob = getComputedAvailableDispatchModality() >= min_cops
--     TriggerEvent('blrp_tablet:server:setRobbableStatus', 'fleeca', can_rob)
--     Citizen.Wait(60000)
--   end
-- end)


-- Reset automatically 30 minutes after starting
function resetFleecaRobbery(target_bank_name)
  for bank_name, bank_data in pairs(config_fleeca_robbery.banks) do
    if target_bank_name == bank_name or not target_bank_name then
      config_fleeca_robbery.banks[bank_name].stage = 1 -- Initialize stage
      config_fleeca_robbery.banks[bank_name].being_robbed = false
      config_fleeca_robbery.banks[bank_name].power_cutting = false
      config_fleeca_robbery.banks[bank_name].power_cut = false
      config_fleeca_robbery.banks[bank_name].keyboard_1_being_hacked = false
      config_fleeca_robbery.banks[bank_name].keyboard_1_is_hacked = false
      config_fleeca_robbery.banks[bank_name].keyboard_1_hacker = nil
      config_fleeca_robbery.banks[bank_name].keyboard_2_being_hacked = false
      config_fleeca_robbery.banks[bank_name].keyboard_2_is_hacked = false
      config_fleeca_robbery.banks[bank_name].keyboard_2_hacker = nil
      config_fleeca_robbery.banks[bank_name].being_hacked = false
      config_fleeca_robbery.banks[bank_name].is_hacked = false
      config_fleeca_robbery.banks[bank_name].chalice_awarded = false
      config_fleeca_robbery.banks[bank_name].keycard_awarded = false
      config_fleeca_robbery.banks[bank_name].meta_name = nil
      config_fleeca_robbery.banks[bank_name].rotating_door = false
      config_fleeca_robbery.banks[bank_name].door_open = false
      config_fleeca_robbery.banks[bank_name].front_door_open = false
      config_fleeca_robbery.banks[bank_name].resetting = false
      config_fleeca_robbery.banks[bank_name].lockers_picked = {}
      config_fleeca_robbery.banks[bank_name].lockers = { [1] = {}, [2] = {}, [3] = {}, [4] = {}, }
      config_fleeca_robbery.banks[bank_name].vault_attempts = 0
      exports.blrp_doors:OverrideByUid(bank_data.door_uid, true)
      exports.blrp_doors:OverrideByUid(bank_data.door_back_uid, true)
    end
  end

  syncFleecaClient()
end

SetTimeout(200, resetFleecaRobbery)


function fleecaGenerateBags(group)
  local reward_group = config_fleeca_robbery.reward_groups[group]

  if not reward_group then
    error('Invalid reward group: ' .. group)
    return
  end

  local min_reward, max_reward = table.unpack(reward_group)
  local total_reward = math.random(min_reward, max_reward)

  -- Randomly divide total reward into random number of bags
  local bags = math.random(config_fleeca_robbery.min_bags, config_fleeca_robbery.max_bags)
  local weights = table.normalize(table.randomWeights(bags, 20, 100, 100))
  local bag_values, checksum = table.mult(weights, total_reward, math.round)

  -- Distribute values into lockboxes
  local bag_distribution = config_fleeca_robbery.bag_distributions[bags]
  local bag_amount_index = 1
  local lockbox_bags = {}

  for lockbox_idx = 1, 4 do
    lockbox_bags[lockbox_idx] = {}

    local bags_in_this_lockbox = bag_distribution[lockbox_idx]

    for bag_idx = 1, bags_in_this_lockbox do
      lockbox_bags[lockbox_idx][bag_idx] = math.floor(bag_values[bag_amount_index])
      bag_amount_index = bag_amount_index + 1
    end
  end

  return lockbox_bags, total_reward, checksum
end

-- TODO: remove - dev
-- SetTimeout(1000, function()
--   exports.blrp_core:print_r(fleecaGenerateBags('city'))
-- end)

RegisterNetEvent('core:server:fleeca-robbery:cutPower', function(_, event_data)
  local character = core.character(source)
  local cooldown_until = config_fleeca_robbery.lastrobbed + config_fleeca_robbery.cooldown
  local time_left = cooldown_until - os.time()

  if not config_fleeca_robbery.banks[event_data.bank_name] then
    return
  end

  if config_fleeca_robbery.banks[event_data.bank_name].stage > 1 then
    character.notify('This bank is already in progress.')
    return
  end

  if config_fleeca_robbery.banks[event_data.bank_name].power_cut then
    character.notify('Power has already been cut')
    return
  end

  if isItCloseToReboot() and
  (
    not GlobalState.is_dev or
    not character.request('[DEV SERVER] Override To close to reboot to start robbery?')
  )
  then
    character.notify('You cannot rob this right now. It is too close to reboot (or reboot recently happened < 1 hr ago)')
    return
  end

  if ((os.time() - config_fleeca_robbery.lastrobbed) < (config_fleeca_robbery.cooldown)) and
  (
    not GlobalState.is_dev or
    not character.request('[DEV SERVER] Override Cooldown to start robbery?')
  )
  then
    character.notifyError('[DO NOT READ THIS OUT LOUD] Bank is still on global cooldown for ' .. math.ceil(time_left / 60) .. ' minute(s)')
    return
  end

  -- Gang cooldown check
  local gangs_on_cooldown, cooldown_str = getPlayerGangsOnCooldown(character.source, 'fleeca')

  if #gangs_on_cooldown > 0 and
  (
    not GlobalState.is_dev or
    not character.request('[DEV SERVER] Override Gang Cooldown to start robbery?')
  )
  then
    character.notifyNew('You can\'t start this bank yet—your gang is laying low. Cooldown ends in: ' .. cooldown_str, 15000)
    return
  end
  -- /Gang cooldown check

  if getComputedAvailableDispatchModality() < min_cops and
  (
    not GlobalState.is_dev or
    not character.request('[DEV SERVER] Override Cop # to start robbery?')
  )
  then
    character.notify('[DO NOT READ THIS OUT LOUD] Not enough police online/available')
    return
  end

  for _, _bank_data in pairs(config_fleeca_robbery.banks) do
    if _bank_data.power_cutting then
      character.notify('This security system is being interrupted elsewhere')
      return
    end
  end

  if config_fleeca_robbery.banks[event_data.bank_name].power_cutting then
    character.notify('Someone is already doing this')
    return
  end

  if not character.hasItemQuantity('wire_cutters', 1) then
    character.notify('You have nothing to cut the wires with')
    return
  end

  config_fleeca_robbery.banks[event_data.bank_name].power_cutting = true

  syncFleecaClient()

  queuePoliceReminderFleeca()
  queuePoliceReminderFleecafront()

  local progress = character.progressPromise('Cutting Door Power', config_fleeca_robbery.banks[event_data.bank_name].power_duration, {
    controlDisables = {
      disableMovement = true,
      disableCarMovement = true,
      disableMouse = true,
      disableCombat = true,
    },
    animation = {
      animDict = 'mini@repair',
      anim = 'fixing_a_ped',
      flags = 49
    }
  })

  config_fleeca_robbery.banks[event_data.bank_name].power_cutting = false
  config_fleeca_robbery.banks[event_data.bank_name].meta_name = os.date('%Y%m%d%H%M%S')
  config_fleeca_robbery.banks[event_data.bank_name].front_door_open = true

  syncFleecaClient()

  if math.random(1, 100) <= 25 then
    character.log('FLEECA-ROBBERY', 'Consumed wire cutters')
    character.notify('Your wire cutters break in the process of tampering')

    if not character.take('wire_cutters', 1, false) then
      return
    end
  end

  if not progress then
    return
  end

  local bank = config_fleeca_robbery.banks[event_data.bank_name]
  local lockbox_bags, total_reward, sum = fleecaGenerateBags(bank.reward_group)

  for locker, data in ipairs(lockbox_bags) do
  local locker_data = config_fleeca_robbery.banks[event_data.bank_name].lockers[locker]
    for bag, amount in ipairs(data) do
    locker_data[bag] = amount
    end
  end

  setPlayerGangsOnCooldown(character.source, 'fleeca', os.time() + config_fleeca_robbery.gang_cooldown)
  config_fleeca_robbery.lastrobbed = os.time()
  config_fleeca_robbery.banks[event_data.bank_name].stage = 2 -- Advance to stage 2
  config_fleeca_robbery.banks[event_data.bank_name].being_robbed = true
  config_fleeca_robbery.banks[event_data.bank_name].power_cut = true
  config_fleeca_robbery.banks[event_data.bank_name].robbery_number = fleeca_number

  fleeca_number = fleeca_number + 1

  core.group('Police').alert({
    coords = config_fleeca_robbery.banks[event_data.bank_name].keypad,
    location_override = 'Fleeca Bank ' .. event_data.bank_name,
    badge = 'Priority 3',
    badge_style = 'info',
    msg = 'Power interruption',
    icon = 'far fa-sack-dollar',
    sound = 'ToneP3',

    leo_auto_status_code = 'OC',
    leo_auto_status_message = 'Fleeca Bank ' .. event_data.bank_name,
    leo_call_id = 'fleeca-' .. config_fleeca_robbery.banks[event_data.bank_name].robbery_number,
  })

  TriggerClientEvent('InteractSound_CL:PlayWithinDistance', -1, character.source, 3, 'alarm-jewl', 0.05)
  character.notify('Power cut successfully')
  exports.blrp_doors:OverrideByUid(config_fleeca_robbery.banks[event_data.bank_name].door_uid, false)
  character.log('FLEECA-ROBBERY', 'Initiating Robbery / location = ' .. event_data.bank_name, {
    fleeca_number = 'fleeca-' .. fleeca_number,
    payout = {
      lockbox_bags = lockbox_bags,
      total_reward = total_reward,
      sum = sum,
    },
  })
  StageHostages({
    HostageGroup = "Fleeca",
    JobLocation = event_data.bank_name
  })
  syncFleecaClient()
end)

RegisterNetEvent('core:server:fleeca-robbery:resetBank', function(_, event_data)
  local character = core.character(source)

  if config_fleeca_robbery.banks[event_data.bank_name].resetting then
    return
  end
  DespawnHostages(event_data.bank_name..'Fleeca')
  config_fleeca_robbery.banks[event_data.bank_name].resetting = true
  syncFleecaClient()

  character.notify('Resetting bank security system in 10 seconds. All doors will automatically close')

  SetTimeout(10 * 1000, function()
    config_fleeca_robbery.banks[event_data.bank_name].rotating_door = true
    config_fleeca_robbery.banks[event_data.bank_name].door_open = false
    config_fleeca_robbery.banks[event_data.bank_name].front_door_open = false
    syncFleecaClient()

    TriggerClientEvent('core:client:fleeca-robbery:rotateDoorOverTime', -1, event_data.bank_name, false)

    SetTimeout((config_fleeca_robbery.transition_time + 2) * 1000, function()
      resetFleecaRobbery(event_data.bank_name)
      character.notify('Security system reset')
    end)
  end)
end)

RegisterNetEvent('core:server:fleeca-robbery:deskhack', function(_, event_data)
  local character = core.character(source)
  local char_id = character.get('id')
  local bank = config_fleeca_robbery.banks[event_data.bank_name]
  local keyboard = event_data.keyboard
  local being_hacked_key = 'keyboard_' .. keyboard .. '_being_hacked'
  local is_hacked_key = 'keyboard_' .. keyboard .. '_is_hacked'
  local hacker_key = 'keyboard_' .. keyboard .. '_hacker'
  local other_keyboard = (keyboard == 1) and 2 or 1
  local other_hacker_key = 'keyboard_' .. other_keyboard .. '_hacker'

  if not bank then
    character.notify('Invalid bank data.')
    return
  end

  if bank.stage < 2 then
    character.notify('Power must be cut first.')
    return
  elseif bank.stage > 2 then
    character.notify('This step is already completed.')
    return
  end

  if not config_fleeca_robbery.banks[event_data.bank_name].power_cut then
    character.notify("How did you get back here")
    return
  end

  if not keyboard or (keyboard ~= 1 and keyboard ~= 2) then
    character.notify('Invalid keyboard specified.')
    return
  end

  if bank[hacker_key] and bank[hacker_key] ~= char_id then
    character.notify('Someone else is already using this')
    return
  end

  if bank[other_hacker_key] == char_id then
    character.notify('You cannot hack both keyboards.')
    return
  end

  if not bank[hacker_key] then
    bank[hacker_key] = char_id
  end

  if bank[is_hacked_key] then
    character.notify('This this already Hacked.')
    return
  end

  if bank[being_hacked_key] then
    character.notify('This keyboard is already being hacked.')
    return
  else
    bank[being_hacked_key] = true
  end

  syncFleecaClient()

  character.animate({{ 'anim@heists@prison_heistig1_p1_guard_checks_bus', 'loop', 1 }}, true, true)

  local success = T.getInstance('SN-Hacking', 'SnHacking').hack(character.source, {'MemoryGame', {keysNeeded = 5, rounds = 2, time = 10000}})

  character.log('FLEECA-ROBBERY', 'Keyboard Hack completed', {
    keyboard = keyboard,
    success = success,
  })

  character.stopAnimation(true)

  if not success then
    character.notify('You failed to hack the system')
    bank[being_hacked_key] = false
    bank.keyboard_1_is_hacked = false
    bank.keyboard_1_being_hacked = false

    bank.keyboard_2_is_hacked = false
    bank.keyboard_2_being_hacked = false
    syncFleecaClient()
    return
  end

  bank[is_hacked_key] = true -- marks keyboard as hacked so you can do it after you pass it the first time
  character.notify('Keyboard hacked successfully')

  if bank.keyboard_1_is_hacked and bank.keyboard_2_is_hacked then
    if bank.keyboard_1_hacker ~= bank.keyboard_2_hacker then
      character.notify('Firewall breach successfully!')
      character.give('lockbox_fleeca_key:meta:' .. bank.meta_name, 1)
      exports.blrp_doors:OverrideByUid(bank.door_back_uid, false)
      bank.stage = 3 -- Advance to stage 3
    end
  else
    bank[being_hacked_key] = false
  end

  syncFleecaClient()
end)

RegisterNetEvent('core:server:fleeca-robbery:hackKeypad', function(_, event_data)
  local character = core.character(source)

  if config_fleeca_robbery.banks[event_data.bank_name].stage < 3 then
    tCore.text3D(character.source, { event_data.position + vector3(0, 0, 0.3), 'Status: ~g~Firewall Up ~s~', 5 })
    return
  end

  if config_fleeca_robbery.banks[event_data.bank_name].is_hacked then
    tCore.text3D(character.source, { event_data.position + vector3(0, 0, 0.3), 'Status: ~r~Panel Hacked ~s~', 5 })
    return
  end

  if config_fleeca_robbery.banks[event_data.bank_name].vault_attempts >= 2 then
    character.notify('Vault security locked down')
    tCore.text3D(character.source, { event_data.position + vector3(0, 0, 0.3), 'Status: ~o~LOCKED DOWN~s~', 5 })
    return
  end

  if os.time() - config_fleeca_robbery.lastrobbed > (15 * 60) then
    resetFleecaRobbery(event_data.bank_name)
    return
  end

  if isItCloseToReboot() and
  (
    not GlobalState.is_dev or
    not character.request('[DEV SERVER] Override To close to reboot to start robbery?')
  )
  then
    character.notify('You cannot rob this right now. It is too close to reboot (or reboot recently happened < 1 hr ago)')
    return
  end

  if config_fleeca_robbery.banks[event_data.bank_name].being_hacked then
    character.notify('Someone is already doing this')
    return
  end

  if not character.take('laptop_h', 1, true) then
    character.notify('You don\'t have the tools needed!')
    return
  end

  config_fleeca_robbery.banks[event_data.bank_name].being_hacked = true

  if not character.progressPromise('Connecting Laptop', 45, {
    animation = {
      animDict = 'mp_common_heist',
      anim = 'pick_door',
      flags = 49,
    }
  }) then
    config_fleeca_robbery.banks[event_data.bank_name].being_hacked = false
    return
  end

  syncFleecaClient()

  character.animate({{ 'anim@heists@prison_heistig1_p1_guard_checks_bus', 'loop', 1 }}, true, true)

  local success, reason, duration = exports.blrp_hack9:Hack(character.source, 7, 10, 8, 35)


  character.log('FLEECA-ROBBERY', 'Hack 9 (Cyberpunk 2077) completed', {
    success = success,
    reason = reason,
    duration = duration,
  })

  character.stopAnimation(true)

  config_fleeca_robbery.banks[event_data.bank_name].being_hacked = false

  config_fleeca_robbery.banks[event_data.bank_name].vault_attempts = config_fleeca_robbery.banks[event_data.bank_name].vault_attempts + 1

  syncFleecaClient()

  if not success then
    character.notify('You failed to hack the system')
    character.stopAnimation(true)
    return
  end

  config_fleeca_robbery.banks[event_data.bank_name].is_hacked = true
  config_fleeca_robbery.banks[event_data.bank_name].rotating_door = true
  config_fleeca_robbery.banks[event_data.bank_name].door_open = true
  config_fleeca_robbery.banks[event_data.bank_name].stage = 4 -- Advance to stage 4

  character.notify('Panel hacked successfully')

  core.group('Police').alert({
    coords = config_fleeca_robbery.banks[event_data.bank_name].keypad,
    location_override = 'Fleeca Bank ' .. event_data.bank_name,
    badge = 'Priority 3',
    badge_style = 'info',
    msg = 'Vault door breach',
    icon = 'far fa-door-open',
    sound = 'ToneP3',

    leo_auto_status_code = 'OC',
    leo_auto_status_message = 'Fleeca Bank ' .. event_data.bank_name,
    leo_call_id = 'fleeca-' .. config_fleeca_robbery.banks[event_data.bank_name].robbery_number,
  })

  syncFleecaClient()
  TriggerClientEvent('core:client:fleeca-robbery:rotateDoorOverTime', -1, event_data.bank_name, true)
end)

RegisterNetEvent('core:server:fleeca-robbery:takeBag', function(_, event_data)
  local character = core.character(source)
  local bank_name = event_data.bank_name
  local locker_id = event_data.locker_id

  if not bank_name or not locker_id then
    character.notifyError('Invalid locker or bank')
    return
  end

  local bank = config_fleeca_robbery.banks[bank_name]
  local locker = bank.lockers[locker_id]

  if bank.stage < 4 then
    character.notify('Vault door must be opened first.')
    return
  end

  if bank.lockers_picked[locker_id] then
    character.notifyError('This locker has already been picked.')
    return
  end

  if character.hasItemQuantity('lockpick', 1) then
    character.animate({ {'mp_common_heist', 'pick_door'} }, true)

    character.take('lockpick', 1)

    local has_key = character.hasItemQuantity('lockbox_fleeca_key:meta:' .. bank.meta_name, 1)
    local pick_amount = 10
    local game_type = 'alpha'

    if has_key then
      pick_amount = 7
      game_type = 'number'
    end

    if tLockpick.lockpick(character.source, { pick_amount, game_type }) then
      if bank.lockers_picked[locker_id] then
        character.notifyError('This locker is empty.')
        return
      end


      if not config_fleeca_robbery.banks[event_data.bank_name].chalice_awarded and math.random(1, 100) <= 2 then
        config_fleeca_robbery.banks[event_data.bank_name].chalice_awarded = true
        character.give('gold_chalice', 1)
        character.log('FLEECA-ROBBERY', 'Bank ' .. event_data.bank_name .. ' awarding 1 gold_chalice')
      end

      if
        not config_fleeca_robbery.banks[event_data.bank_name].keycard_awarded and
        keycards_given < 3 and
        (
          last_keycard_given == 0 or
          (GetGameTimer() - last_keycard_given) > (5 * 60 * 60 * 1000) -- Last keycard given greater than 5 hours ago
        ) and
        math.random(1, 100) <= 10
      then
        keycards_given = keycards_given + 1
        last_keycard_given = GetGameTimer()
        config_fleeca_robbery.banks[event_data.bank_name].keycard_awarded = true
        character.give('bl_prop_access_card_violet', 1)
        character.log('FLEECA-ROBBERY', 'Bank ' .. event_data.bank_name .. ' awarding 1 bl_prop_access_card_violet')
      end

      local dropped_bags = false
      local received_bags = 0
      for bag, ammount in ipairs(locker) do
        local bag_amount =  ammount
        local bag_item_id = 'money_bag2:meta:' .. GetGameTimer() .. bag_amount
        local bag_meta = { bag_amount = bag_amount }
        local dropped = false

        -- Give money bag if they have room, otherwise drop it on the ground
        if character.hasRoomFor('money_bag2', 1) then
          character.give(bag_item_id, 1, bag_meta, false)
          received_bags = received_bags + 1
        else
          local player_coords = character.getCoordinates()

          exports.blrp_inventory:DropGroundItem(player_coords, bag_item_id, 1, bag_meta)
          dropped = true
          dropped_bags = true
        end

        character.log('FLEECA-ROBBERY', 'Lockpicked lockbox and got money bag', {
          bank_name = bank_name,
          locker_id = locker_id,
          amount = bag_amount,
          bag_item_id = bag_item_id,
          dropped = dropped
        })
        syncFleecaClient()
      end
      if received_bags > 0 then
        character.notify('You received ' .. received_bags..' fleeca money bag(s).')
      end
      if dropped_bags then
        character.notify('You did not have enough room for all the bags and dropped some on the ground')
      end
      bank.lockers_picked[locker_id] = true
    else
      character.notify('Failed to lockpick')
    end
  end
end)

function syncFleecaClient()
  TriggerClientEvent('client:syncFleecaBankData', -1, config_fleeca_robbery)
end

function queuePoliceReminderFleeca()
  SetTimeout(15 * 60 * 1000, function()
    local door_open = false

    for bank_name, bank_data in pairs(config_fleeca_robbery.banks) do
      if bank_data.door_open then
        door_open = true

        core.group('Police').alert({
          coords = config_fleeca_robbery.banks[bank_name].keypad,
          location_override = 'Fleeca Bank ' .. bank_name,
          badge = 'Priority 4',
          badge_style = 'info',
          msg = 'Insecure vault door',
          icon = 'far fa-door-open',
        })
      end
    end

    if door_open then
      queuePoliceReminderFleeca()
    end
  end)
end

function queuePoliceReminderFleecafront()
  SetTimeout(15 * 60 * 1000, function()
    local front_door_open = false

    for bank_name, bank_data in pairs(config_fleeca_robbery.banks) do
      if bank_data.front_door_open then
        front_door_open = true

        core.group('Police').alert({
          coords = config_fleeca_robbery.banks[bank_name].keypad,
          location_override = 'Fleeca Bank ' .. bank_name,
          badge = 'Priority 4',
          badge_style = 'info',
          msg = 'Insecure Teller Door',
          icon = 'far fa-door-open',
        })
      end
    end

    if front_door_open then
      queuePoliceReminderFleecafront()
    end
  end)
end

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(60000)
    for bank_name, bank_data in pairs(config_fleeca_robbery.banks) do
      if bank_data.power_cut and (os.time() - config_fleeca_robbery.lastrobbed) > (30 * 60) then
        resetFleecaRobbery(bank_name)
      end
    end
  end
end)