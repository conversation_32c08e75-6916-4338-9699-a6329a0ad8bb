import store from './store.js'

export default new class ActiveDuty {
    constructor() {
      const savedsortBy = localStorage.getItem('sortBy');
      const savedorderBy = localStorage.getItem('orderBy');
      this.sortBy = savedsortBy || 'lastname'; // Default to 'lastname' if nothing is saved
      this.orderBy = savedorderBy || 'ascending'; // Default to 'ascending' if nothing is saved
      this.searchTerm = ''; // Initialize search term

      // Saved position was causing an error
      const savedPosition = JSON.parse(localStorage.getItem('dispatchPosition'));
      if (savedPosition) {
          $('.active-dispatch').css({
              'top': savedPosition.top + 'px',
              'left': (savedPosition.left + 50) + 'px'
          });
      } else {
          $('.active-dispatch').css({'top': '170px',}); //Old position
      }

      this.contextMenuSetup = false;
      // Set up context menu once
      this.setupContextMenu();
      // Set up interface message handlers
      this.setupInterfaceHandlers();
    }

    mysrc = 0;
    server_time = 0;
    dispatchOpen = false
    dispatchUsers = [];
    radioColors = { }
    proximityGroups = {};
    letterReservations = {}; // Maps source -> reserved letter
    signal13FlashTimers = {}; // Maps source -> timestamp when Signal 13 was first detected
    lastProximityCalculation = 0;
    proximityThreshold = 1.4; // Distance threshold for grouping (in game units)
    dispatchGroups = {
      'police_rank0': { name: 'Recruit', icon: 'recruit'},
      'police_rank1': { name: 'Officer', icon: 'officer'},
      'police_rank2': { name: 'Senior Officer', icon: 'senior-deputy'},
      'police_rank3': { name: 'Corporal', icon: 'corporal'},
      'police_rank4': { name: 'Sergeant', icon: 'sergeant'},
      'police_rank5': { name: 'Lieutenant', icon: 'vert-gold-single'},
      'police_rank6': { name: 'Captain', icon: 'captain'},
      'police_rank7': { name: 'Assistant Police Chief', icon: 'star-silver-tri'},
      'police_rank8': { name: 'Chief of Police', icon: 'star-silver-quad'},

      'sheriff_rank0': { name: 'Probationary Deputy', icon: 'recruit'},
      'sheriff_rank1': { name: 'Deputy', icon: 'officer'},
      'sheriff_rank2': { name: 'Senior Deputy', icon: 'senior-deputy'},
      'sheriff_rank3': { name: 'Corporal', icon: 'corporal'},
      'sheriff_rank4': { name: 'Sergeant', icon: 'sergeant'},
      'sheriff_rank5': { name: 'Lieutenant', icon: 'vert-gold-single'},
      'sheriff_rank6': { name: 'Captain', icon: 'captain'},
      'sheriff_rank7': { name: 'Major', icon: 'major'},
      'sheriff_rank8': { name: 'Undersheriff', icon: 'star-silver-tri'},
      'sheriff_rank9': { name: 'Sheriff', icon: 'star-silver-quad'},

      'sahp_rank0': { name: 'Trooper', icon: 'officer' },
      'sahp_rank1': { name: 'Sergeant', icon: 'sergeant' },
      'sahp_rank2': { name: 'Lieutenant', icon: 'vert-silver-single' },
      'sahp_rank3': { name: 'Captain', icon: 'vert-silver-double' },
      'sahp_rank4': { name: 'Assistant Chief', icon: 'star-silver-tri' },
      'sahp_rank5': { name: 'Chief', icon: 'star-silver-quad' },

      'nysp_rank0': { name: 'Trooper', icon: 'senior-deputy'},
      'nysp_rank1': { name: 'Corporal', icon: 'corporal'},
      'nysp_rank2': { name: 'Sergeant', icon: 'sergeant'},
      'nysp_rank3': { name: 'Lieutenant', icon: 'vert-gold-single'},
      'nysp_rank4': { name: 'Captain', icon: 'captain'},
      'nysp_rank5': { name: 'Assistant Colonel', icon: 'star-silver-single'},
      'nysp_rank6': { name: 'Colonel', icon: 'star-silver-quad'},

      'ems_rank0': { name: 'Cadet', icon: 'ems_rank_0'},
      'ems_rank1': { name: 'EMT', icon: 'ems_rank_1'},
      'ems_rank2': { name: 'A-EMT', icon: 'ems_rank_2'},
      'ems_rank3': { name: 'Paramedic', icon: 'ems_rank_3'},
      'ems_rank4': { name: 'Lieutenant', icon: 'ems_rank_4'},
      'ems_rank5': { name: 'Captain', icon: 'ems_rank_5'},
      'ems_rank6': { name: 'Asst. Fire Chief', icon: 'ems_rank_6'},
      'ems_rank7': { name: 'Fire Chief', icon: 'ems_rank_7'},

      'doc_rank0': { name: 'Recruit Corrections Officer', icon: 'recruit'},
      'doc_rank1': { name: 'Corrections Officer', icon: 'officer'},
      'doc_rank2': { name: 'Ranking Corrections Officer', icon: 'corporal'},
      'doc_rank3': { name: 'Corrections Operations Supervisor', icon: 'sergeant'},
      'doc_rank4': { name: 'Chief Prison Officer', icon: 'vert-gold-single'},
      'doc_rank5': { name: 'Captain', icon: 'star-silver-single'},
    };

    setDispatchUsersList(characters, mysrc, server_time) {
      if (!this.dispatchOpen) {
          return;
      }

      this.mysrc = mysrc;
      this.server_time = server_time;

      for (const character of characters) {
          const radioChannelNumber = character.radio;

          if (this.radioColors[radioChannelNumber]) {
              character.radioColor = this.radioColors[radioChannelNumber];
          } else {
              this.radioColors[radioChannelNumber] = this.getRandomColor();
              character.radioColor = this.radioColors[radioChannelNumber];
          }
      }

      // Apply search filter
      characters = this.filterDispatchUsers(characters);

      // Calculate proximity groups
      this.calculateProximityGroups(characters);

      const groupOrder = (a, b) => {
        const ems_order = Boolean(a.groups['LSFD']) - Boolean(b.groups['LSFD']);
        const doc_order = Boolean(a.groups['DOC']) - Boolean(b.groups['DOC']);
        const ranger_order = Boolean(a.groups['Ranger_Internal']) - Boolean(b.groups['Ranger_Internal']);
        const tmu_order = Boolean(a.groups['TMU_Internal']) - Boolean(b.groups['TMU_Internal']);
        const court_order = Boolean(a?.status?.code === 'CT') - Boolean(b?.status?.code === 'CT');
        const training_order = Boolean(a?.status?.code === 'TN') - Boolean(b?.status?.code === 'TN');
        return tmu_order || ems_order || doc_order || ranger_order || court_order || training_order;
      }

      if (this.sortBy === 'callsign') {
        characters.sort((a, b) => {
          const callsignOrder = this.distillCallsign(a.callsign) - this.distillCallsign(b.callsign);
          return groupOrder(a, b) || (this.orderBy === 'ascending' ? callsignOrder : -callsignOrder);
      });
      } else if (this.sortBy === 'radio') {
        characters.sort((a, b) => {
          const name_order = a.lname.localeCompare(b.lname);
          let radio_order = 0;
          if (a.radio !== undefined && b.radio !== undefined) {
            if (a.radio === '--') {
              a.radio = 1;
            }
            if (b.radio === '--') {
              b.radio = 1;
            }
            radio_order = a.radio - b.radio;
          }
          return groupOrder(a, b) || (this.orderBy === 'ascending' ? radio_order : -radio_order) || name_order;
        });
      } else {
          characters.sort((a, b) => {
              const name_order = a.lname.localeCompare(b.lname); //Last name
              return groupOrder(a, b) || (this.orderBy === 'ascending' ? name_order : -name_order);
          });
      }

      this.dispatchUsers = characters;
      this.renderListHtml();
  }

  distillCallsign(callsign) {
    let matches = callsign.toString().match(/(\d+)/);

    if (matches) {
      return parseInt(matches[0], 10);
    }

    return callsign;
  }

  getNextAvailableRadio() {
    const radioRange = [12, 13, 14, 15, 16, 17];
    const usedRadios = new Set(this.dispatchUsers.map(user => Number(user.radio)).filter(radio => radioRange.includes(radio)));
    for (const channel of radioRange) {
        if (!usedRadios.has(channel)) {
            return channel;
        }
    }
    return null;
  }

  getRadioOccupancy(channel) {
    if (!this.dispatchUsers || !channel) return 0;

    return this.dispatchUsers.filter(user =>
      Number(user.radio) === Number(channel) &&
      !user.groups['Dispatch'] && !user.groups['FIB']
    ).length;
  }

  // Calculate distance squared between two coordinate points (avoids expensive sqrt)
  getDistanceSquared(coord1, coord2) {
    if (!coord1 || !coord2) return Infinity;
    const dx = coord1.x - coord2.x;
    const dy = coord1.y - coord2.y;
    return dx * dx + dy * dy;
  }

  // Calculate proximity groups using connected components algorithm with letter reservations
  calculateProximityGroups(characters) {
    // Only recalculate if enough time has passed or significant changes occurred
    const now = Date.now();
      if (now - this.lastProximityCalculation < 2000) {
      return; // Don't recalculate more than once every 2 seconds
    }
    this.lastProximityCalculation = now;

    // Reset proximity groups but preserve reservations
    this.proximityGroups = {};


    // Filter characters with valid coordinates
    const charactersWithCoords = characters.filter(char =>
      char.pos &&
      typeof char.pos.x === 'number' &&
      typeof char.pos.y === 'number' &&
      !char.groups['Dispatch'] &&
      !char.groups['FIB']
    );

    if (charactersWithCoords.length === 0) {
      // Clean up reservations for offline players
      this.cleanupReservations(characters);
      return;
    }

    // Build adjacency list for connected components
    const adjacencyList = new Map();
    const thresholdSquared = this.proximityThreshold * this.proximityThreshold;

    // Initialize adjacency list
    charactersWithCoords.forEach(char => {
      adjacencyList.set(char.source, []);
    });

    // Find all pairs within proximity threshold
    for (let i = 0; i < charactersWithCoords.length; i++) {
      for (let j = i + 1; j < charactersWithCoords.length; j++) {
        const char1 = charactersWithCoords[i];
        const char2 = charactersWithCoords[j];

        const distanceSquared = this.getDistanceSquared(char1.pos, char2.pos);

        if (distanceSquared <= thresholdSquared) {
          adjacencyList.get(char1.source).push(char2.source);
          adjacencyList.get(char2.source).push(char1.source);
        }
      }
    }

    // Find connected components using DFS
    const visited = new Set();
    const groups = [];

    const dfs = (source, currentGroup) => {
      visited.add(source);
      currentGroup.push(source);

      const neighbors = adjacencyList.get(source) || [];
      neighbors.forEach(neighbor => {
        if (!visited.has(neighbor)) {
          dfs(neighbor, currentGroup);
        }
      });
    };

    // Process each unvisited character
    charactersWithCoords.forEach(char => {
      if (!visited.has(char.source)) {
        const group = [];
        dfs(char.source, group);
        if (group.length > 0) {
          groups.push(group);
        }
      }
    });

    // Assign letters using smart reservation system
    this.assignLettersWithReservations(groups);

    // Clean up reservations for offline players
    this.cleanupReservations(characters);
  }

  // Smart letter assignment that preserves reservations when possible
  assignLettersWithReservations(groups) {
    // Sort groups by smallest source ID for consistency
    groups.sort((a, b) => Math.min(...a) - Math.min(...b));

    const usedLetters = new Set();
    const groupLetterAssignments = new Map();

    // First pass: Try to preserve existing reservations for groups that still exist
    groups.forEach((group, groupIndex) => {
      let bestLetter = null;
      let reservationCount = 0;

      // Check if any members of this group have existing reservations
      group.forEach(source => {
        const reservedLetter = this.letterReservations[source];
        if (reservedLetter && !usedLetters.has(reservedLetter)) {
          if (!bestLetter) {
            bestLetter = reservedLetter;
            reservationCount = 1;
          } else if (reservedLetter === bestLetter) {
            reservationCount++;
          }
        }
      });

      // If we found a good reserved letter, use it
      if (bestLetter && reservationCount > 0) {
        groupLetterAssignments.set(groupIndex, bestLetter);
        usedLetters.add(bestLetter);
      }
    });

    // Second pass: Assign new letters to groups without reservations
    groups.forEach((group, groupIndex) => {
      if (!groupLetterAssignments.has(groupIndex)) {
        const letter = this.getNextAvailableLetter(usedLetters);
        groupLetterAssignments.set(groupIndex, letter);
        usedLetters.add(letter);
      }
    });

    // Apply assignments and update reservations
    groups.forEach((group, groupIndex) => {
      const letter = groupLetterAssignments.get(groupIndex);
      group.forEach(source => {
        this.proximityGroups[source] = letter;
        this.letterReservations[source] = letter; // Reserve this letter for this player
      });
    });
  }

  // Get the next available letter in sequence
  getNextAvailableLetter(usedLetters) {
    // Try A-Z first
    for (let i = 0; i < 26; i++) {
      const letter = String.fromCharCode(65 + i);
      if (!usedLetters.has(letter)) {
        return letter;
      }
    }

    // Try AA-AZ
    for (let i = 0; i < 26; i++) {
      const letter = 'A' + String.fromCharCode(65 + i);
      if (!usedLetters.has(letter)) {
        return letter;
      }
    }

    // Try BA-BZ
    for (let i = 0; i < 26; i++) {
      const letter = 'B' + String.fromCharCode(65 + i);
      if (!usedLetters.has(letter)) {
        return letter;
      }
    }

    // Fallback to numeric
    let counter = 1;
    while (usedLetters.has('Z' + counter)) {
      counter++;
    }
    return 'Z' + counter;
  }

  // Clean up reservations for players who are no longer online
  cleanupReservations(characters) {
    const onlineSourceIds = new Set(characters.map(char => char.source));

    // Remove reservations for offline players
    Object.keys(this.letterReservations).forEach(source => {
      if (!onlineSourceIds.has(parseInt(source))) {
        delete this.letterReservations[source];
      }
    });

    // Clean up Signal 13 flash timers for offline players
    Object.keys(this.signal13FlashTimers).forEach(source => {
      if (!onlineSourceIds.has(parseInt(source))) {
        delete this.signal13FlashTimers[source];
      }
    });
  }

  renderListHtml() {
    let nextAvailableRadioText = '';
    if (store.myselfHasGroup('LEO')) {
        const nextAvailableRadio = this.getNextAvailableRadio();
        if (nextAvailableRadio) {
            nextAvailableRadioText = `Available: <span class="dispatch-available-radio" data-channel="${nextAvailableRadio}">${nextAvailableRadio}</span>`;
        } else {
            nextAvailableRadioText = `Radio Ch. 12-17 are used`;
        }
    }

    // Check if navbar already exists to avoid recreating it
    if ($('#active-dispatch .dispatch-navbar').length === 0) {
        $('#active-dispatch').html(`
            <div class="dispatch-navbar">
                <div class="dispatch-navbar-left">
                    <div class="dispatch-title">
                        <i class="fa-solid fa-radio"></i>
                        <span>Dispatch</span>
                    </div>
                </div>

                <div class="dispatch-navbar-center">
                    <div class="dispatch-search-group">
                        <div class="search-container">
                            <i class="fa-solid fa-search search-icon"></i>
                            <input type="text" id="dispatch-search" class="search-input" placeholder="Search personnel..." value="${this.searchTerm}" />
                            <button id="clear-search" class="clear-search-btn" style="display: ${this.searchTerm ? 'block' : 'none'};" title="Clear Search">
                                <i class="fa-solid fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <div class="dispatch-sorting-group">
                        <span class="sorting-label">Sort:</span>
                        <div class="sorting-buttons">
                            <button id="sort-by-callsign" class="sorting-btn ${this.sortBy === 'callsign' ? 'active' : ''}" title="Sort by Callsign">
                                <i class="fa-solid fa-hashtag"></i>
                            </button>
                            <button id="sort-by-name" class="sorting-btn ${this.sortBy === 'lastname' ? 'active' : ''}" title="Sort by Name">
                                <i class="fa-solid fa-user"></i>
                            </button>
                            <button id="sort-by-radio" class="sorting-btn ${this.sortBy === 'radio' ? 'active' : ''}" title="Sort by Radio">
                                <i class="fa-solid fa-walkie-talkie"></i>
                            </button>
                        </div>
                    </div>

                    <div class="dispatch-order-group">
                        <button id="order-asc-desc" class="order-btn" title="Toggle Sort Order">
                            <i class="fa-solid ${this.orderBy === 'ascending' ? 'fa-arrow-up' : 'fa-arrow-down'}"></i>
                        </button>
                    </div>
                </div>

                <div class="dispatch-navbar-right">
                    <div class="radio-info">
                        <i class="fa-solid fa-tower-broadcast"></i>
                        <span>${nextAvailableRadioText}</span>
                    </div>
                </div>
            </div>
            <div class="dispatch-list-container"></div>
        `);

        // Set up event handlers only once
        this.setupNavbarEventHandlers();
        this.setupInputFocusDetection();
    } else {
        // Just update the radio info and button states without recreating the navbar
        $('.radio-info span').html(nextAvailableRadioText);
        $('.sorting-btn').removeClass('active');
        $(`.sorting-btn[id="sort-by-${this.sortBy === 'lastname' ? 'name' : this.sortBy}"]`).addClass('active');
        $('#order-asc-desc i').removeClass('fa-arrow-up fa-arrow-down').addClass(this.orderBy === 'ascending' ? 'fa-arrow-up' : 'fa-arrow-down');
    }

    // Clear the dispatch list container and rebuild it with column layout
    $('.dispatch-list-container').empty();

    // Organize characters into columns of 20 each
    const charactersPerColumn = 20;
    const columns = [];
    let currentColumn = [];

    let finished = [];
    for (const char of this.dispatchUsers) {
        if (finished[char.source]) continue;

        currentColumn.push(char);
        finished[char.source] = true;

        // When we reach 20 characters, start a new column
        if (currentColumn.length >= charactersPerColumn) {
            columns.push(currentColumn);
            currentColumn = [];
        }
    }

    // Add any remaining characters to the last column
    if (currentColumn.length > 0) {
        columns.push(currentColumn);
    }

    // Create column elements and populate them
    for (let i = 0; i < columns.length; i++) {
        let leftOffset = i * 286;
        if (i === 2) {
            leftOffset = leftOffset - 285;
        }
        const columnDiv = $(`<div class="dispatch-column" style="margin-left: ${leftOffset}px;"></div>`);

        for (const char of columns[i]) {
            const html = this.makeProfileHtml(char);
            columnDiv.append(html);
        }

        $('.dispatch-list-container').append(columnDiv);
    }

    // Setup hover effects after rebuilding the list
    this.setupHoverEffects();



    $('.active-dispatch').draggable({
        handle: '.dispatch-navbar',
        containment: 'window',
        stop: function (event, ui) {
          localStorage.setItem('dispatchPosition', JSON.stringify({
              top: ui.position.top,
              left: ui.position.left
          }));
      }
    });
  }

    togglesortBy(mode) {
      if (mode === 'lastname') {
          this.sortBy = 'lastname';
      } else if (mode === 'radio') {
          this.sortBy = 'radio';
      } else if (mode === 'callsign') {
          this.sortBy = 'callsign';
      }
      this.setDispatchUsersList(this.dispatchUsers, this.mysrc, this.server_time);
    }

    showDispatchUsers() {
      if (
          store.myselfHasGroup('LEO') ||
          store.myselfHasGroup('LSFD') ||
          store.myselfHasGroup('DOC') ||
          store.myselfHasGroup('Dispatch') ||
          store.myselfHasGroup('Ranger') ||
          store.myselfHasGroup('DOJ') ||
          store.myselfHasGroup('Lawyer') ||
          (
            store.myselfHasGroup('Rocky Road Towing') &&
            store.myselfHasGroup('Tow Truck')
          )
      ) {
          this.renderListHtml();
          this.dispatchOpen = true;
          $('.active-dispatch').show();
      }
  }

    hideDispatchUsers() {
        this.dispatchOpen = false;
        $('#active-dispatch').hide();
        $('#active-dispatch').html('');
    }

    makeProfileHtml(char) {
        const rank = this.getRankName(char.callsign, char.groups);

        let radio = char.radio

        if(!radio) {
          radio = 0;
        }

        // Determine department color for left border indicator
        const departmentColor = this.getDepartmentColor(char.groups);

        radio = Number(radio);

        // Format radio display first
        let formattedRadio = radio;
        if(char.groups['Dispatch'] == true) {
          formattedRadio = '';
        } else if(radio >= 100 || radio == 0) {
          formattedRadio = '--';
        }

        // Get proximity group letter for this character
        const proximityLetter = this.proximityGroups[char.source] || '';

        // Add proximity letter prefix to radio display
        if (proximityLetter && formattedRadio && formattedRadio !== '--') {
          formattedRadio = `${proximityLetter}-${formattedRadio}`;
        }

        // Create enhanced radio display with solid black background and colored text
        let radioDisplay = formattedRadio;

        if (radio && radio !== 0 && !char.groups['Dispatch']) {
            // Calculate occupancy for this radio channel
            const occupancy = this.getRadioOccupancy(radio);
            const occupancyIndicator = occupancy > 0 ? `<span class="radio-occupancy-indicator">${occupancy}</span>` : '';

            radioDisplay = `<span class="radio-container"><span class="radio-channel-box dispatch-radio" style="background-color: #000000; color: ${char.radioColor};">${formattedRadio}</span>${occupancyIndicator}</span>`;
        }

        let callsign = char.callsign

        if(!callsign) {
          callsign = ''
        }

        let dispatch_classes = 'dispatch-profile';

        if(char.groups['DOC'] == true) {
          dispatch_classes = dispatch_classes + ' dispatch-bg-doc';
        }

        if(char.groups['Ranger'] == true) {
          dispatch_classes = dispatch_classes + ' dispatch-bg-ranger';
        } else if(char.groups['Ranger_Internal'] == true) {
          dispatch_classes = dispatch_classes + ' dispatch-bg-ranger-leo';
        }

        if(char.groups['TMU_Internal'] == true) {
          dispatch_classes = dispatch_classes + ' dispatch-bg-emstmu';
        }

        if (char.groups['Rocky Road Towing'] === true && char.groups['Tow Truck'] === true) {
          dispatch_classes += ' dispatch-bg-rrtow';
        }

        // Add Signal 13 background (takes priority over busy)
        if(char.status && char.status.code === '13') {
          dispatch_classes += ' dispatch-bg-signal13';

          // Check if this is a new Signal 13 or if we should still be flashing
          const now = Date.now();
          const flashDuration = 15000;

          if (!this.signal13FlashTimers[char.source]) {
            // New Signal 13 detected - ONLY for actual Signal 13 status code
            this.signal13FlashTimers[char.source] = now;
          }

          // Check if we should still be flashing (within 15 seconds)
          const timeSinceDetection = now - this.signal13FlashTimers[char.source];
          if (timeSinceDetection < flashDuration) {
            dispatch_classes += ' dispatch-bg-signal13-flash';
          }
        } else {
          // Not Signal 13 anymore, remove the timer
          if (this.signal13FlashTimers[char.source]) {
            delete this.signal13FlashTimers[char.source];
          }
        }

        // Add orange tint for busy status (only if not Signal 13)
        if(char.status && char.status.busy && char.status.code !== '13') {
          dispatch_classes += ' dispatch-bg-busy';
        }

        // Add training background
        if(char.status && char.status.code === 'TN') {
          dispatch_classes += ' dispatch-bg-tn';
        }

        let buttons = ``;

        let button_block = `
          <span class="dispatch-btn-wrap">
            ${buttons}
          </span>
        `
        let dispatch_status_block = '';

        // Determine status dot color and pulse state
        let statusColor = '#666'; // Default gray
        let statusPulse = '';

        if(char.status != undefined) {
          if(char.status.code == 'DP') {
            statusColor = '#9b59b6'; // Purple for dispatcher
          } else if(char.status.code == '13') {
            statusColor = '#8B0000'; // Dark red for Signal 13 (different from busy red)
          } else if(char.status.code == 'SU') {
            statusColor = '#2ecc71'; // Green for service
          } else if(char.status.code == 'TN') {
            statusColor = '#9b59b6'; // Purple for training
          } else if(char.status.busy) {
            statusColor = '#e67e22'; // Orange for busy
          } else {
            statusColor = '#27ae60'; // Green for available
          }

          let diff = Math.max(0, this.server_time - char.status.time)

          // Check if status was changed recently (within 2 minutes = 120 seconds)
          if(diff <= 120) {
            statusPulse = ' dispatch-status-pulse';
          }

          let status_message = '';

          if(char.status.message != undefined) {
            status_message = char.status.message.toUpperCase().trim();
          }

          dispatch_status_block = `
          <div class="dispatch-status">
            <span class="dispatch-status-time">${new Date(diff * 1000).toISOString().slice(11, 19)}</span>
            <span class="dispatch-status-code">${char.status.code}</span>
            <span class="dispatch-status-message">${status_message}</span>
          </div>
          `
        }

        return `
            <div class="${dispatch_classes} showing dispatch-profile-item" id="profile_${char.source}" data-source="${char.source}" data-callsign="${callsign}" data-fname="${char.fname}" data-lname="${char.lname}">
                <div class="department-indicator" style="background-color: ${departmentColor};"></div>
                <div class="dispatch-person">
                    <span class="dispatch-callsign">${callsign}</span>
                    <span class="dispatch-name">${char.fname.substring(0, 1)}. ${char.lname}</span>
                </div>

                <div class="dispatch-buttons">
                    <span class="dispatch-radio">${radioDisplay}</span>
                    <span class="dispatch-rank-picture" style="margin-bottom: 3px">
                        <img src="../images/ranks/${rank.icon}.png" alt="${rank.name}">
                    </span>
                    ${button_block}
                </div>

                ${dispatch_status_block}
            </div>
        `
    }

    /**
     *                     // <span class="dispatch-status-dot${statusPulse}" style="background-color: ${statusColor}">
     *                     //     ${statusPulse ? `<span class="dispatch-pulse-ring" style="background-color: ${statusColor}"></span>` : ''}
     *                     // </span>
     */

    setupNavbarEventHandlers() {
        // click handling
        $('#sort-by-callsign').on('click', () => {
            this.togglesortBy('callsign');
            localStorage.setItem('sortBy', 'callsign');
            this.setDispatchUsersList(this.dispatchUsers, this.mysrc, this.server_time);
        });

        $('#sort-by-name').on('click', () => {
            this.togglesortBy('lastname');
            localStorage.setItem('sortBy', 'lastname');
            this.setDispatchUsersList(this.dispatchUsers, this.mysrc, this.server_time);
        });

        $('#sort-by-radio').on('click', () => {
            this.togglesortBy('radio');
            localStorage.setItem('sortBy', 'radio');
            this.setDispatchUsersList(this.dispatchUsers, this.mysrc, this.server_time);
        });

        $('#order-asc-desc').on('click', () => {
            const currentOrderBy = this.orderBy;
            this.orderBy = currentOrderBy === 'ascending' ? 'descending' : 'ascending';
            localStorage.setItem('orderBy', this.orderBy);
            this.setDispatchUsersList(this.dispatchUsers, this.mysrc, this.server_time);
        });

        // Search functionality
        $('#dispatch-search').on('input', (e) => {
            this.searchTerm = e.target.value.toLowerCase().trim();
            this.updateClearButton();
            this.setDispatchUsersList(this.dispatchUsers, this.mysrc, this.server_time);
        });

        $('#clear-search').on('click', () => {
            this.searchTerm = '';
            $('#dispatch-search').val('');
            this.updateClearButton();
            this.setDispatchUsersList(this.dispatchUsers, this.mysrc, this.server_time);
        });

        // Handle Enter key in search
        $('#dispatch-search').on('keypress', (e) => {
            if (e.which === 13) { // Enter key
                e.target.blur(); // Remove focus from input
            }
        });
    }

    setupInterfaceHandlers() {
        // Listen for interface messages
        window.addEventListener('message', (event) => {
            if (event.data.type === 'core:hideContextMenus') {
                this.hideAllContextMenus();
            }
        });
    }

    hideAllContextMenus() {
        // Hide dispatch context menu
        $('#dispatch-context-menu').hide();

        // Hide any open dialogs
        $('#dispatch-input-dialog').remove();
        $('#dispatch-status-dialog').remove();

        // Remove any event handlers that might be lingering
        $(document).off('keydown.dispatch-dialog');
        $(document).off('keydown.dispatch-status-dialog');
        $(document).off('click.hide-context-menu');
    }

    setupInputFocusDetection() {
        // Track focus state for all input elements
        $(document).on('focus', 'input, textarea', () => {
            fetch(`https://blrp_core/setInputFocused`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json; charset=UTF-8',
                },
                body: JSON.stringify({
                    value: true
                })
            });
        });

        $(document).on('blur', 'input, textarea', () => {
            // Small delay to check if focus moved to another input
            setTimeout(() => {
                const activeElement = document.activeElement;
                const isInputFocused = activeElement && (
                    activeElement.tagName === 'INPUT' ||
                    activeElement.tagName === 'TEXTAREA'
                );

                if (!isInputFocused) {
                    fetch(`https://blrp_core/setInputFocused`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json; charset=UTF-8',
                        },
                        body: JSON.stringify({
                            value: false
                        })
                    });
                }
            }, 10);
        });
    }

    updateClearButton() {
        const clearBtn = $('#clear-search');
        if (this.searchTerm && this.searchTerm.length > 0) {
            clearBtn.show();
        } else {
            clearBtn.hide();
        }
    }

    filterDispatchUsers(users) {
        if (!this.searchTerm || this.searchTerm.length === 0) {
            return users;
        }

        return users.filter(user => {
            const searchableText = [
                user.callsign || '',
                user.fname || '',
                user.lname || '',
                (user.fname + ' ' + user.lname) || '',
                user.radioChannel || '',
                user.status?.code || '',
                user.status?.message || ''
            ].join(' ').toLowerCase();

            return searchableText.includes(this.searchTerm);
        });
    }

    setupContextMenu() {
        // Only set up once - use static event delegation
        if (this.contextMenuSetup) {
            return;
        }
        this.contextMenuSetup = true;

        // Create context menu HTML if it doesn't exist
        if ($('#dispatch-context-menu').length === 0) {
            const contextMenuHtml = `
                <div id="dispatch-context-menu" class="dispatch-context-menu" style="display: none;">
                    <div class="context-menu-item" data-action="callDuty">
                        <i class="fa-solid fa-phone-flip"></i> Call
                    </div>
                    <div class="context-menu-item" data-action="routeGps">
                        <i class="fa-solid fa-globe"></i> Route GPS
                    </div>
                    <div class="context-menu-item" data-action="setRadio">
                        <i class="fa-solid fa-walkie-talkie"></i> Set Radio
                    </div>
                    <div class="context-menu-item" data-action="setStatus">
                        <i class="fa-solid fa-circle-dot"></i> Set Status
                    </div>
                </div>
            `;
            $('body').append(contextMenuHtml);
        }

        const self = this;

        // Use event delegation on document for right-click
        $(document).on('contextmenu.dispatch-profiles', '.dispatch-profile-item', function(e) {
            e.preventDefault();
            e.stopImmediatePropagation();

            const $target = $(this);
            const source = $target.data('source');
            const callsign = $target.data('callsign');
            const fname = $target.data('fname');
            const lname = $target.data('lname');

            // Hide any existing menu
            $('#dispatch-context-menu').hide();

            // Store target data
            $('#dispatch-context-menu').data({
                source: source,
                callsign: callsign,
                fname: fname,
                lname: lname
            });

            // Position and show menu
            $('#dispatch-context-menu').css({
                left: e.clientX + 'px',
                top: e.clientY + 'px',
                display: 'block'
            });

            // Set up one-time click handler to hide menu
            setTimeout(() => {
                $(document).one('click.hide-context-menu', function(clickEvent) {
                    if (!$(clickEvent.target).closest('#dispatch-context-menu').length) {
                        $('#dispatch-context-menu').hide();
                    }
                });
            }, 50);

            return false;
        });

        // Handle context menu item clicks
        $(document).on('click.dispatch-context-items', '.context-menu-item', function(e) {
            e.preventDefault();
            e.stopImmediatePropagation();

            const action = $(this).data('action');
            const menuData = $('#dispatch-context-menu').data();

            $('#dispatch-context-menu').hide();

            if (action === 'callDuty') {
                self.callDutyNumber(menuData);
            } else if (action === 'routeGps') {
                self.routeGps(menuData);
            } else if (action === 'setRadio') {
                self.showRadioDialog(menuData);
            } else if (action === 'setStatus') {
                self.showStatusDialog(menuData);
            }

            return false;
        });

        // Handle radio channel clicks to join that channel
        $(document).on('click.dispatch-radio-join', '.dispatch-radio', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const radioText = $(this).text().trim();
            const channelMatch = radioText.match(/(\d+)/);

            if (channelMatch) {
                const channel = parseInt(channelMatch[1]);

                // Send to server to join this radio channel
                fetch(`https://blrp_core/joinRadioChannel`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=UTF-8',
                    },
                    body: JSON.stringify({
                        channel: channel
                    })
                }).then(resp => resp.json()).then(resp => {
                    return resp;
                });

                // Send success notification to server
                fetch(`https://blrp_core/showNotification`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=UTF-8',
                    },
                    body: JSON.stringify({
                        message: `Joining radio channel ${channel}`,
                        type: 'info'
                    })
                });

                // Close the dispatch interface
                fetch(`https://blrp_core/escape`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=UTF-8',
                    },
                    body: JSON.stringify({})
                });
            }

            return false;
        });

        // Handle available radio channel clicks to join that channel
        $(document).on('click.dispatch-available-radio', '.dispatch-available-radio', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const channel = parseInt($(this).data('channel'));

            if (channel) {
                // Send to server to join this radio channel
                fetch(`https://blrp_core/joinRadioChannel`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=UTF-8',
                    },
                    body: JSON.stringify({
                        channel: channel
                    })
                }).then(resp => resp.json()).then(resp => {
                    return resp;
                });

                // Send success notification to server
                fetch(`https://blrp_core/showNotification`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=UTF-8',
                    },
                    body: JSON.stringify({
                        message: `Joining available radio channel ${channel}`,
                        type: 'info'
                    })
                });

                // Close the dispatch interface
                fetch(`https://blrp_core/escape`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=UTF-8',
                    },
                    body: JSON.stringify({})
                });
            }

            return false;
        });
    }

    callDutyNumber(targetData) {
        fetch(`https://blrp_core/callDutyNumber`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({
                source: targetData.source
            })
        }).then(resp => resp.json()).then(resp => {
            return resp;
        });
    }

    routeGps(targetData) {
        fetch(`https://blrp_core/routeGps`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({
                source: targetData.source
            })
        }).then(resp => resp.json()).then(resp => {
            return resp;
        });
    }

    showRadioDialog(targetData) {
        this.showInputDialog(
            `Set Radio Channel`,
            `Set radio channel for ${targetData.callsign} (${targetData.fname} ${targetData.lname})`,
            'Enter channel (1-28):',
            '',
            (value) => {
                const channelNum = parseInt(value.trim());

                if (isNaN(channelNum) || channelNum < 1 || channelNum > 28) {
                    // Send error notification to server
                    fetch(`https://blrp_core/showNotification`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json; charset=UTF-8',
                        },
                        body: JSON.stringify({
                            message: 'Invalid channel! Please enter a number between 1 and 28.',
                            type: 'error'
                        })
                    });
                    return;
                }

                // Send to server
                fetch(`https://blrp_core/setRadioChannel`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=UTF-8',
                    },
                    body: JSON.stringify({
                        callsign: targetData.callsign,
                        channel: channelNum
                    })
                }).then(resp => resp.json()).then(resp => {
                    return resp;
                });
            }
        );
    }

    showStatusDialog(targetData) {
        this.showStatusSelectionDialog(
            `Set Status`,
            `Set status for ${targetData.callsign} (${targetData.fname} ${targetData.lname})`,
            ['BU', 'IS'],
            (statusCode, message) => {
                // Send to server
                fetch(`https://blrp_core/setStatus`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=UTF-8',
                    },
                    body: JSON.stringify({
                        callsign: targetData.callsign,
                        status: statusCode,
                        message: message
                    })
                }).then(resp => resp.json()).then(resp => {
                    return resp;
                });
            }
        );
    }

    showStatusSelectionDialog(title, description, statusCodes, callback) {
        // Remove any existing dialog
        $('#dispatch-status-dialog').remove();

        // Generate status code buttons
        const statusButtons = statusCodes.map(code =>
            `<button class="dispatch-status-btn" data-status="${code}">${code}</button>`
        ).join('');

        const dialogHtml = `
            <div id="dispatch-status-dialog" class="dispatch-input-dialog">
                <div class="dispatch-dialog-overlay"></div>
                <div class="dispatch-dialog-content">
                    <div class="dispatch-dialog-header">
                        <h3>${title}</h3>
                        <button class="dispatch-dialog-close">&times;</button>
                    </div>
                    <div class="dispatch-dialog-body">
                        <p>${description}</p>
                        <div class="dispatch-status-selection">
                            <p style="margin-bottom: 10px; font-weight: bold;">Select a status code:</p>
                            <div class="dispatch-status-buttons">
                                ${statusButtons}
                            </div>
                            <div class="dispatch-reason-section" style="margin-top: 20px;">
                                <p style="margin-bottom: 10px; font-weight: bold;">Enter message:</p>
                                <input type="text" id="dispatch-status-message" placeholder="Enter status message..." />
                            </div>
                        </div>
                    </div>
                    <div class="dispatch-dialog-footer">
                        <button class="dispatch-dialog-btn dispatch-dialog-btn-cancel">Cancel</button>
                        <button class="dispatch-dialog-btn dispatch-dialog-btn-confirm" disabled>Set Status</button>
                    </div>
                </div>
            </div>
        `;

        $('body').append(dialogHtml);

        let selectedStatus = 'BU'; // Default to BU

        // Set BU as selected by default
        $('#dispatch-status-dialog .dispatch-status-btn[data-status="BU"]').addClass('selected');

        // Handle status button clicks
        $('#dispatch-status-dialog .dispatch-status-btn').on('click', function() {
            // Remove previous selection
            $('#dispatch-status-dialog .dispatch-status-btn').removeClass('selected');

            // Mark this button as selected
            $(this).addClass('selected');
            selectedStatus = $(this).data('status');

            // Enable confirm button if we have both status and message
            updateConfirmButton();
        });

        // Handle message input
        $('#dispatch-status-message').on('input', function() {
            updateConfirmButton();
        });

        // Focus the message input
        setTimeout(() => {
            $('#dispatch-status-message').focus();
            // Update button state initially
            updateConfirmButton();
        }, 100);

        function updateConfirmButton() {
            const message = $('#dispatch-status-message').val().trim();
            const confirmBtn = $('#dispatch-status-dialog .dispatch-dialog-btn-confirm');

            // IS doesn't require a message, BU does
            if (selectedStatus === 'IS') {
                confirmBtn.prop('disabled', false);
            } else if (selectedStatus === 'BU' && message) {
                confirmBtn.prop('disabled', false);
            } else {
                confirmBtn.prop('disabled', true);
            }
        }

        // Handle confirm button
        $('#dispatch-status-dialog .dispatch-dialog-btn-confirm').on('click', function() {
            if ($(this).prop('disabled')) return;

            const message = $('#dispatch-status-message').val().trim();
            $('#dispatch-status-dialog').remove();
            callback(selectedStatus, message);
        });

        // Handle cancel/close
        $('#dispatch-status-dialog .dispatch-dialog-btn-cancel, #dispatch-status-dialog .dispatch-dialog-close').on('click', () => {
            $('#dispatch-status-dialog').remove();
        });

        // Handle overlay click
        $('#dispatch-status-dialog .dispatch-dialog-overlay').on('click', () => {
            $('#dispatch-status-dialog').remove();
        });

        // Handle Enter key in message field
        $('#dispatch-status-message').on('keypress', (e) => {
            if (e.which === 13) { // Enter key
                $('#dispatch-status-dialog .dispatch-dialog-btn-confirm').click();
            }
        });

        // Handle Escape key
        $(document).on('keydown.dispatch-status-dialog', (e) => {
            if (e.which === 27) { // Escape key
                $('#dispatch-status-dialog').remove();
                $(document).off('keydown.dispatch-status-dialog');
            }
        });
    }

    showInputDialog(title, description, placeholder, defaultValue, callback) {
        // Remove any existing dialog
        $('#dispatch-input-dialog').remove();

        const dialogHtml = `
            <div id="dispatch-input-dialog" class="dispatch-input-dialog">
                <div class="dispatch-dialog-overlay"></div>
                <div class="dispatch-dialog-content">
                    <div class="dispatch-dialog-header">
                        <h3>${title}</h3>
                        <button class="dispatch-dialog-close">&times;</button>
                    </div>
                    <div class="dispatch-dialog-body">
                        <p>${description}</p>
                        <input type="text" id="dispatch-dialog-input" placeholder="${placeholder}" value="${defaultValue}" />
                    </div>
                    <div class="dispatch-dialog-footer">
                        <button class="dispatch-dialog-btn dispatch-dialog-btn-cancel">Cancel</button>
                        <button class="dispatch-dialog-btn dispatch-dialog-btn-confirm">Confirm</button>
                    </div>
                </div>
            </div>
        `;

        $('body').append(dialogHtml);

        // Focus the input
        setTimeout(() => {
            $('#dispatch-dialog-input').focus().select();
        }, 100);

        // Handle confirm
        $('#dispatch-input-dialog .dispatch-dialog-btn-confirm').on('click', () => {
            const value = $('#dispatch-dialog-input').val();
            $('#dispatch-input-dialog').remove();
            if (value.trim()) {
                callback(value);
            }
        });

        // Handle cancel/close
        $('#dispatch-input-dialog .dispatch-dialog-btn-cancel, #dispatch-input-dialog .dispatch-dialog-close').on('click', () => {
            $('#dispatch-input-dialog').remove();
        });

        // Handle overlay click
        $('#dispatch-input-dialog .dispatch-dialog-overlay').on('click', () => {
            $('#dispatch-input-dialog').remove();
        });

        // Handle Enter key
        $('#dispatch-dialog-input').on('keypress', (e) => {
            if (e.which === 13) { // Enter key
                $('#dispatch-input-dialog .dispatch-dialog-btn-confirm').click();
            }
        });

        // Handle Escape key
        $(document).on('keydown.dispatch-dialog', (e) => {
            if (e.which === 27) { // Escape key
                $('#dispatch-input-dialog').remove();
                $(document).off('keydown.dispatch-dialog');
            }
        });
    }

    getDepartmentColor(groups) {
        // Check for specific departments in priority order
        if (groups['LSPD'] || groups['LSPD_Internal']) {
            return '#2563eb'; // Vibrant blue for LSPD
        }
        if (groups['Sheriff'] || groups['Sheriff_Internal'] || groups['Sheriff Tow Truck']) {
            return '#674316'; // Brown for BCSO/Sheriff
        }
        if (groups['SAHP'] || groups['SAHP_Internal']) {
            return '#6b7280'; // Grey for SAHP
        }
        if (groups['LSFD'] || groups['LSFD_OffDuty'] || groups['TMU_Internal']) {
            return '#ef4444'; // Vibrant red for EMS/Fire
        }
        if (groups['Tow Truck'] || groups['Rocky Road Towing']) {
            return '#f97316'; // Vibrant orange for Tow Truck
        }
        if (groups['Dispatch']) {
            return '#8b5cf6'; // Vibrant purple for Dispatch
        }
        if (groups['DOC']) {
            return '#4b5563'; // Vibrant gray for DOC
        }
        if (groups['Ranger'] || groups['Ranger_Internal']) {
            return '#22c55e'; // Vibrant light green for Rangers
        }

        // Default color for civilians or unknown departments
        return '#9ca3af'; // Vibrant neutral gray
    }

    getRadioTextColor(backgroundColor) {
        // Convert hex to RGB to calculate luminance
        const hex = backgroundColor.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        // Calculate relative luminance using WCAG formula
        const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

        // Return white text for dark backgrounds, black text for light backgrounds
        return luminance > 0.5 ? '#000000' : '#ffffff';
    }



    getRankName(callsign, groups) {
      if ( callsign && callsign == 478 ) {
          return { name: 'Commander', icon: 'major' }
      }

      for (const group in groups)
      {
        let _group = this.dispatchGroups[group];

        if (_group !== undefined)
        {
          return _group;
        }
      }

      return {name: '', icon: ''};
    }

    getRandomColor() {
        // Organized color palette with brighter, more vibrant colors
        const colorPalette = [
            // Blues (channels 1-4)
            '#3b82ff', // Bright Electric Blue
            '#60a5fa', // Vivid Sky Blue
            '#2563eb', // Bright Blue
            '#14b8ff', // Neon Light Blue

            // Greens (channels 5-8)
            '#22c55e', // Bright Green
            '#4ade80', // Vivid Light Green
            '#16a34a', // Electric Green
            '#10d9c4', // Bright Emerald

            // Reds/Oranges (channels 9-12)
            '#ff3333', // Bright Red
            '#ff8c00', // Vivid Orange
            '#ff6b35', // Bright Orange
            '#ff5555', // Electric Red

            // Purples/Pinks (channels 13-16)
            '#a855f7', // Bright Purple
            '#c084fc', // Vivid Light Purple
            '#8b5cf6', // Electric Purple
            '#ff69b4', // Hot Pink

            // Yellows/Ambers (channels 17-20)
            '#ffd700', // Gold Yellow
            '#ffb347', // Bright Amber
            '#ffa500', // Vivid Orange
            '#ffff00', // Electric Yellow

            // Teals/Cyans (channels 21-24)
            '#00bfff', // Bright Cyan
            '#40e0d0', // Turquoise
            '#00ced1', // Dark Turquoise
            '#00ffff', // Electric Cyan

            // Bright Others (channels 25-28)
            '#9370db', // Medium Orchid
            '#ff1493', // Deep Pink
            '#00ff7f', // Spring Green
            '#ff4500'  // Orange Red
        ];

        // Use a cycling approach based on radio channel number
        if (!this.colorIndex) {
            this.colorIndex = 0;
        }

        const color = colorPalette[this.colorIndex % colorPalette.length];
        this.colorIndex++;

        return color;
    }

    generateRandomColor() {
        // Generate a random color with good contrast and visibility
        const hue = Math.floor(Math.random() * 360);
        const saturation = Math.floor(Math.random() * 40) + 60; // 60-100% saturation
        const lightness = Math.floor(Math.random() * 30) + 40;  // 40-70% lightness

        return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
    }

    setupHoverEffects() {
        // Remove existing hover event handlers to prevent duplicates
        $('.dispatch-profile-item').off('mouseenter.dispatch mouseleave.dispatch contextmenu.dispatch click.dispatch');

        // Add hover effects with namespaced events
        $('.dispatch-profile-item').on('mouseenter.dispatch', function() {
            $(this).addClass('dispatch-hovered');
        }).on('mouseleave.dispatch', function() {
            // Only remove hover if no context menu is open for this item
            if (!$(this).hasClass('dispatch-context-active')) {
                $(this).removeClass('dispatch-hovered');
            }
        }).on('contextmenu.dispatch', function(e) {
            // Mark this item as having an active context menu
            $('.dispatch-profile-item').removeClass('dispatch-context-active');
            $(this).addClass('dispatch-context-active dispatch-hovered');
        }).on('click.dispatch', (e) => {
            // Handle clicking on the name to set GPS
            if ($(e.target).closest('.dispatch-person').length) {
                const targetSource = $(e.currentTarget).data('source');
                if (targetSource) {
                    this.routeGpsToTarget(targetSource);
                }
            }
        });

        // Listen for context menu closing
        $(document).off('click.dispatch-context').on('click.dispatch-context', function(e) {
            // If clicking outside context menu, remove active states
            if (!$(e.target).closest('#dispatch-context-menu').length) {
                $('.dispatch-profile-item').removeClass('dispatch-context-active');
                // Remove hover from items that don't currently have mouse over them
                $('.dispatch-profile-item').each(function() {
                    if (!$(this).is(':hover')) {
                        $(this).removeClass('dispatch-hovered');
                    }
                });
            }
        });
    }

    routeGpsToTarget(targetSource) {
        // Send GPS routing request to server
        $.post('https://blrp_core/routeGps', JSON.stringify({
            source: targetSource
        }));
    }
};
