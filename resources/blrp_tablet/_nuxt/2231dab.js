(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[19],{

/***/ 1556:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=template&id=95986b30

var render = function render() {
  var _vm$realName, _vm$realView;
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _vm.ready ? _c('crudy-table', {
    attrs: {
      "name": (_vm$realName = _vm.realName) !== null && _vm$realName !== void 0 ? _vm$realName : _vm.name,
      "view": (_vm$realView = _vm.realView) !== null && _vm$realView !== void 0 ? _vm$realView : _vm.view
    }
  }) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=template&id=95986b30

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// EXTERNAL MODULE: ./components/Crudy/crudy-form/crudy-form-debug.vue + 9 modules
var crudy_form_debug = __webpack_require__(315);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=script&lang=ts






/* harmony default export */ var crudy_genvue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  name: 'crudy-gen',
  components: {
    CrudyFormDebug: crudy_form_debug["a" /* default */],
    CrudyTable: crudy_table["a" /* default */]
  },
  props: ['name', 'view', 'filter'],
  data: function data() {
    return {
      meta: null,
      ready: false,
      tableUUID: null,
      realName: null,
      realView: null
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      var name, route;
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            name = _this.$route.name;
            _context.next = 3;
            return _this.$router.resolve({
              name: name
            }).route;
          case 3:
            route = _context.sent;
            _this.meta = route === null || route === void 0 ? void 0 : route.meta;
            _this.realName = _this.meta.model;
            _this.realView = _this.meta.view;
            _this.ready = true;
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  }
}));
// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=script&lang=ts
 /* harmony default export */ var crudy_gen_crudy_genvue_type_script_lang_ts = (crudy_genvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  crudy_gen_crudy_genvue_type_script_lang_ts,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var crudy_gen = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1562:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(1532);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Documents/DocumentsList.vue?vue&type=template&id=257a5de0






var DocumentsListvue_type_template_id_257a5de0_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VRow["a" /* default */], {
    staticClass: "p-top",
    attrs: {
      "no-gutters": ""
    }
  }, [!_vm.faction_type ? _c(VCol["a" /* default */], {
    staticClass: "ml-1 mb-3",
    attrs: {
      "md": "4"
    }
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "success"
    },
    on: {
      "click": function click($event) {
        _vm.createModal = true;
      }
    }
  }, [_vm._v("\n        Create Doodly Doc (Contract)\n      ")])], 1) : _vm._e()], 1), _vm._v(" "), _c('app-roll-out', {
    attrs: {
      "title": "Create Contract",
      "hide-footer": ""
    },
    model: {
      value: _vm.createModal,
      callback: function callback($$v) {
        _vm.createModal = $$v;
      },
      expression: "createModal"
    }
  }, [_c('span', {
    staticClass: "p-top"
  }), _vm._v(" "), _c('app-form-group', {
    attrs: {
      "label": "Contract Title"
    }
  }, [_c(VTextField["a" /* default */], {
    model: {
      value: _vm.createForm.TITLE,
      callback: function callback($$v) {
        _vm.$set(_vm.createForm, "TITLE", $$v);
      },
      expression: "createForm.TITLE"
    }
  })], 1), _vm._v(" "), _c('app-form-group', {
    attrs: {
      "label": "Contract Expiration"
    }
  }, [_c(VTextField["a" /* default */], {
    attrs: {
      "placeholder": "N/A"
    },
    model: {
      value: _vm.createForm.EXPIRATION,
      callback: function callback($$v) {
        _vm.$set(_vm.createForm, "EXPIRATION", $$v);
      },
      expression: "createForm.EXPIRATION"
    }
  })], 1), _vm._v(" "), _c('app-form-group', {
    attrs: {
      "label": "Contract Details"
    }
  }, [_c(VTextarea["a" /* default */], {
    attrs: {
      "filled": ""
    },
    model: {
      value: _vm.createForm.DETAILS,
      callback: function callback($$v) {
        _vm.$set(_vm.createForm, "DETAILS", $$v);
      },
      expression: "createForm.DETAILS"
    }
  })], 1), _vm._v(" "), _c('app-form-group', [_c('span', {
    staticClass: "text-primary"
  }, [_vm._v("\n                      The contract will pop up for the "), _c('b', [_vm._v("closest player to you")]), _vm._v(".\n                    "), _c('br'), _vm._v("\n                      Once the player fills and submits the contract it will show up under "), _c('b', {
    staticClass: "text-warning"
  }, [_vm._v("My Documents")]), _vm._v(" "), _c('br'), _vm._v(" "), _c('span', {
    staticClass: "text-muted"
  }, [_vm._v("If the player does not fill or agree to the form, it will not show up")])])]), _vm._v(" "), _c('app-form-group', [_c(VBtn["a" /* default */], {
    attrs: {
      "variant": "success"
    },
    on: {
      "click": _vm.createContract
    }
  }, [_vm._v("\n        Offer to closest player\n      ")])], 1)], 1), _vm._v(" "), _vm.view ? _c('crudy-table', {
    attrs: {
      "name": "Document",
      "view": _vm.view
    }
  }) : _vm.tableFilter ? _c('crudy-table', {
    attrs: {
      "name": "Document",
      "view": "index",
      "filter": _vm.tableFilter
    }
  }) : _vm._e()], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Documents/DocumentsList.vue?vue&type=template&id=257a5de0

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/TableWrapper.vue + 4 modules
var TableWrapper = __webpack_require__(311);

// EXTERNAL MODULE: ./components/app-roll-out.vue + 4 modules
var app_roll_out = __webpack_require__(82);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// EXTERNAL MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue + 4 modules
var crudy_gen = __webpack_require__(1556);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Documents/DocumentsList.vue?vue&type=script&lang=js







/* harmony default export */ var DocumentsListvue_type_script_lang_js = ({
  name: 'DocumentsList',
  components: {
    CrudyTable: crudy_table["a" /* default */],
    CrudyGen: crudy_gen["a" /* default */],
    AppFormGroup: app_form_group["a" /* default */],
    AppRollOut: app_roll_out["a" /* default */],
    TableWrapper: TableWrapper["a" /* default */]
  },
  props: ['faction_type', 'personal_id', 'business_id', 'character_number', 'view'],
  data: function data() {
    return {
      createModal: false,
      tableFilter: null,
      createForm: {
        TITLE: null,
        EXPIRATION: null,
        DETAILS: null
      }
    };
  },
  created: function created() {
    if (this.faction_type) {
      this.tableFilter = {
        faction_type: this.faction_type
      };
      this.createForm.faction_type = this.faction_type;
    }
    if (this.personal_id) {
      this.tableFilter = {
        requested_by_id: 5996
      };
      this.createForm.personal_id = this.personal_id;
    }
    if (this.business_id) {
      this.tableFilter = {
        business_id: this.business_id
      };
      this.createForm.business_id = this.business_id;
    }
  },
  methods: {
    createContract: function createContract() {
      this.createModal = false;
      if (!this.createForm.expiration) this.createForm.expiration = 'Not Applicable';
      fetch("https://blrp_tablet/createContract", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify(this.createForm)
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {
        return resp;
      });
    }
  },
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  })
});
// CONCATENATED MODULE: ./components/Pages/Documents/DocumentsList.vue?vue&type=script&lang=js
 /* harmony default export */ var Documents_DocumentsListvue_type_script_lang_js = (DocumentsListvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Documents/DocumentsList.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Documents_DocumentsListvue_type_script_lang_js,
  DocumentsListvue_type_template_id_257a5de0_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var DocumentsList = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1626:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1655);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("01a1e194", content, true, {"sourceMap":false});

/***/ }),

/***/ 1654:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_note_container_vue_vue_type_style_index_0_id_b700020c_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1626);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_note_container_vue_vue_type_style_index_0_id_b700020c_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_note_container_vue_vue_type_style_index_0_id_b700020c_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1655:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".note-body{cursor:pointer;margin-bottom:2px;padding:10px}.note-body:hover{background-color:rgba(49,49,49,.7)}.note-body .note-inside{border-left:4px solid gray;padding-left:10px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1675:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(1532);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/note-container.vue?vue&type=template&id=b700020c





var note_containervue_type_template_id_b700020c_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "p-top"
  }, [_vm._l(_vm.incidentPerson.list_notes, function (note) {
    return _vm.incidentPerson.list_notes ? _c('div', {
      staticClass: "note-body"
    }, [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_c('span', [_vm._v(_vm._s(note.inserted_by))]), _vm._v(" "), _c('span', [_vm._v(" - ")]), _vm._v(" "), _c('span', [_vm._v(_vm._s(note.created_at))])]), _vm._v(" "), _c(VCol["a" /* default */], [_vm._v("\n        " + _vm._s(note.body) + "\n      ")])], 1)], 1) : _vm._e();
  }), _vm._v(" "), _vm.addingNote ? _c('div', [_c('app-form-group', [_c(VTextarea["a" /* default */], {
    attrs: {
      "filled": "",
      "outlined": "",
      "rows": "3"
    },
    model: {
      value: _vm.content,
      callback: function callback($$v) {
        _vm.content = $$v;
      },
      expression: "content"
    }
  })], 1), _vm._v(" "), _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    attrs: {
      "color": "primary"
    },
    on: {
      "click": _vm.submit
    }
  }, [_vm._v("Submit Note")])], 1) : _vm._e()], 2);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/note-container.vue?vue&type=template&id=b700020c

// EXTERNAL MODULE: ./node_modules/vue-markdown/dist/vue-markdown.common.js
var vue_markdown_common = __webpack_require__(496);
var vue_markdown_common_default = /*#__PURE__*/__webpack_require__.n(vue_markdown_common);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/note-container.vue?vue&type=script&lang=js


/* harmony default export */ var note_containervue_type_script_lang_js = ({
  name: 'note-container',
  props: ['incidentPerson'],
  components: {
    AppFormGroup: app_form_group["a" /* default */],
    VueMarkdown: vue_markdown_common_default.a
  },
  data: function data() {
    return {
      addingNote: false,
      content: null
    };
  },
  methods: {
    startAddingNote: function startAddingNote() {
      this.addingNote = true;
    },
    addExternal: function addExternal(content) {
      this.content = content;
      this.submit();
    },
    submit: function submit() {
      var _this = this;
      return this.$axios.$post("/police/incidents/add-person-note/".concat(this.incidentPerson.id), {
        contents: this.content
      }).then(function (r) {
        _this.content = null;
        _this.addingNote = false;
        window.$events.$emit('reload');
      });
    }
  }
});
// CONCATENATED MODULE: ./components/Common/note-container.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_note_containervue_type_script_lang_js = (note_containervue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/note-container.vue?vue&type=style&index=0&id=b700020c&prod&lang=scss
var note_containervue_type_style_index_0_id_b700020c_prod_lang_scss = __webpack_require__(1654);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/note-container.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_note_containervue_type_script_lang_js,
  note_containervue_type_template_id_b700020c_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var note_container = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1754:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(2008);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("5f76279c", content, true, {"sourceMap":false});

/***/ }),

/***/ 1755:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(2010);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("579876d2", content, true, {"sourceMap":false});

/***/ }),

/***/ 1756:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(2012);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("10140551", content, true, {"sourceMap":false});

/***/ }),

/***/ 2007:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MedicalRecordItem_vue_vue_type_style_index_0_id_f6c4351e_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1754);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MedicalRecordItem_vue_vue_type_style_index_0_id_f6c4351e_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MedicalRecordItem_vue_vue_type_style_index_0_id_f6c4351e_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 2008:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".lb-larger{width:130px!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2009:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SubmitMedicRecord_vue_vue_type_style_index_0_id_176206e4_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1755);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SubmitMedicRecord_vue_vue_type_style_index_0_id_176206e4_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SubmitMedicRecord_vue_vue_type_style_index_0_id_176206e4_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 2010:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".add-new-injury{border:.2px solid #1e1e1e;margin:10px;padding:15px}.add-new-injury:hover{background-color:#0e0e0e}.custom-control-input{border-radius:0!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2011:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_35670473_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1756);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_35670473_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_35670473_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 2012:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".procedure-item{padding-bottom:15px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2025:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTab.js
var VTab = __webpack_require__(1534);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabs.js + 3 modules
var VTabs = __webpack_require__(1550);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/_archived/ems/index.vue?vue&type=template&id=35670473



var emsvue_type_template_id_35670473_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('b-alert', {
    attrs: {
      "variant": "danger",
      "show": "",
      "dismissible": ""
    }
  }, [_vm._v("\n    This page is deprecated. Please use this only for historical records. Use the new incidents system going forward.\n  ")]), _vm._v(" "), _c(VTabs["a" /* default */], {
    attrs: {
      "no-fade": "",
      "no-key-nav": ""
    },
    model: {
      value: _vm.tabIndex,
      callback: function callback($$v) {
        _vm.tabIndex = $$v;
      },
      expression: "tabIndex"
    }
  }, [_c(VTab["a" /* default */], {
    attrs: {
      "title": "Records"
    }
  }, [_c('MedicalRecords')], 1), _vm._v(" "), _c(VTab["a" /* default */], {
    attrs: {
      "title": "Submit Record"
    }
  }, [_c('SubmitMedicRecord')], 1), _vm._v(" "), _c(VTab["a" /* default */], {
    attrs: {
      "title": "Documents",
      "lazy": ""
    }
  }, [_c('DocumentsList', {
    attrs: {
      "faction_type": "medical",
      "personal_id": false
    }
  })], 1)], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/_archived/ems/index.vue?vue&type=template&id=35670473

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(1533);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/EMS/MedicalRecords.vue?vue&type=template&id=231926b3






var MedicalRecordsvue_type_template_id_231926b3_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VRow["a" /* default */], {
    staticClass: "p-top"
  }, [_c(VCol["a" /* default */], {
    attrs: {
      "md": "4"
    }
  }, [_c(VForm["a" /* default */], {
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm.index('all');
      }
    }
  }, [_c(VTextField["a" /* default */], {
    staticStyle: {
      "width": "330px"
    },
    attrs: {
      "placeholder": "Search name"
    },
    model: {
      value: _vm.searchTerm,
      callback: function callback($$v) {
        _vm.searchTerm = $$v;
      },
      expression: "searchTerm"
    }
  })], 1)], 1), _vm._v(" "), _c(VCol["a" /* default */], [_c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "btn btn-outline-primary btn-sm",
    on: {
      "click": function click($event) {
        return _vm.index('all');
      }
    }
  }, [_vm._v("Search")]), _vm._v(" "), _c('div', {
    staticClass: "float-right"
  }, [_c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "btn btn-outline-primary btn-sm",
    staticStyle: {
      "margin-left": "15px"
    },
    on: {
      "click": _vm.indexAll
    }
  }, [_vm._v("Refresh Records")])], 1)], 1)], 1), _vm._v(" "), _c(VRow["a" /* default */], {
    staticClass: "p-top"
  }, [_c(VCol["a" /* default */], {
    attrs: {
      "md": "3"
    }
  }), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "text-small text-muted"
  }, [_vm._v("Name")]), _vm._v(" "), _c('br')]), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "text-small text-muted"
  }, [_vm._v("Medic")]), _vm._v(" "), _c('br')]), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "text-small text-muted"
  }, [_vm._v("Created")])])], 1), _vm._v(" "), _c('div', [_vm._l(_vm.records, function (r) {
    return _c('div', [_c('MedicalRecordItem', {
      attrs: {
        "r": r
      },
      on: {
        "index": _vm.index
      }
    })], 1);
  }), _vm._v(" "), _c('p', {
    staticClass: "text-muted text-center p-top"
  }, [_vm._v("\n            Results are limited to 200 records\n        ")])], 2)], 1);
};
var MedicalRecordsvue_type_template_id_231926b3_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/EMS/MedicalRecords.vue?vue&type=template&id=231926b3

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.join.js
var es_array_join = __webpack_require__(126);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/EMS/MedicalRecordItem.vue?vue&type=template&id=f6c4351e







var MedicalRecordItemvue_type_template_id_f6c4351e_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VRow["a" /* default */], {
    staticClass: "case-row",
    on: {
      "click": function click($event) {
        return _vm.select(_vm.r);
      }
    }
  }, [_c(VCol["a" /* default */], {
    staticClass: "tet-right",
    attrs: {
      "md": "3"
    }
  }, [_vm.r.eventType === 'Vehicle Collision' ? _c('b-badge', {
    staticClass: "text-primary lb-larger",
    attrs: {
      "variant": "dark",
      "size": "lg"
    }
  }, [_vm._v("Vehicle Collision")]) : _vm.r.eventType === 'Gun Shot Wound' ? _c('b-badge', {
    staticClass: "t-warrant lb-larger",
    attrs: {
      "variant": "dark",
      "size": "lg"
    }
  }, [_vm._v("Gun Shot Wound")]) : _vm.r.eventType === 'Falling Accident' ? _c('b-badge', {
    staticClass: "t-arrest lb-larger",
    attrs: {
      "variant": "dark",
      "size": "lg"
    }
  }, [_vm._v("Falling Accident")]) : _vm.r.eventType === 'Blunt Force' ? _c('b-badge', {
    staticClass: "t-citation lb-larger",
    attrs: {
      "variant": "dark",
      "size": "lg"
    }
  }, [_vm._v("Blunt Force")]) : _vm.r.eventType === 'Other' ? _c('b-badge', {
    staticClass: "t-warning lb-larger",
    attrs: {
      "variant": "dark",
      "size": "lg"
    }
  }, [_vm._v("Other")]) : _c('b-badge', {
    attrs: {
      "variant": "dark",
      "size": "lg"
    }
  }, [_vm._v(_vm._s(_vm.r.eventType))])], 1), _vm._v(" "), _c(VCol["a" /* default */], [_vm._v("\n                " + _vm._s(_vm.r.name) + "\n            ")]), _vm._v(" "), _c(VCol["a" /* default */], [_vm._v("\n                " + _vm._s(_vm.r.inserted_by) + "\n            ")]), _vm._v(" "), _c(VCol["a" /* default */], [_vm._v("\n                " + _vm._s(_vm.r.created_at) + "\n            ")])], 1), _vm._v(" "), _vm.selected && _vm.r.id === _vm.selected.id ? _c(VRow["a" /* default */], {
    staticClass: "case-description"
  }, [_c(VCol["a" /* default */], [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
    attrs: {
      "md": "2"
    }
  }, [_c('span', {
    staticClass: "text-muted font-weight-bold"
  }, [_vm._v("Medic (Author)")]), _vm._v(" "), _c('br'), _vm._v("\n                        " + _vm._s(_vm.r.inserted_by) + "\n                    ")]), _vm._v(" "), _c(VCol["a" /* default */], {
    attrs: {
      "md": "3"
    }
  }, [_c('span', {
    staticClass: "text-muted font-weight-bold"
  }, [_vm._v("STAMP")]), _vm._v(" "), _c('br'), _vm._v("\n                        " + _vm._s(_vm.r.created_at) + "\n                    ")]), _vm._v(" "), _c(VCol["a" /* default */], {
    attrs: {
      "md": "1"
    }
  }, [_c('span', {
    staticClass: "text-muted font-weight-bold"
  }, [_vm._v("Name")]), _vm._v(" "), _c('br'), _vm._v("\n                        " + _vm._s(_vm.r.name) + "\n                    ")]), _vm._v(" "), _c(VCol["a" /* default */], [_c('i', {
    staticClass: "fas fa-arrow-up fa-2x text-danger float-right",
    on: {
      "click": function click($event) {
        _vm.selected = null;
      }
    }
  })])], 1), _vm._v(" "), _c(VRow["a" /* default */], {
    staticStyle: {
      "padding-top": "20px"
    }
  }, [_c(VCol["a" /* default */], {
    attrs: {
      "md": "3"
    }
  }, [_c('span', {
    staticClass: "text-muted font-weight-bold"
  }, [_vm._v("Transport Method")]), _vm._v(" "), _c('br'), _vm._v(" "), _vm.r.transport ? _c('span', [_vm._v(_vm._s(_vm.r.transport))]) : _c('span', [_vm._v("N/A")])]), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "text-muted font-weight-bold"
  }, [_vm._v("Medication")]), _vm._v(" "), _c('br'), _vm._v(" "), _vm.r.medication ? _c('span', [_vm._v(_vm._s(_vm.r.medication))]) : _c('span', [_vm._v("N/A")])]), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "text-muted font-weight-bold"
  }, [_vm._v("Injury Locations")]), _vm._v(" "), _c('br'), _vm._v(" "), _vm.r.injuries ? _c('span', [_vm._v(_vm._s(_vm.r.injuries.join(', ')))]) : _c('span', [_vm._v("N/A")])]), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "text-muted font-weight-bold"
  }, [_vm._v("Symptoms")]), _vm._v(" "), _c('br'), _vm._v(" "), _vm.r.symptoms ? _c('span', [_vm._v(_vm._s(_vm.r.symptoms.join(', ')))]) : _c('span', [_vm._v("N/A")])])], 1), _vm._v(" "), _c(VRow["a" /* default */], {
    staticClass: "p-top"
  }, [_c('hr'), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "text-muted font-weight-bold"
  }, [_vm._v("Details")]), _vm._v(" "), _c('br'), _vm._v(" "), _c('p', [_c('vue-markdown', {
    attrs: {
      "source": _vm.r.details
    }
  })], 1)])], 1), _vm._v(" "), _vm.r.notes && _vm.r.notes.length > 0 && !_vm.preview ? _c('div', {
    staticClass: "p-top"
  }, [_c('b', [_vm._v("Notes")]), _vm._v(" "), _c('br'), _vm._v(" "), _c('NoteContainer', {
    attrs: {
      "notes": _vm.r.notes
    }
  })], 1) : _vm._e(), _vm._v(" "), !_vm.preview ? _c('div', {
    staticClass: "p-top"
  }, [_vm.addingNote ? _c('app-form-group', {
    attrs: {
      "label": "Add Record Note"
    }
  }, [_c(VTextField["a" /* default */], {
    model: {
      value: _vm.note,
      callback: function callback($$v) {
        _vm.note = $$v;
      },
      expression: "note"
    }
  })], 1) : _vm._e(), _vm._v(" "), _c('app-form-group', [_vm.addingNote ? _c(VBtn["a" /* default */], {
    attrs: {
      "size": "sm",
      "variant": "success"
    },
    on: {
      "click": _vm.saveNote
    }
  }, [_vm._v("\n                            Save Note\n                        ")]) : _vm._e(), _vm._v(" "), !_vm.addingNote ? _c(VBtn["a" /* default */], {
    staticClass: "btn btn-outline-success btn-sm",
    attrs: {
      "size": "sm"
    },
    on: {
      "click": function click($event) {
        _vm.addingNote = true;
      }
    }
  }, [_vm._v("\n                            Add Note\n                        ")]) : _vm._e(), _vm._v(" "), _c('span', {
    staticClass: "text-muted",
    staticStyle: {
      "padding-left": "30px"
    }
  }, [_vm._v("\n                            Record # "), _c('span', {
    staticClass: "text-warning"
  }, [_vm._v("MED-" + _vm._s(_vm.r.id))])])], 1)], 1) : _vm._e()], 1)], 1) : _vm._e()], 1);
};
var MedicalRecordItemvue_type_template_id_f6c4351e_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/EMS/MedicalRecordItem.vue?vue&type=template&id=f6c4351e

// EXTERNAL MODULE: ./node_modules/vue-markdown/dist/vue-markdown.common.js
var vue_markdown_common = __webpack_require__(496);
var vue_markdown_common_default = /*#__PURE__*/__webpack_require__.n(vue_markdown_common);

// EXTERNAL MODULE: ./components/Common/note-container.vue + 4 modules
var note_container = __webpack_require__(1675);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/EMS/MedicalRecordItem.vue?vue&type=script&lang=js





/* harmony default export */ var MedicalRecordItemvue_type_script_lang_js = ({
  name: 'MedicalRecordItem',
  components: {
    AppFormGroup: app_form_group["a" /* default */],
    NoteContainer: note_container["a" /* default */],
    VueMarkdown: vue_markdown_common_default.a
  },
  props: ['r', 'preview'],
  data: function data() {
    return {
      addingNote: false,
      selected: null,
      note: null
    };
  },
  mounted: function mounted() {
    var _this = this;
    window.$events.$on('medical::record::selected', function (data) {
      if (_this.selected && _this.selected.id !== data.id) {
        _this.selected = null;
      }
    });
  },
  destroyed: function destroyed() {
    window.$events.$off('medical::record::selected');
  },
  methods: {
    select: function select(record) {
      window.$events.$emit('medical::record::selected', record);
      if (this.selected && this.selected.id === record.id) {
        this.selected = null;
      } else {
        this.selected = record;
      }
    },
    searchRegistration: function searchRegistration() {
      window.$events.$emit('medical:record:search', this.selected.registration);
    },
    editRecord: function editRecord() {
      window.$events.$emit('medical::record::edit', this.selected);
    },
    saveNote: function saveNote() {
      var _this2 = this;
      this.$axios.$post('/medical/record-notes', {
        cat_mdt_id: this.selected.id,
        author: this.user.name,
        contents: this.note
      }).then(function (res) {
        _this2.$emit('index');
      });
    }
  },
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  })
});
// CONCATENATED MODULE: ./components/Pages/EMS/MedicalRecordItem.vue?vue&type=script&lang=js
 /* harmony default export */ var EMS_MedicalRecordItemvue_type_script_lang_js = (MedicalRecordItemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/EMS/MedicalRecordItem.vue?vue&type=style&index=0&id=f6c4351e&prod&lang=scss
var MedicalRecordItemvue_type_style_index_0_id_f6c4351e_prod_lang_scss = __webpack_require__(2007);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/EMS/MedicalRecordItem.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  EMS_MedicalRecordItemvue_type_script_lang_js,
  MedicalRecordItemvue_type_template_id_f6c4351e_render,
  MedicalRecordItemvue_type_template_id_f6c4351e_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var MedicalRecordItem = (component.exports);
// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/EMS/MedicalRecords.vue?vue&type=script&lang=js



/* harmony default export */ var MedicalRecordsvue_type_script_lang_js = ({
  name: 'MedicalRecords',
  components: {
    MedicalRecordItem: MedicalRecordItem
  },
  data: function data() {
    return {
      recordsTab: null,
      searchTerm: null,
      records: []
    };
  },
  mounted: function mounted() {
    var _this = this;
    this.index();
    window.$events.$on('medical:record:submitted', function (data) {
      _this.index();
    });
    window.$events.$on('medical:record:search', function (term) {
      _this.searchTerm = term;
      _this.index();
    });
  },
  destroyed: function destroyed() {
    window.$events.$off('medical:record:submitted');
    window.$events.$off('medical:record:search');
  },
  methods: {
    indexAll: function indexAll() {
      var type = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'all';
      this.searchTerm = null;
      return this.index();
    },
    index: function index() {
      var _this2 = this;
      var type = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'all';
      return this.$axios.$get("/medical/records?type=".concat(type, "&searchTerm=").concat(this.searchTerm)).then(function (r) {
        _this2.records = r;
      });
    }
  },
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  })
});
// CONCATENATED MODULE: ./components/Pages/EMS/MedicalRecords.vue?vue&type=script&lang=js
 /* harmony default export */ var EMS_MedicalRecordsvue_type_script_lang_js = (MedicalRecordsvue_type_script_lang_js); 
// CONCATENATED MODULE: ./components/Pages/EMS/MedicalRecords.vue





/* normalize component */

var MedicalRecords_component = Object(componentNormalizer["a" /* default */])(
  EMS_MedicalRecordsvue_type_script_lang_js,
  MedicalRecordsvue_type_template_id_231926b3_render,
  MedicalRecordsvue_type_template_id_231926b3_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var MedicalRecords = (MedicalRecords_component.exports);
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(1532);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/EMS/SubmitMedicRecord.vue?vue&type=template&id=176206e4







var SubmitMedicRecordvue_type_template_id_176206e4_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "p-top"
  }, [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_c(VRow["a" /* default */], {
    staticStyle: {
      "padding-top": "15px"
    }
  }, [_c(VCol["a" /* default */], {
    staticClass: "record-type t-bolo",
    class: {
      selected: _vm.activeType === 'Vehicle Collision'
    },
    on: {
      "click": function click($event) {
        return _vm.selectType('Vehicle Collision');
      }
    }
  }, [_vm._v("\n\t\t\t            Vehicle Collision\n\t\t            ")]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "record-type t-warrant",
    class: {
      selected: _vm.activeType === 'Gun Shot Wound'
    },
    on: {
      "click": function click($event) {
        return _vm.selectType('Gun Shot Wound');
      }
    }
  }, [_vm._v("\n\t\t\t            Gun Shot Wound\n\t\t            ")]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "record-type t-arrest",
    class: {
      selected: _vm.activeType === 'Falling Accident'
    },
    on: {
      "click": function click($event) {
        return _vm.selectType('Falling Accident');
      }
    }
  }, [_vm._v("\n\t\t\t            Falling Accident\n\t\t            ")]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "record-type t-citation",
    class: {
      selected: _vm.activeType === 'Blunt Force'
    },
    on: {
      "click": function click($event) {
        return _vm.selectType('Blunt Force');
      }
    }
  }, [_vm._v("\n\t\t\t            Blunt Force\n\t\t            ")]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "record-type t-warning",
    class: {
      selected: _vm.activeType === 'Other'
    },
    on: {
      "click": function click($event) {
        return _vm.selectType('Other');
      }
    }
  }, [_vm._v("\n\t\t\t            Other\n\t\t            ")])], 1), _vm._v(" "), _vm.activeType ? _c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
    attrs: {
      "md": "5"
    }
  }, [_c('app-form-group', {
    attrs: {
      "label": "Patients Name"
    }
  }, [_c(VTextField["a" /* default */], {
    attrs: {
      "autocomplete": "off"
    },
    model: {
      value: _vm.form.name,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "name", $$v);
      },
      expression: "form.name"
    }
  })], 1)], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    attrs: {
      "md": "4"
    }
  }, [_c('app-form-group', {
    attrs: {
      "label": "Transport Method"
    }
  }, [_c('b-form-select', {
    attrs: {
      "options": _vm.transportOptions
    },
    model: {
      value: _vm.form.transport,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "transport", $$v);
      },
      expression: "form.transport"
    }
  })], 1)], 1), _vm._v(" "), _c(VCol["a" /* default */], [_c('app-form-group', {
    attrs: {
      "label": "Medication Given"
    }
  }, [_c('b-form-select', {
    attrs: {
      "options": _vm.medicationOptions
    },
    model: {
      value: _vm.form.medication,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "medication", $$v);
      },
      expression: "form.medication"
    }
  })], 1)], 1)], 1) : _vm._e(), _vm._v(" "), _vm.activeType ? _c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
    attrs: {
      "md": "5"
    }
  }, [_c('app-form-group', {
    attrs: {
      "label": "Details"
    }
  }, [_c(VTextarea["a" /* default */], {
    attrs: {
      "filled": "",
      "autocomplete": "off",
      "rows": "15"
    },
    model: {
      value: _vm.form.details,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "details", $$v);
      },
      expression: "form.details"
    }
  })], 1)], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    staticStyle: {
      "padding-left": "30px"
    },
    attrs: {
      "md": "2"
    }
  }, [_c('app-form-group', {
    attrs: {
      "label": "Accident Location"
    }
  }, [_c('b-form-checkbox-group', {
    attrs: {
      "id": "checkbox-group-1",
      "options": _vm.injuriesTypes,
      "name": "flavour-1",
      "stacked": ""
    },
    model: {
      value: _vm.form.injuries,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "injuries", $$v);
      },
      expression: "form.injuries"
    }
  })], 1)], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    staticStyle: {
      "padding-left": "60px"
    },
    attrs: {
      "md": "5"
    }
  }, [_c('app-form-group', {
    attrs: {
      "label": "Symptoms"
    }
  }, [_c('b-form-checkbox-group', {
    attrs: {
      "id": "checkbox-group-2",
      "options": _vm.symptomTypes,
      "name": "flavour-2",
      "stacked": ""
    },
    model: {
      value: _vm.form.symptoms,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "symptoms", $$v);
      },
      expression: "form.symptoms"
    }
  })], 1)], 1)], 1) : _vm._e(), _vm._v(" "), _vm.activeType ? _c(VRow["a" /* default */], [_c(VCol["a" /* default */])], 1) : _vm._e(), _vm._v(" "), _vm.activeType ? _c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_vm.activeType ? _c('div', {
    staticStyle: {
      "padding-top": "15px"
    },
    attrs: {
      "id": "generic-fields-submit"
    }
  }, [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "btn btn-success",
    on: {
      "click": _vm.submitRecord
    }
  }, [_vm._v("Submit Medical Record")]), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "float-right",
    attrs: {
      "variant": "dark"
    },
    on: {
      "click": _vm.reset
    }
  }, [_vm._v("Reset Fields")])], 1)], 1)], 1) : _vm._e()])], 1) : _vm._e()], 1)], 1)], 1);
};
var SubmitMedicRecordvue_type_template_id_176206e4_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/EMS/SubmitMedicRecord.vue?vue&type=template&id=176206e4

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/EMS/SubmitMedicRecord.vue?vue&type=script&lang=js



/* harmony default export */ var SubmitMedicRecordvue_type_script_lang_js = ({
  name: 'SubmitMedicRecord',
  components: {
    AppFormGroup: app_form_group["a" /* default */]
  },
  props: [],
  data: function data() {
    return {
      activeType: null,
      form: {
        name: null,
        eventType: null,
        transport: 'No Transport Needed',
        medication: 'None given',
        symptoms: [],
        injuries: [],
        details: null
      },
      transportOptions: [{
        value: 'No Transport Needed',
        text: 'No transport needed'
      }, {
        value: 'Moved via EMS Ground',
        text: 'Moved via EMS ground'
      }, {
        value: 'Moved via Police Ground',
        text: 'Moved via Police ground'
      }, {
        value: 'Moved via EMS Air',
        text: 'Moved via EMS air'
      }, {
        value: 'Moved via Police Air',
        text: 'Moved via Police air'
      }, {
        value: 'Moved via Civilian',
        text: 'Moved via Civilian'
      }],
      medicationOptions: [{
        value: 'None given',
        text: 'None given'
      }, {
        value: 'Vicodin',
        text: 'Vicodin'
      }, {
        value: 'Hydrocodone',
        text: 'Hydrocodone'
      }, {
        value: 'Morphine',
        text: 'Morphine'
      }, {
        value: 'Other Medication',
        text: 'Other Medication'
      }],
      injuriesTypes: [{
        value: 'Left Leg',
        text: 'Left Leg'
      }, {
        value: 'Right Leg',
        text: 'Right Leg'
      }, {
        value: 'Spinal',
        text: 'Spinal'
      }, {
        value: 'Chest',
        text: 'Chest'
      }, {
        value: 'Left Arm',
        text: 'Left Arm'
      }, {
        value: 'Right Arm',
        text: 'Right Arm'
      }, {
        value: 'Neck',
        text: 'Neck'
      }, {
        value: 'Head',
        text: 'Head'
      }],
      symptomTypes: [{
        value: 'Headache',
        text: 'Headache'
      }, {
        value: 'Confusion',
        text: 'Confusion'
      }, {
        value: 'Dizziness',
        text: 'Dizziness'
      }, {
        value: 'Nausea and/or vomiting',
        text: 'Nausea and/or vomiting'
      }, {
        value: 'Slurred speech',
        text: 'Slurred speech'
      }, {
        value: 'Sensitivity to light',
        text: 'Sensitivity to light'
      }, {
        value: 'Delayed response to questions',
        text: 'Delayed response to questions'
      }, {
        value: 'Fever',
        text: 'Fever'
      }, {
        value: 'Whiplash',
        text: 'Whiplash'
      }, {
        value: 'Concussion',
        text: 'Concussion'
      }, {
        value: 'Anxiety or post-traumatic stress disorder (PTSD)',
        text: 'PTSD'
      }, {
        value: 'Stress',
        text: 'Stress'
      }, {
        value: 'Blurry Vision',
        text: 'Blurry Vision'
      }, {
        value: 'Swelling',
        text: 'Swelling'
      }, {
        value: 'Redness',
        text: 'Redness'
      }, {
        value: 'Abrasions',
        text: 'Abrasions'
      }]
    };
  },
  methods: {
    reset: function reset() {
      this.form = {
        name: null,
        eventType: null,
        transport: 'No Transport Needed',
        medication: 'None given',
        symptoms: [],
        injuries: [],
        details: null
      };
    },
    selectType: function selectType(type) {
      this.error = null;
      this.activeType = type;
    },
    submitRecord: function submitRecord() {
      var _this = this;
      this.form.eventType = this.activeType;
      this.form.inserted_by = this.user.name;
      return this.$axios.$post('/medical/records', this.form).then(function (r) {
        _this.reset();
        window.$events.$emit('medical:record:submitted', r);
      });
    }
  },
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  })
});
// CONCATENATED MODULE: ./components/Pages/EMS/SubmitMedicRecord.vue?vue&type=script&lang=js
 /* harmony default export */ var EMS_SubmitMedicRecordvue_type_script_lang_js = (SubmitMedicRecordvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/EMS/SubmitMedicRecord.vue?vue&type=style&index=0&id=176206e4&prod&lang=scss
var SubmitMedicRecordvue_type_style_index_0_id_176206e4_prod_lang_scss = __webpack_require__(2009);

// CONCATENATED MODULE: ./components/Pages/EMS/SubmitMedicRecord.vue






/* normalize component */

var SubmitMedicRecord_component = Object(componentNormalizer["a" /* default */])(
  EMS_SubmitMedicRecordvue_type_script_lang_js,
  SubmitMedicRecordvue_type_template_id_176206e4_render,
  SubmitMedicRecordvue_type_template_id_176206e4_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var SubmitMedicRecord = (SubmitMedicRecord_component.exports);
// EXTERNAL MODULE: ./components/Pages/Documents/DocumentsList.vue + 4 modules
var DocumentsList = __webpack_require__(1562);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/_archived/ems/index.vue?vue&type=script&lang=js



/* harmony default export */ var emsvue_type_script_lang_js = ({
  components: {
    DocumentsList: DocumentsList["a" /* default */],
    SubmitMedicRecord: SubmitMedicRecord,
    MedicalRecords: MedicalRecords
  },
  data: function data() {
    return {
      tabIndex: 0
    };
  }
});
// CONCATENATED MODULE: ./pages/_archived/ems/index.vue?vue&type=script&lang=js
 /* harmony default export */ var _archived_emsvue_type_script_lang_js = (emsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/_archived/ems/index.vue?vue&type=style&index=0&id=35670473&prod&lang=scss
var emsvue_type_style_index_0_id_35670473_prod_lang_scss = __webpack_require__(2011);

// CONCATENATED MODULE: ./pages/_archived/ems/index.vue






/* normalize component */

var ems_component = Object(componentNormalizer["a" /* default */])(
  _archived_emsvue_type_script_lang_js,
  emsvue_type_template_id_35670473_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var ems = __webpack_exports__["default"] = (ems_component.exports);

/***/ })

}]);