(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[16],{

/***/ 2237:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/parts/RowCitizenNote.vue?vue&type=template&id=22b1f0f2&scoped=true
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.note ? _c('div', {
    staticClass: "row-details"
  }, [_c('div', {
    staticClass: "action-buttons mt-2"
  }, [_c('app-action-btn', {
    attrs: {
      "label": "Show Temporary Display",
      "icon": "fa-solid fa-location-pin",
      "click": _vm.printTempt
    }
  }), _vm._v(" "), _c('app-action-btn', {
    attrs: {
      "label": "Print Physical Copy",
      "icon": "fa-solid fa-print",
      "click": _vm.printPaper
    }
  }), _vm._v(" "), _c('app-action-btn', {
    attrs: {
      "disabled": _vm.sharePending || _vm.shareSuccess,
      "label": _vm.sharePending ? 'Sharing.. Do not close' : 'Share Closest Person',
      "icon": "fa-solid fa-share-all",
      "click": _vm.share
    }
  })], 1), _vm._v(" "), _c('app-markdown-view', {
    staticClass: "mt-4",
    attrs: {
      "source": _vm.note.contents
    }
  })], 1) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/parts/RowCitizenNote.vue?vue&type=template&id=22b1f0f2&scoped=true

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./components/Common/app-action-btn.vue + 4 modules
var app_action_btn = __webpack_require__(63);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./store/mutation-types.js
var mutation_types = __webpack_require__(45);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/parts/RowCitizenNote.vue?vue&type=script&lang=js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }














/* harmony default export */ var RowCitizenNotevue_type_script_lang_js = ({
  name: "RowNote",
  components: {
    AppActionBtn: app_action_btn["a" /* default */],
    AppMarkdownView: app_markdown_view["a" /* default */]
  },
  props: ['data'],
  data: function data() {
    return {
      sharePending: false,
      shareSuccess: false
    };
  },
  mounted: function mounted() {
    var _this = this;
    window.$events.$on('tools:offerRequestNearPlayer:accept', function (data) {
      if (_this.sharePending) {
        _this.sharePending = false;
        _this.$axios.$post("/notes/links/".concat(_this.note.id, "/").concat(data.character_number));
        setTimeout(function () {
          _this.shareSuccess = false;
        }, 2000);
      }
    });
  },
  destroyed: function destroyed() {
    window.$events.$off('tools:offerRequestNearPlayer:accept');
  },
  methods: {
    publishNote: function publishNote() {
      var _this2 = this;
      return this.$axios.$post('/news/submit', {
        title: this.note.title,
        note_id: this.note.id
      }).then(function (r) {
        return _this2.$store.dispatch('nui/publishPaper', {
          id: _this2.note.id,
          title: _this2.note.title,
          content: _this2.note.contents
        });
      });
    },
    printPaper: function printPaper() {
      var _this3 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return _this3.$store.dispatch('nui/printPaper', {
                id: _this3.note.id,
                title: _this3.note.title,
                content: _this3.note.contents
              });
            case 2:
              return _context.abrupt("return", _context.sent);
            case 3:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    },
    printTempt: function printTempt() {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              _context2.next = 2;
              return _this4.$store.dispatch('nui/pinTempPaper', {
                title: "".concat(_this4.note.title),
                contents: _this4.note.contents
              });
            case 2:
              return _context2.abrupt("return", _context2.sent);
            case 3:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    },
    share: function share() {
      var _this5 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _this5.sharePending = true;
              setTimeout(function () {
                _this5.sharePending = false;
              }, 15000);
              _context3.next = 4;
              return _this5.$store.dispatch('nui/requestNearPlayer', 'Someone is offering to share a note with you.');
            case 4:
              return _context3.abrupt("return", _context3.sent);
            case 5:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }))();
    }
  },
  watch: {
    // closestCharacterNumber(value)
    // {
    //   if (!value) return;
    //
    //   this.sharePending = false;
    //   this.shareSuccess = true;
    //   console.log('got character number change')
    // }
  },
  computed: _objectSpread({
    note: function note() {
      return this.data.item;
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    closestCharacterNumber: 'system/closestCharacterNumber'
  }))
});
// CONCATENATED MODULE: ./components/Common/parts/RowCitizenNote.vue?vue&type=script&lang=js
 /* harmony default export */ var parts_RowCitizenNotevue_type_script_lang_js = (RowCitizenNotevue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/parts/RowCitizenNote.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  parts_RowCitizenNotevue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  "22b1f0f2",
  null
  
)

/* harmony default export */ var RowCitizenNote = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);