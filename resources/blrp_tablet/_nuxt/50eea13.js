(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[114],{

/***/ 1555:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(890);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VProgressLinear/VProgressLinear.js
var VProgressLinear = __webpack_require__(460);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-page/crudy-page.vue?vue&type=template&id=a90d2eb4












var crudy_pagevue_type_template_id_a90d2eb4_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.instance && _vm.instance._responseCode === 401 ? _c('div', [_c('app-no-perms')], 1) : _vm.instance ? _c('div', [!_vm.instance._loadingFirstTime && _vm.instance._item ? _c('div', [!_vm.config.hideHeader ? _c(VCard["a" /* default */], [_vm.image ? _c(VImg["a" /* default */], {
    attrs: {
      "src": _vm.image
    }
  }) : _vm._e(), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
    staticClass: "text-h3",
    attrs: {
      "cols": "2",
      "align": "center"
    }
  }, [_c('i', {
    class: _vm.instance._meta.icon,
    style: "color: ".concat(_vm.instance._item.color)
  })]), _vm._v(" "), _c(VCol["a" /* default */], [_c('div', {
    staticClass: "mb-2",
    staticStyle: {
      "font-size": "25px"
    }
  }, [_vm.$route.path.includes('cad') ? _c('span', [_vm._v("\n                " + _vm._s(_vm.instance._item.id) + " -\n              ")]) : _vm._e(), _vm._v("\n\n              " + _vm._s(_vm.title) + "\n\n              "), _c('crudy-context-menu', {
    attrs: {
      "uuid": _vm.uuid,
      "name": _vm.name,
      "actions": _vm.actions,
      "item": _vm.instance._item
    }
  })], 1), _vm._v(" "), _c('div', {
    staticClass: "text-muted",
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm.instance._item.created_at ? _c('app-timestamp', {
    attrs: {
      "stamp": _vm.instance._item.created_at
    }
  }) : _vm._e(), _vm._v(" "), _vm.instance._item.created_at ? _c('span', [_vm._v("•")]) : _vm._e(), _vm._v(" " + _vm._s(_vm.instance._meta.title) + "\n            ")], 1)])], 1)], 1)], 1) : _vm._e(), _vm._v(" "), !_vm.config.hideHeader ? _c(VCard["a" /* default */], [_c(components_VCard["b" /* VCardText */], [_c(VRow["a" /* default */], [_vm.logo ? _c(VCol["a" /* default */], {
    attrs: {
      "cols": "3"
    }
  }, [_c(VImg["a" /* default */], {
    attrs: {
      "src": _vm.logo,
      "height": "128",
      "contain": ""
    }
  })], 1) : _vm._e(), _vm._v(" "), _c(VCol["a" /* default */], [_c(VRow["a" /* default */], _vm._l(_vm.infoColumns, function (column) {
    return _c(VCol["a" /* default */], {
      key: "a-body-field-".concat(column.key),
      attrs: {
        "cols": "3"
      }
    }, [_c('div', [_vm._v("\n                  " + _vm._s(column.label) + "\n                ")]), _vm._v(" "), _c('div', {
      staticClass: "text-white"
    }, [column.key !== 'actions' ? _c('crudy-table-column-component', {
      attrs: {
        "item": _vm.instance._item,
        "value": _vm.instance._item[column.key],
        "column": column
      }
    }) : _vm._e()], 1)]);
  }), 1), _vm._v(" "), _vm._l(_vm.contentColumns, function (column) {
    return _c('div', {
      key: "b-body-field-".concat(column.key),
      staticClass: "mt-5",
      attrs: {
        "cols": "3"
      }
    }, [column.key !== 'actions' ? _c('crudy-table-column-component', {
      attrs: {
        "name": _vm.name,
        "item": _vm.instance._item,
        "value": _vm.instance._item[column.key],
        "column": column
      }
    }) : _vm._e()], 1);
  })], 2)], 1)], 1), _vm._v(" "), _c(components_VCard["a" /* VCardActions */], [_vm._t("actions")], 2)], 1) : _vm._e(), _vm._v(" "), _vm.instance._item.contents ? _c('div', {
    staticClass: "mt-3"
  }, [_c('crudy-editor', {
    model: {
      value: _vm.instance._item.contents,
      callback: function callback($$v) {
        _vm.$set(_vm.instance._item, "contents", $$v);
      },
      expression: "instance._item.contents"
    }
  }), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "mt-3",
    attrs: {
      "color": "success"
    },
    on: {
      "click": function click($event) {
        return _vm.triggerFieldUpdate('contents', _vm.instance._item.contents);
      }
    }
  }, [_vm._v("\n        Save Changes\n      ")])], 1) : _vm._e(), _vm._v(" "), _vm.instance._item.content ? _c('div', {
    staticClass: "mt-3"
  }, [_c('crudy-editor', {
    model: {
      value: _vm.instance._item.content,
      callback: function callback($$v) {
        _vm.$set(_vm.instance._item, "content", $$v);
      },
      expression: "instance._item.content"
    }
  }), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "mt-3",
    attrs: {
      "color": "success"
    },
    on: {
      "click": function click($event) {
        return _vm.triggerFieldUpdate('contents', _vm.instance._item.content);
      }
    }
  }, [_vm._v("\n        Save Changes\n      ")])], 1) : _vm._e(), _vm._v(" "), !_vm.config.hideRelationships ? _c('crudy-relations-renderer', {
    attrs: {
      "relationships": _vm.instance._relationships
    }
  }) : _vm._e(), _vm._v(" "), _vm._t("body", null, {
    "item": _vm.instance._item,
    "uuid": _vm.uuid,
    "actions": _vm.actions,
    "model": _vm.name
  })], 2) : _c('div', {
    staticClass: "p-5"
  }, [_c(VProgressLinear["a" /* default */], {
    attrs: {
      "active": true,
      "indeterminate": true
    }
  })], 1)]) : !_vm.instance ? _c('div', {
    staticClass: "p-5"
  }, [_c(VProgressLinear["a" /* default */], {
    attrs: {
      "active": true,
      "indeterminate": true
    }
  })], 1) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Crudy/crudy-page/crudy-page.vue?vue&type=template&id=a90d2eb4

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./utils/store-accessor.ts
var store_accessor = __webpack_require__(27);

// EXTERNAL MODULE: ./components/Common/TableWrapper.vue + 4 modules
var TableWrapper = __webpack_require__(311);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table-items-component.vue + 4 modules
var crudy_table_items_component = __webpack_require__(492);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table-column-component/crudy-table-column-component.vue + 4 modules
var crudy_table_column_component = __webpack_require__(269);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTab.js
var VTab = __webpack_require__(1534);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabItem.js + 1 modules
var VTabItem = __webpack_require__(1554);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabs.js + 3 modules
var VTabs = __webpack_require__(1550);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabsItems.js + 1 modules
var VTabsItems = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-parts/crudy-relations-renderer.vue?vue&type=template&id=5de72f5d







var crudy_relations_renderervue_type_template_id_5de72f5d_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "mt-4"
  }, [_c(VTabs["a" /* default */], {
    staticClass: "mt-4",
    attrs: {
      "grow": "",
      "background-color": "transparent",
      "slider-color": "white"
    },
    model: {
      value: _vm.selectedRelationshipTab,
      callback: function callback($$v) {
        _vm.selectedRelationshipTab = $$v;
      },
      expression: "selectedRelationshipTab"
    }
  }, _vm._l(_vm.relationships, function (relationship) {
    return relationship.type === 'hasMany' && relationship.relatedModelSchemaMeta ? _c(VTab["a" /* default */], {
      key: "a-".concat(relationship.knowsModel, "-").concat(relationship.foreignKeyName)
    }, [relationship.relatedModelSchemaMeta.icon ? _c('i', {
      staticClass: "mr-2",
      class: relationship.relatedModelSchemaMeta.icon
    }) : _vm._e(), _vm._v(" "), _c('span', [_vm._v("\n        " + _vm._s(relationship.relatedModelSchemaMeta.title) + "\n      ")])]) : _vm._e();
  }), 1), _vm._v(" "), _c(VTabsItems["a" /* default */], {
    staticStyle: {
      "background-color": "transparent"
    },
    model: {
      value: _vm.selectedRelationshipTab,
      callback: function callback($$v) {
        _vm.selectedRelationshipTab = $$v;
      },
      expression: "selectedRelationshipTab"
    }
  }, _vm._l(_vm.relationships, function (relationship) {
    return relationship.type === 'hasMany' && relationship.relatedModelSchemaMeta ? _c(VTabItem["a" /* default */], {
      key: "b-".concat(relationship.knowsModel, "-").concat(relationship.foreignKeyName),
      staticStyle: {
        "background-color": "transparent"
      }
    }, [_c('crudy-table', {
      attrs: {
        "name": relationship.knowsModel,
        "view": "index",
        "autofill": Object(defineProperty["a" /* default */])({}, relationship.foreignKeyName, _vm.$route.params.id),
        "filter": Object(defineProperty["a" /* default */])({}, relationship.foreignKeyName, _vm.$route.params.id)
      }
    })], 1) : _vm._e();
  }), 1)], 1);
};
var crudy_relations_renderervue_type_template_id_5de72f5d_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Crudy/crudy-parts/crudy-relations-renderer.vue?vue&type=template&id=5de72f5d

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-parts/crudy-relations-renderer.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/* harmony default export */ var crudy_relations_renderervue_type_script_lang_js = ({
  name: 'crudy-relations-renderer',
  components: {
    CrudyTable: crudy_table["a" /* default */]
  },
  props: ['relationships'],
  data: function data() {
    return {
      selectedRelationshipTab: null
    };
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./components/Crudy/crudy-parts/crudy-relations-renderer.vue?vue&type=script&lang=js
 /* harmony default export */ var crudy_parts_crudy_relations_renderervue_type_script_lang_js = (crudy_relations_renderervue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Crudy/crudy-parts/crudy-relations-renderer.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  crudy_parts_crudy_relations_renderervue_type_script_lang_js,
  crudy_relations_renderervue_type_template_id_5de72f5d_render,
  crudy_relations_renderervue_type_template_id_5de72f5d_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var crudy_relations_renderer = (component.exports);
// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-context-menu.vue + 4 modules
var crudy_context_menu = __webpack_require__(74);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-editor.vue + 27 modules
var crudy_editor = __webpack_require__(312);

// EXTERNAL MODULE: ./components/Common/app-no-perms.vue + 4 modules
var app_no_perms = __webpack_require__(491);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-page/crudy-page.vue?vue&type=script&lang=ts


function crudy_pagevue_type_script_lang_ts_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function crudy_pagevue_type_script_lang_ts_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? crudy_pagevue_type_script_lang_ts_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : crudy_pagevue_type_script_lang_ts_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





















/* harmony default export */ var crudy_pagevue_type_script_lang_ts = ({
  name: 'crudy-page',
  components: {
    AppNoPerms: app_no_perms["a" /* default */],
    CrudyEditor: crudy_editor["a" /* default */],
    AppTimestamp: app_timestamp["a" /* default */],
    CrudyContextMenu: crudy_context_menu["a" /* default */],
    CrudyRelationsRenderer: crudy_relations_renderer,
    CrudyTableColumnComponent: crudy_table_column_component["default"],
    CrudyTableItemsComponent: crudy_table_items_component["a" /* default */],
    CrudyTable: crudy_table["a" /* default */],
    TableWrapper: TableWrapper["a" /* default */]
  },
  props: {
    name: {},
    view: {},
    config: {
      default: function _default() {
        return {
          hideHeader: false,
          hideRelationships: false,
          hideLoader: false
        };
      }
    }
  },
  data: function data() {
    return {
      uuid: null,
      identifier: null,
      selectedRelationshipTab: null
    };
  },
  created: function created() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      var _a, item;
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _this.identifier = _this.$route.params.id;
            _context.next = 3;
            return store_accessor["d" /* CrudyStore */].activatePage({
              name: _this.name,
              view: (_a = _this.view) !== null && _a !== void 0 ? _a : 'view',
              pk: _this.$route.params.id
            });
          case 3:
            _this.uuid = _context.sent;
            // Get item from CrudyStore
            item = store_accessor["c" /* CrudyPageStore */].instances[_this.uuid]._item; // @ts-ignore
            window.$events.$on('crudy:form:finish', function () {
              store_accessor["c" /* CrudyPageStore */].refreshCrudyPage(_this.uuid);
            });
          case 6:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  beforeDestroy: function beforeDestroy() {
    // @ts-ignore
    window.$events.$off('crudy:form:finish');
  },
  methods: {
    tableFilter: function tableFilter(key, value) {
      var filter = {};
      filter[key] = value;
      return filter;
    },
    debounce: function debounce(func) {
      var _this2 = this;
      var timeout = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2000;
      var timer;
      return function () {
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        clearTimeout(timer);
        timer = setTimeout(function () {
          func.apply(_this2, args);
        }, timeout);
      };
    },
    triggerFieldUpdate: function triggerFieldUpdate(fieldName, fieldValue) {
      var _this3 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        var values;
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              values = {
                id: _this3.instance._item.id
              };
              values[fieldName] = fieldValue;
              _context2.next = 4;
              return store_accessor["d" /* CrudyStore */].sendItemFieldUpdate({
                name: _this3.name,
                values: values
              });
            case 4:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    },
    refresh: function refresh() {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _context3.next = 2;
              return store_accessor["c" /* CrudyPageStore */].refreshCrudyPage(_this4.uuid);
            case 2:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }))();
    }
  },
  computed: crudy_pagevue_type_script_lang_ts_objectSpread({
    instance: function instance() {
      if (this.uuid) {
        return store_accessor["c" /* CrudyPageStore */].instances[this.uuid];
      }
    },
    title: function title() {
      if (this.instance._item.name) return this.instance._item.name;
      if (this.instance._item.title) return this.instance._item.title;
      if (this.instance._item.address) return this.instance._item.address;
      if (this.instance._item.registration) return this.instance._item.registration;
      if (this.instance._item.display_name) return this.instance._item.display_name;
    },
    image: function image() {
      if (this.instance._item.main_picture) return this.instance._item.main_picture;
      if (this.instance._item.image_link) return this.instance._item.image_link;
    },
    logo: function logo() {
      var _a, _b;
      if ((_a = this.instance._item.department) === null || _a === void 0 ? void 0 : _a.logo) return (_b = this.instance._item.department) === null || _b === void 0 ? void 0 : _b.logo;
      if (this.instance._item.avatar_url) return this.instance._item.avatar_url;
    },
    infoColumns: function infoColumns() {
      return this.instance._table.columns.filter(function (c) {
        return c.key !== 'description' && c.key !== 'created_at' && c.key !== 'updated_at' && c.key !== 'deleted_at' && c.key !== 'name' && c.key !== 'title' && c.key !== 'content' && c.key !== 'contents';
      });
    },
    contentColumns: function contentColumns() {
      return this.instance._table.columns.filter(function (c) {
        return c.key === 'content' || c.key === 'contents' || c.key === 'description';
      });
    },
    actions: function actions() {
      return store_accessor["d" /* CrudyStore */].instances[this.name]._schema.actions;
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode'
  })),
  watch: {
    'instance._item': function instance_item() {
      this.$emit('loaded', this.instance._item);
      var item = this.instance._item;
      console.log('item', item);
      // if item has title, or name add it to the history
      if (item.title || item.name) {
        var title = item.title || item.name;
        // Dispatch to store
        this.$store.dispatch('system/addHistoryItem', {
          name: title,
          path: this.$route.fullPath,
          icon: item.icon || this.instance._meta.icon
        });
      }
    }
  }
});
// CONCATENATED MODULE: ./components/Crudy/crudy-page/crudy-page.vue?vue&type=script&lang=ts
 /* harmony default export */ var crudy_page_crudy_pagevue_type_script_lang_ts = (crudy_pagevue_type_script_lang_ts); 
// CONCATENATED MODULE: ./components/Crudy/crudy-page/crudy-page.vue





/* normalize component */

var crudy_page_component = Object(componentNormalizer["a" /* default */])(
  crudy_page_crudy_pagevue_type_script_lang_ts,
  crudy_pagevue_type_template_id_a90d2eb4_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var crudy_page = __webpack_exports__["a"] = (crudy_page_component.exports);

/* nuxt-component-imports */
installComponents(crudy_page_component, {CrudyTableColumnComponent: __webpack_require__(269).default})


/***/ }),

/***/ 1563:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(7);
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(9);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(14);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(15);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(8);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(11);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(0);
/* harmony import */ var _mixins_measurable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(188);
/* harmony import */ var _mixins_toggleable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(100);
/* harmony import */ var _directives_intersect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(175);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(16);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(1);








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
// Mixins

 // Directives

 // Utilities



/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"])(_mixins_measurable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], _mixins_toggleable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"]).extend({
  name: 'VLazy',
  directives: {
    intersect: _directives_intersect__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"]
  },
  props: {
    options: {
      type: Object,
      // For more information on types, navigate to:
      // https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API
      default: function _default() {
        return {
          root: undefined,
          rootMargin: undefined,
          threshold: undefined
        };
      }
    },
    tag: {
      type: String,
      default: 'div'
    },
    transition: {
      type: String,
      default: 'fade-transition'
    }
  },
  computed: {
    styles: function styles() {
      return _objectSpread({}, this.measurableStyles);
    }
  },
  methods: {
    genContent: function genContent() {
      var children = this.isActive && Object(_util_helpers__WEBPACK_IMPORTED_MODULE_12__[/* getSlot */ "s"])(this);
      return this.transition ? this.$createElement('transition', {
        props: {
          name: this.transition
        }
      }, children) : children;
    },
    onObserve: function onObserve(entries, observer, isIntersecting) {
      if (this.isActive) return;
      this.isActive = isIntersecting;
    }
  },
  render: function render(h) {
    return h(this.tag, {
      staticClass: 'v-lazy',
      attrs: this.$attrs,
      directives: [{
        name: 'intersect',
        value: {
          handler: this.onObserve,
          options: this.options
        }
      }],
      on: this.$listeners,
      style: this.styles
    }, [this.genContent()]);
  }
}));

/***/ }),

/***/ 1593:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1646);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("64c527b9", content, true, {"sourceMap":false});

/***/ }),

/***/ 1596:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/aemt.98dc3a1.png";

/***/ }),

/***/ 1597:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7EAAAOxAGVKw4bAAACPUlEQVQ4ja2SX0sUYRTGf+/OvLM7s/9wE8FulNKLksgwwds2+gZ9ii66CBIzlGSTzKLu+gpB1EV/LqLQDIwIiRILSlYjSRNTc9fZmdmZ2Xm7CLbctbueu/N733MeOOeBBs1Nj6gXj8dUI39+f0i9eTbaxPVGULU9YmETxhA+vhc08X01++Rik9Pso2YGENsPmmaC+Zfj9Ya3UyNKJsx/O85NDauZB3sdlucn6vXSu4k9b9P3Lqi5qWEFIABmHg4rwgAzrRPXdaSZwrAiNtdsktk46WwG1w5w7TJhLcQph5w6e0PUBzSq+P6m8v1dNr5vI6VGri2HoRkcPjHU9F8UFyaVlBpBUMP9WcLZ9Yg0i5QVoSV0vIoLShKEGgCZjIlMSNAiVFRD13UNy4ywWk2q2RjlHYetHwHlioEZ+oDEdgysuEdbm0WqRRK34jgVD8c1iNnbDvaOi+8HSNNEaRn6ThdErFahWoXAD4kbNU6euSZqehppmvh+QKXkUilFv3cwP31ZWWlJd/8VAfDp9VWlGwFdfWPiw6uCSicVjic5MnBJLH+8owJ7Hbei6M0XRAzgeH5crHyxAVgr3lLtnSkOtOcASLVkybVnOdhhsrp4Wx3qOSdWv9r05gsC/gpSsjXDt8XrKmFEKKEIgxoAnUfPCwUooTATNVY+TyrMVP0K9dAP5EdFcWFSuSWbwPPw/T/ZWV3aQMZ1ZCJJR8/gnlPuiXLXsUHR3X9F7JZ0NtcdAJ7eHVI7WxHlUryp+b/oF1h29nweCsRbAAAAAElFTkSuQmCC"

/***/ }),

/***/ 1598:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAA7DAAAOwwHHb6hkAAABkklEQVQ4y32ST0uVQRSHn5k5c29dEyuUbgvR1UVQEhetwiJw4aJ92+gD9QH6Bm4DCRRauJNKlATlluWyRVgk3D++M3NavK9xJ2jOdp55Zs6Pnznef6Pt6hPJeACcSVyOu6w9fWFopsRIGH1neX0DzByoghly+G6bySkxYgww6BP4AaqIH2GImaDEiPeBODok6TQAGsc8ePyI062e3l6M3Lo3T2f+CfHn64wR37oWRNL4iBBv1K9VFd9OIkvP+00GZ3z+sKML93PG+9Va4CSi1Rc01AGpBkSWshVKjDiXsBIxyTX7JqzNMygxNgVlPACsYluK9QpoJigxUg0NX98LriNMd5WpmUSsTCYoMWIdoIbhhWN4Ac4qrV52nxIjKYITEKNNy/LvA5QYCVHpzCpR09/DyzxDSoyEAHcWr0jS2Aks9DY53dqZKNIzfh+/yphf53otMHTmVtD2FKqKcMXHt7s8nCjSwe62riznTDiTWkAy+JmXcLMLmoAByt4/IfyfEdeepb+3j7q655YRmLt5kQrMH/CiBlfgPh5hAAAAAElFTkSuQmCC"

/***/ }),

/***/ 1599:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/chief-paramedic.cc890e3.png";

/***/ }),

/***/ 1600:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/corporal.1b33e32.png";

/***/ }),

/***/ 1601:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAALcSURBVDhPVVNLTxNRFP5mWsY+KMhDoIUIKNBKIikk6o4t7NSg+AOqGxO2sMANiQiEkOjWBCILNxITEDCRhRo1BgoJEoGCFZDwRqBTygClM72ee0sNfJOZe+855/vuebQSI4AQPT6Gf3wcJyc6bK4CxI0YDrVDRMIq4kzCpeJcOLMsUBQLclOyYLVYOQ1CILi5iL3gNlLsCtxuD7SRDzBbrYgu/IYe08Ey0sEkGzLvPsD+1gq+T47CkZaG2pqahMDqxhoKnPlCkWPq3n3o0WNI4bBYrd4qsOJiyI980H4twGK9gDd9fWhtbYWZExyONEGMx+OQZRnOhz5sDw3BVl0NQzuAlJEJ+XoFjnb3IJkk8JrtdrvgyPxjkiRxSCKntha76en41NODydFRzL8bQObVKzCTTaILzCYTdF0XsSIDiR4Ofvt4ZyeODiJYHRigejchaRrU5WX4m5uhbW2hrKMDbH1dZCs44msYYjnc28PHxkb0P23FIQVUPmtDfHYWu2tr8Le3Y767G+rYGJR8F1LM4u6EwAGNjON9fT02aL1M5JP9fag01qw7t2GljqdyG/mmfT4xXhuVwyEEViIr4nCjqQneqipEaa/SrQeBAJw0ESU3F8dkyy4qgrulBZvBIFx5eYIjBJyWHOgGgz01VTSJV8cTVCgoMhvA0dISTDyYfCZFQSgUQqXXy6mnAtl5mAvtYKqrC1MTE3AIF/VkehrG7l8wSl+h8/biIgLNT5DpKkRpSYmIEQJCeT8CBzXwZk4OEgVRFpRRbGcHBo0szM/03up5DU9ZAaTT0f//L3C8Gh5G6eQPWCRGsx/EH78fNrLbCwvhqqtDiZwFue0xSs0XEwQOLpAE/ThY79t+sZ978Zy99HhYb3k5+9LQwMJk29Q1FjNiwp/EOYEkPn/9xiZnAuyE9qqmscHBEfYzMEOtiCcCzuBcCWexuraO2eACVDWECrcbnmvuU89ZAP8Aam+ACdrSn4IAAAAASUVORK5CYII="

/***/ }),

/***/ 1602:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/fire-cheif.f5a0d0a.png";

/***/ }),

/***/ 1603:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/fts.1b5e2e1.png";

/***/ }),

/***/ 1604:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/lieutenant.237accb.png";

/***/ }),

/***/ 1605:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/major.4937d58.png";

/***/ }),

/***/ 1606:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/officer.1a94d8b.png";

/***/ }),

/***/ 1607:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,/9j/4AAQSkZJRgABAQEASABIAAD/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAEAAAAAAAD/2wBDAAIBAQIBAQICAgICAgICAwUDAwMDAwYEBAMFBwYHBwcGBwcICQsJCAgKCAcHCg0KCgsMDAwMBwkODw0MDgsMDAz/2wBDAQICAgMDAwYDAwYMCAcIDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAz/wAARCAAQABADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9Pv2hPjxq3i74h32jw+I4bHw7eWyTWlusMe+3t1kijF+8LK5v1mklYLbrmOSDEhCOiieh8JP2mbr4R/ELQ9O1bxbpP/CNXV42mtbAQsNWuJYpJ2NpDGAbRrRYsSISsLRRPJGs0twPKuftH/AjxB4L8X6xq2jeHdNuNJitZhpt9KkU4TzYotlvM7urWsdvLbRmN1xbpEBHIGErtDnfAj9lvWPiFd+B7PxPpVnrnhnwuVvBqUs2UsLtYf372Uu4vcvePK32hyvkOjShGWSPzbnxZe29rpe9/O2/5f1sfq1JZX/Zqc3Hk5XdLl5r8r1/xbXTfVtfvEj/2Q=="

/***/ }),

/***/ 1608:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsIAAA7CARUoSoAAAAHVSURBVDhPY6ApiI6u5/PwmMgO5ZIOZGTCEp2di2qhXNLAlCn7eRQUIt9KSoY8mDlzJitUmHhgb18Yw8RU+J+JKee/mVlmKFSYOJCWVs8lIxP/koHh7n8Ghmv/hYVD7tjb17NApQkDO7v8KQwMs4Ga/wPxPyCe+t/EJKMPKo0f2NrmmHFzFwA1fQfi+0D8CIi//OfmzvqfmNjpA1WGHdTXz9USF096z8DwAKyJgSERiNOA+AcQ3/kvJBT5qr5+kQpUOSowNk5j5eIK3sXAcAKo+M9/RsbW/zY2ecetrfPOMTB0AcVAXjnyX1o65tLKlTsVodogoLt7jTw3d9A2BoY9UIWL/uvqJp/u7V3J2d8/X0BLK+kSA8NyoDgoTHb+l5GJPt3evlwBrFlTM0peUjLuOQPDQajmDf/l5KLOxcQUc4MVAEFUVLugrGzUVQaGrVBDDvwXE4t5qqQUKweUdopgYCgFCv4F4pX/5eWjL1lZlfJCtCJAQECFsLx8zA0GhrVAdSCLioEYpBdMpAP93PlfTS1+a35+vwBECyaorJwrqqmZsJ+BoROoOQthAAeH+x9b24IWiDLCwNGxqIOLyxNigKlptmVycocZVI5okJ7ea2ZjU2ANALneqwyoKPt6AAAAAElFTkSuQmCC"

/***/ }),

/***/ 1609:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAB3klEQVQ4jWNgoCVob68Q7GuplsanhgWfpLb4j2QBju9GDAwMUSTb3tFRzv/q2sQbX58u+j2rv8AAlzomXBJWWj+yRWV+qnMJfWEx0/2XS5IBnZ2dvJrKb6L+/D7H8PvLQQZlxWchC6aUaxPl9IaGBqY7B73X/n2t/P/RcavbN3e7nvj7Wvn/izNWV2ZPaBUn6AIvwyuNcooXgn5+/Mlw8Kxe9e7TJnHvnzJ9E5W+p+1ksG92Q0MD7oDfuyy24Os1hf+/70n9P7vRewlMfPfyqJpfd2T+/7gl8//S1qAZyHqYYYwNU0LKrfRP9bCy/mF48VT+wtYTdtG7Dhz4xsDAwKCsG3xEmPW3ujDfKx1+3hcmHtY2ikYyHtu3nzr1l3lqQwNPth/jDHPdS6VcHP8ZHj0Uu775vJNfcW3LM5jhBw4c+C+hHrOV+99XXQm+txpigs8N/v/9aKdvGneAOdxRONLdnbuRlV2Y4fgFsUWHbppFF9d2PUX33oEDB/5wSfqs//+D+7+0GI+9mq68/KvHr9mYg53UzcXEZHwPneHqOvtSI7e6oe0LrjA6derU35Xbb+xXVXV9JsQj6PvuzffLjFOass3///kllNs0ezvO0MUC2isyDPl4/suQooc2AACgxcG2ROO2ZAAAAABJRU5ErkJggg=="

/***/ }),

/***/ 1610:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/sergeant.bb9191c.png";

/***/ }),

/***/ 1611:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAZdEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjQ+jcx2AAABTUlEQVQ4T+3Tz07CMBwH8B2Mpid9A07e/MdFE+N7ePVsYvQVlCXAWTInMFw3OoV1f6BbRwzRbTAO3nwAT76AEQ+EkMwxspmZ8AY06aH9/Nr8+k3KMKuRSeBRaefaWN9dFovyFPvO0tg0vXvZIdb1sgJsmFekQ7MuNNB6TUDgvo6A03P80Wj0LooIRPugWpMBX4Vg4RDQHvWDIEg9qtlgRFE6lSX5y+yaM9uxQ0VRwnKpPOMq3NTAxsRyrEkTNqdzpzZNnb/jfxqCeB53e8MW99gC++G5XqipWth/7n96g+FRhauvzWehWNqfu/vqpu67w8PMU6NObJvaoaqq4Yvnvf3PQXiQIqaxu56bdSi1tiih3wM/uLjl+DPcxuOuQXLJJTJEm4SQP1fxWDfM1BkFtfI6Ng+SA1BE2wi1TpJ1E+G8qhmpSzD249UvWCTwC8t7zQZDErCoAAAAAElFTkSuQmCC"

/***/ }),

/***/ 1612:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAZdEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjQ+jcx2AAACw0lEQVQ4T3VSS08TURgtEJGEnSYGJFFkDQtjxEQtLnTFxoDhH5j4K0YJhSpQJCVqmb5nOnc6D+fBvKczfdFCpCnhB1jjI0ZYaA1hpQuutyTTlJhOMsm93zn33PPd8/l8HZ/ACf3oH+ysda4BCQZbnG64j+P5JxwnPutGkLfUp0SGnukqoOt6plSqmN0ItmEbhbybPIen0pk5SRQxXpCfW6bVLBaLf1hewNZCYYwEWYylWEwUBEzaUl9YtvW3kC/8pKgstrryGoslMnO+5dXw5XA47GTIDHQdF0bwCFwKLkGO5+qabsYt241vhDf2KYqCbt6F6+F1uBZag6Ik2oZmXTpzgx6nd3Fx0XYcB6qaCvdq9TqghQue1VQK9AeDwf3WBaZhwnqt7pJp0NtuJUOAXoEXGqbl/CJJ8tCxnGNeVgc8AkWCAUESjtHhQ4Ikmo7tfKEI0NcWoAF3U1XMmusUx1JJcpgg6YIgydMegcny07qmFxw7fxWPJcey2WxN4OVbbQECsENZmrvIMe/v87w8i+z1oUhHPIIgCiOozT5BUh4j3j207icpfui/tCRJWrctl+wWI3KR1jR95Rwei6UmKAD86NYp3dI/71R3muXtqh9QjD/4ctkfXA75WzhK6YFpmb8rlUoj5zhTyTTtj7yLTvg2o+l7eDTxQ1ZkiHKGqXQKzi/MwygePdE0rYFibMRisRNVVWEul4P4Jg4XAgswEU98pxnu7pmbjbdvhjAM288X8hC1AXd3dz8ajnXNs/pqJTQaCAQ+lctlKG0hvPrhQDfMK+1WUM49FGD3DMOA6IVPkdC3Vq1jDno4hvuq6dopzdKwlC8doJbbuA8A9rqiKM3tcnUG30w8RCJHqmbc9gQQPqkZ2lGhuP0ogsdneYZvKpJ6o3MOxmVRGfMKBMkOMyw32RHjHZbjh709GrxRkmLGW/t/plOw/lUY0UAAAAAASUVORK5CYII="

/***/ }),

/***/ 1613:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAZdEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjQ+jcx2AAACG0lEQVQ4T52SPWgTYRzGQ0FnN10cihIEcXfSSXBy0s3ZoYiWiK1oGkmKUCQlH/SSuzb3kQtvkuYul+vVO99Lm6S9mK/Lx+AkBScHHcRdxTw9AoKiidgX3ul9+D3/5/m/Pt+ME9tgrqVSwuVZmplvqqbzcpFETgSQJXKqrJU/5+Tc2xMBeF6+QW2Keq0+VlT14n9DBEHk+m4fo+EI+7X646kAjs9ekCTizwhZf2aL93Oboj/Fpq+IovhpOByiP+jDsuyRaZiXyobp92JNbklR/VKWnPexbOZloVgY27aNSqUCY9eATW3ouo6BO4Bz6IDkCEieILYeQygcwtqLNaTY1PeMKD2cTPY0uHLreTj8RSkpsKgFraKB2+LQ73nutoWF+wtYfLCISDgCOSeDUvqhcehc/y1WNJqYX1pecoPPgpBECe1WG912d+Iej8eRSCbQbXXRafeoab0++9dOiExOBwKB7dXwKsxdE71OD/aejWa3iaN3R+j03HVPMzdzI6HQyhMxK8J1XeQLeeg7OkpKCcPBEAfN5r1/rpPZYAbOgQOvaaiaCjbNorZfg/nKhNvt05kAns/OeyWNG42GN777zWm+WU5xm1GGYX4YhoEqrX7V1J0zUyHJJPOIWhRNp/W+Vm9c/Sn0wDfTbPpjRaugtte4OxVQLCqdslJWvK/7hwsh2+cEUa4WCgVtKkCScndmZRRFMicI2du/ao4BpTVSysErnvgAAAAASUVORK5CYII="

/***/ }),

/***/ 1614:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABU0lEQVQ4jZXQP2obURDH8e/7s7uSZWEMEag0mJBCvoZbg6+QpDDkGDlD3OgKyQXsJmVwlTQpQiIwBNQYOUHKSm/37fulELgIr9hMOfzmM8MYMvXh+s3bF6f2MlEIwJlkfiz+fLq4mr/+N+tzwOGofT47fzXDTEACs2X1/t0ml80CIjbU34g8gIQvdqS0Db2Bsgx0u88kjfdgF6iKOhfNA4NhJIUvxG4AgI0t5cD1B6oqoPY7igUASZGiPOkPOCes7zBpv9WYhLMxC9hcs2sToQassKWwhYDU/4Kwtiw+etyBZzwVo6NEW2d35QFrARl2j47tCpwV7bHpD3QtOA/JCABnRFB2Pg+0MXHwTHRKT8DvX3khCzRBHJ80yAsBjsj93f88sXEMJ2dQjZCEpyHGbX/AyPvy6CUMp6AE1GDmRW9gF8vl19ubpdxYAJZgNmt+5rJ/AWjgj5luIA5yAAAAAElFTkSuQmCC"

/***/ }),

/***/ 1615:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1650);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("afc81cd0", content, true, {"sourceMap":false});

/***/ }),

/***/ 1624:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1625);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("7b5d4dc6", content, true, {"sourceMap":false});

/***/ }),

/***/ 1625:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-input--checkbox.v-input--indeterminate.v-input--is-disabled{opacity:.6}.v-input--checkbox.v-input--dense{margin-top:4px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1626:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1655);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("01a1e194", content, true, {"sourceMap":false});

/***/ }),

/***/ 1639:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony default export */ __webpack_exports__["a"] = ({
  mounted: function mounted() {
    var _this = this;
    window.$events.$on('reload', function () {
      _this.refresh();
    });
    window.$events.$on('crudy:form:finish', function () {
      // this.refresh()
    });
  },
  beforeDestroy: function beforeDestroy() {
    window.$events.$off('reload');
    window.$events.$off('crudy:form:finish');
  }
});

/***/ }),

/***/ 1642:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-auto-textarea.vue?vue&type=template&id=5680c6a2
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div");
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-auto-textarea.vue?vue&type=template&id=5680c6a2

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.map.js
var es_array_map = __webpack_require__(70);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-auto-textarea.vue?vue&type=script&lang=js




/* harmony default export */ var app_auto_textareavue_type_script_lang_js = ({
  name: "app-auto-textarea",
  components: {},
  props: ['predefinedValues', 'disableBadWorld', 'disableAllPerson', 'disableAllLocation'],
  data: function data() {
    return {
      text: '',
      config: {
        collection: [{
          trigger: '@',
          values: []
        }, {
          trigger: '@',
          values: []
          // selectTemplate: item => item.original.value,
        }, {
          trigger: '#',
          values: [],
          selectTemplate: function selectTemplate(item) {
            return item.original.value;
          }
        }]
      }
    };
  },
  mounted: function mounted() {
    if (this.predefinedValues) {
      this.config.collection[0].values = this.predefinedValues;
      this.config.collection[0].selectTemplate = function (item) {
        return item.original.value;
      };
    } else {
      this.config.collection[0].values = this.searchFriends;
    }
    if (!this.disableAllLocation) {
      this.config.collection[2].values = this.searchLocations;
    }
  },
  methods: {
    searchFriends: function searchFriends(text, callback) {
      if (text) {
        return this.$axios.$get("/social/search-mentionable/".concat(text)).then(function (data) {
          callback(data.map(function (i) {
            return {
              key: i.display_name,
              value: i.display_name
            };
          }));
        });
      } else {
        callback([]);
      }
    },
    searchLocations: function searchLocations(text, callback) {
      if (text) {
        return this.$axios.$get("/police/locations/".concat(text)).then(function (data) {
          callback(data.map(function (i) {
            return {
              key: i.name,
              value: i.name
            };
          }));
        });
      } else {
        callback([]);
      }
    },
    replaced: function replaced() {},
    noMatch: function noMatch() {}
  }
});
// CONCATENATED MODULE: ./components/Common/app-auto-textarea.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_auto_textareavue_type_script_lang_js = (app_auto_textareavue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-auto-textarea.vue?vue&type=style&index=0&id=5680c6a2&prod&lang=scss
var app_auto_textareavue_type_style_index_0_id_5680c6a2_prod_lang_scss = __webpack_require__(1645);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-auto-textarea.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_auto_textareavue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_auto_textarea = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1645:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_auto_textarea_vue_vue_type_style_index_0_id_5680c6a2_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1593);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_auto_textarea_vue_vue_type_style_index_0_id_5680c6a2_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_auto_textarea_vue_vue_type_style_index_0_id_5680c6a2_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1646:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".tribute-container ul{background-color:#131313;border-left:3px solid #4f4f4f;list-style-type:none;margin-top:5px;padding:10px}.tribute-container ul li{cursor:pointer;padding:4px}.tribute-container ul li.highlight{background-color:#9d0000}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1648:
/***/ (function(module, exports, __webpack_require__) {

var map = {
	"./aemt.png": 1596,
	"./asst-fire-cheif.png": 1597,
	"./captain.png": 1598,
	"./chief-paramedic.png": 1599,
	"./corporal.png": 1600,
	"./emt.png": 1601,
	"./fire-cheif.png": 1602,
	"./fts.png": 1603,
	"./lieutenant.png": 1604,
	"./major.png": 1605,
	"./officer.png": 1606,
	"./paramedic.png": 1607,
	"./recruit.png": 1608,
	"./senior-deputy.png": 1609,
	"./sergeant.png": 1610,
	"./star-silver-double.png": 1611,
	"./star-silver-quad.png": 1612,
	"./star-silver-single.png": 1613,
	"./vert-gold-single.png": 1614
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = 1648;

/***/ }),

/***/ 1649:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_person_icon_vue_vue_type_style_index_0_id_6bbb1a2f_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1615);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_person_icon_vue_vue_type_style_index_0_id_6bbb1a2f_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_person_icon_vue_vue_type_style_index_0_id_6bbb1a2f_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1650:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".rank-image{filter:brightness(1.5)}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1654:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_note_container_vue_vue_type_style_index_0_id_b700020c_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1626);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_note_container_vue_vue_type_style_index_0_id_b700020c_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_note_container_vue_vue_type_style_index_0_id_b700020c_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1655:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".note-body{cursor:pointer;margin-bottom:2px;padding:10px}.note-body:hover{background-color:rgba(49,49,49,.7)}.note-body .note-inside{border-left:4px solid gray;padding-left:10px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1669:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.search.js
var es_string_search = __webpack_require__(192);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/PolicePersonSelector.vue?vue&type=template&id=931dbd24







var PolicePersonSelectorvue_type_template_id_931dbd24_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VRow["a" /* default */], {
    attrs: {
      "no-gutters": ""
    }
  }, [!_vm.scanSuccessful ? _c(VCol["a" /* default */], {
    attrs: {
      "md": "6"
    }
  }, [_c('multiselect', {
    attrs: {
      "options": _vm.options,
      "loading": _vm.isLoading,
      "internal-search": false
    },
    on: {
      "search-change": _vm.search,
      "select": function select($event) {
        return _vm.$emit('selected', $event.id);
      }
    },
    scopedSlots: _vm._u([{
      key: "singleLabel",
      fn: function fn(props) {
        return [_c('span', {
          staticClass: "option__title"
        }, [_vm._v(_vm._s(props.option.name))])];
      }
    }, {
      key: "option",
      fn: function fn(props) {
        return [_c('div', {
          staticClass: "option__desc"
        }, [_c('span', {
          staticClass: "option__title"
        }, [_vm._v(_vm._s(props.option.name))]), _vm._v(" "), _c('span', [_vm._v(" - ")]), _vm._v(" "), _c('span', {
          staticClass: "option__small"
        }, [_vm._v(_vm._s(props.option.license))])])];
      }
    }], null, false, **********),
    model: {
      value: _vm.value,
      callback: function callback($$v) {
        _vm.value = $$v;
      },
      expression: "value"
    }
  })], 1) : _vm._e(), _vm._v(" "), _c(VCol["a" /* default */], [_c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "ml-3",
    attrs: {
      "disabled": _vm.scanSuccessful,
      "color": _vm.scanSuccessful ? 'success' : 'primary'
    },
    on: {
      "click": _vm.scanId
    }
  }, [_vm.scanSuccessful ? _c('i', {
    staticClass: "fa-solid fa-badge-check mr-2"
  }) : _vm._e(), _vm._v("\n      " + _vm._s(_vm.buttonLabel) + "\n    ")]), _vm._v(" "), _vm.scanSuccessful ? _c(VBtn["a" /* default */], {
    staticClass: "ml-4 btn btn-outline-danger",
    on: {
      "click": _vm.reset
    }
  }, [_vm._v("\n      Reset\n    ")]) : _vm._e()], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/PolicePersonSelector.vue?vue&type=template&id=931dbd24

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// EXTERNAL MODULE: ./node_modules/vue-multiselect/dist/vue-multiselect.min.js
var vue_multiselect_min = __webpack_require__(202);
var vue_multiselect_min_default = /*#__PURE__*/__webpack_require__.n(vue_multiselect_min);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/PolicePersonSelector.vue?vue&type=script&lang=js




/* harmony default export */ var PolicePersonSelectorvue_type_script_lang_js = ({
  name: "PolicePersonSelector",
  components: {
    Multiselect: vue_multiselect_min_default.a
  },
  props: ['current'],
  data: function data() {
    return {
      value: null,
      options: [],
      isLoading: false,
      scannedDL: null,
      scanSuccessful: false,
      buttonLabel: 'Scan ID'
    };
  },
  methods: {
    clear: function clear() {
      this.value = null;
    },
    search: function search(searchQuery) {
      var _this = this;
      if (searchQuery.length > 2) {
        this.isLoading = true;
        if (this.$route.path.includes('cad')) {
          this.$axios.$get("/police/persons/simple-search/".concat(searchQuery)).then(function (r) {
            _this.isLoading = false;
            _this.options = r;
          });
        } else {
          this.$axios.$get("/social/persons/simple-search/".concat(searchQuery)).then(function (r) {
            _this.isLoading = false;
            _this.options = r;
          });
        }
      }
    },
    scanId: function scanId() {
      var _this2 = this;
      this.isLoading = true;
      return this.$axios.$post('https://blrp_tablet/scanID').then(function (data) {
        var DL = data.DL;
        _this2.$axios.$get("/police/persons/simple-search/".concat(DL)).then(function (data) {
          _this2.isLoading = false;
          _this2.options = data;
          if (_this2.options.length > 0) {
            _this2.value = _this2.options[0].id;
            _this2.$emit('selected', _this2.value);
            _this2.buttonLabel = _this2.options[0].name;
            _this2.scanSuccessful = true;
          } else {
            _this2.$store.dispatch('audio/play', '/audio/alert.mp3');
          }
        });
      });
    },
    reset: function reset() {
      this.buttonLabel = 'Scan ID';
      this.value = null;
      this.scanSuccessful = false;
    }
  }
});
// CONCATENATED MODULE: ./components/Common/PolicePersonSelector.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_PolicePersonSelectorvue_type_script_lang_js = (PolicePersonSelectorvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/PolicePersonSelector.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_PolicePersonSelectorvue_type_script_lang_js,
  PolicePersonSelectorvue_type_template_id_931dbd24_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var PolicePersonSelector = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1673:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-person-icon.vue?vue&type=template&id=6bbb1a2f

var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.rankData && _vm.rankData.icon !== 'none' ? _c('img', {
    directives: [{
      name: "tooltip",
      rawName: "v-tooltip",
      value: _vm.rankData.name,
      expression: "rankData.name"
    }],
    staticClass: "mb-1 rank-image",
    attrs: {
      "src": __webpack_require__(1648)("./".concat(_vm.rankData.icon, ".png")),
      "alt": _vm.rankData.name
    }
  }) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-person-icon.vue?vue&type=template&id=6bbb1a2f

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-person-icon.vue?vue&type=script&lang=js


/* harmony default export */ var app_person_iconvue_type_script_lang_js = ({
  name: "app-person-icon",
  props: ['groups'],
  computed: {
    rankData: function rankData() {
      if (!this.groups) return false;
      if (this.groups['police_rank0']) return {
        name: 'Recruit',
        icon: 'recruit'
      };
      if (this.groups['police_rank1']) return {
        name: 'Officer',
        icon: 'officer'
      };
      if (this.groups['police_rank2']) return {
        name: 'Senior Officer',
        icon: 'senior-deputy'
      };
      if (this.groups['police_rank3']) return {
        name: 'Corporal',
        icon: 'corporal'
      };
      if (this.groups['police_rank4']) return {
        name: 'Sergeant',
        icon: 'sergeant'
      };
      if (this.groups['police_rank5']) return {
        name: 'Lieutenant',
        icon: 'vert-gold-single'
      };
      if (this.groups['police_rank6']) return {
        name: 'Captain',
        icon: 'captain'
      };
      if (this.groups['police_rank7']) return {
        name: 'Asst. Chief of Police',
        icon: 'star-silver-single'
      };
      if (this.groups['police_rank8']) return {
        name: 'Chief of Police',
        icon: 'star-silver-single'
      };
      if (this.groups['sheriff_rank0']) return {
        name: 'Probationary Deputy',
        icon: 'recruit'
      };
      if (this.groups['sheriff_rank1']) return {
        name: 'Deputy',
        icon: 'officer'
      };
      if (this.groups['sheriff_rank2']) return {
        name: 'Senior Deputy',
        icon: 'senior-deputy'
      };
      if (this.groups['sheriff_rank3']) return {
        name: 'Corporal',
        icon: 'corporal'
      };
      if (this.groups['sheriff_rank4']) return {
        name: 'Sergeant',
        icon: 'sergeant'
      };
      if (this.groups['sheriff_rank5']) return {
        name: 'Lieutenant',
        icon: 'vert-gold-single'
      };
      if (this.groups['sheriff_rank6']) return {
        name: 'Captain',
        icon: 'captain'
      };
      if (this.groups['sheriff_rank7']) return {
        name: 'Major',
        icon: 'major'
      };
      if (this.groups['sheriff_rank8']) return {
        name: 'Undersheriff',
        icon: 'star-silver-single'
      };
      if (this.groups['sheriff_rank9']) return {
        name: 'Sheriff',
        icon: 'star-silver-quad'
      };
      if (this.groups['ems_rank0']) return {
        name: 'EMT',
        icon: 'emt'
      };
      if (this.groups['ems_rank1']) return {
        name: 'A-EMT',
        icon: 'aemt'
      };
      if (this.groups['ems_rank2']) return {
        name: 'Paramedic',
        icon: 'paramedic'
      };
      if (this.groups['ems_rank3']) return {
        name: 'Senior Paramedic',
        icon: 'paramedic'
      };
      if (this.groups['ems_rank4']) return {
        name: 'Chief Paramedic',
        icon: 'chief-paramedic'
      };
      if (this.groups['ems_rank5']) return {
        name: 'Asst. Fire Chief',
        icon: 'asst-fire-cheif'
      };
      if (this.groups['ems_rank6']) return {
        name: 'Fire Chief',
        icon: 'fire-cheif'
      };
      if (this.groups['doc_rank0']) return {
        name: 'Recruit Corrections Officer',
        icon: 'recruit'
      };
      if (this.groups['doc_rank1']) return {
        name: 'Corrections Officer',
        icon: 'officer'
      };
      if (this.groups['doc_rank2']) return {
        name: 'Ranking Corrections Officer',
        icon: 'corporal'
      };
      if (this.groups['doc_rank3']) return {
        name: 'Corrections Operations Supervisor',
        icon: 'sergeant'
      };
      if (this.groups['doc_rank4']) return {
        name: 'Chief Prison Officer',
        icon: 'vert-gold-single'
      };
      if (this.groups['doc_rank5']) return {
        name: 'Captain',
        icon: 'star-silver-single'
      };
      if (this.groups['SAHP']) return {
        name: 'Trooper',
        icon: 'officer'
      };

      // No one yell at me for this

      if (Array.isArray(this.groups)) {
        if (this.groups.includes('police_rank0')) return {
          name: 'Recruit',
          icon: 'recruit'
        };
        if (this.groups.includes('police_rank1')) return {
          name: 'Officer',
          icon: 'officer'
        };
        if (this.groups.includes('police_rank2')) return {
          name: 'Senior Officer',
          icon: 'senior-deputy'
        };
        if (this.groups.includes('police_rank3')) return {
          name: 'Corporal',
          icon: 'corporal'
        };
        if (this.groups.includes('police_rank4')) return {
          name: 'Sergeant',
          icon: 'sergeant'
        };
        if (this.groups.includes('police_rank5')) return {
          name: 'Lieutenant',
          icon: 'vert-gold-single'
        };
        if (this.groups.includes('police_rank6')) return {
          name: 'Captain',
          icon: 'captain'
        };
        if (this.groups.includes('police_rank7')) return {
          name: 'Asst. Chief of Police',
          icon: 'star-silver-single'
        };
        if (this.groups.includes('police_rank8')) return {
          name: 'Chief of Police',
          icon: 'star-silver-single'
        };
        if (this.groups.includes('sheriff_rank0')) return {
          name: 'Probationary Deputy',
          icon: 'recruit'
        };
        if (this.groups.includes('sheriff_rank1')) return {
          name: 'Deputy',
          icon: 'officer'
        };
        if (this.groups.includes('sheriff_rank2')) return {
          name: 'Senior Deputy',
          icon: 'senior-deputy'
        };
        if (this.groups.includes('sheriff_rank3')) return {
          name: 'Corporal',
          icon: 'corporal'
        };
        if (this.groups.includes('sheriff_rank4')) return {
          name: 'Sergeant',
          icon: 'sergeant'
        };
        if (this.groups.includes('sheriff_rank5')) return {
          name: 'Lieutenant',
          icon: 'vert-gold-single'
        };
        if (this.groups.includes('sheriff_rank6')) return {
          name: 'Captain',
          icon: 'captain'
        };
        if (this.groups.includes('sheriff_rank7')) return {
          name: 'Major',
          icon: 'major'
        };
        if (this.groups.includes('sheriff_rank8')) return {
          name: 'Undersheriff',
          icon: 'star-silver-single'
        };
        if (this.groups.includes('sheriff_rank9')) return {
          name: 'Sheriff',
          icon: 'star-silver-quad'
        };
        if (this.groups.includes('ems_rank0')) return {
          name: 'EMT',
          icon: 'emt'
        };
        if (this.groups.includes('ems_rank1')) return {
          name: 'A-EMT',
          icon: 'aemt'
        };
        if (this.groups.includes('ems_rank2')) return {
          name: 'Paramedic',
          icon: 'paramedic'
        };
        if (this.groups.includes('ems_rank3')) return {
          name: 'Senior Paramedic',
          icon: 'paramedic'
        };
        if (this.groups.includes('ems_rank4')) return {
          name: 'Chief Paramedic',
          icon: 'chief-paramedic'
        };
        if (this.groups.includes('ems_rank5')) return {
          name: 'Asst. Fire Chief',
          icon: 'asst-fire-cheif'
        };
        if (this.groups.includes('ems_rank6')) return {
          name: 'Fire Chief',
          icon: 'fire-cheif'
        };
        if (this.groups.includes('doc_rank0')) return {
          name: 'Recruit Corrections Officer',
          icon: 'recruit'
        };
        if (this.groups.includes('doc_rank1')) return {
          name: 'Corrections Officer',
          icon: 'officer'
        };
        if (this.groups.includes('doc_rank2')) return {
          name: 'Ranking Corrections Officer',
          icon: 'corporal'
        };
        if (this.groups.includes('doc_rank3')) return {
          name: 'Corrections Operations Supervisor',
          icon: 'sergeant'
        };
        if (this.groups.includes('doc_rank4')) return {
          name: 'Chief Prison Officer',
          icon: 'vert-gold-single'
        };
        if (this.groups.includes('doc_rank5')) return {
          name: 'Captain',
          icon: 'star-silver-single'
        };
        if (this.groups.includes('SAHP')) return {
          name: 'Trooper',
          icon: 'officer'
        };
      }
      return false;
    }
  }
});
// CONCATENATED MODULE: ./components/Common/app-person-icon.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_person_iconvue_type_script_lang_js = (app_person_iconvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-person-icon.vue?vue&type=style&index=0&id=6bbb1a2f&prod&lang=scss
var app_person_iconvue_type_style_index_0_id_6bbb1a2f_prod_lang_scss = __webpack_require__(1649);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-person-icon.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_person_iconvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_person_icon = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1675:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(1532);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/note-container.vue?vue&type=template&id=b700020c





var note_containervue_type_template_id_b700020c_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "p-top"
  }, [_vm._l(_vm.incidentPerson.list_notes, function (note) {
    return _vm.incidentPerson.list_notes ? _c('div', {
      staticClass: "note-body"
    }, [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_c('span', [_vm._v(_vm._s(note.inserted_by))]), _vm._v(" "), _c('span', [_vm._v(" - ")]), _vm._v(" "), _c('span', [_vm._v(_vm._s(note.created_at))])]), _vm._v(" "), _c(VCol["a" /* default */], [_vm._v("\n        " + _vm._s(note.body) + "\n      ")])], 1)], 1) : _vm._e();
  }), _vm._v(" "), _vm.addingNote ? _c('div', [_c('app-form-group', [_c(VTextarea["a" /* default */], {
    attrs: {
      "filled": "",
      "outlined": "",
      "rows": "3"
    },
    model: {
      value: _vm.content,
      callback: function callback($$v) {
        _vm.content = $$v;
      },
      expression: "content"
    }
  })], 1), _vm._v(" "), _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    attrs: {
      "color": "primary"
    },
    on: {
      "click": _vm.submit
    }
  }, [_vm._v("Submit Note")])], 1) : _vm._e()], 2);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/note-container.vue?vue&type=template&id=b700020c

// EXTERNAL MODULE: ./node_modules/vue-markdown/dist/vue-markdown.common.js
var vue_markdown_common = __webpack_require__(496);
var vue_markdown_common_default = /*#__PURE__*/__webpack_require__.n(vue_markdown_common);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/note-container.vue?vue&type=script&lang=js


/* harmony default export */ var note_containervue_type_script_lang_js = ({
  name: 'note-container',
  props: ['incidentPerson'],
  components: {
    AppFormGroup: app_form_group["a" /* default */],
    VueMarkdown: vue_markdown_common_default.a
  },
  data: function data() {
    return {
      addingNote: false,
      content: null
    };
  },
  methods: {
    startAddingNote: function startAddingNote() {
      this.addingNote = true;
    },
    addExternal: function addExternal(content) {
      this.content = content;
      this.submit();
    },
    submit: function submit() {
      var _this = this;
      return this.$axios.$post("/police/incidents/add-person-note/".concat(this.incidentPerson.id), {
        contents: this.content
      }).then(function (r) {
        _this.content = null;
        _this.addingNote = false;
        window.$events.$emit('reload');
      });
    }
  }
});
// CONCATENATED MODULE: ./components/Common/note-container.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_note_containervue_type_script_lang_js = (note_containervue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/note-container.vue?vue&type=style&index=0&id=b700020c&prod&lang=scss
var note_containervue_type_style_index_0_id_b700020c_prod_lang_scss = __webpack_require__(1654);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/note-container.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_note_containervue_type_script_lang_js,
  note_containervue_type_template_id_b700020c_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var note_container = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1700:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(7);
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(9);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(14);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(15);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(8);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(11);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(139);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(0);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(6);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(62);
/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var _src_components_VCheckbox_VCheckbox_sass__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(1624);
/* harmony import */ var _src_components_VCheckbox_VCheckbox_sass__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_src_components_VCheckbox_VCheckbox_sass__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(894);
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(49);
/* harmony import */ var _VInput__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(106);
/* harmony import */ var _mixins_selectable__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(505);








var _excluded = ["title"];


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
// Styles

 // Components


 // Mixins


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (_mixins_selectable__WEBPACK_IMPORTED_MODULE_14__[/* default */ "a"].extend({
  name: 'v-checkbox',
  props: {
    indeterminate: Boolean,
    indeterminateIcon: {
      type: String,
      default: '$checkboxIndeterminate'
    },
    offIcon: {
      type: String,
      default: '$checkboxOff'
    },
    onIcon: {
      type: String,
      default: '$checkboxOn'
    }
  },
  data: function data() {
    return {
      inputIndeterminate: this.indeterminate
    };
  },
  computed: {
    classes: function classes() {
      return _objectSpread(_objectSpread({}, _VInput__WEBPACK_IMPORTED_MODULE_13__[/* default */ "a"].options.computed.classes.call(this)), {}, {
        'v-input--selection-controls': true,
        'v-input--checkbox': true,
        'v-input--indeterminate': this.inputIndeterminate
      });
    },
    computedIcon: function computedIcon() {
      if (this.inputIndeterminate) {
        return this.indeterminateIcon;
      } else if (this.isActive) {
        return this.onIcon;
      } else {
        return this.offIcon;
      }
    },
    // Do not return undefined if disabled,
    // according to spec, should still show
    // a color when disabled and active
    validationState: function validationState() {
      if (this.isDisabled && !this.inputIndeterminate) return undefined;
      if (this.hasError && this.shouldValidate) return 'error';
      if (this.hasSuccess) return 'success';
      if (this.hasColor !== null) return this.computedColor;
      return undefined;
    }
  },
  watch: {
    indeterminate: function indeterminate(val) {
      var _this = this;
      // https://github.com/vuetifyjs/vuetify/issues/8270
      this.$nextTick(function () {
        return _this.inputIndeterminate = val;
      });
    },
    inputIndeterminate: function inputIndeterminate(val) {
      this.$emit('update:indeterminate', val);
    },
    isActive: function isActive() {
      if (!this.indeterminate) return;
      this.inputIndeterminate = false;
    }
  },
  methods: {
    genCheckbox: function genCheckbox() {
      var _this$attrs$ = this.attrs$,
        title = _this$attrs$.title,
        checkboxAttrs = Object(_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"])(_this$attrs$, _excluded);
      return this.$createElement('div', {
        staticClass: 'v-input--selection-controls__input'
      }, [this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"], this.setTextColor(this.validationState, {
        props: {
          dense: this.dense,
          dark: this.dark,
          light: this.light
        }
      }), this.computedIcon), this.genInput('checkbox', _objectSpread(_objectSpread({}, checkboxAttrs), {}, {
        'aria-checked': this.inputIndeterminate ? 'mixed' : this.isActive.toString()
      })), this.genRipple(this.setTextColor(this.rippleState))]);
    },
    genDefaultSlot: function genDefaultSlot() {
      return [this.genCheckbox(), this.genLabel()];
    }
  }
}));

/***/ }),

/***/ 1701:
/***/ (function(module, exports, __webpack_require__) {

"use strict";

var defineWellKnownSymbol = __webpack_require__(512);

// `Symbol.replace` well-known symbol
// https://tc39.es/ecma262/#sec-symbol.replace
defineWellKnownSymbol('replace');


/***/ }),

/***/ 1702:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1811);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("739839b6", content, true, {"sourceMap":false});

/***/ }),

/***/ 1703:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1813);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("77d71004", content, true, {"sourceMap":false});

/***/ }),

/***/ 1704:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1815);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("6d89ee7a", content, true, {"sourceMap":false});

/***/ }),

/***/ 1705:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1817);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("7131ed79", content, true, {"sourceMap":false});

/***/ }),

/***/ 1706:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1819);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("723b2630", content, true, {"sourceMap":false});

/***/ }),

/***/ 1810:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_police_person_evidence_vue_vue_type_style_index_0_id_979b823e_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1702);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_police_person_evidence_vue_vue_type_style_index_0_id_979b823e_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_police_person_evidence_vue_vue_type_style_index_0_id_979b823e_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1811:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".note-body{cursor:pointer;margin-bottom:2px;padding:10px}.note-body:hover{background-color:rgba(49,49,49,.7)}.note-body .note-inside{border-left:4px solid gray;padding-left:10px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1812:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_submit_record_charges_vue_vue_type_style_index_0_id_6448ac3a_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1703);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_submit_record_charges_vue_vue_type_style_index_0_id_6448ac3a_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_submit_record_charges_vue_vue_type_style_index_0_id_6448ac3a_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1813:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".charge-row-item{background-color:#161616;border-left:2px solid red;margin-left:1px;margin-right:1px;margin-top:10px;padding:8px}.charge-row-item.level-Infraction{border-left:4px solid #32cd32}.charge-row-item.level-Misdemeanor{border-left:4px solid #ff0}.charge-row-item.level-Felony{border-left:4px solid red}.charge-row-item-selected{background-color:#161616;border-left:2px solid #fff;margin-left:1px;margin-right:1px;margin-top:10px;padding:8px}.charge-row-item-selected.level-Infraction{border-left:4px solid #32cd32}.charge-row-item-selected.level-Misdemeanor{border-left:4px solid #ff0}.charge-row-item-selected.level-Felony{border-left:4px solid red}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1814:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_police_incident_person_item_vue_vue_type_style_index_0_id_728fdaa0_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1704);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_police_incident_person_item_vue_vue_type_style_index_0_id_728fdaa0_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_police_incident_person_item_vue_vue_type_style_index_0_id_728fdaa0_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1815:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".status-badge[data-v-728fdaa0]{border-radius:3px;box-shadow:0 1px 2px rgba(0,0,0,.3);display:inline-block;font-size:9px;font-weight:600;letter-spacing:.5px;line-height:1.2;min-width:45px;padding:2px 6px;text-align:center;text-transform:uppercase}.status-badge.jailed[data-v-728fdaa0]{background:linear-gradient(135deg,#b71c1c,#d32f2f);border:1px solid #8b0000;color:#fff}.status-badge.fined[data-v-728fdaa0]{background:linear-gradient(135deg,#e65100,#ff9800);border:1px solid #bf360c;color:#fff}.status-badge.warned[data-v-728fdaa0]{background:linear-gradient(135deg,#f57f17,#ffc107);border:1px solid #e65100;color:#1a1a1a}.status-badge.pending[data-v-728fdaa0]{animation:pulse-728fdaa0 2s infinite;background:linear-gradient(135deg,#1565c0,#2196f3);border:1px solid #0d47a1;color:#fff}.status-badge.processed[data-v-728fdaa0]{background:linear-gradient(135deg,#424242,#616161);border:1px solid #212121;color:#fff}@keyframes pulse-728fdaa0{0%{opacity:1}50%{opacity:.7}to{opacity:1}}.fixed-height-header[data-v-728fdaa0]{max-height:48px!important;min-height:48px!important;overflow:hidden}.fixed-height-header .v-expansion-panel-header__icon[data-v-728fdaa0]{align-self:center}.avatar-stack-container[data-v-728fdaa0]{height:48px;overflow:visible;position:relative}.avatar-stack-fade-enter-active[data-v-728fdaa0],.avatar-stack-fade-leave-active[data-v-728fdaa0]{transition:all .3s ease}.avatar-stack-fade-enter[data-v-728fdaa0],.avatar-stack-fade-leave-to[data-v-728fdaa0]{opacity:0;transform:translateX(10px)}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1816:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_police_incident_selector_vue_vue_type_style_index_0_id_1f3a0cb9_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1705);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_police_incident_selector_vue_vue_type_style_index_0_id_1f3a0cb9_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_police_incident_selector_vue_vue_type_style_index_0_id_1f3a0cb9_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1817:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".multiselect{background-color:#1d1d1d!important}.multiselect .multiselect__tags{background-color:gray;border-color:#2c2c2c;border-radius:0;height:85%!important}.multiselect .multiselect__input,.multiselect .multiselect__tags{background-color:#1d1d1d!important;color:#fff}.multiselect .multiselect__spinner{background-color:#1d1d1d!important}.multiselect .multiselect__single{background:#1d1d1d!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1818:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_1014cb8a_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1706);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_1014cb8a_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_1014cb8a_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1819:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, "mark{background-color:#797979}.incident-section{margin-bottom:15px}.incident-page,.incident-section{padding-bottom:10px;padding-top:10px}.incident-page{background-color:#1a1a1a;margin-top:10px}.heading-thing{color:#6b6b6b}.vue-simplemde{border-radius:0!important}.linked-incident-container{position:relative}.floating-delete-btn{height:32px!important;position:absolute!important;right:-3px!important;top:-10px!important;width:32px!important;z-index:10!important}.viewers-label{color:#6b6b6b;font-size:14px;font-weight:500}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2023:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanel.js
var VExpansionPanel = __webpack_require__(1528);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanels.js
var VExpansionPanels = __webpack_require__(1527);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VLazy/VLazy.js
var VLazy = __webpack_require__(1563);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VSelect/VSelect.js + 4 modules
var VSelect = __webpack_require__(375);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VSpacer.js
var VSpacer = __webpack_require__(1523);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.split.js
var es_string_split = __webpack_require__(277);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/incidents/view/_id/index.vue?vue&type=template&id=1014cb8a


















var _idvue_type_template_id_1014cb8a_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('app-page', {
    attrs: {
      "loading": _vm.loading
    }
  }, [_c('crudy-page', {
    attrs: {
      "name": "PoliceIncident",
      "config": {
        hideRelationships: true
      }
    },
    scopedSlots: _vm._u([{
      key: "actions",
      fn: function fn() {
        return [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_c('app-socket-channel-presence-listener', {
          ref: "incidentPresenceListener",
          attrs: {
            "channel": "incidents.".concat(_vm.$route.params.id),
            "listeners": {
              'IncidentUpdated': _vm.handleSocketUpdate,
              '.CrimeAttributeUpdated': _vm.handleCrimeAttributeUpdate,
              '.PersonViewingChanged': _vm.handlePersonViewingChanged,
              '.PersonTotalsUpdated': _vm.handlePersonTotalsUpdate
            },
            "show-avatars": true
          }
        })], 1)], 1), _vm._v(" "), _c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
          staticClass: "ml-4"
        }, [_c('div', [_vm.incident.category.name === 'BOLO' || _vm.hasAnyGroup(['Captain', 'Major', 'Undersheriff', 'SAHP', 'admin']) || _vm.incident.editable && _vm.incident.person_id === _vm.user.id ? _c(VBtn["a" /* default */], {
          directives: [{
            name: "promise-btn",
            rawName: "v-promise-btn"
          }],
          staticClass: "mr-2",
          attrs: {
            "small": ""
          },
          on: {
            "click": function click($event) {
              _vm.removeIncident = true;
            }
          }
        }, [_c(VIcon["a" /* default */], {
          staticClass: "mr-2",
          attrs: {
            "color": "red",
            "small": ""
          }
        }, [_vm._v("\n                fa-solid fa-fingerprint\n              ")]), _vm._v("\n              Remove " + _vm._s(_vm.incident.category.name) + "\n            ")], 1) : _vm._e(), _vm._v(" "), _c(VBtn["a" /* default */], {
          staticClass: "mr-2",
          attrs: {
            "small": ""
          },
          on: {
            "click": function click($event) {
              _vm.restrictionModal = true;
            }
          }
        }, [_c(VIcon["a" /* default */], {
          staticClass: "mr-2",
          attrs: {
            "color": "orange",
            "small": ""
          }
        }, [_vm._v("\n                fa-solid fa-fingerprint\n              ")]), _vm._v("\n              Restrict\n            ")], 1), _vm._v(" "), _vm.incident.category.allow_evidence ? _c(VBtn["a" /* default */], {
          staticClass: "mr-2",
          attrs: {
            "small": ""
          },
          on: {
            "click": _vm.accessEvidence
          }
        }, [_c(VIcon["a" /* default */], {
          staticClass: "mr-2",
          attrs: {
            "color": "secondary",
            "small": ""
          }
        }, [_vm._v("\n                fa-solid fa-boxes-stacked\n              ")]), _vm._v("\n              Evidence\n            ")], 1) : _vm._e(), _vm._v(" "), _c(VBtn["a" /* default */], {
          staticClass: "mr-2",
          attrs: {
            "small": ""
          },
          on: {
            "click": function click($event) {
              _vm.incidentModel = true;
            }
          }
        }, [_c(VIcon["a" /* default */], {
          staticClass: "mr-2",
          attrs: {
            "color": "success",
            "small": ""
          }
        }, [_vm._v("\n                fa-solid fa-link\n              ")]), _vm._v("\n              Link Incident\n            ")], 1), _vm._v(" "), _vm.incident.category.allow_add_vehicles ? _c(VBtn["a" /* default */], {
          staticClass: "mr-2",
          attrs: {
            "small": ""
          },
          on: {
            "click": function click($event) {
              _vm.vehicleModel = true;
            }
          }
        }, [_c(VIcon["a" /* default */], {
          staticClass: "mr-2",
          attrs: {
            "color": "success",
            "small": ""
          }
        }, [_vm._v("\n                fa-solid fa-car\n              ")]), _vm._v("\n              Add Vehicle\n            ")], 1) : _vm._e(), _vm._v(" "), _c(VBtn["a" /* default */], {
          staticClass: "mr-2",
          attrs: {
            "small": ""
          },
          on: {
            "click": _vm.triggerPersonAddForm
          }
        }, [_c(VIcon["a" /* default */], {
          staticClass: "mr-2",
          attrs: {
            "color": "success",
            "small": ""
          }
        }, [_vm._v("\n                fa-solid fa-person\n              ")]), _vm._v("\n              Add Person\n            ")], 1), _vm._v(" "), _c(VBtn["a" /* default */], {
          attrs: {
            "small": ""
          },
          on: {
            "click": _vm.printIncident
          }
        }, [_c(VIcon["a" /* default */], {
          staticClass: "mr-2",
          attrs: {
            "color": "primary",
            "small": ""
          }
        }, [_vm._v("\n                fa-solid fa-print\n              ")]), _vm._v("\n              Print\n            ")], 1)], 1)])], 1)];
      },
      proxy: true
    }])
  }), _vm._v(" "), _vm.incident && _vm.incident.department ? _c('div', [_c('div', {
    attrs: {
      "id": "modals"
    }
  }, [_c('app-roll-out', {
    attrs: {
      "title": "Add Vehicle"
    },
    model: {
      value: _vm.vehicleModel,
      callback: function callback($$v) {
        _vm.vehicleModel = $$v;
      },
      expression: "vehicleModel"
    }
  }, [_c('app-form-group', {
    staticStyle: {
      "display": "block"
    }
  }, [_c('label', [_vm._v("Vehicle")]), _vm._v(" "), _c('police-car-selector', {
    ref: "carSelector",
    attrs: {
      "type": "vehicle",
      "incident": _vm.incident
    },
    on: {
      "selected": function selected($event) {
        _vm.vehicleForm.car_id = $event;
      }
    }
  })], 1), _vm._v(" "), _c('app-form-group', {
    staticClass: "mt-8"
  }, [_c('label', [_vm._v("Impound Type")]), _vm._v(" "), _c(VSelect["a" /* default */], {
    attrs: {
      "solo": "",
      "items": ['Unknown', 'No Impound', 'Temp Impound', '3 Hour Impound', '1 Day Impound', '3 Day Impound', 'Seized']
    },
    model: {
      value: _vm.vehicleForm.impound_type,
      callback: function callback($$v) {
        _vm.$set(_vm.vehicleForm, "impound_type", $$v);
      },
      expression: "vehicleForm.impound_type"
    }
  })], 1), _vm._v(" "), _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "mt-8",
    attrs: {
      "color": "primary"
    },
    on: {
      "click": _vm.addVehicle
    }
  }, [_vm._v("Add Vehicle\n        ")])], 1), _vm._v(" "), _c('app-roll-out', {
    attrs: {
      "title": "Link Incident"
    },
    model: {
      value: _vm.incidentModel,
      callback: function callback($$v) {
        _vm.incidentModel = $$v;
      },
      expression: "incidentModel"
    }
  }, [_c('app-form-group', {
    staticStyle: {
      "display": "block"
    }
  }, [_c('label', [_vm._v("Incident")]), _vm._v(" "), _c('police-incident-selector', {
    ref: "incidentSelector",
    attrs: {
      "type": "incident",
      "incident": _vm.incident
    },
    on: {
      "selected": function selected($event) {
        _vm.linkForm.incident_id_b = $event;
      }
    }
  })], 1), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticStyle: {
      "margin-top": "32px"
    },
    attrs: {
      "color": "primary"
    },
    on: {
      "click": _vm.linkIncident
    }
  }, [_vm._v("\n          Link Incident\n        ")])], 1), _vm._v(" "), _c('app-roll-out', {
    attrs: {
      "title": "Link Document"
    },
    model: {
      value: _vm.documentModal,
      callback: function callback($$v) {
        _vm.documentModal = $$v;
      },
      expression: "documentModal"
    }
  }, [_c('app-form-group', {
    staticStyle: {
      "display": "block"
    }
  }, [_c('label', [_vm._v("Document")]), _vm._v(" "), _c('crud-select', {
    ref: "documentSelector",
    attrs: {
      "column": "id",
      "model": "Document",
      "haltload": true
    },
    on: {
      "changed": function changed($event) {
        _vm.documentForm.document_id = $event.id;
      }
    }
  }), _vm._v(" "), _c('p', {
    staticClass: "text-muted mt-3"
  }, [_vm._v("\n            Enter the "), _c('b', [_vm._v("Document # that you want to add")]), _vm._v(". After searching is complete, select the document and\n            click the link button bellow.\n          ")])], 1), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "btn btn-primary",
    staticStyle: {
      "margin-top": "32px"
    },
    on: {
      "click": _vm.linkDocument
    }
  }, [_vm._v("Link Document")])], 1), _vm._v(" "), _c('app-roll-out', {
    attrs: {
      "title": "Incident Restriction"
    },
    model: {
      value: _vm.restrictionModal,
      callback: function callback($$v) {
        _vm.restrictionModal = $$v;
      },
      expression: "restrictionModal"
    }
  }, [_c('app-form-group', {
    staticStyle: {
      "display": "block"
    }
  }, [_c('label', [_vm._v("Person to Whitelist")]), _vm._v(" "), _c('police-person-selector', {
    ref: "personWhitelistSelector",
    attrs: {
      "type": "restriction",
      "incident": _vm.incident
    },
    on: {
      "selected": function selected($event) {
        _vm.restrictForm.person_id = $event;
      }
    }
  })], 1), _vm._v(" "), _c('app-form-group', [_c('span', [_vm._v("\n            Warning: When adding any user, this report will become only available to the specified person(s).\n            Your person will be automatically added.\n          ")])]), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticStyle: {
      "margin-top": "32px"
    },
    on: {
      "click": _vm.addUserRestriction
    }
  }, [_vm._v("\n          Whitelist Person\n        ")])], 1), _vm._v(" "), _c('app-roll-out', {
    attrs: {
      "title": "Remove Incident"
    },
    model: {
      value: _vm.removeIncident,
      callback: function callback($$v) {
        _vm.removeIncident = $$v;
      },
      expression: "removeIncident"
    }
  }, [_c('div', {
    staticClass: "text-muted mb-2"
  }, [_vm._v("\n          This will remove this record, and all data.\n        ")]), _vm._v(" "), _vm.incident.persons.length > 0 ? _c('div', {
    staticClass: "text-danger mb-2"
  }, [_vm._v("\n          This record can not be removed because it has attached person(s)\n        ")]) : _vm._e(), _vm._v(" "), _vm.incident.persons.length < 1 ? _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "btn btn-danger",
    on: {
      "click": _vm.remove
    }
  }, [_vm._v("\n          Remove Incident\n        ")]) : _vm._e()], 1)], 1), _vm._v(" "), _c('div', {
    staticClass: "mt-4"
  }, [_c('div', {
    staticClass: "p-2"
  }, [_c(VExpansionPanels["a" /* default */], {
    model: {
      value: _vm.selectedPersonExpansionPanel,
      callback: function callback($$v) {
        _vm.selectedPersonExpansionPanel = $$v;
      },
      expression: "selectedPersonExpansionPanel"
    }
  }, _vm._l(_vm.incident.persons, function (person, index) {
    return _c(VExpansionPanel["a" /* default */], {
      key: person.id
    }, [_c(VLazy["a" /* default */], [_c('police-incident-person-item', {
      ref: "person-item-".concat(person.id),
      refInFor: true,
      attrs: {
        "visible": _vm.selectedPersonItem === person.id,
        "refer": "".concat(_vm.incident.category.prefix, "-").concat(_vm.incident.id),
        "faction": _vm.incident.department,
        "incident-person": person,
        "incident": _vm.incident,
        "viewers": _vm.getPersonViewers(person.id)
      },
      on: {
        "person-became-visible": _vm.handlePersonBecameVisible,
        "person-became-hidden": _vm.handlePersonBecameHidden
      }
    })], 1)], 1);
  }), 1)], 1), _vm._v(" "), _c('div', {
    staticClass: "my-3"
  }), _vm._v(" "), _vm.incident.category.allow_add_vehicles ? _c('crudy-table', {
    staticClass: "mt-4",
    attrs: {
      "name": "PoliceIncidentVehicle",
      "view": "index",
      "simplified": true,
      "hideIfNone": true,
      "filter": {
        police_incident_id: _vm.incident.id
      },
      "title": "Vehicles"
    }
  }) : _vm._e()], 1), _vm._v(" "), _vm.incident.links.length > 0 ? _c('div', {
    staticClass: "incident-section p-1 mt-3"
  }, [_c('h5', {
    staticClass: "ml-4 heading-thing"
  }, [_vm._v("Linked Incidents")]), _vm._v(" "), _vm._l(_vm.incident.links, function (link) {
    return _c('div', {
      key: link.id,
      staticClass: "linked-incident-container"
    }, [_c('crudy-table-items-component-police-incident', {
      attrs: {
        "item": link
      }
    }), _vm._v(" "), _vm.hasAnyGroup(['Sergeant', 'Lieutenant', 'Captain', 'Major', 'Undersheriff', 'SAHP', 'admin']) ? _c(VBtn["a" /* default */], {
      directives: [{
        name: "promise-btn",
        rawName: "v-promise-btn"
      }],
      staticClass: "floating-delete-btn",
      attrs: {
        "fab": "",
        "small": "",
        "color": "error"
      },
      on: {
        "click": function click($event) {
          return _vm.unlinkIncident(link.id);
        }
      }
    }, [_c(VIcon["a" /* default */], {
      attrs: {
        "small": ""
      }
    }, [_vm._v("fa-solid fa-times")])], 1) : _vm._e()], 1);
  })], 2) : _vm._e(), _vm._v(" "), _c('div', {
    staticClass: "p-1"
  }, [_c(VTextField["a" /* default */], {
    attrs: {
      "placeholder": "Page Title (Leave Blank to autofill)",
      "solo": ""
    },
    model: {
      value: _vm.pageForm.title,
      callback: function callback($$v) {
        _vm.$set(_vm.pageForm, "title", $$v);
      },
      expression: "pageForm.title"
    }
  }), _vm._v(" "), _c('crudy-editor', {
    attrs: {
      "value": _vm.pageForm.body,
      "mentions-endpoint": "/editor/incident-mentions/{term}",
      "templatesEndpoint": "/system/meta/incident-templates/".concat(_vm.incident.category.id)
    },
    on: {
      "input": _vm.debouncedBodyUpdate
    }
  }), _vm._v(" "), _vm.pageForm ? _c('div', {
    attrs: {
      "align": "right"
    }
  }, [_c(VBtn["a" /* default */], {
    staticClass: "success btn-sm mt-3 mr-3 btn btn-dark",
    attrs: {
      "disabled": !_vm.pageForm.body || _vm.pageForm.body.length < 10
    },
    on: {
      "click": _vm.submitPage
    }
  }, [_vm._v("\n          Submit Page\n        ")])], 1) : _vm._e(), _vm._v(" "), _c('div', {
    staticClass: "mt-4"
  }, _vm._l(_vm.incident.pages, function (page) {
    return _vm.incident.pages ? _c(VCard["a" /* default */], {
      key: "page-".concat(page.id),
      staticClass: "incident-page"
    }, [_c(components_VCard["c" /* VCardTitle */], [_vm._v("\n            " + _vm._s(page.title) + "\n          ")]), _vm._v(" "), _c(components_VCard["b" /* VCardText */], {
      staticClass: "ml-4 mt-4"
    }, [_c(VRow["a" /* default */], {
      attrs: {
        "no-gutters": ""
      }
    }, [_c(VCol["a" /* default */], {
      staticClass: "font-weight-bold"
    }, [page.person ? _c('span', {
      staticClass: "mb-1"
    }, [_c('app-avatar', {
      attrs: {
        "size": "24",
        "src": page.person.avatar_url
      }
    }), _vm._v(" "), _c('span', {
      staticStyle: {
        "font-size": "23px"
      }
    }, [_vm._v("\n                    " + _vm._s(page.person.name) + "\n                  ")])], 1) : _vm._e(), _vm._v(" "), _c('app-timestamp', {
      attrs: {
        "stamp": page.created_at
      }
    })], 1), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "text-right mr-4 hover-icon",
      staticStyle: {
        "cursor": "pointer"
      },
      attrs: {
        "md": "1"
      },
      on: {
        "click": function click($event) {
          return _vm.printPage(page);
        }
      }
    }, [_vm._v("\n                PRINT\n              ")]), _vm._v(" "), _vm.hasAnyGroup(['Lieutenant']) || page.editable && page.person_id === _vm.user.id ? _c(VCol["a" /* default */], {
      staticClass: "ml-1 text-right mr-4 hover-icon",
      staticStyle: {
        "cursor": "pointer"
      },
      attrs: {
        "md": "1"
      },
      on: {
        "click": function click($event) {
          return _vm.startEditPage(page);
        }
      }
    }, [_vm._v("\n                EDIT\n              ")]) : _vm._e(), _vm._v(" "), _vm.hasAnyGroup(['Lieutenant']) || page.editable && page.person_id === _vm.user.id ? _c(VCol["a" /* default */], {
      staticClass: "ml-1 text-right mr-4 hover-icon",
      staticStyle: {
        "cursor": "pointer"
      },
      attrs: {
        "md": "1"
      },
      on: {
        "click": function click($event) {
          return _vm.removePage(page.id);
        }
      }
    }, [_vm._v("\n                DELETE\n              ")]) : _vm._e()], 1), _vm._v(" "), _c('div', {
      staticClass: "mt-3"
    }, [_c('app-markdown-view', {
      attrs: {
        "source": page.body
      }
    })], 1)], 1)], 1) : _vm._e();
  }), 1), _vm._v(" "), _vm.evidence && _vm.incident.category.allow_evidence ? _c(VCard["a" /* default */], {
    staticClass: "mt-3"
  }, [_c(components_VCard["c" /* VCardTitle */], [_vm._v("\n          Evidence\n\n          "), _c(VSpacer["a" /* default */]), _vm._v(" "), _c(VBtn["a" /* default */], {
    on: {
      "click": _vm.refreshEvidence
    }
  }, [_vm._v("\n            Refresh Evidence\n          ")])], 1), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_c('div', {
    staticClass: "mt-1 mb-4 text-muted"
  }, [_vm._v("\n            Incident evidence listed below is only updated once per minute. Changes to evidence will not immediately reflect.\n          ")]), _vm._v(" "), _vm._l(_vm.evidence, function (section, sectionIndex) {
    return _c('div', {
      key: sectionIndex
    }, [_c('h6', [_vm._v("\n              " + _vm._s(section.name) + "\n            ")]), _vm._v(" "), _c('ul', _vm._l(section.items, function (item, sectionItem) {
      var _item$meta, _item$idname, _item$idname$split, _item$amount;
      return _c('li', {
        key: sectionItem
      }, [item.type === 'nested_chest' ? [_c('span', [_vm._v("\n                📁  "), _c('b', [_vm._v("\n                      " + _vm._s((item.label || item.name || item.chest_id || '-') + ((_item$meta = item.meta) !== null && _item$meta !== void 0 && _item$meta.storage_label ? ' - ' + item.meta.storage_label : '')) + "\n                    ")])]), _vm._v(" "), _c('ul', {
        staticClass: "ml-4"
      }, _vm._l(item.items, function (subItem, index) {
        var _subItem$idname, _subItem$idname$split, _subItem$amount;
        return _c('li', {
          key: index
        }, [subItem.type === 'nested_chest' ? [_c('span', [_vm._v("📁 "), _c('b', [_vm._v(_vm._s(subItem.storage_label || subItem.name || subItem.chest_id))])]), _vm._v(" "), _c('ul', {
          staticClass: "ml-4"
        }, _vm._l(subItem.items, function (nested, subIndex) {
          var _nested$idname, _nested$idname$split, _nested$amount;
          return _c('li', {
            key: subIndex
          }, [_c('b', [_vm._v(_vm._s(nested.label || nested.name || ((_nested$idname = nested.idname) === null || _nested$idname === void 0 || (_nested$idname$split = _nested$idname.split) === null || _nested$idname$split === void 0 ? void 0 : _nested$idname$split.call(_nested$idname, ':')[0])))]), _vm._v("\n                            x" + _vm._s((_nested$amount = nested.amount) !== null && _nested$amount !== void 0 ? _nested$amount : 1) + "\n                          ")]);
        }), 0)] : [_c('b', [_vm._v(_vm._s(subItem.label || subItem.name || ((_subItem$idname = subItem.idname) === null || _subItem$idname === void 0 || (_subItem$idname$split = _subItem$idname.split) === null || _subItem$idname$split === void 0 ? void 0 : _subItem$idname$split.call(_subItem$idname, ':')[0])))]), _vm._v("\n                        x" + _vm._s((_subItem$amount = subItem.amount) !== null && _subItem$amount !== void 0 ? _subItem$amount : 1) + "\n                      ")]], 2);
      }), 0)] : item.label || item.name || item.idname ? [_c('b', [_vm._v(_vm._s(item.label || item.name || ((_item$idname = item.idname) === null || _item$idname === void 0 || (_item$idname$split = _item$idname.split) === null || _item$idname$split === void 0 ? void 0 : _item$idname$split.call(_item$idname, ':')[0])))]), _vm._v("\n                  x" + _vm._s((_item$amount = item.amount) !== null && _item$amount !== void 0 ? _item$amount : 1) + "\n                ")] : _vm._e()], 2);
    }), 0)]);
  })], 2)], 1) : _vm._e()], 1), _vm._v(" "), _c('div', [_vm.incident.access.length > 0 ? _c('div', {
    staticClass: "incident-section mt-5"
  }, [_c('h5', {
    staticClass: "ml-4 heading-thing"
  }, [_vm._v("Allowed Access")]), _vm._v(" "), _vm._l(_vm.incident.access, function (access, index) {
    return _c(VRow["a" /* default */], {
      key: index,
      staticClass: "case-row-no-hover",
      attrs: {
        "no-gutters": ""
      }
    }, [_c(VCol["a" /* default */], {
      staticClass: "ml-4",
      attrs: {
        "md": "2"
      }
    }, [_c('b', [_vm._v(_vm._s(access.person.name))])]), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "ml-4",
      attrs: {
        "md": "2"
      }
    }, [_vm._v("\n            " + _vm._s(access.created_at) + "\n          ")]), _vm._v(" "), access.person_id !== _vm.user.id || _vm.incident.access.length === 1 ? _c(VCol["a" /* default */], {
      staticClass: "ml-4"
    }, [_c('span', {
      directives: [{
        name: "promise-btn",
        rawName: "v-promise-btn"
      }],
      staticClass: "text-danger font-weight-bold",
      on: {
        "click": function click($event) {
          return _vm.removeUserRestriction(access.id);
        }
      }
    }, [_vm._v("\n              Remove\n            ")])]) : _vm._e()], 1);
  })], 2) : _vm._e()])]) : _vm._e()], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/incidents/view/_id/index.vue?vue&type=template&id=1014cb8a

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find.js
var es_array_find = __webpack_require__(89);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find-index.js
var es_array_find_index = __webpack_require__(113);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.join.js
var es_array_join = __webpack_require__(126);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.map.js
var es_array_map = __webpack_require__(70);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.replace.js
var es_string_replace = __webpack_require__(72);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/Loader.vue + 4 modules
var Loader = __webpack_require__(190);

// EXTERNAL MODULE: ./components/Common/ItemDetail.vue + 4 modules
var ItemDetail = __webpack_require__(151);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAlert/VAlert.js
var VAlert = __webpack_require__(1537);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCheckbox/VCheckbox.js
var VCheckbox = __webpack_require__(1700);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelContent.js
var VExpansionPanelContent = __webpack_require__(1530);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelHeader.js
var VExpansionPanelHeader = __webpack_require__(1529);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/transitions/index.js + 2 modules
var transitions = __webpack_require__(78);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(1532);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-incident-person-item.vue?vue&type=template&id=728fdaa0&scoped=true

























var police_incident_person_itemvue_type_template_id_728fdaa0_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VExpansionPanelHeader["a" /* default */], {
    staticClass: "fixed-height-header",
    on: {
      "click": _vm.handleExpansionToggle
    }
  }, [_c(VRow["a" /* default */], {
    staticClass: "align-center",
    attrs: {
      "no-gutters": ""
    },
    on: {
      "click": function click($event) {
        return _vm.$emit('toggled');
      }
    }
  }, [_c(VCol["a" /* default */], {
    staticClass: "d-flex align-center pl-4",
    attrs: {
      "cols": "4"
    }
  }, [_vm.incidentPerson.is_pardoned ? _c('span', [_vm._v("\n          REDACTED\n        ")]) : _c('span', [_vm.incidentPerson.type.includes('officer') ? _c('app-avatar', {
    attrs: {
      "size": "24",
      "src": _vm.incidentPerson.person.avatar_url
    }
  }) : _vm._e(), _vm._v("\n            " + _vm._s(_vm.incidentPerson.person.name) + "\n          ")], 1)]), _vm._v(" "), _vm.incidentPersonType ? _c(VCol["a" /* default */], {
    staticClass: "d-flex align-center",
    style: "color: ".concat(_vm.incidentPersonType.color),
    attrs: {
      "cols": "3"
    }
  }, [_c('span', [_vm._v(_vm._s(_vm.incidentPersonType.name))]), _vm._v(" "), _vm.hasActionStatus ? _c('span', {
    staticClass: "status-badge ml-2",
    class: _vm.getActionStatusClass()
  }, [_vm._v("\n            " + _vm._s(_vm.getActionStatusText()) + "\n          ")]) : _vm._e()]) : _c(VCol["a" /* default */], {
    staticClass: "d-flex align-center",
    attrs: {
      "cols": "3"
    }
  }, [_vm.hasActionStatus ? _c('span', {
    staticClass: "status-badge",
    class: _vm.getActionStatusClass()
  }, [_vm._v("\n            " + _vm._s(_vm.getActionStatusText()) + "\n          ")]) : _vm._e()]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "d-flex align-center",
    attrs: {
      "cols": "2"
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-timer mr-2 text-muted font-weight-bold"
  }), _vm._v(" "), _c('span', {
    staticClass: "text-muted"
  }, [_c('app-timestamp', {
    attrs: {
      "stamp": _vm.incidentPerson.created_at
    }
  })], 1)]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "d-flex align-center justify-center",
    attrs: {
      "cols": "1"
    }
  }, [_vm.incidentPerson.is_wanted ? _c('span', {
    staticClass: "text text-danger font-weight-bolder"
  }, [_vm._v("WANTED")]) : _vm._e()]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "d-flex justify-center align-center",
    attrs: {
      "cols": "1"
    }
  }, [_vm.incidentPerson.type === 'suspect' && _vm.incident.sub_type !== 'Probation' ? _c('span', [_vm.incidentPerson.is_guilty ? _c('span', [_vm._v("GUILTY")]) : _vm._e(), _vm._v(" "), !_vm.incidentPerson.is_guilty ? _c('span', {
    staticClass: "text text-warning"
  }, [_vm._v("NON GUILTY")]) : _vm._e()]) : _vm._e()]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "d-flex justify-end align-center pr-4 avatar-stack-container",
    attrs: {
      "cols": "1"
    }
  }, [_c('transition', {
    attrs: {
      "name": "avatar-stack-fade"
    }
  }, [_vm.viewers && _vm.viewers.length > 0 ? _c('app-avatar-stack', {
    attrs: {
      "size": 30,
      "class-name": "''",
      "users": _vm.viewers
    }
  }) : _vm._e()], 1)], 1)], 1)], 1), _vm._v(" "), _c(VExpansionPanelContent["a" /* default */], [_c(VLazy["a" /* default */], [_c('div', {
    staticClass: "ml-4 mr-4 mt-3",
    attrs: {
      "id": "person-details-".concat(this.incidentPerson)
    }
  }, [_vm.incidentPerson.charges.length > 0 ? _c('div', [_vm.incident.category.allow_evidence && _vm.incidentPerson.type === 'suspect' ? _c('div', [_vm.charges.length > 0 ? _c('div', [_c(transitions["g" /* VScrollYTransition */]), _vm._v(" "), _c('div', [_c('div', [!_vm.incidentPerson.fined ? _c(VTextField["a" /* default */], {
    ref: "refQuery",
    attrs: {
      "placeholder": "Start typing a charge name"
    },
    model: {
      value: _vm.query,
      callback: function callback($$v) {
        _vm.query = $$v;
      },
      expression: "query"
    }
  }) : _vm._e(), _vm._v(" "), _vm.chargesShowing ? _c('div', _vm._l(_vm.filteredCharges, function (charge, index) {
    return _c(VRow["a" /* default */], {
      key: index,
      staticClass: "p-3 mt-2",
      class: "charge-row-item level-".concat(charge.type),
      attrs: {
        "no-gutters": ""
      }
    }, [_c(VCol["a" /* default */], {
      attrs: {
        "md": "10"
      }
    }, [_vm._v("\n                          " + _vm._s(charge.name) + " "), _c('br'), _vm._v(" "), _c('p', {
      staticClass: "text-muted",
      staticStyle: {
        "font-size": "13px"
      }
    }, [_vm._v("\n                            " + _vm._s(charge.description) + "\n                          ")])]), _vm._v(" "), _c(VCol["a" /* default */], [_c(VBtn["a" /* default */], {
      attrs: {
        "variant": "primary"
      },
      on: {
        "click": function click($event) {
          return _vm.selectCharge(charge.id);
        }
      }
    }, [_vm._v("Add Charge")])], 1)], 1);
  }), 1) : _vm._e()], 1), _vm._v(" "), _c('transition-group', {
    attrs: {
      "name": "slider"
    }
  }, _vm._l(_vm.computedCharges, function (charge, index) {
    return _c(VRow["a" /* default */], {
      key: charge.id,
      staticClass: "p-3 mt-2",
      class: "charge-row-item-selected level-".concat(charge.type),
      attrs: {
        "no-gutters": ""
      }
    }, [_c(VCol["a" /* default */], {
      attrs: {
        "md": "4"
      }
    }, [_vm._v("\n                        " + _vm._s(charge.name) + "\n                        "), _c('p', {
      staticClass: "text-muted",
      staticStyle: {
        "font-size": "13px"
      }
    }, [_vm._v("\n                          " + _vm._s(charge.description) + "\n                        ")])]), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "d-flex justify-content-center",
      attrs: {
        "md": "2"
      }
    }, [_c('div', [_c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("FINE:")]), _vm._v(" "), charge.fine ? _c('span', [_vm._v(" $" + _vm._s(charge.fine))]) : _vm._e(), _vm._v(" "), !charge.fine ? _c('span', {
      staticClass: "text-danger"
    }, [_vm._v("JAIL")]) : _vm._e()]), _vm._v(" "), _c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("TIME:")]), _vm._v(" "), charge.months ? _c('span', [_vm._v(" " + _vm._s(charge.months) + "m")]) : _vm._e()]), _vm._v(" "), _c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("REST:")]), _vm._v(" "), charge.restitution ? _c('span', [_vm._v(" $" + _vm._s(charge.restitution))]) : _vm._e(), _vm._v(" "), !charge.restitution ? _c('span', {
      staticClass: "text-muted"
    }, [_vm._v("N/A")]) : _vm._e()])])]), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "d-flex justify-content-center",
      attrs: {
        "md": "1"
      }
    }, [_c(VTextField["a" /* default */], {
      attrs: {
        "autocomplete": "off",
        "label": "Counts",
        "disabled": _vm.hasBeenFined
      },
      on: {
        "input": _vm.inputChanged,
        "blur": function blur($event) {
          return _vm.chargesUpdated(_vm.charges);
        }
      },
      model: {
        value: charge.counts,
        callback: function callback($$v) {
          _vm.$set(charge, "counts", $$v);
        },
        expression: "charge.counts"
      }
    })], 1), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "d-flex justify-content-center",
      attrs: {
        "md": "4"
      }
    }, [_c(VTextarea["a" /* default */], {
      attrs: {
        "rows": "1",
        "auto-grow": "",
        "label": "Description/Reasoning",
        "disabled": _vm.hasBeenFined
      },
      on: {
        "input": _vm.inputChanged,
        "blur": function blur($event) {
          return _vm.chargesUpdated(_vm.charges);
        }
      },
      model: {
        value: charge.content,
        callback: function callback($$v) {
          _vm.$set(charge, "content", $$v);
        },
        expression: "charge.content"
      }
    })], 1), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "d-flex justify-content-center align-items-center hover-icon",
      attrs: {
        "md": "1"
      }
    }, [_c(VBtn["a" /* default */], {
      on: {
        "click": function click($event) {
          return _vm.removeCharge(charge.police_charge_id);
        }
      }
    }, [_c('i', {
      staticClass: "fas fa-times text-danger font-weight-bold"
    })])], 1)], 1);
  }), 1)], 1), _vm._v(" "), _c('div', {
    staticClass: "charges-calc-area"
  }, [_c(VRow["a" /* default */], {
    staticStyle: {
      "margin-top": "25px",
      "font-size": "14px"
    },
    attrs: {
      "no-gutters": ""
    }
  }, [_c(VCol["a" /* default */], {
    attrs: {
      "md": "6"
    }
  }, [_c('app-form-group', {
    attrs: {
      "label": "Charges (read only)"
    }
  }, [_c('div', {
    staticClass: "text-muted"
  }, [_c('div', {
    domProps: {
      "innerHTML": _vm._s(_vm.computedCharges.map(function (c) {
        return " ".concat(c.name, " - ").concat(c.counts, " (").concat(c.content, ")");
      }).toString())
    }
  })])])], 1), _vm._v(" "), _vm.sentence.fines > -1 ? _c(VCol["a" /* default */], [_c('div', {
    staticClass: "font-weight-bold"
  }, [_vm._v("If they want to pay fines")]), _vm._v(" "), _c('div', [_vm.sentence.fines <= 25000 ? _c('span', [_vm._v("Fines: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('b', [_vm._v("$" + _vm._s(_vm.sentence.fines.toLocaleString()))])])]) : _c('span', [_vm._v("Fines: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('strike', [_vm._v("$" + _vm._s(_vm.sentence.fines.toLocaleString()))]), _vm._v(" "), _c('b', [_vm._v("$25,000")])], 1)])]), _vm._v(" "), _c('div', [_vm.sentence.man <= 210 ? _c('span', [_vm._v("(M) Time: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('b', [_vm._v(_vm._s(_vm.sentence.man) + " months")])])]) : _c('span', [_vm._v("(M) Time: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('strike', [_vm._v(_vm._s(_vm.sentence.man) + " months")]), _vm._v(" "), _c('b', [_vm._v("210\n                          months")])], 1)])]), _vm._v(" "), _c('div', [_vm.sentence.fineres <= 15000 ? _c('span', [_vm._v("\n                          Restitution: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('b', [_vm._v("$" + _vm._s(_vm.sentence.fineres.toLocaleString()))])])]) : _c('span', [_vm._v("Restitution: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('strike', [_vm._v("$" + _vm._s(_vm.sentence.fineres.toLocaleString()))]), _vm._v(" "), _c('b', [_vm._v("$15,000")])], 1)])])]) : _vm._e(), _vm._v(" "), _vm.sentence.fines > -1 ? _c(VCol["a" /* default */], [_c('div', {
    staticClass: "font-weight-bold"
  }, [_vm._v("\n                        " + _vm._s(_vm.sentence.fines ? "Don't want to pay fines" : "Mandatory Sentence") + "\n                      ")]), _vm._v(" "), _c('div', [_vm.sentence.time <= 210 ? _c('span', [_vm._v("Time: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('b', [_vm._v(_vm._s(_vm.sentence.time) + "\n                          months")])])]) : _c('span', [_vm._v("Time: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('strike', [_vm._v(_vm._s(_vm.sentence.time) + " months")]), _vm._v(" "), _c('b', [_vm._v("210\n                          months")])], 1)])]), _vm._v(" "), _c('div', [_vm.sentence.res <= 15000 ? _c('span', [_vm._v("Restitution: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('b', [_vm._v("$" + _vm._s(_vm.sentence.res.toLocaleString()))])])]) : _c('span', [_vm._v("Restitution: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('strike', [_vm._v("$" + _vm._s(_vm.sentence.res.toLocaleString()))]), _vm._v(" "), _c('b', [_vm._v("$15,000")])], 1)])])]) : _vm._e()], 1)], 1)], 1) : _vm._e(), _vm._v(" "), _vm.evidenceItems.length > 0 ? _c('div', [_c('app-form-group', {
    staticClass: "text-white-50",
    attrs: {
      "label": "Evidence Items (read only)"
    }
  }, _vm._l(_vm.evidenceItems, function (item, index) {
    return _c(VRow["a" /* default */], {
      key: index
    }, [_c(VCol["a" /* default */], [_c('span', {
      staticClass: "text-muted"
    }, [item.name.includes('INVALID') ? _c('span', [_vm._v(_vm._s(item.idname))]) : _c('span', [_vm._v(_vm._s(item.name))]), _vm._v(" "), _c('span', [_vm._v(" ("), _c('b', [_vm._v(_vm._s(item.amount))]), _vm._v(")")])])])], 1);
  }), 1)], 1) : _vm._e(), _vm._v(" "), _vm.incidentPerson.fined ? _c('div', {
    staticClass: "mt-4"
  }, [_vm.incidentPerson.fined.paid && _vm.incidentPerson.fined.action === 'fined' ? _c('span', {
    staticClass: "text-success"
  }, [_vm._v("\n                  Person has been fined and has paid.\n                ")]) : _vm.incidentPerson.fined.paid && _vm.incidentPerson.fined.action === 'imprisoned' ? _c('span', {
    staticClass: "text-success"
  }, [_vm._v("\n                  Person has been sent to prison.\n                ")]) : _vm.incidentPerson.fined.paid ? _c('span', {
    staticClass: "text-success"
  }, [_vm._v("\n                  Action has been issued on the person\n                ")]) : _vm.incidentPerson.fined ? _c('span', {
    staticClass: "text-warning"
  }, [_c('i', {
    staticClass: "fa-solid fa-spin fa-spinner-third mr-2"
  }), _vm._v("\n                  Action is pending confirmation...\n                ")]) : _vm._e()]) : _vm._e(), _vm._v(" "), _vm.sideSuspect ? _c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_vm.incidentPerson.type === 'suspect' && _vm.incident.sub_type !== 'GTF Profile' && !_vm.incidentPerson.fined && this.incidentPerson.charges.length < 1 ? _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "btn text-white-50",
    staticStyle: {
      "margin-top": "31px"
    },
    on: {
      "click": function click($event) {
        return _vm.syncCharges(_vm.sideSuspect);
      }
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-clipboard text mr-2"
  }), _vm._v("\n                    Sync Charges from " + _vm._s(_vm.sideSuspect.person.name) + "\n                  ")]) : _vm._e()], 1)], 1) : _vm._e()], 1) : _vm._e()]) : _c('div', [_c('div', [_vm.incident.category.allow_evidence && _vm.incidentPerson.type === 'suspect' ? _c('div', [_vm.crimes.length > 0 ? _c('div', [_c(transitions["g" /* VScrollYTransition */], [_vm.hasChanged ? _c(VAlert["a" /* default */], {
    attrs: {
      "color": "red"
    }
  }, [_vm._v("\n                      Charges are not currently saved\n                    ")]) : _vm._e()], 1), _vm._v(" "), _c('div', [_c('div', [!_vm.incidentPerson.fined ? _c(VTextField["a" /* default */], {
    ref: "refQuery",
    attrs: {
      "placeholder": "Start typing a crime name"
    },
    model: {
      value: _vm.query,
      callback: function callback($$v) {
        _vm.query = $$v;
      },
      expression: "query"
    }
  }) : _vm._e(), _vm._v(" "), _vm.chargesShowing ? _c('div', _vm._l(_vm.filteredCrimes, function (crime, index) {
    return _c(VRow["a" /* default */], {
      key: index,
      staticClass: "p-3 mt-2",
      class: "charge-row-item level-".concat(crime.type),
      attrs: {
        "no-gutters": ""
      }
    }, [_c(VCol["a" /* default */], {
      attrs: {
        "md": "10"
      }
    }, [_vm._v("\n                            " + _vm._s(crime.name) + " "), _c('br'), _vm._v(" "), _c('p', {
      staticClass: "text-muted",
      staticStyle: {
        "font-size": "13px"
      }
    }, [_vm._v("\n                              " + _vm._s(crime.description) + "\n                            ")])]), _vm._v(" "), _c(VCol["a" /* default */], [_c(VBtn["a" /* default */], {
      attrs: {
        "variant": "primary"
      },
      on: {
        "click": function click($event) {
          return _vm.selectCrime(crime.id);
        }
      }
    }, [_vm._v("Add Crime")])], 1)], 1);
  }), 1) : _vm._e()], 1), _vm._v(" "), _c('transition-group', {
    attrs: {
      "name": "slider"
    }
  }, _vm._l(_vm.incidentPerson.crimes, function (charge, index) {
    return _c('div', {
      key: charge.id,
      staticClass: "p-3 mt-2",
      class: "charge-row-item-selected level-".concat(charge.crime.type)
    }, [_c(VRow["a" /* default */], {
      attrs: {
        "no-gutters": ""
      }
    }, [_c(VCol["a" /* default */], {
      attrs: {
        "cols": "6"
      }
    }, [_vm._v("\n                            " + _vm._s(charge.crime_name) + "\n"), _vm._v(" "), _c('p', {
      staticClass: "text-muted",
      staticStyle: {
        "font-size": "13px"
      }
    }, [_vm._v("\n                              " + _vm._s(charge.crime.description) + "\n                            ")])]), _vm._v(" "), _c(VSpacer["a" /* default */]), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "d-flex justify-content-center",
      attrs: {
        "cols": "2"
      }
    }, [_c('div', [_c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("FINE:")]), _vm._v(" "), charge.crime.totals_ticket ? _c('span', [_vm._v(" $" + _vm._s(charge.crime.totals_ticket))]) : _vm._e(), _vm._v(" "), !charge.crime.totals_ticket ? _c('span', {
      staticClass: "text-danger"
    }, [_vm._v("JAIL")]) : _vm._e()]), _vm._v(" "), _c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("TIME:")]), _vm._v(" "), charge.crime.totals_prison ? _c('span', [_vm._v(" " + _vm._s(charge.crime.totals_prison) + "m")]) : _vm._e()]), _vm._v(" "), _c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("REST:")]), _vm._v(" "), charge.crime.totals_restitution ? _c('span', [_vm._v(" $" + _vm._s(charge.crime.totals_restitution))]) : _vm._e(), _vm._v(" "), !charge.crime.totals_restitution ? _c('span', {
      staticClass: "text-muted"
    }, [_vm._v("N/A")]) : _vm._e()]), _vm._v(" "), _c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("PAROLE:")]), _vm._v(" "), charge.crime.totals_parole ? _c('span', [_vm._v(" " + _vm._s(charge.crime.totals_parole) + "m")]) : _vm._e(), _vm._v(" "), !charge.crime.totals_parole ? _c('span', {
      staticClass: "text-muted"
    }, [_vm._v("N/A")]) : _vm._e()])])]), _vm._v(" "), charge.is_accessory || charge.is_attempted || charge.is_against_government || charge.is_against_leo || charge.is_commercial_vehicle ? _c(VCol["a" /* default */], {
      staticClass: "d-flex justify-content-center",
      attrs: {
        "cols": "2"
      }
    }, [_c('div', [_c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("FINE:")]), _vm._v(" "), charge.crime.totals_ticket ? _c('span', [_vm._v(" $" + _vm._s(_vm.calculateChargeTotals(charge.crime.totals_ticket, charge)))]) : _vm._e(), _vm._v(" "), !charge.crime.totals_ticket ? _c('span', {
      staticClass: "text-danger"
    }, [_vm._v("JAIL")]) : _vm._e()]), _vm._v(" "), _c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("TIME:")]), _vm._v(" "), charge.crime.totals_prison ? _c('span', [_vm._v(" " + _vm._s(_vm.calculateChargeTotals(charge.crime.totals_prison, charge)) + "m")]) : _vm._e()]), _vm._v(" "), _c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("REST:")]), _vm._v(" "), charge.crime.totals_restitution ? _c('span', [_vm._v(" $" + _vm._s(charge.crime.totals_restitution))]) : _vm._e(), _vm._v(" "), !charge.crime.totals_restitution ? _c('span', {
      staticClass: "text-muted"
    }, [_vm._v("N/A")]) : _vm._e()]), _vm._v(" "), _c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("PAROLE:")]), _vm._v(" "), charge.crime.totals_parole ? _c('span', [_vm._v(" " + _vm._s(charge.crime.totals_parole) + "m")]) : _vm._e(), _vm._v(" "), !charge.crime.totals_parole ? _c('span', {
      staticClass: "text-muted"
    }, [_vm._v("N/A")]) : _vm._e()])])]) : _vm._e(), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "text-right d-flex justify-content-end align-items-center hover-icon",
      attrs: {
        "cols": "1"
      }
    }, [_c(VBtn["a" /* default */], {
      attrs: {
        "fab": ""
      },
      on: {
        "click": function click($event) {
          return _vm.removeCrime(charge.id);
        }
      }
    }, [_c('i', {
      staticClass: "fas fa-times text-danger font-weight-bold"
    })])], 1)], 1), _vm._v(" "), _c(VRow["a" /* default */], {
      staticStyle: {
        "margin-top": "0 !important"
      },
      attrs: {
        "no-gutters": ""
      }
    }, [charge.crime.can_commercial_vehicle ? _c(VCol["a" /* default */], [_c(VCheckbox["a" /* default */], {
      attrs: {
        "label": "Commercial Vehicle",
        "hide-details": "",
        "disabled": _vm.hasBeenFined
      },
      on: {
        "change": function change($event) {
          return _vm.handleCrimeAttributesChange(charge.id, 'is_commercial_vehicle', $event);
        }
      },
      model: {
        value: charge.is_commercial_vehicle,
        callback: function callback($$v) {
          _vm.$set(charge, "is_commercial_vehicle", $$v);
        },
        expression: "charge.is_commercial_vehicle"
      }
    })], 1) : _vm._e(), _vm._v(" "), charge.crime.can_attempted ? _c(VCol["a" /* default */], [_c(VCheckbox["a" /* default */], {
      attrs: {
        "label": "Attempted",
        "hide-details": "",
        "disabled": _vm.hasBeenFined
      },
      on: {
        "change": function change($event) {
          return _vm.handleCrimeAttributesChange(charge.id, 'is_attempted', $event);
        }
      },
      model: {
        value: charge.is_attempted,
        callback: function callback($$v) {
          _vm.$set(charge, "is_attempted", $$v);
        },
        expression: "charge.is_attempted"
      }
    })], 1) : _vm._e(), _vm._v(" "), charge.crime.can_accessory ? _c(VCol["a" /* default */], [_c(VCheckbox["a" /* default */], {
      attrs: {
        "label": "Accessory",
        "hide-details": "",
        "disabled": _vm.hasBeenFined
      },
      on: {
        "change": function change($event) {
          return _vm.handleCrimeAttributesChange(charge.id, 'is_accessory', $event);
        }
      },
      model: {
        value: charge.is_accessory,
        callback: function callback($$v) {
          _vm.$set(charge, "is_accessory", $$v);
        },
        expression: "charge.is_accessory"
      }
    })], 1) : _vm._e(), _vm._v(" "), charge.crime.can_against_leo || charge.crime.can_against_government ? _c(VCol["a" /* default */], [charge.crime.can_against_leo ? _c(VCheckbox["a" /* default */], {
      attrs: {
        "label": "Against LEO",
        "hide-details": "",
        "disabled": _vm.hasBeenFined || charge.is_against_government
      },
      on: {
        "change": function change($event) {
          return _vm.handleCrimeAttributesChange(charge.id, 'is_against_leo', $event);
        }
      },
      model: {
        value: charge.is_against_leo,
        callback: function callback($$v) {
          _vm.$set(charge, "is_against_leo", $$v);
        },
        expression: "charge.is_against_leo"
      }
    }) : _vm._e(), _vm._v(" "), charge.crime.can_against_government ? _c(VCheckbox["a" /* default */], {
      attrs: {
        "label": "Against Government Official",
        "hide-details": "",
        "disabled": _vm.hasBeenFined || charge.is_against_leo
      },
      on: {
        "change": function change($event) {
          return _vm.handleCrimeAttributesChange(charge.id, 'is_against_government', $event);
        }
      },
      model: {
        value: charge.is_against_government,
        callback: function callback($$v) {
          _vm.$set(charge, "is_against_government", $$v);
        },
        expression: "charge.is_against_government"
      }
    }) : _vm._e()], 1) : _vm._e()], 1)], 1);
  }), 0)], 1), _vm._v(" "), _c(VCard["a" /* default */], {
    staticClass: "charges-calc-area mb-4",
    attrs: {
      "outlined": ""
    }
  }, [_c(components_VCard["b" /* VCardText */], [_c(VRow["a" /* default */], {
    staticStyle: {
      "font-size": "18px"
    },
    attrs: {
      "no-gutters": ""
    }
  }, [_c(VCol["a" /* default */], {
    attrs: {
      "md": "6"
    }
  }, [_c('app-form-group', {
    attrs: {
      "label": "Charges (read only)"
    }
  }, [_c('div', {
    staticClass: "text-muted"
  }, [_c('div', {
    staticStyle: {
      "font-size": "13px"
    },
    domProps: {
      "innerHTML": _vm._s(_vm.chargesString)
    }
  })])])], 1), _vm._v(" "), _c(VCol["a" /* default */], [_c('div', {
    staticClass: "font-weight-bold mb-1"
  }, [_c('u', [_vm._v("\n                              If they want to pay fines\n                            ")])]), _vm._v(" "), _c('div', [_c('span', [_vm._v("\n                              Fines:\n                            ")]), _vm._v(" "), _c('span', {
    staticClass: "text-white font-weight-bold"
  }, [_vm._v("\n                              $" + _vm._s(_vm.incidentPerson.totals['paying-fines']['final_ticket']) + "\n                            ")])]), _vm._v(" "), _c('div', [_c('span', [_vm._v("\n                              Time:\n                            ")]), _vm._v(" "), _c('span', {
    staticClass: "text-white font-weight-bold"
  }, [_vm._v("\n                              " + _vm._s(_vm.incidentPerson.totals['paying-fines']['final_prison']) + "m\n                            ")])]), _vm._v(" "), _c('div', [_c('span', [_vm._v("\n                              Restitution:\n                            ")]), _vm._v(" "), _c('span', {
    staticClass: "text-white font-weight-bold"
  }, [_vm._v("\n                              $" + _vm._s(_vm.incidentPerson.totals['paying-fines']['final_restitution']) + "\n                            ")])]), _vm._v(" "), _c('div', [_c('span', [_vm._v("\n                              Parole:\n                            ")]), _vm._v(" "), _c('span', {
    staticClass: "text-white font-weight-bold"
  }, [_vm._v("\n                              " + _vm._s(_vm.incidentPerson.totals['paying-fines']['final_parole']) + "m\n                            ")])])]), _vm._v(" "), _c(VCol["a" /* default */], [_c('div', {
    staticClass: "font-weight-bold mb-1"
  }, [_c('u', [_vm._v("\n                              If they do not pay fines\n                            ")])]), _vm._v(" "), _c('div', [_c('span', [_vm._v("\n                              Fines:\n                            ")]), _vm._v(" "), _c('span', {
    staticClass: "text-white font-weight-bold"
  }, [_vm._v("\n                              $" + _vm._s(_vm.incidentPerson.totals['not-paying-fines']['final_ticket']) + "\n                            ")])]), _vm._v(" "), _c('div', [_c('span', [_vm._v("\n                              Time:\n                            ")]), _vm._v(" "), _c('span', {
    staticClass: "text-white font-weight-bold"
  }, [_vm._v("\n                              " + _vm._s(_vm.incidentPerson.totals['not-paying-fines']['final_prison']) + "m\n                            ")])]), _vm._v(" "), _c('div', [_c('span', [_vm._v("\n                              Restitution:\n                            ")]), _vm._v(" "), _c('span', {
    staticClass: "text-white font-weight-bold"
  }, [_vm._v("\n                              $" + _vm._s(_vm.incidentPerson.totals['not-paying-fines']['final_restitution']) + "\n                            ")])]), _vm._v(" "), _c('div', [_c('span', [_vm._v("\n                              Parole:\n                            ")]), _vm._v(" "), _c('span', {
    staticClass: "text-white font-weight-bold"
  }, [_vm._v("\n                              " + _vm._s(_vm.incidentPerson.totals['not-paying-fines']['final_parole']) + "m\n                            ")])])])], 1)], 1)], 1)], 1) : _vm._e(), _vm._v(" "), _vm.evidenceItems.length > 0 ? _c('div', [_c('app-form-group', {
    staticClass: "text-white-50",
    attrs: {
      "label": "Evidence Items (read only)"
    }
  }, _vm._l(_vm.evidenceItems, function (item, index) {
    return _c(VRow["a" /* default */], {
      key: index
    }, [_c(VCol["a" /* default */], [_c('span', {
      staticClass: "text-muted"
    }, [item.name.includes('INVALID') ? _c('span', [_vm._v(_vm._s(item.idname))]) : _c('span', [_vm._v(_vm._s(item.name))]), _vm._v(" "), _c('span', [_vm._v(" ("), _c('b', [_vm._v(_vm._s(item.amount))]), _vm._v(")")])])])], 1);
  }), 1)], 1) : _vm._e(), _vm._v(" "), _vm.incidentPerson.fined ? _c('div', {
    staticClass: "mt-4"
  }, [_vm.incidentPerson.fined.paid && _vm.incidentPerson.fined.action === 'fined' ? _c('span', {
    staticClass: "text-success"
  }, [_vm._v("\n                    Person has been fined and has paid.\n                  ")]) : _vm.incidentPerson.fined.paid && _vm.incidentPerson.fined.action === 'imprisoned' ? _c('span', {
    staticClass: "text-success"
  }, [_vm._v("\n                    Person has been sent to prison.\n                  ")]) : _vm.incidentPerson.fined.paid ? _c('span', {
    staticClass: "text-success"
  }, [_vm._v("\n                    Action has been issued on the person\n                  ")]) : _vm.incidentPerson.fined ? _c('span', {
    staticClass: "text-warning"
  }, [_c('i', {
    staticClass: "fa-solid fa-spin fa-spinner-third mr-2"
  }), _vm._v("\n                    Action is pending confirmation...\n                  ")]) : _vm._e()]) : _vm._e(), _vm._v(" "), _c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_vm.sideSuspect && _vm.incidentPerson.type === 'suspect' && _vm.incident.sub_type !== 'GTF Profile' && !_vm.incidentPerson.fined && this.incidentPerson.crimes.length < 1 ? _c(VBtn["a" /* default */], {
    staticClass: "mr-4",
    on: {
      "click": function click($event) {
        return _vm.syncCharges(_vm.sideSuspect);
      }
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-clipboard text mr-2"
  }), _vm._v("\n                      Sync Charges from " + _vm._s(_vm.sideSuspect.person.name) + "\n                    ")]) : _vm._e(), _vm._v(" "), _vm.incidentPerson.type === 'suspect' && _vm.incidentPerson.person.incidents_wanted && _vm.incidentPerson.person.incidents_wanted.length > 0 && !_vm.incidentPerson.fined ? _c(VBtn["a" /* default */], {
    staticClass: "ml-4",
    on: {
      "click": function click($event) {
        return _vm.syncWarrantCharges();
      }
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-rocket text mr-2"
  }), _vm._v("\n                      Attempt Sync Charges from past warrants\n                    ")]) : _vm._e(), _vm._v(" "), _vm.incidentPerson.type === 'suspect' && _vm.incident.category.name.includes('SANDBOX') ? _c(VBtn["a" /* default */], {
    staticClass: "ml-4",
    on: {
      "click": function click($event) {
        return _vm.clearCharges();
      }
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-trash-can text mr-2"
  }), _vm._v("\n                      Clear Charges\n                    ")]) : _vm._e()], 1), _vm._v("`\n                ")], 1)], 1) : _vm._e()])]), _vm._v(" "), _vm.incidentPerson.time > 60 ? _c(VAlert["a" /* default */], {
    staticClass: "mt-2",
    attrs: {
      "color": "red"
    }
  }, [_vm._v("\n            Time exceeds the maximum of 60 months. Please ensure you have approval before sending to jail.\n          ")]) : _vm._e(), _vm._v(" "), _vm.incidentPerson.res > 50000 ? _c(VAlert["a" /* default */], {
    staticClass: "mt-2",
    attrs: {
      "color": "red"
    }
  }, [_vm._v("\n            Restitution exceeds the maximum of $50,000. Please ensure you have approval before sending to jail.\n          ")]) : _vm._e(), _vm._v(" "), _vm.incidentPerson.fine > 25000 ? _c(VAlert["a" /* default */], {
    staticClass: "mt-2",
    attrs: {
      "color": "red"
    }
  }, [_vm._v("\n            Fine exceeds the maximum of $25,000. Please ensure you have approval before issuing a ticket, or sending to jail.\n          ")]) : _vm._e(), _vm._v(" "), !_vm.incident.category.name.includes('SANDBOX') ? _c('div', [_vm.incidentPerson.type === 'suspect' && _vm.incident.category.allow_evidence ? _c(VRow["a" /* default */], {
    staticClass: "mt-5",
    attrs: {
      "no-gutters": ""
    }
  }, [_c(VCol["a" /* default */], {
    attrs: {
      "md": "1"
    }
  }, [_c(VTextField["a" /* default */], {
    attrs: {
      "dense": "",
      "label": "Months",
      "type": "number",
      "disabled": _vm.hasBeenFined
    },
    on: {
      "input": _vm.timeChanged
    },
    model: {
      value: _vm.incidentPerson.time,
      callback: function callback($$v) {
        _vm.$set(_vm.incidentPerson, "time", $$v);
      },
      expression: "incidentPerson.time"
    }
  })], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "ml-2",
    attrs: {
      "cols": "2"
    }
  }, [_c(VTextField["a" /* default */], {
    attrs: {
      "dense": "",
      "label": "Restitution $",
      "type": "number",
      "disabled": _vm.hasBeenFined
    },
    on: {
      "input": _vm.resChanged
    },
    model: {
      value: _vm.incidentPerson.res,
      callback: function callback($$v) {
        _vm.$set(_vm.incidentPerson, "res", $$v);
      },
      expression: "incidentPerson.res"
    }
  })], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "ml-2",
    attrs: {
      "cols": "1"
    }
  }, [_c(VTextField["a" /* default */], {
    attrs: {
      "dense": "",
      "label": "Fine $",
      "type": "number",
      "disabled": _vm.hasBeenFined
    },
    on: {
      "input": _vm.fineChanged
    },
    model: {
      value: _vm.incidentPerson.fine,
      callback: function callback($$v) {
        _vm.$set(_vm.incidentPerson, "fine", $$v);
      },
      expression: "incidentPerson.fine"
    }
  })], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "ml-4"
  }, [_vm.incidentPerson.type === 'suspect' && _vm.incident.sub_type !== 'GTF Profile' && !_vm.incidentPerson.fined ? _c(VBtn["a" /* default */], {
    attrs: {
      "color": "success"
    },
    on: {
      "click": _vm.updatePerson
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-cloud-arrow-up text mr-2"
  }), _vm._v("\n                  Save\n                ")]) : _vm._e(), _vm._v(" "), _vm.$refs.recordCharges ? _c(VBtn["a" /* default */], {
    on: {
      "click": _vm.$refs.recordCharges.printCharges
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "small": ""
    }
  }, [_vm._v("\n                    fa-regular fa-hand\n                  ")]), _vm._v("\n                  Pin Charges\n                ")], 1) : _vm._e(), _vm._v(" "), (!_vm.incidentPerson.fine || _vm.incidentPerson.fine < 1) && (!_vm.incidentPerson.res || _vm.incidentPerson.res < 1) && (!_vm.incidentPerson.time || _vm.incidentPerson.time < 1) && !_vm.incidentPerson.fined ? _c(VBtn["a" /* default */], {
    attrs: {
      "color": "warning"
    },
    on: {
      "click": _vm.doWarning
    }
  }, [_vm._v("\n                  Issue Warning\n                ")]) : _vm._e(), _vm._v(" "), _vm.incidentPerson.time < 1 && _vm.incidentPerson.fine > 1 && !_vm.incidentPerson.fined && _vm.incidentPerson.time < 1 ? _c(VBtn["a" /* default */], {
    on: {
      "click": _vm.doFine
    }
  }, [_vm._v("\n                  Issue Fine\n                ")]) : _vm._e(), _vm._v(" "), (_vm.incidentPerson.time && _vm.incidentPerson.time > 0 || _vm.incidentPerson.res && _vm.incidentPerson.res > 1) && !_vm.incidentPerson.fined ? _c(VBtn["a" /* default */], {
    attrs: {
      "color": "red",
      "disabled": _vm.hasBeenFined
    },
    on: {
      "click": _vm.doJail
    }
  }, [_vm._v("\n                  Send Jail\n                ")]) : _vm._e(), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "color": "primary"
    },
    on: {
      "click": _vm.printCharges
    }
  }, [_vm._v("\n                  Pin Charges\n                ")])], 1)], 1) : _vm._e(), _vm._v(" "), _vm.incidentPerson.res > 0 && _vm.incidentPerson.time < 1 ? _c(VAlert["a" /* default */], {
    attrs: {
      "color": "red"
    }
  }, [_vm._v("\n              You have added restitution but no time in prison. Restitution will be ignored.\n            ")]) : _vm._e(), _vm._v(" "), _vm.isReady && !_vm.incident.category.name.includes('SANDBOX') ? _c('div', {
    staticClass: "d-flex justify-content-between mt-4"
  }, [_c('div', {
    attrs: {
      "dense": "",
      "borderless": ""
    }
  }, [_vm.incidentPerson.type === 'suspect' && !_vm.incidentPerson.is_pardoned ? _c('div', {
    staticClass: "d-inline"
  }, [!_vm.incidentPerson.is_pardoned && _vm.incident.sub_type !== 'GTF Profile' && _vm.hasAnyGroup(['Judge', 'ADA', 'DOJ Paralegal']) ? _c(VBtn["a" /* default */], {
    on: {
      "click": _vm.markPardoned
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-star-christmas text-warning mr-2"
  }), _vm._v("\n                    Pardon\n                  ")]) : _vm._e(), _vm._v(" "), _vm.incidentPerson.is_wanted && _vm.incident.sub_type !== 'GTF Profile' ? _c(VBtn["a" /* default */], {
    on: {
      "click": _vm.markArrested
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-handcuffs text-success mr-2"
  }), _vm._v("\n                    Mark Arrested\n                  ")]) : _vm._e(), _vm._v(" "), !_vm.incidentPerson.is_wanted && _vm.incident.sub_type !== 'GTF Profile' && !_vm.incidentPerson.fined ? _c(VBtn["a" /* default */], {
    on: {
      "click": _vm.markWanted
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-light-emergency-on text-danger text-white-50 mr-2"
  }), _vm._v("\n                    Mark Wanted\n                  ")]) : _vm._e(), _vm._v(" "), !_vm.incidentPerson.is_guilty && _vm.incident.sub_type !== 'Probation' && _vm.incident.sub_type !== 'GTF Profile' ? _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    on: {
      "click": function click($event) {
        return _vm.markGuilty(true);
      }
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-gavel text-danger mr-2"
  }), _vm._v("\n                    Mark Guilty\n                  ")]) : _vm._e(), _vm._v(" "), _vm.incidentPerson.is_guilty && _vm.incident.sub_type !== 'GTF Profile' ? _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    on: {
      "click": function click($event) {
        return _vm.markGuilty(false);
      }
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-gavel text-success mr-2"
  }), _vm._v("\n                    Mark Not Guilty\n                  ")]) : _vm._e()], 1) : _vm._e(), _vm._v(" "), _c('div', {
    staticClass: "d-inline"
  }, [_c(VBtn["a" /* default */], {
    on: {
      "click": function click($event) {
        return _vm.$refs['notes'].startAddingNote();
      }
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-note mr-2"
  }), _vm._v("\n                    + Note\n                  ")]), _vm._v(" "), _vm.incident.category.allow_evidence ? _c(VBtn["a" /* default */], {
    on: {
      "click": function click($event) {
        return _vm.$refs['evidence'].startAddingNote();
      }
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-box-open text-primary mr-2"
  }), _vm._v("\n                    + Evidence\n                  ")]) : _vm._e(), _vm._v(" "), _vm.incident.category.allow_evidence ? _c(VBtn["a" /* default */], {
    on: {
      "click": _vm.openPersonLocker
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-box-open text-primary mr-2"
  }), _vm._v("\n                    + Prison Locker\n                  ")]) : _vm._e(), _vm._v(" "), _vm.hasAnyGroup(['SAHP', 'admin']) || (_vm.isAbleDeleteCatRecord || _vm.isPoliceLt || _vm.incidentPerson.editable && _vm.incidentPerson.person_id === _vm.user.id) && !_vm.incidentPerson.fined ? _c(VBtn["a" /* default */], {
    on: {
      "click": _vm.removePerson
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-trash-can text-danger mr-2"
  }), _vm._v("\n                    Remove\n                  ")]) : _vm._e()], 1)]), _vm._v(" "), _c('div', [!_vm.incidentPerson.is_pardoned && _vm.incident.category.allow_add_vehicles ? _c(VBtn["a" /* default */], {
    on: {
      "click": _vm.viewProfile
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-person mr-2"
  }), _vm._v("\n                  Profile\n                ")]) : _vm._e()], 1)]) : _vm._e(), _vm._v(" "), _c('note-container', {
    ref: "notes",
    attrs: {
      "incident-person": _vm.incidentPerson
    }
  }), _vm._v(" "), _c('police-person-evidence', {
    ref: "evidence",
    attrs: {
      "incident-person": _vm.incidentPerson
    }
  }), _vm._v(" "), !_vm.incidentPerson.is_pardoned && _vm.incident.category.allow_evidence ? _c('div', {
    staticClass: "mt-5"
  }, [_c('crudy-table-items-component-citizen', {
    attrs: {
      "item": _vm.incidentPerson.person
    }
  })], 1) : _vm._e()], 1) : _vm._e(), _vm._v(" "), _c('br')], 1)])], 1)], 1);
};
var police_incident_person_itemvue_type_template_id_728fdaa0_scoped_true_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/police-incident-person-item.vue?vue&type=template&id=728fdaa0&scoped=true

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(37);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/inherits.js
var inherits = __webpack_require__(28);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js
var setPrototypeOf = __webpack_require__(515);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.replace.js
var es_symbol_replace = __webpack_require__(1701);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.values.js
var es_object_values = __webpack_require__(316);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.constructor.js
var es_regexp_constructor = __webpack_require__(217);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.match.js
var es_string_match = __webpack_require__(145);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.weak-map.js
var es_weak_map = __webpack_require__(895);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.weak-map.delete-all.js
var esnext_weak_map_delete_all = __webpack_require__(896);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./components/Common/note-container.vue + 4 modules
var note_container = __webpack_require__(1675);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-person-evidence.vue?vue&type=template&id=979b823e



var police_person_evidencevue_type_template_id_979b823e_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('div'), _vm._v(" "), _vm.addingNote ? _c('div', [_c('app-form-group', [_c(VTextarea["a" /* default */], {
    attrs: {
      "filled": "",
      "rows": "2"
    },
    model: {
      value: _vm.content,
      callback: function callback($$v) {
        _vm.content = $$v;
      },
      expression: "content"
    }
  }, [_vm._v("\n        Reason for Access\n      ")])], 1), _vm._v(" "), _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    attrs: {
      "color": "primary"
    },
    on: {
      "click": _vm.submit
    }
  }, [_vm._v("\n      Access Evidence\n    ")])], 1) : _vm._e()]);
};
var police_person_evidencevue_type_template_id_979b823e_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/police-person-evidence.vue?vue&type=template&id=979b823e

// EXTERNAL MODULE: ./node_modules/vue-markdown/dist/vue-markdown.common.js
var vue_markdown_common = __webpack_require__(496);
var vue_markdown_common_default = /*#__PURE__*/__webpack_require__.n(vue_markdown_common);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-person-evidence.vue?vue&type=script&lang=js



/* harmony default export */ var police_person_evidencevue_type_script_lang_js = ({
  name: 'police-person-evidence',
  props: ['incidentPerson', 'addNote'],
  components: {
    AppFormGroup: app_form_group["a" /* default */],
    VueMarkdown: vue_markdown_common_default.a
  },
  data: function data() {
    return {
      addingNote: false,
      content: 'Reason for access'
    };
  },
  methods: {
    startAddingNote: function startAddingNote() {
      this.addingNote = true;
    },
    submit: function submit() {
      var _this = this;
      return this.$axios.$post("/police/incidents/add-person-note/".concat(this.incidentPerson.id), {
        contents: "Accessed Evidence: ".concat(this.content)
      }).then(function (r) {
        _this.content = null;
        _this.addingNote = false;
        window.$events.$emit('reload');
        _this.openEvidence();
      });

      // return this.axios.post('/police/evidence-log', {
      //     record_id: this.incidentPerson.id,
      //     reason: this.content,
      // }).then(r => {
      //     this.content = null
      //     this.addingNote = false;
      // });
    },
    openEvidence: function openEvidence() {
      fetch("https://blrp_tablet/openEvidence", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify({
          id: "inc_person_".concat(this.incidentPerson.id)
        })
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {
        return resp;
      });
    }
  }
});
// CONCATENATED MODULE: ./components/Pages/Police/police-person-evidence.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_police_person_evidencevue_type_script_lang_js = (police_person_evidencevue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Police/police-person-evidence.vue?vue&type=style&index=0&id=979b823e&prod&lang=scss
var police_person_evidencevue_type_style_index_0_id_979b823e_prod_lang_scss = __webpack_require__(1810);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Police/police-person-evidence.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Police_police_person_evidencevue_type_script_lang_js,
  police_person_evidencevue_type_template_id_979b823e_render,
  police_person_evidencevue_type_template_id_979b823e_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var police_person_evidence = (component.exports);
// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/submit-record-charges.vue?vue&type=template&id=6448ac3a











var submit_record_chargesvue_type_template_id_6448ac3a_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.charges.length > 0 ? _c('div', [_c(transitions["g" /* VScrollYTransition */], [_vm.hasChanged ? _c(VAlert["a" /* default */], {
    attrs: {
      "color": "red"
    }
  }, [_vm._v("\n      Charges are not currently saved\n    ")]) : _vm._e()], 1), _vm._v(" "), _c('div', [_c('div', [!_vm.editDisabled ? _c(VTextField["a" /* default */], {
    ref: "refQuery",
    attrs: {
      "placeholder": "Start typing a charge name"
    },
    model: {
      value: _vm.query,
      callback: function callback($$v) {
        _vm.query = $$v;
      },
      expression: "query"
    }
  }) : _vm._e(), _vm._v(" "), _vm.showing ? _c('div', _vm._l(_vm.filteredCharges, function (charge, index) {
    return _c(VRow["a" /* default */], {
      key: index,
      staticClass: "p-3 mt-2",
      class: "charge-row-item level-".concat(charge.type),
      attrs: {
        "no-gutters": ""
      }
    }, [_c(VCol["a" /* default */], {
      attrs: {
        "md": "10"
      }
    }, [_vm._v("\n            " + _vm._s(charge.name) + " "), _c('br'), _vm._v(" "), _c('p', {
      staticClass: "text-muted",
      staticStyle: {
        "font-size": "13px"
      }
    }, [_vm._v("\n              " + _vm._s(charge.description) + "\n            ")])]), _vm._v(" "), _c(VCol["a" /* default */], [_c(VBtn["a" /* default */], {
      attrs: {
        "variant": "primary"
      },
      on: {
        "click": function click($event) {
          return _vm.selectCharge(charge.id);
        }
      }
    }, [_vm._v("Add Charge")])], 1)], 1);
  }), 1) : _vm._e()], 1), _vm._v(" "), _c('transition-group', {
    attrs: {
      "name": "slider"
    }
  }, _vm._l(_vm.charges, function (charge, index) {
    return charge.selected ? _c(VRow["a" /* default */], {
      key: charge.id,
      staticClass: "p-3 mt-2",
      class: "charge-row-item-selected level-".concat(charge.type),
      attrs: {
        "no-gutters": ""
      }
    }, [_c(VCol["a" /* default */], {
      attrs: {
        "md": "4"
      }
    }, [_vm._v("\n          " + _vm._s(charge.name) + "\n          "), _c('p', {
      staticClass: "text-muted",
      staticStyle: {
        "font-size": "13px"
      }
    }, [_vm._v("\n            " + _vm._s(charge.description) + "\n          ")])]), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "d-flex justify-content-center",
      attrs: {
        "md": "2"
      }
    }, [_c('div', [_c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("FINE:")]), _vm._v(" "), charge.fine ? _c('span', [_vm._v(" $" + _vm._s(charge.fine))]) : _vm._e(), _vm._v(" "), !charge.fine ? _c('span', {
      staticClass: "text-danger"
    }, [_vm._v("JAIL")]) : _vm._e()]), _vm._v(" "), _c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("TIME:")]), _vm._v(" "), charge.months ? _c('span', [_vm._v(" " + _vm._s(charge.months) + "m")]) : _vm._e()]), _vm._v(" "), _c('div', [_c('span', {
      staticStyle: {
        "width": "auto",
        "background-color": "black"
      },
      attrs: {
        "variant": "dark"
      }
    }, [_vm._v("REST:")]), _vm._v(" "), charge.restitution ? _c('span', [_vm._v(" $" + _vm._s(charge.restitution))]) : _vm._e(), _vm._v(" "), !charge.restitution ? _c('span', {
      staticClass: "text-muted"
    }, [_vm._v("N/A")]) : _vm._e()])])]), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "d-flex justify-content-center",
      attrs: {
        "md": "1"
      }
    }, [_c(VTextField["a" /* default */], {
      attrs: {
        "autocomplete": "off",
        "label": "Counts",
        "disabled": _vm.editDisabled
      },
      on: {
        "input": _vm.inputChanged,
        "keyup": function keyup($event) {
          return _vm.$emit('charges-updated', _vm.charges);
        }
      },
      model: {
        value: charge.counts,
        callback: function callback($$v) {
          _vm.$set(charge, "counts", $$v);
        },
        expression: "charge.counts"
      }
    })], 1), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "d-flex justify-content-center",
      attrs: {
        "md": "4"
      }
    }, [_c(VTextarea["a" /* default */], {
      attrs: {
        "rows": "1",
        "auto-grow": "",
        "label": "Description/Reasoning",
        "disabled": _vm.editDisabled
      },
      on: {
        "input": _vm.inputChanged,
        "mouseleave": function mouseleave($event) {
          return _vm.$emit('charges-updated', _vm.charges);
        }
      },
      model: {
        value: charge.reasoning,
        callback: function callback($$v) {
          _vm.$set(charge, "reasoning", $$v);
        },
        expression: "charge.reasoning"
      }
    })], 1), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "d-flex justify-content-center align-items-center hover-icon",
      attrs: {
        "md": "1"
      }
    }, [!charge.selected ? _c('i', {
      staticClass: "fas fa-plus text-success font-weight-bold"
    }) : _c('i', {
      staticClass: "fas fa-times text-danger font-weight-bold",
      on: {
        "click": function click($event) {
          return _vm.selectCharge(charge.id);
        }
      }
    })])], 1) : _vm._e();
  }), 1)], 1), _vm._v(" "), _c('div', {
    staticClass: "charges-calc-area"
  }, [_c(VRow["a" /* default */], {
    staticStyle: {
      "margin-top": "25px",
      "font-size": "14px"
    },
    attrs: {
      "no-gutters": ""
    }
  }, [_c(VCol["a" /* default */], {
    attrs: {
      "md": "6"
    }
  }, [_vm._t("default")], 2), _vm._v(" "), _vm.sentence.fines > -1 ? _c(VCol["a" /* default */], [_c('div', {
    staticClass: "font-weight-bold"
  }, [_vm._v("If they want to pay fines")]), _vm._v(" "), _c('div', [_vm.sentence.fines <= 25000 ? _c('span', [_vm._v("Fines: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('b', [_vm._v("$" + _vm._s(_vm.sentence.fines.toLocaleString()))])])]) : _c('span', [_vm._v("Fines: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('strike', [_vm._v("$" + _vm._s(_vm.sentence.fines.toLocaleString()))]), _vm._v(" "), _c('b', [_vm._v("$25,000")])], 1)])]), _vm._v(" "), _c('div', [_vm.sentence.man <= 210 ? _c('span', [_vm._v("(M) Time: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('b', [_vm._v(_vm._s(_vm.sentence.man) + " months")])])]) : _c('span', [_vm._v("(M) Time: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('strike', [_vm._v(_vm._s(_vm.sentence.man) + " months")]), _vm._v(" "), _c('b', [_vm._v("210 months")])], 1)])]), _vm._v(" "), _c('div', [_vm.sentence.fineres <= 15000 ? _c('span', [_vm._v("\n            Restitution: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('b', [_vm._v("$" + _vm._s(_vm.sentence.fineres.toLocaleString()))])])]) : _c('span', [_vm._v("Restitution: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('strike', [_vm._v("$" + _vm._s(_vm.sentence.fineres.toLocaleString()))]), _vm._v(" "), _c('b', [_vm._v("$15,000")])], 1)])])]) : _vm._e(), _vm._v(" "), _vm.sentence.fines > -1 ? _c(VCol["a" /* default */], [_c('div', {
    staticClass: "font-weight-bold"
  }, [_vm._v("\n          " + _vm._s(_vm.sentence.fines ? "Don't want to pay fines" : "Mandatory Sentence") + "\n        ")]), _vm._v(" "), _c('div', [_vm.sentence.time <= 210 ? _c('span', [_vm._v("Time: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('b', [_vm._v(_vm._s(_vm.sentence.time) + " months")])])]) : _c('span', [_vm._v("Time: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('strike', [_vm._v(_vm._s(_vm.sentence.time) + " months")]), _vm._v(" "), _c('b', [_vm._v("210 months")])], 1)])]), _vm._v(" "), _c('div', [_vm.sentence.res <= 15000 ? _c('span', [_vm._v("Restitution: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('b', [_vm._v("$" + _vm._s(_vm.sentence.res.toLocaleString()))])])]) : _c('span', [_vm._v("Restitution: "), _c('span', {
    staticClass: "text-primary"
  }, [_c('strike', [_vm._v("$" + _vm._s(_vm.sentence.res.toLocaleString()))]), _vm._v(" "), _c('b', [_vm._v("$15,000")])], 1)])])]) : _vm._e()], 1)], 1)], 1) : _vm._e();
};
var submit_record_chargesvue_type_template_id_6448ac3a_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/submit-record-charges.vue?vue&type=template&id=6448ac3a

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/submit-record-charges.vue?vue&type=script&lang=js




















function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _wrapRegExp() { _wrapRegExp = function _wrapRegExp(e, r) { return new BabelRegExp(e, void 0, r); }; var e = RegExp.prototype, r = new WeakMap(); function BabelRegExp(e, t, p) { var o = RegExp(e, t); return r.set(o, p || r.get(e)), Object(setPrototypeOf["a" /* default */])(o, BabelRegExp.prototype); } function buildGroups(e, t) { var p = r.get(t); return Object.keys(p).reduce(function (r, t) { var o = p[t]; if ("number" == typeof o) r[t] = e[o];else { for (var i = 0; void 0 === e[o[i]] && i + 1 < o.length;) i++; r[t] = e[o[i]]; } return r; }, Object.create(null)); } return Object(inherits["a" /* default */])(BabelRegExp, RegExp), BabelRegExp.prototype.exec = function (r) { var t = e.exec.call(this, r); if (t) { t.groups = buildGroups(t, this); var p = t.indices; p && (p.groups = buildGroups(p, this)); } return t; }, BabelRegExp.prototype[Symbol.replace] = function (t, p) { if ("string" == typeof p) { var o = r.get(this); return e[Symbol.replace].call(this, t, p.replace(/\$<([^>]+)>/g, function (e, r) { var t = o[r]; return "$" + (Array.isArray(t) ? t.join("$") : t); })); } if ("function" == typeof p) { var i = this; return e[Symbol.replace].call(this, t, function () { var e = arguments; return "object" != Object(esm_typeof["a" /* default */])(e[e.length - 1]) && (e = [].slice.call(e)).push(buildGroups(e, i)), p.apply(this, e); }); } return e[Symbol.replace].call(this, t, p); }, _wrapRegExp.apply(this, arguments); }













function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }



/* harmony default export */ var submit_record_chargesvue_type_script_lang_js = ({
  name: 'SubmitRecordCharges',
  props: ['faction', 'incidentPerson', 'refer', 'editDisabled', 'personForm'],
  data: function data() {
    return {
      addingNew: false,
      query: null,
      showing: false,
      hasChanged: false,
      sentence: {},
      chargeText: ''
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _this.$store.commit('system/RESET_CHARGES');
            _context.next = 3;
            return _this.$store.dispatch('system/syncCharges');
          case 3:
            _context.next = 5;
            return _this.index();
          case 5:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  methods: {
    index: function index() {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        var incidentPerson, _iterator, _step, _loop, _iterator2, _step2, charge, _iterator3, _step3, _loop2;
        return regeneratorRuntime.wrap(function _callee2$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              _this2.$nextTick(function () {
                _this2.compileSentence();
              });
              _context4.next = 3;
              return _this2.$axios.$get("/police/incidents/view-person/".concat(_this2.incidentPerson.id));
            case 3:
              incidentPerson = _context4.sent;
              _iterator = _createForOfIteratorHelper(incidentPerson.charges);
              _context4.prev = 5;
              _loop = /*#__PURE__*/regeneratorRuntime.mark(function _loop() {
                var charge, localCharge;
                return regeneratorRuntime.wrap(function _loop$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      charge = _step.value;
                      localCharge = _this2.charges.find(function (c) {
                        return c.id === charge.police_charge_id;
                      });
                      localCharge.selected = false;
                    case 3:
                    case "end":
                      return _context2.stop();
                  }
                }, _loop);
              });
              _iterator.s();
            case 8:
              if ((_step = _iterator.n()).done) {
                _context4.next = 12;
                break;
              }
              return _context4.delegateYield(_loop(), "t0", 10);
            case 10:
              _context4.next = 8;
              break;
            case 12:
              _context4.next = 17;
              break;
            case 14:
              _context4.prev = 14;
              _context4.t1 = _context4["catch"](5);
              _iterator.e(_context4.t1);
            case 17:
              _context4.prev = 17;
              _iterator.f();
              return _context4.finish(17);
            case 20:
              _iterator2 = _createForOfIteratorHelper(_this2.charges);
              try {
                for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
                  charge = _step2.value;
                  charge.selected = false;
                }
              } catch (err) {
                _iterator2.e(err);
              } finally {
                _iterator2.f();
              }
              _iterator3 = _createForOfIteratorHelper(incidentPerson.charges);
              _context4.prev = 23;
              _loop2 = /*#__PURE__*/regeneratorRuntime.mark(function _loop2() {
                var _incidentPersonCharge;
                var incidentPersonCharge, charge;
                return regeneratorRuntime.wrap(function _loop2$(_context3) {
                  while (1) switch (_context3.prev = _context3.next) {
                    case 0:
                      incidentPersonCharge = _step3.value;
                      charge = _this2.charges.find(function (c) {
                        return c.id === incidentPersonCharge.police_charge_id;
                      });
                      charge.selected = true;
                      charge.counts = incidentPersonCharge.counts;
                      charge.reasoning = (_incidentPersonCharge = incidentPersonCharge.content) !== null && _incidentPersonCharge !== void 0 ? _incidentPersonCharge : 'N/A';
                    case 5:
                    case "end":
                      return _context3.stop();
                  }
                }, _loop2);
              });
              _iterator3.s();
            case 26:
              if ((_step3 = _iterator3.n()).done) {
                _context4.next = 30;
                break;
              }
              return _context4.delegateYield(_loop2(), "t2", 28);
            case 28:
              _context4.next = 26;
              break;
            case 30:
              _context4.next = 35;
              break;
            case 32:
              _context4.prev = 32;
              _context4.t3 = _context4["catch"](23);
              _iterator3.e(_context4.t3);
            case 35:
              _context4.prev = 35;
              _iterator3.f();
              return _context4.finish(35);
            case 38:
              _this2.compileSentence();
              _this2.$forceUpdate();
            case 40:
            case "end":
              return _context4.stop();
          }
        }, _callee2, null, [[5, 14, 17, 20], [23, 32, 35, 38]]);
      }))();
    },
    save: function save() {
      var _this3 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        var data, _iterator4, _step4, charge, _charge$counts, _charge$reasoning;
        return regeneratorRuntime.wrap(function _callee3$(_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              data = [];
              _this3.hasChanged = false;
              _iterator4 = _createForOfIteratorHelper(_this3.charges);
              try {
                for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {
                  charge = _step4.value;
                  if (charge.selected) {
                    data.push({
                      police_charge_id: charge.id,
                      counts: (_charge$counts = charge.counts) !== null && _charge$counts !== void 0 ? _charge$counts : 1,
                      content: (_charge$reasoning = charge.reasoning) !== null && _charge$reasoning !== void 0 ? _charge$reasoning : ''
                    });
                  }
                }
              } catch (err) {
                _iterator4.e(err);
              } finally {
                _iterator4.f();
              }
              _context5.next = 6;
              return _this3.$axios.$post("/police/incidents/update-person-charges/".concat(_this3.incidentPerson.id), {
                charges: data
              });
            case 6:
            case "end":
              return _context5.stop();
          }
        }, _callee3);
      }))();
    },
    replaceWithPerson: function replaceWithPerson(incidentPersonId) {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee4() {
        return regeneratorRuntime.wrap(function _callee4$(_context6) {
          while (1) switch (_context6.prev = _context6.next) {
            case 0:
              _context6.next = 2;
              return _this4.$axios.$post("/police/incidents/sync-person-charges/".concat(_this4.incidentPerson.id, "/").concat(incidentPersonId)).then(function () {
                _this4.index();
              });
            case 2:
            case "end":
              return _context6.stop();
          }
        }, _callee4);
      }))();
    },
    printCharges: function printCharges() {
      var _this$personForm$time, _this$personForm$fine, _this$personForm$res;
      var text = "";
      text += "[center] ".concat(this.faction.logo, " [/center]\n");
      text += "[center] [f=32] [c=".concat(this.faction.color, "] **").concat(this.faction.name, "** [/c] [/f] [/center] \n");
      text += "###  [c=gray] Charges overview for **".concat(this.incidentPerson.person.name.toUpperCase(), "**  [/c] \n");
      text += "#### **".concat(this.refer, "** \n");
      text += "\n";
      text += "\n";
      text += '| NAME | COUNT | REASON |\n';
      text += '| ------ | ------ | ----------- |\n';
      var _iterator5 = _createForOfIteratorHelper(this.charges.filter(function (c) {
          return c.selected;
        })),
        _step5;
      try {
        for (_iterator5.s(); !(_step5 = _iterator5.n()).done;) {
          var charge = _step5.value;
          var chargesCount = 1;
          if (charge.count > 0) chargesCount = charge.count;
          text += "| ".concat(charge.name, " | ").concat(chargesCount, " | ").concat(charge.reasoning ? charge.reasoning : 'N/A', " |\n");
        }
      } catch (err) {
        _iterator5.e(err);
      } finally {
        _iterator5.f();
      }
      text += "##### If citizen would like to **pay fines**\n";
      text += "* **Fines:** ".concat(this.sentence.fines <= 25000 ? "$".concat(this.sentence.fines.toLocaleString()) : "$25,000", "\n\n");
      text += "* **(Mandatory)** Time: ".concat(this.sentence.man <= 210 ? "".concat(this.sentence.man, " months") : "210 months", "\n\n");
      text += "* **Restitution:** ".concat(this.sentence.fineres <= 15000 ? "$".concat(this.sentence.fineres) : "$15,000", "\n\n");
      text += "##### If citizen **does not** want to **pay fines**\n";
      text += "* **(Mandatory)** Time: ".concat(this.sentence.time <= 210 ? "".concat(this.sentence.time, " months") : "210 months", "\n\n");
      text += "* **Restitution:** ".concat(this.sentence.res <= 15000 ? "$".concat(this.sentence.res) : "$15,000", "\n\n");

      // sentence.fineres <= 15000
      text += "##### Final Amounts (Filled out by LEO)\n";
      text += "* **Time**: ".concat((_this$personForm$time = this.personForm.time) !== null && _this$personForm$time !== void 0 ? _this$personForm$time : 0, "\n\n");
      text += "* **Fines**: $".concat((_this$personForm$fine = this.personForm.fines) !== null && _this$personForm$fine !== void 0 ? _this$personForm$fine : 0, "\n\n");
      text += "* **Restitution**: $".concat((_this$personForm$res = this.personForm.res) !== null && _this$personForm$res !== void 0 ? _this$personForm$res : 0, "\n\n");
      text += "\n";
      text += "\n";
      this.$store.dispatch('nui/smartPin', {
        $axios: this.$axios,
        model: 'PoliceIncidentPerson',
        id: this.incidentPerson.id,
        preAppend: text,
        type: 'default'
      });
    },
    computeChargesInternal: function computeChargesInternal(data) {
      var selectedCharges = data.split(" <br>");
      var _iterator6 = _createForOfIteratorHelper(this.charges),
        _step6;
      try {
        for (_iterator6.s(); !(_step6 = _iterator6.n()).done;) {
          var charge = _step6.value;
          var searchString = "(".concat(charge.type.slice(0, 3), ") ").concat(charge.name);
          var _iterator7 = _createForOfIteratorHelper(selectedCharges),
            _step7;
          try {
            for (_iterator7.s(); !(_step7 = _iterator7.n()).done;) {
              var selectedCharge = _step7.value;
              if (selectedCharge.includes(searchString)) {
                var counts = 1;
                var match = selectedCharge.match(/*#__PURE__*/_wrapRegExp(/\((\d+)\)/, {
                  count: 1
                }));
                if (match && match.groups) {
                  counts = match.groups.count;
                }
                this.selectCharge(charge.id, counts);
              }
            }
          } catch (err) {
            _iterator7.e(err);
          } finally {
            _iterator7.f();
          }
        }
      } catch (err) {
        _iterator6.e(err);
      } finally {
        _iterator6.f();
      }
    },
    selectCharge: function selectCharge(id) {
      var counts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
      this.inputChanged();
      this.query = null;
      this.$refs.refQuery.$el.focus();
      var index = this.charges.findIndex(function (c) {
        return c.id === id;
      });
      vue_runtime_esm["default"].set(this.charges[index], 'selected', !this.charges[index]['selected']);
      vue_runtime_esm["default"].set(this.charges[index], 'counts', counts !== null && counts !== void 0 ? counts : 1);
      this.$emit('charges-updated', this.charges);
    },
    inputChanged: function inputChanged() {
      var _this5 = this;
      this.hasChanged = true;
      this.$nextTick(function () {
        _this5.compileSentence();
      });
    },
    compileSentence: function compileSentence() {
      var hasCharges = this.charges.filter(function (c) {
        return c.selected;
      }).length > 0;
      this.$emit('hasChargesFilled', hasCharges);
      if (!this.charges) return [];
      this.stackIssues = [];
      var data = {
        fines: 0,
        time: 0,
        res: 0,
        man: 0,
        nonman: 0,
        felfines: 0,
        fineres: 0,
        charges: []
      };
      var _iterator8 = _createForOfIteratorHelper(this.charges),
        _step8;
      try {
        for (_iterator8.s(); !(_step8 = _iterator8.n()).done;) {
          var charge = _step8.value;
          if (charge.selected) {
            var counts = parseInt(charge.counts);
            if (!counts || isNaN(counts)) {
              counts = 1;
            }
            charge.vName = "(".concat(charge.type.slice(0, 3), ") ").concat(charge.name);
            data.charges.push(charge);
            if (charge.fine) data.fines += parseInt(charge.fine * counts);
            if (charge.months) data.time += parseInt(charge.months * counts);
            if (charge.restitution) data.res += parseInt(charge.restitution * counts);
            if (charge.restitution && !charge.fine) data.fineres += parseInt(charge.restitution * counts); // add to "if they want to pay fines" if the charge has no fine and mandatory rest
            if (!charge.fine && charge.months) data.man += parseInt(charge.months * counts);
            if (charge.fine) data.nonman += parseInt(charge.months * counts);
            if (charge.type === 'Felonies') data.felfines += parseInt(charge.fine * counts);
          }
        }
      } catch (err) {
        _iterator8.e(err);
      } finally {
        _iterator8.f();
      }
      this.sentence = data;
    }
  },
  watch: {
    query: function query() {
      this.showing = false;
      if (this.query && this.query.length < 3) return false;
      this.showing = true;
    }
  },
  computed: _objectSpread({
    filteredCharges: function filteredCharges() {
      var _this6 = this;
      if (!this.query || !this.charges || this.query.length < 1) return [];
      return this.charges.filter(function (c) {
        return c.name.toLowerCase().includes(_this6.query.toLowerCase());
      });
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    charges: 'system/charges'
  }))
});
// CONCATENATED MODULE: ./components/Pages/Police/submit-record-charges.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_submit_record_chargesvue_type_script_lang_js = (submit_record_chargesvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Police/submit-record-charges.vue?vue&type=style&index=0&id=6448ac3a&prod&lang=scss
var submit_record_chargesvue_type_style_index_0_id_6448ac3a_prod_lang_scss = __webpack_require__(1812);

// CONCATENATED MODULE: ./components/Pages/Police/submit-record-charges.vue






/* normalize component */

var submit_record_charges_component = Object(componentNormalizer["a" /* default */])(
  Police_submit_record_chargesvue_type_script_lang_js,
  submit_record_chargesvue_type_template_id_6448ac3a_render,
  submit_record_chargesvue_type_template_id_6448ac3a_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var submit_record_charges = (submit_record_charges_component.exports);
// EXTERNAL MODULE: ./components/Common/app-person-icon.vue + 4 modules
var app_person_icon = __webpack_require__(1673);

// EXTERNAL MODULE: ./components/Common/app-avatar.vue + 4 modules
var app_avatar = __webpack_require__(189);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// EXTERNAL MODULE: ./components/Common/app-avatar-stack.vue + 4 modules
var app_avatar_stack = __webpack_require__(520);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-incident-person-item.vue?vue&type=script&lang=js





function police_incident_person_itemvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function police_incident_person_itemvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? police_incident_person_itemvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : police_incident_person_itemvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function police_incident_person_itemvue_type_script_lang_js_wrapRegExp() { police_incident_person_itemvue_type_script_lang_js_wrapRegExp = function _wrapRegExp(e, r) { return new BabelRegExp(e, void 0, r); }; var e = RegExp.prototype, r = new WeakMap(); function BabelRegExp(e, t, p) { var o = RegExp(e, t); return r.set(o, p || r.get(e)), Object(setPrototypeOf["a" /* default */])(o, BabelRegExp.prototype); } function buildGroups(e, t) { var p = r.get(t); return Object.keys(p).reduce(function (r, t) { var o = p[t]; if ("number" == typeof o) r[t] = e[o];else { for (var i = 0; void 0 === e[o[i]] && i + 1 < o.length;) i++; r[t] = e[o[i]]; } return r; }, Object.create(null)); } return Object(inherits["a" /* default */])(BabelRegExp, RegExp), BabelRegExp.prototype.exec = function (r) { var t = e.exec.call(this, r); if (t) { t.groups = buildGroups(t, this); var p = t.indices; p && (p.groups = buildGroups(p, this)); } return t; }, BabelRegExp.prototype[Symbol.replace] = function (t, p) { if ("string" == typeof p) { var o = r.get(this); return e[Symbol.replace].call(this, t, p.replace(/\$<([^>]+)>/g, function (e, r) { var t = o[r]; return "$" + (Array.isArray(t) ? t.join("$") : t); })); } if ("function" == typeof p) { var i = this; return e[Symbol.replace].call(this, t, function () { var e = arguments; return "object" != Object(esm_typeof["a" /* default */])(e[e.length - 1]) && (e = [].slice.call(e)).push(buildGroups(e, i)), p.apply(this, e); }); } return e[Symbol.replace].call(this, t, p); }, police_incident_person_itemvue_type_script_lang_js_wrapRegExp.apply(this, arguments); }
function police_incident_person_itemvue_type_script_lang_js_createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = police_incident_person_itemvue_type_script_lang_js_unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function police_incident_person_itemvue_type_script_lang_js_unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return police_incident_person_itemvue_type_script_lang_js_arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? police_incident_person_itemvue_type_script_lang_js_arrayLikeToArray(r, a) : void 0; } }
function police_incident_person_itemvue_type_script_lang_js_arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }










































/* harmony default export */ var police_incident_person_itemvue_type_script_lang_js = ({
  name: "police-incident-person-item",
  components: {
    AppAvatarStack: app_avatar_stack["a" /* default */],
    AppTimestamp: app_timestamp["a" /* default */],
    AppAvatar: app_avatar["a" /* default */],
    AppFormGroup: app_form_group["a" /* default */],
    AppPersonIcon: app_person_icon["a" /* default */],
    SubmitRecordCharges: submit_record_charges,
    PolicePersonEvidence: police_person_evidence,
    NoteContainer: note_container["a" /* default */]
  },
  props: ['incidentPerson', 'incident', 'faction', 'refer', 'visible', 'viewers'],
  data: function data() {
    return {
      chargesShowing: true,
      hasSaved: true,
      query: null,
      form: {
        time: 0,
        res: 0,
        fine: 0,
        charges: {}
      },
      selected: false,
      hasChanged: false,
      hasChargesFilled: false,
      isReady: true,
      evidenceItems: [],
      chargeText: '',
      waitingAction: false,
      sentence: {},
      useNewCharges: false,
      isExpanded: false,
      isVisible: false,
      intersectionObserver: null,
      scrollTimeout: null,
      debounceTimeouts: {
        time: null,
        res: null,
        fine: null
      }
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      var scrollContainer;
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            window.$events.$on('items:listItems', function (data) {
              _this.evidenceItems = data.items;
            });

            // Listen for crime attribute updates
            window.$events.$on('crimeAttributeUpdated', _this.handleCrimeAttributeUpdate);

            // Listen for person totals updates
            window.$events.$on('personTotalsUpdated', _this.handlePersonTotalsUpdate);
            _context.next = 5;
            return _this.$store.dispatch('system/syncCharges');
          case 5:
            _context.next = 7;
            return _this.$store.dispatch('system/syncCrimes');
          case 7:
            _context.next = 9;
            return _this.index();
          case 9:
            setInterval(function () {
              _this.compileSentence();
            }, 500);

            // Set up intersection observer for visibility detection
            _this.setupVisibilityObserver();

            // Add scroll listener for custom div scrolling
            scrollContainer = document.getElementById('scroll-container');
            if (scrollContainer) {
              scrollContainer.addEventListener('scroll', _this.handleScroll);
            }
            // Also listen to window scroll as fallback
            window.addEventListener('scroll', _this.handleScroll);
          case 14:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  beforeDestroy: function beforeDestroy() {
    window.$events.$off('items:listItems');
    window.$events.$off('crimeAttributeUpdated', this.handleCrimeAttributeUpdate);
    window.$events.$off('personTotalsUpdated', this.handlePersonTotalsUpdate);

    // Clean up intersection observer
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }

    // Clean up scroll timeout
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }

    // Clean up debounce timeouts
    Object.values(this.debounceTimeouts).forEach(function (timeout) {
      if (timeout) {
        clearTimeout(timeout);
      }
    });

    // Remove scroll listeners
    var scrollContainer = document.getElementById('scroll-container');
    if (scrollContainer) {
      scrollContainer.removeEventListener('scroll', this.handleScroll);
    }
    window.removeEventListener('scroll', this.handleScroll);
  },
  updated: function updated() {
    // console.log('updated')
  },
  methods: {
    handleExpansionToggle: function handleExpansionToggle() {
      // Toggle expansion state
      this.isExpanded = !this.isExpanded;
      // Update visibility when expansion changes
      this.updateVisibility();
    },
    setupVisibilityObserver: function setupVisibilityObserver() {
      var _this2 = this;
      // Set up intersection observer to detect when the expansion panel content is visible
      this.intersectionObserver = new IntersectionObserver(function (entries) {
        entries.forEach(function (entry) {
          if (entry.target === _this2.$el) {
            // Element is visible and expanded
            _this2.updateVisibility();
          }
        });
      }, {
        threshold: 0.1,
        // Trigger when at least 10% of the element is visible
        rootMargin: '50px' // Add some margin to trigger slightly before/after
      });

      // Start observing this component's root element
      if (this.$el) {
        this.intersectionObserver.observe(this.$el);
      }
    },
    updateVisibility: function updateVisibility() {
      // Only consider visible if both expanded and actually visible on screen
      var wasVisible = this.isVisible;
      if (!this.isExpanded) {
        this.isVisible = false;
      } else {
        var _this$$el;
        // Check if the expansion panel content is visible
        var expansionContent = (_this$$el = this.$el) === null || _this$$el === void 0 ? void 0 : _this$$el.querySelector('.v-expansion-panel-content');
        if (expansionContent) {
          var rect = expansionContent.getBoundingClientRect();
          var isInViewport = rect.top < window.innerHeight && rect.bottom > 0;
          this.isVisible = isInViewport;
        } else {
          this.isVisible = false;
        }
      }

      // Emit visibility change events if visibility changed
      if (wasVisible !== this.isVisible) {
        if (this.isVisible) {
          this.$emit('person-became-visible', this.incidentPerson.id);
        } else {
          this.$emit('person-became-hidden', this.incidentPerson.id);
        }
      }
    },
    handleScroll: function handleScroll() {
      var _this3 = this;
      // Throttle scroll events for performance
      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout);
      }
      this.scrollTimeout = setTimeout(function () {
        _this3.updateVisibility();
      }, 100);
    },
    chargesUpdated: function chargesUpdated() {
      this.compileSentence();
      this.updatePerson();
    },
    syncCharges: function syncCharges(incidentPerson) {
      this.replaceWithPerson(incidentPerson.id);
      this.$forceUpdate();
    },
    syncWarrantCharges: function syncWarrantCharges() {
      this.$axios.$post("/police/incidents/sync-person-warrants", {
        police_incident_person_id: this.incidentPerson.id
      });
    },
    clearCharges: function clearCharges() {
      this.$axios.$post("/police/incidents/clear-charges", {
        police_incident_person_id: this.incidentPerson.id
      });
    },
    // Charge selector
    index: function index() {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              _this4.compileSentence();
              _this4.$forceUpdate();
            case 2:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    },
    replaceWithPerson: function replaceWithPerson(incidentPersonId) {
      var _this5 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _context3.next = 2;
              return _this5.$axios.$post("/police/incidents/sync-person-charges/".concat(_this5.incidentPerson.id, "/").concat(incidentPersonId)).then(function () {
                _this5.index();
              });
            case 2:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }))();
    },
    openPersonLocker: function openPersonLocker() {
      fetch("https://blrp_tablet/openLocker", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify({
          character_number: this.incidentPerson.person.character_number
        })
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {
        return resp;
      });
    },
    printCharges: function printCharges() {
      var _this$incidentPerson$, _this$incidentPerson$2, _this$incidentPerson$3;
      var text = "";
      text += "[center] ".concat(this.faction.logo, " [/center]\n");
      text += "[center] [f=32] [c=".concat(this.faction.color, "] **").concat(this.faction.name, "** [/c] [/f] [/center] \n");
      text += "###  [c=gray] Charges overview for **".concat(this.incidentPerson.person.name.toUpperCase(), "**  [/c] \n");
      text += "#### **".concat(this.refer, "** \n");
      text += "\n";
      text += "\n";
      text += '| NAME | COUNT | REASON |\n';
      text += '| ------ | ------ | ----------- |\n';
      var _iterator = police_incident_person_itemvue_type_script_lang_js_createForOfIteratorHelper(this.computedCrimes),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var charge = _step.value;
          var chargesCount = 1;
          if (charge.count > 0) chargesCount = charge.count;
          text += "| ".concat(charge.crime_name, " |\n");
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      text += "##### If citizen would like to **pay fines**\n";
      text += "* **Fines:**  $".concat(this.incidentPerson['totals']['paying-fines']['final_ticket'], "\n\n");
      text += "* **Time:** ".concat(this.incidentPerson['totals']['paying-fines']['final_prison'], " months\n\n");
      text += "* **Restitution:** $".concat(this.incidentPerson['totals']['paying-fines']['final_restitution'], "\n\n");
      text += "* **Parole:** ".concat(this.incidentPerson['totals']['paying-fines']['final_parole'], "m\n\n");
      text += "##### If citizen **does not** want to **pay fines**\n";
      text += "* **Fines:**  $".concat(this.incidentPerson['totals']['not-paying-fines']['final_ticket'], "\n\n");
      text += "* **Time:** ".concat(this.incidentPerson['totals']['not-paying-fines']['final_prison'], " months\n\n");
      text += "* **Restitution:** $".concat(this.incidentPerson['totals']['not-paying-fines']['final_restitution'], "\n\n");
      text += "* **Parole:** ".concat(this.incidentPerson['totals']['not-paying-fines']['final_parole'], "m\n\n");
      text += "##### Final Amounts (Filled out by LEO)\n";
      text += "* **Time**: ".concat((_this$incidentPerson$ = this.incidentPerson.time) !== null && _this$incidentPerson$ !== void 0 ? _this$incidentPerson$ : 'Not filled out yet', "\n\n");
      text += "* **Fines**: $".concat((_this$incidentPerson$2 = this.incidentPerson.fines) !== null && _this$incidentPerson$2 !== void 0 ? _this$incidentPerson$2 : 'Not filled out yet', "\n\n");
      text += "* **Restitution**: $".concat((_this$incidentPerson$3 = this.incidentPerson.res) !== null && _this$incidentPerson$3 !== void 0 ? _this$incidentPerson$3 : 'Not filled out yet', "\n\n");
      text += "\n";
      text += "\n";
      this.$store.dispatch('nui/smartPin', {
        $axios: this.$axios,
        model: 'PoliceIncidentPerson',
        id: this.incidentPerson.id,
        preAppend: text,
        type: 'default'
      });
    },
    computeChargesInternal: function computeChargesInternal(data) {
      var selectedCharges = data.split(" <br>");
      var _iterator2 = police_incident_person_itemvue_type_script_lang_js_createForOfIteratorHelper(this.charges),
        _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          var charge = _step2.value;
          var searchString = "(".concat(charge.type.slice(0, 3), ") ").concat(charge.name);
          var _iterator3 = police_incident_person_itemvue_type_script_lang_js_createForOfIteratorHelper(selectedCharges),
            _step3;
          try {
            for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
              var selectedCharge = _step3.value;
              if (selectedCharge.includes(searchString)) {
                var counts = 1;
                var match = selectedCharge.match(/*#__PURE__*/police_incident_person_itemvue_type_script_lang_js_wrapRegExp(/\((\d+)\)/, {
                  count: 1
                }));
                if (match && match.groups) {
                  counts = match.groups.count;
                }
                this.selectCharge(charge.id, counts);
              }
            }
          } catch (err) {
            _iterator3.e(err);
          } finally {
            _iterator3.f();
          }
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
    },
    selectCharge: function selectCharge(police_charge_id) {
      var _arguments = arguments,
        _this6 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee4() {
        var counts;
        return regeneratorRuntime.wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              counts = _arguments.length > 1 && _arguments[1] !== undefined ? _arguments[1] : null;
              _this6.query = null;
              _this6.$refs.refQuery.$el.focus();
              _context4.next = 5;
              return _this6.$axios.$post("/police/incidents/add-person-charge/".concat(_this6.incidentPerson.id), {
                police_charge_id: police_charge_id,
                police_incident_person_id: _this6.incidentPerson.id
              });
            case 5:
              window.$events.$emit('reload');
            case 6:
            case "end":
              return _context4.stop();
          }
        }, _callee4);
      }))();
    },
    selectCrime: function selectCrime(police_charge_id) {
      var _arguments2 = arguments,
        _this7 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee5() {
        var counts;
        return regeneratorRuntime.wrap(function _callee5$(_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              counts = _arguments2.length > 1 && _arguments2[1] !== undefined ? _arguments2[1] : null;
              _this7.query = null;
              _this7.$refs.refQuery.$el.focus();
              _context5.next = 5;
              return _this7.$axios.$post("/police/incidents/add-person-crime/".concat(_this7.incidentPerson.id), {
                police_crime_id: police_charge_id,
                police_incident_person_id: _this7.incidentPerson.id
              });
            case 5:
              window.$events.$emit('reload');
            case 6:
            case "end":
              return _context5.stop();
          }
        }, _callee5);
      }))();
    },
    removeCharge: function removeCharge(police_charge_id) {
      var _this8 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee6() {
        return regeneratorRuntime.wrap(function _callee6$(_context6) {
          while (1) switch (_context6.prev = _context6.next) {
            case 0:
              _context6.next = 2;
              return _this8.$axios.$post("/police/incidents/remove-person-charge/".concat(_this8.incidentPerson.id), {
                police_charge_id: police_charge_id,
                police_incident_person_id: _this8.incidentPerson.id
              });
            case 2:
              window.$events.$emit('reload');
              _this8.compileSentence();
            case 4:
            case "end":
              return _context6.stop();
          }
        }, _callee6);
      }))();
    },
    removeCrime: function removeCrime(police_incident_crime_id) {
      var _this9 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee7() {
        return regeneratorRuntime.wrap(function _callee7$(_context7) {
          while (1) switch (_context7.prev = _context7.next) {
            case 0:
              _context7.next = 2;
              return _this9.$axios.$post("/police/incidents/remove-person-crime", {
                police_incident_person_id: _this9.incidentPerson.id,
                police_incident_crime_id: police_incident_crime_id
              });
            case 2:
              window.$events.$emit('reload');
              _this9.compileSentence();
            case 4:
            case "end":
              return _context7.stop();
          }
        }, _callee7);
      }))();
    },
    handleCrimeAttributesChange: function handleCrimeAttributesChange(police_incident_crime_id, field, value) {
      var _this10 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee8() {
        return regeneratorRuntime.wrap(function _callee8$(_context8) {
          while (1) switch (_context8.prev = _context8.next) {
            case 0:
              _context8.next = 2;
              return _this10.$axios.$post("/police/incidents/change-person-crime/".concat(police_incident_crime_id), {
                police_incident_crime_id: police_incident_crime_id,
                field: field,
                value: value
              });
            case 2:
              // Rely on socket event to update both the field and crime_name
              _this10.compileSentence();
            case 3:
            case "end":
              return _context8.stop();
          }
        }, _callee8);
      }))();
    },
    handleCrimeAttributeUpdate: function handleCrimeAttributeUpdate(payload) {
      var crimeId = payload.crimeId,
        incidentPersonId = payload.incidentPersonId,
        field = payload.field,
        value = payload.value,
        crimeName = payload.crimeName,
        totals = payload.totals;

      // Check if this update is for this person
      if (incidentPersonId === this.incidentPerson.id) {
        // Find the crime index in this person's crimes
        var crimeIndex = this.incidentPerson.crimes.findIndex(function (c) {
          return c.id === crimeId;
        });
        if (crimeIndex !== -1) {
          // Create a new crime object with updated values to ensure reactivity
          var updatedCrime = police_incident_person_itemvue_type_script_lang_js_objectSpread(police_incident_person_itemvue_type_script_lang_js_objectSpread({}, this.incidentPerson.crimes[crimeIndex]), {}, Object(defineProperty["a" /* default */])(Object(defineProperty["a" /* default */])({}, field, value), "crime_name", crimeName));

          // Replace the entire crime object to trigger reactivity
          this.$set(this.incidentPerson.crimes, crimeIndex, updatedCrime);

          // Update the person's totals
          this.$set(this.incidentPerson, 'totals', totals);
        }
      }
    },
    inputChanged: function inputChanged() {
      this.hasChanged = true;
    },
    timeChanged: function timeChanged() {
      this.debouncedTotalsUpdate('time', this.incidentPerson.time);
    },
    resChanged: function resChanged() {
      this.debouncedTotalsUpdate('res', this.incidentPerson.res);
    },
    fineChanged: function fineChanged() {
      this.debouncedTotalsUpdate('fine', this.incidentPerson.fine);
    },
    debouncedTotalsUpdate: function debouncedTotalsUpdate(field, value) {
      var _this11 = this;
      // Clear existing timeout for this field
      if (this.debounceTimeouts[field]) {
        clearTimeout(this.debounceTimeouts[field]);
      }

      // Set new timeout
      this.debounceTimeouts[field] = setTimeout(function () {
        _this11.broadcastTotalsUpdate(field, value);
        _this11.debounceTimeouts[field] = null;
      }, 500); // 500ms debounce
    },
    broadcastTotalsUpdate: function broadcastTotalsUpdate(field, value) {
      var _this12 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee9() {
        return regeneratorRuntime.wrap(function _callee9$(_context9) {
          while (1) switch (_context9.prev = _context9.next) {
            case 0:
              _context9.prev = 0;
              _context9.next = 3;
              return _this12.$axios.$post("/police/incidents/broadcast-totals-update/".concat(_this12.incident.id), {
                incident_person_id: _this12.incidentPerson.id,
                field: field,
                value: value
              });
            case 3:
              _context9.next = 8;
              break;
            case 5:
              _context9.prev = 5;
              _context9.t0 = _context9["catch"](0);
              console.error('Failed to broadcast totals update:', _context9.t0);
            case 8:
            case "end":
              return _context9.stop();
          }
        }, _callee9, null, [[0, 5]]);
      }))();
    },
    handlePersonTotalsUpdate: function handlePersonTotalsUpdate(payload) {
      var incidentPersonId = payload.incidentPersonId,
        field = payload.field,
        value = payload.value,
        fromPersonId = payload.fromPersonId;

      // Only update if this is for this person and not from the current user
      if (incidentPersonId === this.incidentPerson.id && fromPersonId !== this.user.id) {
        // Update the local value to match what was broadcasted
        this.$set(this.incidentPerson, field, value);
      }
    },
    notesChanged: function notesChanged() {
      this.hasChanged = true;
    },
    compileSentence: function compileSentence() {
      return {};
    },
    // Actions
    doWarning: function doWarning() {
      return this.$axios.$post("/police/incidents/issue-fine/".concat(this.incidentPerson.id), {
        fine_amount: 0,
        prison_amount: 0,
        res_amount: 0,
        action: 'warned'
      });
    },
    doFine: function doFine() {
      var _this$incidentPerson$4;
      return this.handleAction({
        fine_amount: (_this$incidentPerson$4 = this.incidentPerson.fine) !== null && _this$incidentPerson$4 !== void 0 ? _this$incidentPerson$4 : 0,
        prison_amount: 0,
        res_amount: 0,
        action: 'fined',
        event: 'sendFine'
      });
    },
    doJail: function doJail() {
      var _this$incidentPerson$5,
        _this$incidentPerson$6,
        _this$incidentPerson$7,
        _this$incidentPerson$8,
        _this$incidentPerson$9,
        _this$incidentPerson$10,
        _this13 = this;
      // console.log('this.form.res', this.form.res)
      return this.handleAction({
        fine_amount: (_this$incidentPerson$5 = this.incidentPerson.fine) !== null && _this$incidentPerson$5 !== void 0 ? _this$incidentPerson$5 : 0,
        prison_amount: (_this$incidentPerson$6 = this.incidentPerson.time) !== null && _this$incidentPerson$6 !== void 0 ? _this$incidentPerson$6 : 0,
        res_amount: (_this$incidentPerson$7 = this.incidentPerson.res) !== null && _this$incidentPerson$7 !== void 0 ? _this$incidentPerson$7 : 0,
        action: 'imprisoned',
        event: 'sendPrison'
      });
      return this.$axios.$post("/police/incidents/issue-fine/".concat(this.incidentPerson.id), {
        fine_amount: (_this$incidentPerson$8 = this.incidentPerson.fine) !== null && _this$incidentPerson$8 !== void 0 ? _this$incidentPerson$8 : 0,
        prison_amount: (_this$incidentPerson$9 = this.incidentPerson.time) !== null && _this$incidentPerson$9 !== void 0 ? _this$incidentPerson$9 : 0,
        res_amount: (_this$incidentPerson$10 = this.incidentPerson.res) !== null && _this$incidentPerson$10 !== void 0 ? _this$incidentPerson$10 : 0,
        action: 'imprisoned'
      }).then(function (data) {
        window.$events.$emit('reload');
        setTimeout(function () {
          _this13.$store.dispatch('nui/sendPrison', {
            fine_id: data.id,
            character_number: _this13.incidentPerson.person.character_number,
            fine: _this13.incidentPerson.fine,
            time: _this13.incidentPerson.time,
            res: _this13.incidentPerson.res
          });
          window.$events.$emit('reload');
          return _this13.$axios.$post("/police/incidents/wait-fine/".concat(data.id)).then(function (waitFineData) {
            if (!waitFineData.removed) {
              window.$events.$emit('reload');
            }
            window.$events.$emit('reload');
            resolve();
          }).catch(function () {
            return resolve();
          });
        }, 500);
      });
    },
    handleAction: function handleAction(_ref) {
      var _this14 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee10() {
        var fine_amount, prison_amount, res_amount, action, event;
        return regeneratorRuntime.wrap(function _callee10$(_context10) {
          while (1) switch (_context10.prev = _context10.next) {
            case 0:
              fine_amount = _ref.fine_amount, prison_amount = _ref.prison_amount, res_amount = _ref.res_amount, action = _ref.action, event = _ref.event;
              _context10.next = 3;
              return _this14.updatePerson();
            case 3:
              return _context10.abrupt("return", _this14.$axios.$post("/police/incidents/issue-fine/".concat(_this14.incidentPerson.id), {
                fine_amount: fine_amount,
                prison_amount: prison_amount,
                res_amount: res_amount,
                action: action
              }));
            case 4:
            case "end":
              return _context10.stop();
          }
        }, _callee10);
      }))();
    },
    markWanted: function markWanted() {
      this.$axios.$post("/police/incidents/update-person/".concat(this.incidentPerson.id), {
        is_wanted: true
      }).then(function (r) {
        window.$events.$emit('reload');
      });
    },
    markGuilty: function markGuilty(value) {
      this.$axios.$post("/police/incidents/update-person/".concat(this.incidentPerson.id), {
        is_guilty: value
      }).then(function (r) {
        window.$events.$emit('reload');
      });
    },
    markArrested: function markArrested() {
      return this.$axios.$post("/police/incidents/update-person/".concat(this.incidentPerson.id), {
        is_wanted: false
      }).then(function (r) {
        window.$events.$emit('reload');
      });
    },
    markPardoned: function markPardoned() {
      return this.$axios.$post("/police/incidents/update-person/".concat(this.incidentPerson.id), {
        is_pardoned: true
      }).then(function (r) {
        window.$events.$emit('reload');
      });
    },
    updatePerson: function updatePerson() {
      var _this15 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee11() {
        return regeneratorRuntime.wrap(function _callee11$(_context11) {
          while (1) switch (_context11.prev = _context11.next) {
            case 0:
              _this15.hasChanged = false;
              return _context11.abrupt("return", _this15.$axios.$post("/police/incidents/update-person/".concat(_this15.incidentPerson.id), {
                charges: _this15.incidentPerson.charges,
                realCharges: _this15.computedCharges,
                notes: _this15.incidentPerson.notes,
                res: _this15.incidentPerson.res,
                time: _this15.incidentPerson.time,
                fine: _this15.incidentPerson.fine
              }));
            case 2:
            case "end":
              return _context11.stop();
          }
        }, _callee11);
      }))();
    },
    removePerson: function removePerson() {
      return this.$axios.$post("/police/incidents/remove-person/".concat(this.incidentPerson.id)).then(function () {
        window.$events.$emit('reload');
      });
    },
    viewProfile: function viewProfile() {
      this.$router.push({
        path: "/cad/persons/".concat(this.incidentPerson.person.id)
      });
    },
    calculateChargeTotals: function calculateChargeTotals(value, charge) {
      if (charge.is_attempted) {
        value = Math.floor(value * .5);
      }
      if (charge.is_accessory) {
        value = Math.floor(value * .75);
      }
      if (charge.is_against_leo) {
        value = Math.floor(value * 1.5);
      }
      if (charge.is_against_government) {
        value = Math.floor(value * 2.0);
      }
      if (charge.is_commercial_vehicle) {
        value = Math.floor(value * 2);
      }
      return value;
    },
    getActionStatusText: function getActionStatusText() {
      // Safety check - return empty string if no fined data
      if (!this.incidentPerson || !this.incidentPerson.fined) {
        return '';
      }
      var fine = this.incidentPerson.fined;
      if (fine.paid) {
        // Determine action based on the amounts in the fine record
        if (fine.prison_amount && fine.prison_amount > 0) {
          return 'JAILED';
        } else if (fine.fine_amount && fine.fine_amount > 0) {
          return 'FINED';
        } else if (fine.fine_amount === 0 && fine.prison_amount === 0) {
          return 'WARNED';
        } else {
          return 'PROCESSED';
        }
      } else {
        return 'PENDING';
      }
    },
    getActionStatusClass: function getActionStatusClass() {
      // Safety check - return default class if no fined data
      if (!this.incidentPerson || !this.incidentPerson.fined) {
        return 'processed';
      }
      var fine = this.incidentPerson.fined;
      if (fine.paid) {
        // Determine action based on the amounts in the fine record
        if (fine.prison_amount && fine.prison_amount > 0) {
          return 'jailed';
        } else if (fine.fine_amount && fine.fine_amount > 0) {
          return 'fined';
        } else if (fine.fine_amount === 0 && fine.prison_amount === 0) {
          return 'warned';
        } else {
          return 'processed';
        }
      } else {
        return 'pending';
      }
    }
  },
  watch: {
    visible: function visible(value) {
      var _this16 = this;
      if (value) {
        this.$nextTick(function () {
          if (_this16.$refs['recordCharges']) {
            _this16.$refs['recordCharges'].index();
          }
        });
      }
    },
    query: function query() {
      this.chargesShowing = false;
      if (this.query && this.query.length < 3) return false;
      this.chargesShowing = true;
    },
    isExpanded: function isExpanded() {
      var _this17 = this;
      // Update visibility when expansion state changes
      this.$nextTick(function () {
        _this17.updateVisibility();
      });
    }
  },
  computed: police_incident_person_itemvue_type_script_lang_js_objectSpread({
    hasBeenFined: function hasBeenFined() {
      return !!this.incidentPerson.fined;
    },
    hasActionStatus: function hasActionStatus() {
      return this.incidentPerson && this.incidentPerson.fined;
    },
    sideSuspect: function sideSuspect() {
      var _this18 = this;
      var filtered = this.incident.persons.filter(function (p) {
        return p.type === 'suspect' && p.id !== _this18.incidentPerson.id;
      });
      if (filtered.length > 0) {
        return filtered[0];
      }
      return false;
    },
    incidentPersonType: function incidentPersonType() {
      var _this19 = this;
      return this.personTypes.find(function (pt) {
        return pt.identifier === _this19.incidentPerson.type;
      });
    },
    filteredCharges: function filteredCharges() {
      var _this20 = this;
      if (!this.query || !this.charges || this.query.length < 1) return [];
      return this.charges.filter(function (c) {
        return c.name.toLowerCase().includes(_this20.query.toLowerCase());
      });
    },
    filteredCrimes: function filteredCrimes() {
      var _this21 = this;
      if (!this.query || !this.crimes || this.query.length < 1) return [];
      return this.crimes.filter(function (c) {
        return c.name.toLowerCase().includes(_this21.query.toLowerCase());
      });
    },
    computedCharges: function computedCharges() {
      var _this22 = this;
      var charges = [];
      var _iterator4 = police_incident_person_itemvue_type_script_lang_js_createForOfIteratorHelper(this.incidentPerson.charges),
        _step4;
      try {
        var _loop = function _loop() {
          var charge = _step4.value;
          var chargeMeta = _this22.charges.find(function (c) {
            return c.id === charge.police_charge_id;
          });
          var chargeMetaCopy = Object.assign(chargeMeta);
          chargeMetaCopy.internal_id = charge.id;
          chargeMetaCopy.police_charge_id = charge.police_charge_id;
          chargeMetaCopy.content = charge.content;
          chargeMetaCopy.counts = charge.counts;
          charges.push(chargeMetaCopy);
        };
        for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {
          _loop();
        }
      } catch (err) {
        _iterator4.e(err);
      } finally {
        _iterator4.f();
      }
      return charges;
    },
    computedCrimes: function computedCrimes() {
      var _this23 = this;
      var crimes = [];
      var _iterator5 = police_incident_person_itemvue_type_script_lang_js_createForOfIteratorHelper(this.incidentPerson.crimes),
        _step5;
      try {
        var _loop2 = function _loop2() {
          var incident_person_crime = _step5.value;
          var chargeMeta = _this23.crimes.find(function (c) {
            return c.id === incident_person_crime.police_charge_id;
          });
          var copy = Object.assign(incident_person_crime, {});
          if (chargeMeta) {
            copy.name = chargeMeta.name;
            copy.type = chargeMeta.type;
            copy.category = chargeMeta.category;
            copy.description = chargeMeta.description;
            copy.can_accessory = chargeMeta.can_accessory;
            copy.can_attempted = chargeMeta.can_attempted;
            copy.can_against_leo = chargeMeta.can_against_leo;
            copy.can_against_government = chargeMeta.can_against_government;
            copy.can_commercial_vehicle = chargeMeta.can_commercial_vehicle;
            copy.totals_ticket = chargeMeta.totals_ticket;
            copy.totals_prison = chargeMeta.totals_prison;
            copy.totals_restitution = chargeMeta.totals_restitution;
          }
          crimes.push(copy);
        };
        for (_iterator5.s(); !(_step5 = _iterator5.n()).done;) {
          _loop2();
        }
      } catch (err) {
        _iterator5.e(err);
      } finally {
        _iterator5.f();
      }
      return crimes;
    },
    chargesString: function chargesString() {
      return this.computedCrimes.map(function (c) {
        var reasoning = '';
        if (c.reasoning) {
          reasoning += " (".concat(c.reasoning, ")");
        }
        return " ".concat(c.crime_name);
      }).toString();
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    charges: 'system/charges',
    crimes: 'system/crimes',
    isAbleDeleteCatRecord: 'auth/isAbleDeleteCatRecord',
    isPoliceLt: 'auth/isPoliceLt',
    hasAnyGroup: 'auth/hasAnyGroup',
    personTypes: 'cad/personTypes'
  }))
});
// CONCATENATED MODULE: ./components/Pages/Police/police-incident-person-item.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_police_incident_person_itemvue_type_script_lang_js = (police_incident_person_itemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Police/police-incident-person-item.vue?vue&type=style&index=0&id=728fdaa0&prod&scoped=true&lang=css
var police_incident_person_itemvue_type_style_index_0_id_728fdaa0_prod_scoped_true_lang_css = __webpack_require__(1814);

// CONCATENATED MODULE: ./components/Pages/Police/police-incident-person-item.vue






/* normalize component */

var police_incident_person_item_component = Object(componentNormalizer["a" /* default */])(
  Police_police_incident_person_itemvue_type_script_lang_js,
  police_incident_person_itemvue_type_template_id_728fdaa0_scoped_true_render,
  police_incident_person_itemvue_type_template_id_728fdaa0_scoped_true_staticRenderFns,
  false,
  null,
  "728fdaa0",
  null
  
)

/* harmony default export */ var police_incident_person_item = (police_incident_person_item_component.exports);

/* nuxt-component-imports */
installComponents(police_incident_person_item_component, {CrudyTableItemsComponentCitizen: __webpack_require__(499).default})

// EXTERNAL MODULE: ./components/Common/PolicePersonSelector.vue + 4 modules
var PolicePersonSelector = __webpack_require__(1669);

// EXTERNAL MODULE: ./components/Common/PoliceCarSelector.vue + 4 modules
var PoliceCarSelector = __webpack_require__(355);

// EXTERNAL MODULE: ./components/Common/TableWrapper.vue + 4 modules
var TableWrapper = __webpack_require__(311);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.search.js
var es_string_search = __webpack_require__(192);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/police-incident-selector.vue?vue&type=template&id=1f3a0cb9


var police_incident_selectorvue_type_template_id_1f3a0cb9_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('multiselect', {
    attrs: {
      "options": _vm.options,
      "loading": _vm.isLoading,
      "internal-search": false
    },
    on: {
      "search-change": _vm.search,
      "select": function select($event) {
        return _vm.$emit('selected', $event.id);
      }
    },
    scopedSlots: _vm._u([{
      key: "singleLabel",
      fn: function fn(props) {
        return [_c('span', {
          staticClass: "option__title font-weight-bold"
        }, [_vm._v(_vm._s(props.option.title))])];
      }
    }, {
      key: "option",
      fn: function fn(props) {
        return [_c('div', {
          staticClass: "option__desc"
        }, [_c('span', {
          staticClass: "option__title font-weight-bold"
        }, [_vm._v(_vm._s(props.option.title))]), _vm._v(" "), _c('span', [_vm._v(" - ")]), _vm._v(" "), _c('span', {
          staticClass: "option__title font-weight-bold"
        }, [_vm._v(_vm._s(props.option.sub_type))]), _vm._v(" "), _c('span', [_vm._v(" - ")]), _vm._v(" "), _c('span', {
          staticClass: "option__title font-weight-bold"
        }, [_vm._v(_vm._s(props.option.type))])])];
      }
    }]),
    model: {
      value: _vm.value,
      callback: function callback($$v) {
        _vm.value = $$v;
      },
      expression: "value"
    }
  });
};
var police_incident_selectorvue_type_template_id_1f3a0cb9_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/police-incident-selector.vue?vue&type=template&id=1f3a0cb9

// EXTERNAL MODULE: ./node_modules/vue-multiselect/dist/vue-multiselect.min.js
var vue_multiselect_min = __webpack_require__(202);
var vue_multiselect_min_default = /*#__PURE__*/__webpack_require__.n(vue_multiselect_min);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/police-incident-selector.vue?vue&type=script&lang=js

/* harmony default export */ var police_incident_selectorvue_type_script_lang_js = ({
  name: "PoliceIncidentSelector",
  components: {
    Multiselect: vue_multiselect_min_default.a
  },
  props: ['current'],
  data: function data() {
    return {
      value: null,
      options: [],
      isLoading: false
    };
  },
  mounted: function mounted() {},
  methods: {
    clear: function clear() {
      this.value = null;
    },
    search: function search(searchQuery) {
      var _this = this;
      if (searchQuery.length > 2) {
        this.isLoading = true;
        this.$axios.$get("/police/incidents/light-search/".concat(searchQuery)).then(function (r) {
          _this.isLoading = false;
          _this.options = r;
        });
      }
    }
  }
});
// CONCATENATED MODULE: ./components/Common/police-incident-selector.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_police_incident_selectorvue_type_script_lang_js = (police_incident_selectorvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/police-incident-selector.vue?vue&type=style&index=0&id=1f3a0cb9&prod&lang=scss
var police_incident_selectorvue_type_style_index_0_id_1f3a0cb9_prod_lang_scss = __webpack_require__(1816);

// CONCATENATED MODULE: ./components/Common/police-incident-selector.vue






/* normalize component */

var police_incident_selector_component = Object(componentNormalizer["a" /* default */])(
  Common_police_incident_selectorvue_type_script_lang_js,
  police_incident_selectorvue_type_template_id_1f3a0cb9_render,
  police_incident_selectorvue_type_template_id_1f3a0cb9_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var police_incident_selector = (police_incident_selector_component.exports);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(23);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(24);

// CONCATENATED MODULE: ./helpers/PrintFormatter.js


function PrintFormatter_createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = PrintFormatter_unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function PrintFormatter_unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return PrintFormatter_arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? PrintFormatter_arrayLikeToArray(r, a) : void 0; } }
function PrintFormatter_arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }













var PrintFormatter_PrintFormatter = /*#__PURE__*/function () {
  function PrintFormatter(incident, faction) {
    Object(classCallCheck["a" /* default */])(this, PrintFormatter);
    this.incident = null;
    this.content = '';
    this.incident = incident;
    this.faction = faction;
    this.content += "[center] ![image](".concat(this.faction.logo, ") [/center] \n");
    this.content += "## ".concat(this.faction.name, " - INC-").concat(this.incident.id, " \n");
    this.content += "### ".concat(this.incident.created_at, " \n");
    this.spacer();
    this.content += '### Involved Individuals \n';
    var _iterator = PrintFormatter_createForOfIteratorHelper(incident.persons),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        var incidentPerson = _step.value;
        this.spacer();
        this.content += '| Name | Involvement | WANTED | GUILTY |\n';
        this.content += '| ---- | ----------- | ------ | ------ |\n';
        this.content += "| ".concat(incidentPerson.person.name.toUpperCase(), " | ").concat(incidentPerson.type.toUpperCase().replace('_', ' '), " | ").concat(incidentPerson.is_wanted, " | ").concat(incidentPerson.is_guilty, " |\n");
        this.spacer();

        // if (incidentPerson.charges) {
        //   this.content += `[c=gray] [f=16] ${ incidentPerson.charges.map(c => {
        //     return { name: c.}
        //   }) } [/f] [/c]`
        // }

        // this.spacer();
        this.spacer();
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
    this.spacer();
    this.content += '### Involved Vehicles \n';
    this.content += '| REG | TYPE | IMPOUND TYPE |\n';
    this.content += '| --- | ---- | ------------ |\n';
    var _iterator2 = PrintFormatter_createForOfIteratorHelper(incident.vehicles),
      _step2;
    try {
      for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
        var car = _step2.value;
        this.content += "| ".concat(car.car.registration, " | ").concat(car.car.vehicle, " | ").concat(car.impound_type, " |\n");
      }
    } catch (err) {
      _iterator2.e(err);
    } finally {
      _iterator2.f();
    }
    this.spacer();
    this.spacer();
    this.spacer();
    this.content += '### Incident Content \n';
    this.content += "----- \n";
    var _iterator3 = PrintFormatter_createForOfIteratorHelper(incident.pages),
      _step3;
    try {
      for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
        var page = _step3.value;
        this.content += "### ".concat(page.title, " - ").concat(page.type, "\n");
        this.content += "".concat(page.body) + ' \n';
      }
    } catch (err) {
      _iterator3.e(err);
    } finally {
      _iterator3.f();
    }
    this.spacer();
  }
  return Object(createClass["a" /* default */])(PrintFormatter, [{
    key: "spacer",
    value: function spacer() {
      this.content += ' \n';
    }
  }, {
    key: "render",
    value: function render() {
      return this.content;
    }
  }]);
}();

// EXTERNAL MODULE: ./components/Crud/parts/CrudSelect.vue + 4 modules
var CrudSelect = __webpack_require__(203);

// EXTERNAL MODULE: ./components/app-roll-out.vue + 4 modules
var app_roll_out = __webpack_require__(82);

// EXTERNAL MODULE: ./components/Common/app-auto-textarea.vue + 4 modules
var app_auto_textarea = __webpack_require__(1642);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// EXTERNAL MODULE: ./utils/store-accessor.ts
var store_accessor = __webpack_require__(27);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-object-renderer.vue + 4 modules
var crudy_object_renderer = __webpack_require__(69);

// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-editor.vue + 27 modules
var crudy_editor = __webpack_require__(312);

// EXTERNAL MODULE: ./components/Crudy/crudy-page/crudy-page.vue + 9 modules
var crudy_page = __webpack_require__(1555);

// EXTERNAL MODULE: ./mixins/Reloadable.js
var Reloadable = __webpack_require__(1639);

// EXTERNAL MODULE: ./components/Common/app-socket-channel-presence-listener.vue + 4 modules
var app_socket_channel_presence_listener = __webpack_require__(497);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/incidents/view/_id/index.vue?vue&type=script&lang=js


function _idvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _idvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? _idvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : _idvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }














































/* harmony default export */ var _idvue_type_script_lang_js = ({
  components: {
    AppSocketChannelPresenceListener: app_socket_channel_presence_listener["a" /* default */],
    AppAvatarStack: app_avatar_stack["a" /* default */],
    AppTimestamp: app_timestamp["a" /* default */],
    CrudyPage: crudy_page["a" /* default */],
    CrudyEditor: crudy_editor["a" /* default */],
    CrudyObjectRenderer: crudy_object_renderer["a" /* default */],
    CrudyTable: crudy_table["a" /* default */],
    AppPage: AppPage["a" /* default */],
    AppAvatar: app_avatar["a" /* default */],
    AppFormGroup: app_form_group["a" /* default */],
    AppCard: app_card["a" /* default */],
    AppMarkdownView: app_markdown_view["a" /* default */],
    AppAutoTextarea: app_auto_textarea["a" /* default */],
    AppRollOut: app_roll_out["a" /* default */],
    CrudSelect: CrudSelect["a" /* default */],
    PoliceIncidentSelector: police_incident_selector,
    TableWrapper: TableWrapper["a" /* default */],
    PoliceCarSelector: PoliceCarSelector["a" /* default */],
    PolicePersonSelector: PolicePersonSelector["a" /* default */],
    PoliceIncidentPersonItem: police_incident_person_item,
    ItemDetail: ItemDetail["a" /* default */],
    Loader: Loader["a" /* default */]
  },
  mixins: [Reloadable["a" /* default */]],
  data: function data() {
    return {
      loading: true,
      addingPage: false,
      removeIncident: false,
      vehicleModel: false,
      incidentModel: false,
      documentModal: false,
      restrictionModal: false,
      selectedPersonItem: null,
      imagesAdded: [],
      vehicleForm: {
        car_id: null,
        impound_type: 'No Impound',
        police_incident_id: null
      },
      documentForm: {
        police_incident_id: null,
        document_id: null
      },
      linkForm: {
        incident_id_a: null,
        incident_id_b: null
      },
      restrictForm: {
        incident_id: null,
        person_id: null
      },
      evidence: null,
      pageForm: {
        title: null,
        type: 'N/A',
        body: null
      },
      viewing: [],
      personViewers: {},
      // Track which person each user is viewing { userId: personId }
      bodyUpdateTimeout: null,
      // For debouncing body updates
      localStorageTimeout: null // For debouncing localStorage saves
    };
  },
  created: function created() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _this.loading = true;
            _this.pageForm.body = localStorage.getItem("".concat(_this.$route.params.id, "-page"));
            _context.next = 4;
            return _this.$store.dispatch('cad/fetchPersonTypes');
          case 4:
            _context.next = 6;
            return _this.$store.dispatch('cad/fetchSetIncident', _this.$route.params.id);
          case 6:
            _this.loading = false;

            // Form data preservation is now handled by the Vuex store

            if (_this.incident.category.allow_evidence) {
              _this.refreshEvidence();
            }
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  mounted: function mounted() {
    var _this2 = this;
    // Override the global reload event to use our refresh method
    window.$events.$off('reload', this.refresh);
    window.$events.$on('reload', this.refresh);

    // Check if any person is currently visible after mount
    this.$nextTick(function () {
      setTimeout(function () {
        _this2.checkInitialVisibility();
      }, 2000); // Single check after components are mounted
    });
  },
  beforeDestroy: function beforeDestroy() {
    window.$events.$off('reload', this.refresh);
  },
  methods: {
    handleSocketUpdate: function handleSocketUpdate(_ref) {
      var _this3 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        var links, entity;
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              links = _ref.links, entity = _ref.entity;
              _this3.refresh();
            case 2:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    },
    handleCrimeAttributeUpdate: function handleCrimeAttributeUpdate(payload) {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        var crimeId, incidentPersonId, field, value, crimeName, totals, fromPerson, person, crime;
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              crimeId = payload.crimeId, incidentPersonId = payload.incidentPersonId, field = payload.field, value = payload.value, crimeName = payload.crimeName, totals = payload.totals, fromPerson = payload.fromPerson; // Find the specific crime in the incident and update only that attribute
              if (_this4.incident && _this4.incident.persons) {
                person = _this4.incident.persons.find(function (p) {
                  return p.id === incidentPersonId;
                });
                if (person && person.crimes) {
                  crime = person.crimes.find(function (c) {
                    return c.id === crimeId;
                  });
                  if (crime) {
                    // Update the specific field and the crime name
                    _this4.$set(crime, field, value);
                    _this4.$set(crime, 'crime_name', crimeName);

                    // Update the person's totals
                    _this4.$set(person, 'totals', totals);

                    // Emit event to child components
                    window.$events.$emit('crimeAttributeUpdated', payload);
                  }
                }
              }
            case 2:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }))();
    },
    handlePersonViewingChanged: function handlePersonViewingChanged(payload) {
      var userId = payload.userId,
        viewingPersonId = payload.viewingPersonId;

      // Update the user viewing state
      this.$set(this.personViewers, userId, viewingPersonId);

      // Force reactivity update
      this.$forceUpdate();
    },
    handlePersonTotalsUpdate: function handlePersonTotalsUpdate(payload) {
      var incidentPersonId = payload.incidentPersonId,
        field = payload.field,
        value = payload.value,
        fromPersonId = payload.fromPersonId;

      // Find the person in the incident and update the field
      if (this.incident && this.incident.persons) {
        var person = this.incident.persons.find(function (p) {
          return p.id === incidentPersonId;
        });
        if (person) {
          // Update the field value
          this.$set(person, field, value);

          // Emit event to child components so they can handle the update
          window.$events.$emit('personTotalsUpdated', payload);
        }
      }
    },
    broadcastPersonViewing: function broadcastPersonViewing(personId) {
      // Broadcast that this user is now viewing a specific person
      this.$axios.$post("/police/incidents/broadcast-person-viewing/".concat(this.incident.id), {
        person_id: personId,
        action: 'viewing'
      }).catch(function (error) {
        console.error('Failed to broadcast person viewing:', error);
      });
    },
    broadcastPersonStoppedViewing: function broadcastPersonStoppedViewing(personId) {
      // Broadcast that this user stopped viewing a specific person
      this.$axios.$post("/police/incidents/broadcast-person-viewing/".concat(this.incident.id), {
        person_id: personId,
        action: 'stopped_viewing'
      }).catch(function (error) {
        console.error('Failed to broadcast person stopped viewing:', error);
      });
    },
    refresh: function refresh() {
      var _arguments = arguments,
        _this5 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee4() {
        var refreshTable;
        return regeneratorRuntime.wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              refreshTable = _arguments.length > 0 && _arguments[0] !== undefined ? _arguments[0] : false;
              _context4.next = 3;
              return _this5.$store.dispatch('cad/fetchSetIncident', _this5.$route.params.id);
            case 3:
              _this5.loading = false;
              if (!refreshTable) {
                _context4.next = 7;
                break;
              }
              _context4.next = 7;
              return store_accessor["e" /* CrudyTableStore */].refreshActiveCrudyTables();
            case 7:
              // Check visibility after data is loaded
              _this5.$nextTick(function () {
                setTimeout(function () {
                  _this5.checkInitialVisibility();
                }, 500);
              });
            case 8:
            case "end":
              return _context4.stop();
          }
        }, _callee4);
      }))();
    },
    triggerPersonAddForm: function triggerPersonAddForm() {
      var _this6 = this;
      store_accessor["b" /* CrudyFormStore */].setSchema({
        action: {
          title: 'Add Person to Incident',
          endpoint: 'police/incidents/add-person',
          fields: {
            police_incident_id: {
              default: this.incident.id,
              editable: true
            },
            is_wanted: {
              default: "false",
              editable: true
            },
            type: {
              type: 'CrudyFormFieldSelect',
              label: 'Person Involvement',
              options: this.personTypes.map(function (pt) {
                return {
                  text: pt.name,
                  value: pt.identifier
                };
              }),
              default: 'suspect',
              editable: true
            },
            person_id: {
              type: 'CrudyFormFieldPerson',
              label: 'Person to Add',
              editable: true
            }
          }
        },
        finish: function finish() {
          return _this6.refresh();
        }
      });
    },
    accessEvidence: function accessEvidence() {
      var _this7 = this;
      return this.$axios.$post("/police/incidents/add-log/".concat(this.$route.params.id), {
        content: 'Accessed Evidence'
      }).then(function (r) {
        fetch("https://blrp_tablet/openEvidence", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=UTF-8'
          },
          body: JSON.stringify({
            id: _this7.incident.id
          })
        }).then(function (resp) {
          return resp.json();
        }).then(function (resp) {
          return resp;
        });
      });
    },
    remove: function remove() {
      var _this8 = this;
      return this.$axios.$post("/police/incidents/remove/".concat(this.$route.params.id)).then(function (r) {
        _this8.$router.back();
      });
    },
    printIncident: function printIncident() {
      var _this9 = this;
      var printer = new PrintFormatter_PrintFormatter(this.incident, this.incident.department);
      return this.$axios.$post('/cloud/create-note', {
        title: "INC ".concat(this.incident),
        content: printer.render(),
        ignore_person: true
      }).then(function (r) {
        fetch("https://blrp_tablet/printPaper", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=UTF-8'
          },
          body: JSON.stringify({
            id: r.id,
            title: "[".concat(_this9.incident.department.short.toUpperCase(), "] ").concat(_this9.incident.category.prefix, "-").concat(_this9.incident.id),
            contents: printer.render()
          })
        }).then(function (resp) {
          return resp.json();
        }).then(function (resp) {
          return resp;
        });
      });
    },
    printPage: function printPage(page) {
      var content = "";
      content += "![image](".concat(this.incident.department.logo, ")\n");
      content += "### ".concat(this.incident.department.name, "\n");
      content += "## ".concat(page.title, " - ").concat(page.type, "\n");
      content += "\n";
      content += "\n";
      content += "".concat(page.body);
      this.$store.dispatch('nui/printSaveNoteContent', {
        content: content,
        title: "".concat(this.incident.category.prefix, "-").concat(this.incident.id)
      });
    },
    addPersonAuto: function addPersonAuto(type) {
      this.personForm.person_id = this.user.id;
      this.personForm.type = type;
      this.personForm.is_wanted = false;
      return this.addPerson();
    },
    addPerson: function addPerson() {
      var _this10 = this;
      this.personForm.police_incident_id = this.incident.id;
      return this.$axios.$post("/police/incidents/add-person", this.personForm).then(function (r) {
        _this10.refresh();
        _this10.addPersonModal = false;
        _this10.$refs['personSelector'].clear();
        _this10.personForm = {
          type: null,
          person_id: null,
          police_incident_id: null,
          is_wanted: false
        };
      });
    },
    addVehicle: function addVehicle() {
      var _this11 = this;
      this.vehicleForm.police_incident_id = this.incident.id;
      return this.$axios.$post("/police/incidents/add-vehicle", this.vehicleForm).then(function (r) {
        _this11.refresh(true);
        _this11.vehicleModel = false;
        _this11.$refs['carSelector'].clear();
        _this11.vehicleForm = {
          car_id: null,
          police_incident_id: null,
          impound_type: null
        };
      });
    },
    linkIncident: function linkIncident() {
      var _this12 = this;
      this.linkForm.incident_id_a = this.incident.id;
      return this.$axios.$post("/police/incidents/link-incident", this.linkForm).then(function (r) {
        _this12.refresh();
        _this12.incidentModel = false;
        _this12.$refs['incidentSelector'].clear();
        _this12.vehicleForm = {
          incident_id_a: null,
          incident_id_b: null
        };
      });
    },
    linkDocument: function linkDocument() {
      var _this13 = this;
      this.documentForm.police_incident_id = this.incident.id;
      return this.$axios.$post("/police/incidents/link-document", this.linkForm).then(function (r) {
        _this13.refresh();
        _this13.incidentModel = false;
        _this13.$refs['documentSelector'].clear();
        _this13.documentForm = {
          police_incident_id: null,
          document_id: null
        };
      });
    },
    addUserRestriction: function addUserRestriction() {
      var _this14 = this;
      this.restrictForm.incident_id = this.incident.id;
      return this.$axios.$post("/police/incidents/add-access", this.restrictForm).then(function (r) {
        _this14.refresh();
        _this14.restrictionModal = false;
        _this14.$refs['personWhitelistSelector'].clear();
        _this14.restrictForm = {
          incident_id_a: null,
          incident_id_b: null
        };
      });
    },
    removeUserRestriction: function removeUserRestriction(id) {
      var _this15 = this;
      return this.$axios.$post("/police/incidents/remove-access/".concat(id)).then(function (r) {
        _this15.refresh();
        _this15.restrictForm = {};
      });
    },
    unlinkIncident: function unlinkIncident(linkedIncidentId) {
      var _this16 = this;
      return this.$axios.$post("/police/incidents/unlink-incident/".concat(this.incident.id, "/").concat(linkedIncidentId)).then(function (r) {
        _this16.refresh();
        _this16.incidentModel = false;
      });
    },
    personExpansionChanged: function personExpansionChanged(data) {
      // console.log('personExpChange', data)
    },
    selectPerson: function selectPerson(personId) {
      var _this17 = this;
      if (this.selectedPersonItem === personId) {
        this.selectedPersonItem = null;
      } else {
        this.selectedPersonItem = personId;
        setTimeout(function () {
          _this17.$refs["person-item-".concat(personId)][0];
          // .$el.scrollIntoView({ block: 'start', behavior: 'smooth' })
        }, 300);
      }
    },
    submitPage: function submitPage() {
      var _this18 = this;
      this.pageForm.police_incident_id = this.incident.id;
      // this.pageForm.body = this.pageForm.body.replace(/(https?:\/\/\S+(\.png|\.jpg|\.jpeg|\.gif))/g, '![image]($1)');

      if (this.pageForm.body.includes('{intro}')) {
        var introText = '';
        var primary = this.user;
        var suspects = this.incident.persons.filter(function (p) {
          return p.type === 'suspect';
        }).map(function (p) {
          return p.person.name;
        }).join(', ');
        introText += "At **".concat(this.incident.created_at, "** an incident occurred involving ").concat(this.incident.persons.length, " persons. ");
        introText += "The incident occurred at **".concat(this.incident.location.name, "**. This location may be considered \"around\", \"at\" or \"by\". ");
        if (primary) introText += "I, **".concat(primary.person.name, "** witnessed or can attest to the following events. The following suspects: **").concat(suspects, "** are included in the events that happened in this series of events. ");
        this.pageForm.body = this.pageForm.body.replace('{intro}', introText);
      }
      return this.$axios.$post("/police/incidents/add-page", this.pageForm).then(function (r) {
        _this18.refresh();
        _this18.addingPage = false;
        _this18.pageForm.title = '';
        _this18.pageForm.body = '';
        _this18.pageForm.id = null;
      });
    },
    removePage: function removePage(id) {
      var _this19 = this;
      return this.$axios.$post("/police/incidents/remove-page/".concat(id)).then(function (r) {
        _this19.refresh();
      });
    },
    startEditPage: function startEditPage(page) {
      this.pageForm = page;
      this.addingPage = true;
    },
    fillContent: function fillContent(template) {
      this.pageForm.body = template.content;
    },
    refreshEvidence: function refreshEvidence() {
      var _this20 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee5() {
        return regeneratorRuntime.wrap(function _callee5$(_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              _context5.next = 2;
              return _this20.$axios.$get('/police/incidents/list-evidence', {
                params: {
                  police_incident_id: _this20.incident.id
                }
              });
            case 2:
              _this20.evidence = _context5.sent;
            case 3:
            case "end":
              return _context5.stop();
          }
        }, _callee5);
      }))();
    },
    getPersonName: function getPersonName(personId) {
      var _this$incident, _person$person;
      var person = (_this$incident = this.incident) === null || _this$incident === void 0 || (_this$incident = _this$incident.persons) === null || _this$incident === void 0 ? void 0 : _this$incident.find(function (p) {
        return p.id === personId;
      });
      return (person === null || person === void 0 || (_person$person = person.person) === null || _person$person === void 0 ? void 0 : _person$person.name) || 'Unknown Person';
    },
    /**
     * Debounced update for pageForm.body to reduce reactive processing
     */
    debouncedBodyUpdate: function debouncedBodyUpdate(value) {
      var _this21 = this;
      // Clear existing timeout
      if (this.bodyUpdateTimeout) {
        clearTimeout(this.bodyUpdateTimeout);
      }

      // Set new timeout
      this.bodyUpdateTimeout = setTimeout(function () {
        _this21.pageForm.body = value;
        _this21.bodyUpdateTimeout = null;
      }, 150); // 150ms debounce - balance between responsiveness and performance

      // Also trigger debounced localStorage save
      this.debouncedLocalStorageSave(value);
    },
    /**
     * Debounced localStorage save to prevent excessive writes while typing
     */
    debouncedLocalStorageSave: function debouncedLocalStorageSave(value) {
      var _this22 = this;
      // Clear existing timeout
      if (this.localStorageTimeout) {
        clearTimeout(this.localStorageTimeout);
      }

      // Set new timeout with longer delay for localStorage
      this.localStorageTimeout = setTimeout(function () {
        localStorage.setItem("".concat(_this22.$route.params.id, "-page"), value);
        _this22.localStorageTimeout = null;
      }, 1000); // 1 second debounce for localStorage - longer delay to reduce writes
    },
    handlePersonBecameVisible: function handlePersonBecameVisible(personId) {
      this.broadcastPersonViewing(personId);
    },
    handlePersonBecameHidden: function handlePersonBecameHidden(personId) {
      this.broadcastPersonStoppedViewing(personId);
    },
    checkInitialVisibility: function checkInitialVisibility() {
      // For now, just skip the initial visibility check on refresh
      // The refresh issue is complex and needs a different approach
      console.log('Skipping initial visibility check - refresh issue needs different solution');
    },
    getPersonViewers: function getPersonViewers(personId) {
      var _this23 = this;
      // Get all users from the presence channel who are viewing this specific person
      var viewers = [];

      // Get the presence channel users from the socket listener component
      if (this.$refs.incidentPresenceListener && this.$refs.incidentPresenceListener.viewing) {
        this.$refs.incidentPresenceListener.viewing.forEach(function (user) {
          // Check if this user is viewing the specified person
          if (_this23.personViewers[user.id] === personId) {
            viewers.push(user);
          }
        });
      }
      return viewers;
    }
  },
  watch: {
    'pageForm.body': function pageFormBody(value) {
      // localStorage.setItem(`${ this.$route.params.id }-page`, value)
    }
  },
  computed: _idvue_type_script_lang_js_objectSpread({
    selectedPersonExpansionPanel: {
      get: function get() {
        var _this$incident2,
          _this24 = this;
        if (!this.selectedPersonItem || !((_this$incident2 = this.incident) !== null && _this$incident2 !== void 0 && _this$incident2.persons)) {
          return null;
        }
        var personIndex = this.incident.persons.findIndex(function (p) {
          return p.id === _this24.selectedPersonItem;
        });
        return personIndex >= 0 ? personIndex : null;
      },
      set: function set(value) {
        var _this$incident3;
        // Handle expansion panel clicks
        if (value !== null && (_this$incident3 = this.incident) !== null && _this$incident3 !== void 0 && _this$incident3.persons && this.incident.persons[value]) {
          var person = this.incident.persons[value];
          this.selectPerson(person.id);
        } else if (value === null) {
          this.selectedPersonItem = null;
        }
      }
    },
    automaticReport: function automaticReport() {
      var _this$incident4, _this$incident5, _this$incident$locati;
      var string = "";
      var suspects = (_this$incident4 = this.incident) === null || _this$incident4 === void 0 ? void 0 : _this$incident4.persons.filter(function (p) {
        return p.type === 'suspect';
      });
      var officers = (_this$incident5 = this.incident) === null || _this$incident5 === void 0 ? void 0 : _this$incident5.persons.filter(function (p) {
        return p.type === 'officer_primary' || p.type === 'officer_secondary';
      });
      var suspectsString = suspects.map(function (s) {
        return "".concat(s.person.name);
      }).toString();
      var officersString = officers.map(function (s) {
        return "".concat(s.person.name);
      }).toString();
      var perPersonSting = suspects.map(function (suspect) {
        var chargesString = suspect.crimes.map(function (c) {
          var reasoning = '';
          if (c.reasoning) {
            reasoning += " for the reasoning of \"".concat(c.reasoning, "\"");
          }
          return " ".concat(c.crime_name, " x").concat(c.counts, " ").concat(reasoning);
        }).toString();
        var chargedString = '';
        var numbers = [];
        if (suspect.fined) {
          var _suspect$fined, _suspect$fined2, _suspect$fined3, _suspect$fined4;
          if (((_suspect$fined = suspect.fined) === null || _suspect$fined === void 0 ? void 0 : _suspect$fined.res_amount) > 0) {
            numbers.push("$".concat(suspect.fined.res_amount, " in restitution"));
          }
          if (((_suspect$fined2 = suspect.fined) === null || _suspect$fined2 === void 0 ? void 0 : _suspect$fined2.fine_amount) > 0) {
            numbers.push("$".concat(suspect.fined.fine_amount, " in fines"));
          }
          if (((_suspect$fined3 = suspect.fined) === null || _suspect$fined3 === void 0 ? void 0 : _suspect$fined3.prison_amount) > 0) {
            numbers.push("and ".concat(suspect.fined.prison_amount, " months in prison."));
          }
          chargedString = " They were ".concat((_suspect$fined4 = suspect.fined) === null || _suspect$fined4 === void 0 ? void 0 : _suspect$fined4.action, " for a total of ").concat(numbers.toString());
        }
        return " ".concat(suspect.person.name, " was charged with ").concat(chargesString, ".").concat(chargedString);
      }).toString();
      string += "On a ".concat(this.randomWeather, " day in Los Santos ").concat(suspects.length, " suspects were seen at ").concat((_this$incident$locati = this.incident.location) === null || _this$incident$locati === void 0 ? void 0 : _this$incident$locati.name, ".");
      string += " Suspects included were ".concat(suspectsString, "; witnessed by a mix of officer including ").concat(officersString);
      string += "".concat(perPersonSting);
      return string;
    },
    randomWeather: function randomWeather() {
      var items = ['sunny', 'cloudy', 'hot', 'rainy', 'foggy', 'wet', 'freezing'];
      return items[Math.floor(Math.random() * items.length)];
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    incident: 'cad/incident',
    personTypes: 'cad/personTypes',
    isAbleDeleteCatRecord: 'auth/isAbleDeleteCatRecord',
    isPoliceLt: 'auth/isPoliceLt',
    hasAnyGroup: 'auth/hasAnyGroup',
    markdownConfig: 'system/markdownConfig'
  }))
});
// CONCATENATED MODULE: ./pages/cad/incidents/view/_id/index.vue?vue&type=script&lang=js
 /* harmony default export */ var view_idvue_type_script_lang_js = (_idvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/cad/incidents/view/_id/index.vue?vue&type=style&index=0&id=1014cb8a&prod&lang=scss
var _idvue_type_style_index_0_id_1014cb8a_prod_lang_scss = __webpack_require__(1818);

// CONCATENATED MODULE: ./pages/cad/incidents/view/_id/index.vue






/* normalize component */

var _id_component = Object(componentNormalizer["a" /* default */])(
  view_idvue_type_script_lang_js,
  _idvue_type_template_id_1014cb8a_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var _id = __webpack_exports__["default"] = (_id_component.exports);

/* nuxt-component-imports */
installComponents(_id_component, {CrudyTableItemsComponentPoliceIncident: __webpack_require__(503).default})


/***/ })

}]);