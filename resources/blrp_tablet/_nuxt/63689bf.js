(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[185],{

/***/ 1563:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(7);
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(9);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(14);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(15);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(8);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(11);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(0);
/* harmony import */ var _mixins_measurable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(188);
/* harmony import */ var _mixins_toggleable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(100);
/* harmony import */ var _directives_intersect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(175);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(16);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(1);








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
// Mixins

 // Directives

 // Utilities



/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"])(_mixins_measurable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], _mixins_toggleable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"]).extend({
  name: 'VLazy',
  directives: {
    intersect: _directives_intersect__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"]
  },
  props: {
    options: {
      type: Object,
      // For more information on types, navigate to:
      // https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API
      default: function _default() {
        return {
          root: undefined,
          rootMargin: undefined,
          threshold: undefined
        };
      }
    },
    tag: {
      type: String,
      default: 'div'
    },
    transition: {
      type: String,
      default: 'fade-transition'
    }
  },
  computed: {
    styles: function styles() {
      return _objectSpread({}, this.measurableStyles);
    }
  },
  methods: {
    genContent: function genContent() {
      var children = this.isActive && Object(_util_helpers__WEBPACK_IMPORTED_MODULE_12__[/* getSlot */ "s"])(this);
      return this.transition ? this.$createElement('transition', {
        props: {
          name: this.transition
        }
      }, children) : children;
    },
    onObserve: function onObserve(entries, observer, isIntersecting) {
      if (this.isActive) return;
      this.isActive = isIntersecting;
    }
  },
  render: function render(h) {
    return h(this.tag, {
      staticClass: 'v-lazy',
      attrs: this.$attrs,
      directives: [{
        name: 'intersect',
        value: {
          handler: this.onObserve,
          options: this.options
        }
      }],
      on: this.$listeners,
      style: this.styles
    }, [this.genContent()]);
  }
}));

/***/ }),

/***/ 1565:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1574);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("4b66da15", content, true, {"sourceMap":false});

/***/ }),

/***/ 1571:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VLazy/VLazy.js
var VLazy = __webpack_require__(1563);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-card-items.vue?vue&type=template&id=809e8f90


var app_card_itemsvue_type_template_id_809e8f90_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VLazy["a" /* default */], [_c('div', {
    staticClass: "page-sub-scroller no-drag"
  }, [_vm._t("header"), _vm._v(" "), _vm._l(_vm.items, function (item, index) {
    return _c('render-item', {
      key: item.id,
      class: {
        'item-selected': _vm.currentIndex === index,
        'socket-active': _vm.visible[_vm.currentIndex]
      },
      attrs: {
        "index": index
      },
      on: {
        "enteredScreen": _vm._enteredScreen,
        "leftScreen": _vm._leftScreen
      },
      scopedSlots: _vm._u([{
        key: "default",
        fn: function fn(_ref) {
          var ref = _ref.ref;
          return [_vm._t("render", null, {
            "item": item,
            "index": index
          })];
        }
      }], null, true)
    });
  })], 2)]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-card-items.vue?vue&type=template&id=809e8f90

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/intersect/index.js
var intersect = __webpack_require__(175);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/render-item.vue?vue&type=template&id=4ba6f808


var render_itemvue_type_template_id_4ba6f808_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    directives: [{
      def: intersect["a" /* default */],
      name: "intersect",
      rawName: "v-intersect",
      value: {
        handler: _vm._onItemShownScreen,
        options: {
          threshold: [0, 0.5, 1.0]
        }
      },
      expression: "{ handler: _onItemShownScreen, options: {  threshold: [0, 0.5, 1.0]  } }"
    }],
    ref: "item"
  }, [_vm._t("default")], 2);
};
var render_itemvue_type_template_id_4ba6f808_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/render-item.vue?vue&type=template&id=4ba6f808

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/render-item.vue?vue&type=script&lang=js

















function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }

/* harmony default export */ var render_itemvue_type_script_lang_js = ({
  name: 'render-item',
  components: {},
  props: ['index'],
  data: function data() {
    return {
      isShowingOnScreen: false
    };
  },
  created: function created() {},
  methods: {
    _onItemShownScreen: function _onItemShownScreen(entries, observer) {
      var _iterator = _createForOfIteratorHelper(entries),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var entry = _step.value;
          this.isShowingOnScreen = entries[0].intersectionRatio >= 0.5;
          if (this.isShowingOnScreen) {
            this.$emit('enteredScreen', this.index);
            // this.$refs.item.style.backgroundColor = 'red'
          } else {
            this.$emit('leftScreen', this.index);
            // this.$refs.item.style.backgroundColor = 'none'
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./components/Common/render-item.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_render_itemvue_type_script_lang_js = (render_itemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/render-item.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_render_itemvue_type_script_lang_js,
  render_itemvue_type_template_id_4ba6f808_render,
  render_itemvue_type_template_id_4ba6f808_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var render_item = (component.exports);
// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-card-items.vue?vue&type=script&lang=js








function app_card_itemsvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function app_card_itemsvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? app_card_itemsvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : app_card_itemsvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/* harmony default export */ var app_card_itemsvue_type_script_lang_js = ({
  name: 'app-card-items',
  components: {
    RenderItem: render_item,
    AppCard: app_card["a" /* default */]
  },
  props: ['items', 'socketChannel'],
  data: function data() {
    return {
      visible: {},
      currentIndex: -1
    };
  },
  methods: {
    _onPrevious: function _onPrevious() {
      if (this.currentIndex > -2) {
        var _this$$refs$;
        this.currentIndex--;
        (_this$$refs$ = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$ === void 0 || _this$$refs$.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      } else {
        var _this$$refs$2;
        this.currentIndex = this.items.length - 1;
        (_this$$refs$2 = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$2 === void 0 || _this$$refs$2.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      }
    },
    _onNext: function _onNext() {
      if (this.currentIndex < this.items.length - 2) {
        var _this$$refs$3;
        this.currentIndex++;
        (_this$$refs$3 = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$3 === void 0 || _this$$refs$3.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      } else {
        var _this$$refs$4;
        this.currentIndex = 0;
        (_this$$refs$4 = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$4 === void 0 || _this$$refs$4.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      }
    },
    _onEnter: function _onEnter() {
      var item = this.items[this.currentIndex];
      this.$emit('chosen', item);
    },
    _enteredScreen: function _enteredScreen(index) {
      if (this.visible[index]) {
        return;
      }
      this.visible[index] = true;
      this.$emit('enteredView', index);

      // if (this.socketChannel && this.items[index].uuid) {
      //   const channel = `${ this.socketChannel }.${ this.items[index].uuid }`
      //   if ( window.echo ) {
      //     window.echo.listen(channel, 'SocialPostUpdated', ({ post, fromPerson }) => {
      //       this.$emit('socket', {
      //         index: index,
      //         action: 'update',
      //         post: post,
      //       })
      //     })
      //
      //     window.echo.listen(channel, 'SocialPostRemoved', () => {
      //       this.$emit('socket', {
      //         index: index,
      //         action: 'removed',
      //         post: {},
      //       })
      //     })
      //   }
      //
      // } else {
      //   console.error('socket hook failed - no uuid exists - record too old')
      // }
    },
    _leftScreen: function _leftScreen(index) {
      if (this.visible[index]) {
        // const channel = `${ this.socketChannel }.${ this.items[index].uuid }`
        //
        // // ts-ignore
        // window.echo.leave(channel)
        //
        // delete this.visible[index]
      }
    }
  },
  beforeDestroy: function beforeDestroy() {
    // for (const [ index, value ] of Object.entries(this.visible)) {
    //   const channel = `${ this.socketChannel }.${ this.items[index].id }`
    //
    //   // ts-ignore
    //   window.echo.leave(channel)
    //
    //   delete this.visible[index]
    // }
  },
  watch: {
    currentIndex: function currentIndex(value) {
      this.$emit('index', this.currentIndex);
      if (this.currentIndex === -1) {
        this.$emit('nothingChosen');
      } else {
        this.$emit('stuffChosen');
      }
    }
  },
  computed: app_card_itemsvue_type_script_lang_js_objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode',
    focusing: 'system/focusing'
  }))
});
// CONCATENATED MODULE: ./components/Common/app-card-items.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_card_itemsvue_type_script_lang_js = (app_card_itemsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-card-items.vue?vue&type=style&index=0&id=809e8f90&prod&lang=scss
var app_card_itemsvue_type_style_index_0_id_809e8f90_prod_lang_scss = __webpack_require__(1573);

// CONCATENATED MODULE: ./components/Common/app-card-items.vue






/* normalize component */

var app_card_items_component = Object(componentNormalizer["a" /* default */])(
  Common_app_card_itemsvue_type_script_lang_js,
  app_card_itemsvue_type_template_id_809e8f90_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_card_items = __webpack_exports__["a"] = (app_card_items_component.exports);

/***/ }),

/***/ 1573:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_card_items_vue_vue_type_style_index_0_id_809e8f90_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1565);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_card_items_vue_vue_type_style_index_0_id_809e8f90_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_card_items_vue_vue_type_style_index_0_id_809e8f90_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1574:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".item-selected{border-left:2px solid wheat!important}.socket-active{background-color:#ff5e5e!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1671:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-new-message.vue?vue&type=template&id=7992d4bf
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div');
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Text/text-new-message.vue?vue&type=template&id=7992d4bf

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/app-roll-out.vue + 4 modules
var app_roll_out = __webpack_require__(82);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// EXTERNAL MODULE: ./components/Pages/Text/ContactFormMixin.js
var ContactFormMixin = __webpack_require__(1566);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-new-message.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





/* harmony default export */ var text_new_messagevue_type_script_lang_js = ({
  name: "text-new-message",
  components: {
    AppFormGroup: app_form_group["a" /* default */],
    AppCard: app_card["a" /* default */],
    AppRollOut: app_roll_out["a" /* default */]
  },
  mixins: [ContactFormMixin["a" /* default */]],
  props: [],
  data: function data() {
    return {
      error: null,
      showingNewMessageModal: false,
      form: {
        phone: null,
        message: null
      }
    };
  },
  created: function created() {
    var _this = this;
    this.$nextTick(function () {
      var self = _this;
      window.onkeydown = function (e) {
        var code = e.keyCode ? e.keyCode : e.which;
        if (code === 32) {
          // space
          self.showingNewMessageModal = true;
        }
      };
    });
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./components/Pages/Text/text-new-message.vue?vue&type=script&lang=js
 /* harmony default export */ var Text_text_new_messagevue_type_script_lang_js = (text_new_messagevue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Text/text-new-message.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Text_text_new_messagevue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var text_new_message = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1733:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1931);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("ea9ba7ae", content, true, {"sourceMap":false});

/***/ }),

/***/ 1930:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_services_vue_vue_type_style_index_0_id_75f35506_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1733);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_services_vue_vue_type_style_index_0_id_75f35506_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_services_vue_vue_type_style_index_0_id_75f35506_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1931:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".services-container[data-v-75f35506]{padding:0}.services-container[data-v-75f35506],.services-list-view[data-v-75f35506]{display:flex;flex-direction:column;height:100%}.services-header[data-v-75f35506]{align-items:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);background:rgba(0,0,0,.8);border-bottom:1px solid hsla(0,0%,100%,.1);display:flex;justify-content:space-between;padding:16px 20px 12px}.services-header .header-title .services-title[data-v-75f35506]{color:#fff;font-size:24px;font-weight:600;line-height:1.2;margin:0}.services-header .header-title .service-count[data-v-75f35506]{color:#8e8e93;font-size:13px;margin-top:2px}.services-list[data-v-75f35506]{flex:1;overflow-y:auto;padding:8px 0}.services-list .service-item[data-v-75f35506]{cursor:pointer;margin:0 12px 8px;transition:all .2s ease}.services-list .service-item[data-v-75f35506]:hover{transform:translateY(-1px)}.services-list .service-item[data-v-75f35506]:active{transform:translateY(0)}.services-list[data-v-75f35506]::-webkit-scrollbar{width:4px}.services-list[data-v-75f35506]::-webkit-scrollbar-track{background:transparent}.services-list[data-v-75f35506]::-webkit-scrollbar-thumb{background:hsla(0,0%,100%,.2);border-radius:2px}.services-list[data-v-75f35506]::-webkit-scrollbar-thumb:hover{background:hsla(0,0%,100%,.3)}.service-card[data-v-75f35506]{background:linear-gradient(135deg,#1c1c1e,#2c2c2e);border:1px solid hsla(0,0%,100%,.1);border-radius:8px;overflow:hidden;padding:16px;position:relative;transition:all .2s ease}.service-card[data-v-75f35506]:hover{background:linear-gradient(135deg,#2c2c2e,#3c3c3e);border-color:hsla(0,0%,100%,.2);box-shadow:0 4px 12px rgba(0,0,0,.3)}.service-card.service-unavailable[data-v-75f35506]{opacity:.6}.service-card.service-unavailable[data-v-75f35506]:hover{background:linear-gradient(135deg,#1c1c1e,#2c2c2e);border-color:hsla(0,0%,100%,.15);box-shadow:0 2px 8px rgba(0,0,0,.2);opacity:.7}.service-content[data-v-75f35506]{align-items:center;display:flex;gap:16px;justify-content:space-between}.service-info[data-v-75f35506]{flex:1;min-width:0}.service-header[data-v-75f35506]{align-items:center;display:flex;justify-content:space-between;margin-bottom:4px}.service-group[data-v-75f35506]{flex:1;font-size:14px;font-weight:600;line-height:1.2}.service-status[data-v-75f35506]{align-items:center;display:flex;margin-left:8px}.status-dot[data-v-75f35506]{border-radius:50%;height:8px;position:relative;width:8px}.status-dot.status-active[data-v-75f35506]{animation:pulse-green-75f35506 2s infinite;background-color:#28a745;box-shadow:0 0 0 0 rgba(40,167,69,.7)}.status-dot.status-inactive[data-v-75f35506]{animation:pulse-red-75f35506 2s infinite;background-color:#dc3545;box-shadow:0 0 0 0 rgba(220,53,69,.7)}@keyframes pulse-green-75f35506{0%{box-shadow:0 0 0 0 rgba(40,167,69,.7);transform:scale(.95)}70%{box-shadow:0 0 0 4px rgba(40,167,69,0);transform:scale(1)}to{box-shadow:0 0 0 0 rgba(40,167,69,0);transform:scale(.95)}}@keyframes pulse-red-75f35506{0%{box-shadow:0 0 0 0 rgba(220,53,69,.7);transform:scale(.95)}70%{box-shadow:0 0 0 4px rgba(220,53,69,0);transform:scale(1)}to{box-shadow:0 0 0 0 rgba(220,53,69,0);transform:scale(.95)}}.service-title[data-v-75f35506]{color:#fff;font-size:16px;font-weight:500;line-height:1.3}.service-action[data-v-75f35506]{flex-shrink:0}.service-action .action-btn[data-v-75f35506]{background:rgba(0,122,255,.2)!important;border:1px solid rgba(0,122,255,.3)}.service-action .action-btn[data-v-75f35506]:hover{background:rgba(0,122,255,.3)!important;transform:scale(1.05)}.empty-state[data-v-75f35506]{align-items:center;display:flex;flex:1;flex-direction:column;justify-content:center;padding:40px 20px;text-align:center}.empty-state .empty-icon[data-v-75f35506]{margin-bottom:16px;opacity:.6}.empty-state .empty-title[data-v-75f35506]{color:#fff;font-size:20px;font-weight:600;margin:0 0 8px}.empty-state .empty-subtitle[data-v-75f35506]{color:#8e8e93;font-size:14px;line-height:1.4;margin:0}.service-input-page[data-v-75f35506]{background:linear-gradient(180deg,#000,#111);display:flex;flex-direction:column;height:100%}.service-input-page .input-page-header[data-v-75f35506]{align-items:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);background:rgba(0,0,0,.8);border-bottom:1px solid hsla(0,0%,100%,.1);display:flex;padding:16px 20px 12px}.service-input-page .input-page-header .back-btn[data-v-75f35506]{background:transparent!important;box-shadow:none!important;margin-right:16px}.service-input-page .input-page-header .back-btn[data-v-75f35506]:hover{background:rgba(0,122,255,.1)!important}.service-input-page .input-page-header .header-info[data-v-75f35506]{flex:1;text-align:center}.service-input-page .input-page-header .header-info .service-group-name[data-v-75f35506]{font-size:14px;font-weight:600;line-height:1.2;margin-bottom:2px}.service-input-page .input-page-header .header-info .service-title-name[data-v-75f35506]{color:#fff;font-size:18px;font-weight:500;line-height:1.3}.service-input-page .input-page-header .header-spacer[data-v-75f35506]{width:56px}.service-input-page .input-page-content[data-v-75f35506]{gap:20px;padding:20px}.service-input-page .input-page-content[data-v-75f35506],.service-input-page .input-page-content .input-section[data-v-75f35506]{display:flex;flex:1;flex-direction:column}.service-input-page .input-page-content .input-section .input-label[data-v-75f35506]{color:#fff;font-size:16px;font-weight:500;margin-bottom:12px}.service-input-page .input-page-content .input-section .message-input[data-v-75f35506]{background:hsla(0,0%,100%,.1);border:1px solid hsla(0,0%,100%,.2);border-radius:12px;color:#fff;flex:1;font-family:inherit;font-size:16px;min-height:200px;padding:16px;resize:none;transition:all .2s ease;width:100%}.service-input-page .input-page-content .input-section .message-input[data-v-75f35506]:focus{background:hsla(0,0%,100%,.15);border-color:#007aff;box-shadow:0 0 0 2px rgba(0,122,255,.3);outline:none}.service-input-page .input-page-content .input-section .message-input[data-v-75f35506]::-moz-placeholder{color:#8e8e93;opacity:1}.service-input-page .input-page-content .input-section .message-input[data-v-75f35506]::placeholder{color:#8e8e93;opacity:1}.service-input-page .input-page-content .input-section .character-count[data-v-75f35506]{color:#8e8e93;font-size:12px;margin-top:8px;text-align:right}.service-input-page .input-page-content .input-actions[data-v-75f35506]{display:flex;flex-direction:column;gap:12px}.service-input-page .input-page-content .input-actions .cancel-btn[data-v-75f35506]{background:transparent!important;border:1px solid hsla(0,0%,100%,.2)!important;box-shadow:none!important;color:#8e8e93!important}.service-input-page .input-page-content .input-actions .cancel-btn[data-v-75f35506]:hover{background:hsla(0,0%,100%,.1)!important;border-color:hsla(0,0%,100%,.3)!important;color:#fff!important}.service-input-page .input-page-content .input-actions .send-btn[data-v-75f35506]{background:linear-gradient(135deg,#007aff,#0056cc)!important;box-shadow:0 4px 12px rgba(0,122,255,.3)!important;color:#fff!important}.service-input-page .input-page-content .input-actions .send-btn[data-v-75f35506]:hover:not(:disabled){box-shadow:0 6px 16px rgba(0,122,255,.4)!important;transform:translateY(-1px)}.service-input-page .input-page-content .input-actions .send-btn[data-v-75f35506]:disabled{background:hsla(0,0%,100%,.1)!important;box-shadow:none!important;color:#8e8e93!important}.card-selected[data-v-75f35506]{color:var(--person-color);font-weight:bolder}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2173:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTooltip/VTooltip.js
var VTooltip = __webpack_require__(1522);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.trim.js
var es_string_trim = __webpack_require__(103);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/services.vue?vue&type=template&id=75f35506&scoped=true







var servicesvue_type_template_id_75f35506_scoped_true_render = function render() {
  var _vm$selectedService, _vm$selectedService2, _vm$selectedService3;
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "services-container"
  }, [!_vm.showInputPage ? _c('div', {
    staticClass: "services-list-view"
  }, [_c('div', {
    staticClass: "services-header"
  }, [_c('div', {
    staticClass: "header-title"
  }, [_c('h2', {
    staticClass: "services-title"
  }, [_vm._v("Services")]), _vm._v(" "), _c('div', {
    staticClass: "service-count"
  }, [_vm._v(_vm._s(_vm.services.length) + " service" + _vm._s(_vm.services.length !== 1 ? 's' : '') + " available")])])]), _vm._v(" "), _c('div', {
    staticClass: "services-list"
  }, _vm._l(_vm.services, function (item, index) {
    return _c('div', {
      key: index,
      staticClass: "service-item",
      on: {
        "click": function click($event) {
          return _vm.serviceTrigger(item);
        }
      }
    }, [_c('div', {
      staticClass: "service-card",
      class: {
        'service-unavailable': !item.serviceGroup || item.serviceGroup.active < 1
      }
    }, [_c('div', {
      staticClass: "service-content"
    }, [_c('div', {
      staticClass: "service-info"
    }, [_c('div', {
      staticClass: "service-header"
    }, [_c('div', {
      staticClass: "service-group",
      style: "color: ".concat(item.color || '#007AFF')
    }, [_vm._v("\n                  " + _vm._s(item.group) + "\n                ")]), _vm._v(" "), _c('div', {
      staticClass: "service-status"
    }, [_c(VTooltip["a" /* default */], {
      attrs: {
        "bottom": ""
      },
      scopedSlots: _vm._u([{
        key: "activator",
        fn: function fn(_ref) {
          var on = _ref.on,
            attrs = _ref.attrs;
          return [_c('div', _vm._g(_vm._b({
            staticClass: "status-dot",
            class: {
              'status-active': item.serviceGroup && item.serviceGroup.active >= 1,
              'status-inactive': !item.serviceGroup || item.serviceGroup.active < 1
            }
          }, 'div', attrs, false), on))];
        }
      }], null, true)
    }, [_vm._v(" "), _c('span', [_vm._v(_vm._s(item.serviceGroup && item.serviceGroup.active >= 1 ? 'Service Available' : 'Service Unavailable'))])])], 1)]), _vm._v(" "), _c('div', {
      staticClass: "service-title"
    }, [_vm._v("\n                " + _vm._s(item.title) + "\n              ")])]), _vm._v(" "), _c('div', {
      staticClass: "service-action"
    }, [_c(VBtn["a" /* default */], {
      staticClass: "action-btn",
      attrs: {
        "icon": "",
        "large": "",
        "color": "primary"
      },
      on: {
        "click": function click($event) {
          $event.stopPropagation();
          return _vm.serviceTrigger(item);
        }
      }
    }, [item.title.includes('Call') ? _c(VIcon["a" /* default */], {
      attrs: {
        "size": "24"
      }
    }, [_vm._v("fa-solid fa-phone")]) : item.title.includes('Send') ? _c(VIcon["a" /* default */], {
      attrs: {
        "size": "24"
      }
    }, [_vm._v("fa-solid fa-message-arrow-up")]) : _c(VIcon["a" /* default */], {
      attrs: {
        "size": "24"
      }
    }, [_vm._v("fa-solid fa-arrow-right")])], 1)], 1)])])]);
  }), 0), _vm._v(" "), _vm.services.length === 0 ? _c('div', {
    staticClass: "empty-state"
  }, [_c('div', {
    staticClass: "empty-icon"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "size": "64",
      "color": "grey"
    }
  }, [_vm._v("fa-regular fa-bell-concierge")])], 1), _vm._v(" "), _c('h3', {
    staticClass: "empty-title"
  }, [_vm._v("No Services Available")]), _vm._v(" "), _c('p', {
    staticClass: "empty-subtitle"
  }, [_vm._v("Services will appear here when available")])]) : _vm._e()]) : _vm._e(), _vm._v(" "), _vm.showInputPage ? _c('div', {
    staticClass: "service-input-page"
  }, [_c('div', {
    staticClass: "input-page-header"
  }, [_c(VBtn["a" /* default */], {
    staticClass: "back-btn",
    attrs: {
      "icon": "",
      "large": ""
    },
    on: {
      "click": _vm.goBackToServices
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "size": "24",
      "color": "#007AFF"
    }
  }, [_vm._v("fa-solid fa-arrow-left")])], 1), _vm._v(" "), _c('div', {
    staticClass: "header-info"
  }, [_c('div', {
    staticClass: "service-group-name",
    style: "color: ".concat(((_vm$selectedService = _vm.selectedService) === null || _vm$selectedService === void 0 ? void 0 : _vm$selectedService.color) || '#007AFF')
  }, [_vm._v("\n          " + _vm._s((_vm$selectedService2 = _vm.selectedService) === null || _vm$selectedService2 === void 0 ? void 0 : _vm$selectedService2.group) + "\n        ")]), _vm._v(" "), _c('div', {
    staticClass: "service-title-name"
  }, [_vm._v("\n          " + _vm._s((_vm$selectedService3 = _vm.selectedService) === null || _vm$selectedService3 === void 0 ? void 0 : _vm$selectedService3.title) + "\n        ")])]), _vm._v(" "), _c('div', {
    staticClass: "header-spacer"
  })], 1), _vm._v(" "), _c('div', {
    staticClass: "input-page-content"
  }, [_c('div', {
    staticClass: "input-section"
  }, [_c('div', {
    staticClass: "input-label"
  }, [_vm._v("Enter your message:")]), _vm._v(" "), _c('textarea', {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.serviceMessage,
      expression: "serviceMessage"
    }],
    ref: "messageInput",
    staticClass: "message-input",
    attrs: {
      "placeholder": "Type your message here...",
      "rows": "8",
      "maxlength": "500"
    },
    domProps: {
      "value": _vm.serviceMessage
    },
    on: {
      "keydown": [function ($event) {
        if (!$event.type.indexOf('key') && _vm._k($event.keyCode, "enter", 13, $event.key, "Enter")) return null;
        if (!$event.ctrlKey) return null;
        return _vm.sendServiceWithMessage.apply(null, arguments);
      }, function ($event) {
        if (!$event.type.indexOf('key') && _vm._k($event.keyCode, "escape", undefined, $event.key, undefined)) return null;
        return _vm.goBackToServices.apply(null, arguments);
      }],
      "input": function input($event) {
        if ($event.target.composing) return;
        _vm.serviceMessage = $event.target.value;
      }
    }
  }), _vm._v(" "), _c('div', {
    staticClass: "character-count"
  }, [_vm._v(_vm._s(_vm.serviceMessage.length) + "/500")])]), _vm._v(" "), _c('div', {
    staticClass: "input-actions"
  }, [_c(VBtn["a" /* default */], {
    staticClass: "cancel-btn",
    attrs: {
      "text": "",
      "large": "",
      "block": ""
    },
    on: {
      "click": _vm.goBackToServices
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "left": "",
      "small": ""
    }
  }, [_vm._v("fa-solid fa-times")]), _vm._v("\n          Cancel\n        ")], 1), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "send-btn",
    attrs: {
      "disabled": !_vm.serviceMessage.trim(),
      "color": "primary",
      "large": "",
      "block": ""
    },
    on: {
      "click": _vm.sendServiceWithMessage
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "left": "",
      "small": ""
    }
  }, [_vm._v("fa-solid fa-paper-plane")]), _vm._v("\n          Send Message\n        ")], 1)], 1)])]) : _vm._e()]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/phone/services.vue?vue&type=template&id=75f35506&scoped=true

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// EXTERNAL MODULE: ./components/Pages/Text/text-thread.vue + 4 modules
var text_thread = __webpack_require__(1670);

// EXTERNAL MODULE: ./components/app-roll-out.vue + 4 modules
var app_roll_out = __webpack_require__(82);

// EXTERNAL MODULE: ./components/Pages/Text/text-new-message.vue + 4 modules
var text_new_message = __webpack_require__(1671);

// EXTERNAL MODULE: ./components/Common/app-card-items.vue + 9 modules
var app_card_items = __webpack_require__(1571);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/services.vue?vue&type=script&lang=js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

















/* harmony default export */ var servicesvue_type_script_lang_js = ({
  scrollToTop: true,
  components: {
    AppCardItems: app_card_items["a" /* default */],
    TextNewMessage: text_new_message["a" /* default */],
    AppRollOut: app_roll_out["a" /* default */],
    TextThread: text_thread["a" /* default */],
    AppCard: app_card["a" /* default */]
  },
  data: function data() {
    return {
      showInputPage: false,
      selectedService: null,
      serviceMessage: ''
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return _this.refreshServices();
          case 2:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  activated: function activated() {
    var _this2 = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
      return regeneratorRuntime.wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return _this2.refreshServices();
          case 2:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }))();
  },
  methods: {
    serviceTrigger: function serviceTrigger(item) {
      // Check if service is available
      if (!item.serviceGroup || item.serviceGroup.active < 1) {
        var _item$serviceGroup;
        this.$store.dispatch('notifications/triggerLocalNotification', {
          icon: 'fa-solid fa-exclamation-triangle',
          color: 'orange',
          title: 'Service Unavailable',
          message: "".concat(((_item$serviceGroup = item.serviceGroup) === null || _item$serviceGroup === void 0 ? void 0 : _item$serviceGroup.display) || item.group, " is currently unavailable. Please try again later."),
          type: 'notifications_warning'
        });
        return;
      }

      // Check if this service requires input (based on title containing "Send" or specific types)
      if (this.requiresInput(item)) {
        this.showServiceInputPage(item);
      } else {
        // Direct service trigger for services that don't need input (like calls)
        this.$axios.$post('https://blrp_phone/requestService', {
          event: item.event,
          type: item.type
        });
      }
    },
    requiresInput: function requiresInput(service) {
      // Check if service requires input based on title or type
      return service.title.toLowerCase().includes('send') || service.title.toLowerCase().includes('message') || service.title.toLowerCase().includes('request') || service.type === 'message' || service.type === 'input';
    },
    showServiceInputPage: function showServiceInputPage(service) {
      var _this3 = this;
      this.selectedService = service;
      this.serviceMessage = '';
      this.showInputPage = true;

      // Focus the input after page shows
      this.$nextTick(function () {
        if (_this3.$refs.messageInput) {
          _this3.$refs.messageInput.focus();
        }
      });
    },
    goBackToServices: function goBackToServices() {
      this.showInputPage = false;
      this.selectedService = null;
      this.serviceMessage = '';
    },
    sendServiceWithMessage: function sendServiceWithMessage() {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              if (!(!_this4.serviceMessage.trim() || !_this4.selectedService)) {
                _context3.next = 2;
                break;
              }
              return _context3.abrupt("return");
            case 2:
              _context3.next = 4;
              return _this4.$axios.$post('https://blrp_phone/requestService', {
                event: _this4.selectedService.event,
                type: _this4.selectedService.type,
                message: _this4.serviceMessage.trim()
              });
            case 4:
              // Go back to services and reset
              _this4.goBackToServices();
            case 5:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }))();
    },
    refreshServices: function refreshServices() {
      var _this5 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee4() {
        return regeneratorRuntime.wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              _context4.prev = 0;
              _context4.next = 3;
              return _this5.$axios.$post('https://blrp_tablet/inquire-services');
            case 3:
              _context4.next = 8;
              break;
            case 5:
              _context4.prev = 5;
              _context4.t0 = _context4["catch"](0);
              console.error('Failed to refresh services:', _context4.t0);
            case 8:
            case "end":
              return _context4.stop();
          }
        }, _callee4, null, [[0, 5]]);
      }))();
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    services: 'text/services'
  }))
});
// CONCATENATED MODULE: ./pages/phone/services.vue?vue&type=script&lang=js
 /* harmony default export */ var phone_servicesvue_type_script_lang_js = (servicesvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/phone/services.vue?vue&type=style&index=0&id=75f35506&prod&lang=scss&scoped=true
var servicesvue_type_style_index_0_id_75f35506_prod_lang_scss_scoped_true = __webpack_require__(1930);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/phone/services.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  phone_servicesvue_type_script_lang_js,
  servicesvue_type_template_id_75f35506_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "75f35506",
  null
  
)

/* harmony default export */ var services = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);