(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[132],{

/***/ 2120:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTooltip/VTooltip.js
var VTooltip = __webpack_require__(1522);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/ticketing-aid/index.vue?vue&type=template&id=3986af3b







var ticketing_aidvue_type_template_id_3986af3b_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "p-2"
  }, _vm._l(_vm.crimes, function (crime) {
    return _c(VTooltip["a" /* default */], {
      key: crime.id,
      attrs: {
        "top": ""
      },
      scopedSlots: _vm._u([{
        key: "activator",
        fn: function fn(_ref) {
          var on = _ref.on,
            attrs = _ref.attrs;
          return [_c(VRow["a" /* default */], _vm._g(_vm._b({
            staticClass: "case-row p-1",
            attrs: {
              "no-gutters": ""
            }
          }, 'v-row', attrs, false), on), [_c(VCol["a" /* default */], {
            attrs: {
              "cols": "4"
            }
          }, [_c('span', {
            staticClass: "ml-2 mr-2",
            style: "color: ".concat(crime.color, " !important")
          }, [_c('i', {
            staticClass: "fa-solid fa-circle-dot"
          })]), _vm._v("\n          " + _vm._s(crime.name) + "\n        ")]), _vm._v(" "), _c(VCol["a" /* default */], {
            staticClass: "text-muted d-flex justify-content-center mr-4"
          }, [_vm._v("\n          " + _vm._s(crime.category) + "\n        ")]), _vm._v(" "), _c(VCol["a" /* default */], {
            attrs: {
              "cols": "1"
            }
          }, [_vm._v("\n          $ " + _vm._s(crime.totals_ticket) + "\n        ")]), _vm._v(" "), _c(VCol["a" /* default */], {
            attrs: {
              "cols": "1"
            }
          }, [_vm._v("\n          " + _vm._s(crime.totals_prison) + "m\n        ")]), _vm._v(" "), _c(VCol["a" /* default */], {
            attrs: {
              "cols": "1"
            }
          }, [_vm._v("\n          $ " + _vm._s(crime.totals_restitution) + "\n        ")]), _vm._v(" "), _c(VCol["a" /* default */], {
            attrs: {
              "cols": "1"
            }
          }, [crime.totals_parole > 0 ? _c('span', {
            staticClass: "text-danger"
          }, [_vm._v("\n            " + _vm._s(crime.totals_parole) + "m\n          ")]) : _c('span', [_vm._v("\n            --\n          ")])])], 1)];
        }
      }], null, true)
    }, [_vm._v("\n\n    " + _vm._s(crime.description) + "\n  ")]);
  }), 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/ticketing-aid/index.vue?vue&type=template&id=3986af3b

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/ticketing-aid/index.vue?vue&type=script&lang=js









function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/* harmony default export */ var ticketing_aidvue_type_script_lang_js = ({
  components: {},
  props: [],
  data: function data() {
    return {};
  },
  created: function created() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return _this.$store.dispatch('system/syncCrimes');
          case 2:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  methods: {},
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    crimes: 'system/crimes'
  }))
});
// CONCATENATED MODULE: ./pages/cad/ticketing-aid/index.vue?vue&type=script&lang=js
 /* harmony default export */ var cad_ticketing_aidvue_type_script_lang_js = (ticketing_aidvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/ticketing-aid/index.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  cad_ticketing_aidvue_type_script_lang_js,
  ticketing_aidvue_type_template_id_3986af3b_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var ticketing_aid = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);