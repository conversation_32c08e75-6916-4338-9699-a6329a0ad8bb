(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[180],{

/***/ 1563:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(7);
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(9);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(14);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(15);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(8);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(11);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(0);
/* harmony import */ var _mixins_measurable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(188);
/* harmony import */ var _mixins_toggleable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(100);
/* harmony import */ var _directives_intersect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(175);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(16);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(1);








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
// Mixins

 // Directives

 // Utilities



/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"])(_mixins_measurable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], _mixins_toggleable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"]).extend({
  name: 'VLazy',
  directives: {
    intersect: _directives_intersect__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"]
  },
  props: {
    options: {
      type: Object,
      // For more information on types, navigate to:
      // https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API
      default: function _default() {
        return {
          root: undefined,
          rootMargin: undefined,
          threshold: undefined
        };
      }
    },
    tag: {
      type: String,
      default: 'div'
    },
    transition: {
      type: String,
      default: 'fade-transition'
    }
  },
  computed: {
    styles: function styles() {
      return _objectSpread({}, this.measurableStyles);
    }
  },
  methods: {
    genContent: function genContent() {
      var children = this.isActive && Object(_util_helpers__WEBPACK_IMPORTED_MODULE_12__[/* getSlot */ "s"])(this);
      return this.transition ? this.$createElement('transition', {
        props: {
          name: this.transition
        }
      }, children) : children;
    },
    onObserve: function onObserve(entries, observer, isIntersecting) {
      if (this.isActive) return;
      this.isActive = isIntersecting;
    }
  },
  render: function render(h) {
    return h(this.tag, {
      staticClass: 'v-lazy',
      attrs: this.$attrs,
      directives: [{
        name: 'intersect',
        value: {
          handler: this.onObserve,
          options: this.options
        }
      }],
      on: this.$listeners,
      style: this.styles
    }, [this.genContent()]);
  }
}));

/***/ }),

/***/ 1565:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1574);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("4b66da15", content, true, {"sourceMap":false});

/***/ }),

/***/ 1571:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VLazy/VLazy.js
var VLazy = __webpack_require__(1563);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-card-items.vue?vue&type=template&id=809e8f90


var app_card_itemsvue_type_template_id_809e8f90_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VLazy["a" /* default */], [_c('div', {
    staticClass: "page-sub-scroller no-drag"
  }, [_vm._t("header"), _vm._v(" "), _vm._l(_vm.items, function (item, index) {
    return _c('render-item', {
      key: item.id,
      class: {
        'item-selected': _vm.currentIndex === index,
        'socket-active': _vm.visible[_vm.currentIndex]
      },
      attrs: {
        "index": index
      },
      on: {
        "enteredScreen": _vm._enteredScreen,
        "leftScreen": _vm._leftScreen
      },
      scopedSlots: _vm._u([{
        key: "default",
        fn: function fn(_ref) {
          var ref = _ref.ref;
          return [_vm._t("render", null, {
            "item": item,
            "index": index
          })];
        }
      }], null, true)
    });
  })], 2)]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-card-items.vue?vue&type=template&id=809e8f90

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/intersect/index.js
var intersect = __webpack_require__(175);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/render-item.vue?vue&type=template&id=4ba6f808


var render_itemvue_type_template_id_4ba6f808_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    directives: [{
      def: intersect["a" /* default */],
      name: "intersect",
      rawName: "v-intersect",
      value: {
        handler: _vm._onItemShownScreen,
        options: {
          threshold: [0, 0.5, 1.0]
        }
      },
      expression: "{ handler: _onItemShownScreen, options: {  threshold: [0, 0.5, 1.0]  } }"
    }],
    ref: "item"
  }, [_vm._t("default")], 2);
};
var render_itemvue_type_template_id_4ba6f808_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/render-item.vue?vue&type=template&id=4ba6f808

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/render-item.vue?vue&type=script&lang=js

















function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }

/* harmony default export */ var render_itemvue_type_script_lang_js = ({
  name: 'render-item',
  components: {},
  props: ['index'],
  data: function data() {
    return {
      isShowingOnScreen: false
    };
  },
  created: function created() {},
  methods: {
    _onItemShownScreen: function _onItemShownScreen(entries, observer) {
      var _iterator = _createForOfIteratorHelper(entries),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var entry = _step.value;
          this.isShowingOnScreen = entries[0].intersectionRatio >= 0.5;
          if (this.isShowingOnScreen) {
            this.$emit('enteredScreen', this.index);
            // this.$refs.item.style.backgroundColor = 'red'
          } else {
            this.$emit('leftScreen', this.index);
            // this.$refs.item.style.backgroundColor = 'none'
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./components/Common/render-item.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_render_itemvue_type_script_lang_js = (render_itemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/render-item.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_render_itemvue_type_script_lang_js,
  render_itemvue_type_template_id_4ba6f808_render,
  render_itemvue_type_template_id_4ba6f808_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var render_item = (component.exports);
// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-card-items.vue?vue&type=script&lang=js








function app_card_itemsvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function app_card_itemsvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? app_card_itemsvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : app_card_itemsvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/* harmony default export */ var app_card_itemsvue_type_script_lang_js = ({
  name: 'app-card-items',
  components: {
    RenderItem: render_item,
    AppCard: app_card["a" /* default */]
  },
  props: ['items', 'socketChannel'],
  data: function data() {
    return {
      visible: {},
      currentIndex: -1
    };
  },
  methods: {
    _onPrevious: function _onPrevious() {
      if (this.currentIndex > -2) {
        var _this$$refs$;
        this.currentIndex--;
        (_this$$refs$ = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$ === void 0 || _this$$refs$.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      } else {
        var _this$$refs$2;
        this.currentIndex = this.items.length - 1;
        (_this$$refs$2 = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$2 === void 0 || _this$$refs$2.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      }
    },
    _onNext: function _onNext() {
      if (this.currentIndex < this.items.length - 2) {
        var _this$$refs$3;
        this.currentIndex++;
        (_this$$refs$3 = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$3 === void 0 || _this$$refs$3.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      } else {
        var _this$$refs$4;
        this.currentIndex = 0;
        (_this$$refs$4 = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$4 === void 0 || _this$$refs$4.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      }
    },
    _onEnter: function _onEnter() {
      var item = this.items[this.currentIndex];
      this.$emit('chosen', item);
    },
    _enteredScreen: function _enteredScreen(index) {
      if (this.visible[index]) {
        return;
      }
      this.visible[index] = true;
      this.$emit('enteredView', index);

      // if (this.socketChannel && this.items[index].uuid) {
      //   const channel = `${ this.socketChannel }.${ this.items[index].uuid }`
      //   if ( window.echo ) {
      //     window.echo.listen(channel, 'SocialPostUpdated', ({ post, fromPerson }) => {
      //       this.$emit('socket', {
      //         index: index,
      //         action: 'update',
      //         post: post,
      //       })
      //     })
      //
      //     window.echo.listen(channel, 'SocialPostRemoved', () => {
      //       this.$emit('socket', {
      //         index: index,
      //         action: 'removed',
      //         post: {},
      //       })
      //     })
      //   }
      //
      // } else {
      //   console.error('socket hook failed - no uuid exists - record too old')
      // }
    },
    _leftScreen: function _leftScreen(index) {
      if (this.visible[index]) {
        // const channel = `${ this.socketChannel }.${ this.items[index].uuid }`
        //
        // // ts-ignore
        // window.echo.leave(channel)
        //
        // delete this.visible[index]
      }
    }
  },
  beforeDestroy: function beforeDestroy() {
    // for (const [ index, value ] of Object.entries(this.visible)) {
    //   const channel = `${ this.socketChannel }.${ this.items[index].id }`
    //
    //   // ts-ignore
    //   window.echo.leave(channel)
    //
    //   delete this.visible[index]
    // }
  },
  watch: {
    currentIndex: function currentIndex(value) {
      this.$emit('index', this.currentIndex);
      if (this.currentIndex === -1) {
        this.$emit('nothingChosen');
      } else {
        this.$emit('stuffChosen');
      }
    }
  },
  computed: app_card_itemsvue_type_script_lang_js_objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode',
    focusing: 'system/focusing'
  }))
});
// CONCATENATED MODULE: ./components/Common/app-card-items.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_card_itemsvue_type_script_lang_js = (app_card_itemsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-card-items.vue?vue&type=style&index=0&id=809e8f90&prod&lang=scss
var app_card_itemsvue_type_style_index_0_id_809e8f90_prod_lang_scss = __webpack_require__(1573);

// CONCATENATED MODULE: ./components/Common/app-card-items.vue






/* normalize component */

var app_card_items_component = Object(componentNormalizer["a" /* default */])(
  Common_app_card_itemsvue_type_script_lang_js,
  app_card_itemsvue_type_template_id_809e8f90_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_card_items = __webpack_exports__["a"] = (app_card_items_component.exports);

/***/ }),

/***/ 1573:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_card_items_vue_vue_type_style_index_0_id_809e8f90_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1565);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_card_items_vue_vue_type_style_index_0_id_809e8f90_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_card_items_vue_vue_type_style_index_0_id_809e8f90_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1574:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".item-selected{border-left:2px solid wheat!important}.socket-active{background-color:#ff5e5e!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1583:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1633);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("a73a2698", content, true, {"sourceMap":false});

/***/ }),

/***/ 1632:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_infinite_scroll_vue_vue_type_style_index_0_id_0734d0e0_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1583);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_infinite_scroll_vue_vue_type_style_index_0_id_0734d0e0_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_infinite_scroll_vue_vue_type_style_index_0_id_0734d0e0_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1633:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".infinite-scroll-container[data-v-0734d0e0]{position:relative}.load-trigger[data-v-0734d0e0]{bottom:0;pointer-events:none;position:absolute;width:100%}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1637:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-call.vue?vue&type=template&id=1906425a





var text_callvue_type_template_id_1906425a_render = function render() {
  var _vm$currentContact$av;
  var _vm = this,
    _c = _vm._self._c;
  return _c(VCard["a" /* default */], {
    staticClass: "p-2 pl-3 mb-2 mt-2"
  }, [_c(VRow["a" /* default */], {
    attrs: {
      "no-gutters": ""
    }
  }, [_c(VCol["a" /* default */], {
    staticClass: "d-flex align-items-center",
    attrs: {
      "cols": "2"
    }
  }, [_vm.currentContact ? _c('app-avatar', {
    attrs: {
      "src": (_vm$currentContact$av = _vm.currentContact.avatar_url) !== null && _vm$currentContact$av !== void 0 ? _vm$currentContact$av : "https://api.dicebear.com/9.x/bottts-neutral/svg?seed=".concat(_vm.currentContact.display, "&backgroundColor=000000")
    }
  }) : _c('app-avatar', {
    attrs: {
      "src": "https://api.dicebear.com/9.x/bottts-neutral/svg?seed=".concat(_vm.call.phone)
    }
  })], 1), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "text-h6"
  }, [_vm._v("\n        " + _vm._s(_vm.currentContact ? _vm.currentContact.display : _vm.call.phone) + "\n      ")]), _vm._v(" "), _c('div', {
    staticStyle: {
      "font-size": "12px"
    }
  }, [_vm._l(_vm.call.items.slice(0, 3), function (log) {
    return _c('span', [log.incoming === 1 ? _c('i', {
      staticClass: "mr-2 fa-solid fa-phone-arrow-down-left",
      class: {
        'text-danger': !log.accepts,
        'text-success': log.accepts
      }
    }) : _c('i', {
      staticClass: "mr-2 fa-solid fa-phone-arrow-up-right",
      class: {
        'text-danger': !log.accepts,
        'text-success': log.accepts
      }
    })]);
  }), _vm._v(" "), _vm.call.items.length < 0 ? _c('span', {
    staticClass: "text-muted"
  }, [_vm._v("\n          --\n        ")]) : _vm._e(), _vm._v(" "), _c('span', {
    staticClass: "text-muted"
  }, [_vm.call.items[0] ? _c('app-timestamp', {
    staticClass: "text-muted",
    attrs: {
      "stamp": _vm.call.items[0].time
    }
  }) : _vm._e()], 1)], 2)]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "d-flex justify-content-end",
    attrs: {
      "cols": "1"
    }
  }, [_c('text-contact-actions', {
    attrs: {
      "phone": _vm.call.phone,
      "contact": _vm.currentContact
    }
  })], 1)], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Text/text-call.vue?vue&type=template&id=1906425a

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// EXTERNAL MODULE: ./components/Common/app-avatar.vue + 4 modules
var app_avatar = __webpack_require__(189);

// EXTERNAL MODULE: ./components/Pages/Text/ContactFormMixin.js
var ContactFormMixin = __webpack_require__(1566);

// EXTERNAL MODULE: ./components/Pages/Text/text-contact-actions.vue + 4 modules
var text_contact_actions = __webpack_require__(1590);

// EXTERNAL MODULE: ./components/Common/app-btn.vue + 4 modules
var app_btn = __webpack_require__(495);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-call.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }






/* harmony default export */ var text_callvue_type_script_lang_js = ({
  name: 'text-call',
  components: {
    AppBtn: app_btn["a" /* default */],
    TextContactActions: text_contact_actions["a" /* default */],
    AppAvatar: app_avatar["a" /* default */],
    AppTimestamp: app_timestamp["a" /* default */]
  },
  props: ['call'],
  mixins: [ContactFormMixin["a" /* default */]],
  data: function data() {
    return {};
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({
    currentContact: function currentContact() {
      return this.keyedContacts[this.call.phone];
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    contacts: 'text/contacts',
    keyedContacts: 'text/keyedContacts'
  }))
});
// CONCATENATED MODULE: ./components/Pages/Text/text-call.vue?vue&type=script&lang=js
 /* harmony default export */ var Text_text_callvue_type_script_lang_js = (text_callvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Text/text-call.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Text_text_callvue_type_script_lang_js,
  text_callvue_type_template_id_1906425a_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var text_call = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1638:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VProgressCircular/VProgressCircular.js
var VProgressCircular = __webpack_require__(300);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-infinite-scroll.vue?vue&type=template&id=0734d0e0&scoped=true



var app_infinite_scrollvue_type_template_id_0734d0e0_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "infinite-scroll-container"
  }, [_vm._l(_vm.paginatedItems, function (item, index) {
    return _c('div', {
      key: _vm.getItemKey(item, index)
    }, [_vm._t("item", null, {
      "item": item,
      "index": index
    })], 2);
  }), _vm._v(" "), _vm.loading ? _c('div', {
    staticClass: "text-center py-3"
  }, [_c(VProgressCircular["a" /* default */], {
    attrs: {
      "indeterminate": "",
      "color": "primary",
      "size": "24"
    }
  }), _vm._v(" "), _c('div', {
    staticClass: "text-caption mt-2"
  }, [_vm._v("Loading more...")])], 1) : _vm._e(), _vm._v(" "), _vm.hasMore && !_vm.loading ? _c('div', {
    staticClass: "text-center py-3"
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "text": "",
      "color": "primary",
      "small": ""
    },
    on: {
      "click": _vm.loadMore
    }
  }, [_vm._v("\n      Load More\n    ")])], 1) : _vm._e(), _vm._v(" "), !_vm.hasMore && _vm.paginatedItems.length > 0 ? _c('div', {
    staticClass: "text-center py-3"
  }, [_c('div', {
    staticClass: "text-caption text-muted"
  }, [_vm._v("No more items to load")])]) : _vm._e(), _vm._v(" "), _c('div', {
    ref: "loadTrigger",
    staticClass: "load-trigger",
    staticStyle: {
      "height": "1px"
    }
  })], 2);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-infinite-scroll.vue?vue&type=template&id=0734d0e0&scoped=true

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.number.constructor.js
var es_number_constructor = __webpack_require__(42);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-infinite-scroll.vue?vue&type=script&lang=js




/* harmony default export */ var app_infinite_scrollvue_type_script_lang_js = ({
  name: 'app-infinite-scroll',
  props: {
    items: {
      type: Array,
      required: true
    },
    itemsPerPage: {
      type: Number,
      default: 10
    },
    keyField: {
      type: String,
      default: 'id'
    },
    loading: {
      type: Boolean,
      default: false
    },
    serverSide: {
      type: Boolean,
      default: false
    },
    totalItems: {
      type: Number,
      default: null
    }
  },
  data: function data() {
    return {
      currentPage: 1,
      observer: null
    };
  },
  computed: {
    paginatedItems: function paginatedItems() {
      if (this.serverSide) {
        return this.items;
      }
      return this.items.slice(0, this.currentPage * this.itemsPerPage);
    },
    hasMore: function hasMore() {
      if (this.serverSide) {
        return this.totalItems ? this.items.length < this.totalItems : false;
      }
      return this.paginatedItems.length < this.items.length;
    }
  },
  mounted: function mounted() {
    this.setupIntersectionObserver();
  },
  beforeDestroy: function beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  watch: {
    items: function items() {
      // Reset pagination when items change
      this.currentPage = 1;
    }
  },
  methods: {
    setupIntersectionObserver: function setupIntersectionObserver() {
      var _this = this;
      if (!window.IntersectionObserver) {
        return; // Fallback to load more button
      }
      this.observer = new IntersectionObserver(function (entries) {
        entries.forEach(function (entry) {
          if (entry.isIntersecting && _this.hasMore && !_this.loading) {
            _this.loadMore();
          }
        });
      }, {
        rootMargin: '100px' // Start loading 100px before the trigger comes into view
      });
      if (this.$refs.loadTrigger) {
        this.observer.observe(this.$refs.loadTrigger);
      }
    },
    loadMore: function loadMore() {
      if (this.hasMore && !this.loading) {
        if (!this.serverSide) {
          this.currentPage++;
        }
        this.$emit('load-more', {
          page: this.serverSide ? Math.ceil(this.items.length / this.itemsPerPage) + 1 : this.currentPage,
          itemsPerPage: this.itemsPerPage,
          offset: this.items.length
        });
      }
    },
    getItemKey: function getItemKey(item, index) {
      return item[this.keyField] || index;
    },
    reset: function reset() {
      this.currentPage = 1;
    }
  }
});
// CONCATENATED MODULE: ./components/Common/app-infinite-scroll.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_infinite_scrollvue_type_script_lang_js = (app_infinite_scrollvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-infinite-scroll.vue?vue&type=style&index=0&id=0734d0e0&prod&scoped=true&lang=css
var app_infinite_scrollvue_type_style_index_0_id_0734d0e0_prod_scoped_true_lang_css = __webpack_require__(1632);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-infinite-scroll.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_infinite_scrollvue_type_script_lang_js,
  app_infinite_scrollvue_type_template_id_0734d0e0_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "0734d0e0",
  null
  
)

/* harmony default export */ var app_infinite_scroll = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1729:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1923);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("1c50ca2a", content, true, {"sourceMap":false});

/***/ }),

/***/ 1730:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1925);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("3dbf7c62", content, true, {"sourceMap":false});

/***/ }),

/***/ 1922:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_text_call_row_vue_vue_type_style_index_0_id_40c7c87a_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1729);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_text_call_row_vue_vue_type_style_index_0_id_40c7c87a_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_text_call_row_vue_vue_type_style_index_0_id_40c7c87a_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1923:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".call-row-card[data-v-40c7c87a]{background:linear-gradient(135deg,#1c1c1e,#2c2c2e);border:1px solid hsla(0,0%,100%,.1);border-radius:8px;overflow:hidden;padding:10px 12px;position:relative;transition:all .2s ease}.call-row-card[data-v-40c7c87a]:hover{background:linear-gradient(135deg,#2c2c2e,#3c3c3e);border-color:hsla(0,0%,100%,.2);box-shadow:0 4px 12px rgba(0,0,0,.3);transform:translateY(-1px)}.call-row-card[data-v-40c7c87a]:active{transform:translateY(0)}.call-content[data-v-40c7c87a]{align-items:flex-start;display:flex;gap:8px}.call-avatar[data-v-40c7c87a]{flex-shrink:0}.call-direction[data-v-40c7c87a]{align-items:center;display:flex;flex-shrink:0;justify-content:center;width:24px}.call-direction .direction-icon[data-v-40c7c87a]{opacity:.8}.call-info[data-v-40c7c87a]{flex:1;min-width:0}.call-header[data-v-40c7c87a]{align-items:flex-start;display:flex;justify-content:space-between}.call-details[data-v-40c7c87a]{flex:1;min-width:0}.contact-name[data-v-40c7c87a]{font-size:14px;font-weight:600;line-height:1.2;margin-bottom:1px}.call-time[data-v-40c7c87a]{color:#8e8e93;font-size:12px;line-height:1.3}.call-actions[data-v-40c7c87a]{align-items:center;display:flex;flex-shrink:0;gap:4px}.call-actions .call-btn[data-v-40c7c87a]{background:rgba(76,175,80,.2)!important}.call-actions .call-btn[data-v-40c7c87a]:hover{background:rgba(76,175,80,.3)!important}@media(max-width:768px){.call-row-card[data-v-40c7c87a]{padding:8px 10px}.contact-name[data-v-40c7c87a]{font-size:13px}.call-time[data-v-40c7c87a]{font-size:11px}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1924:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_call_log_vue_vue_type_style_index_0_id_1ce1995a_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1730);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_call_log_vue_vue_type_style_index_0_id_1ce1995a_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_call_log_vue_vue_type_style_index_0_id_1ce1995a_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1925:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".call-log-container[data-v-1ce1995a]{display:flex;flex-direction:column;height:100%;padding:0}.call-log-header[data-v-1ce1995a]{align-items:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);background:rgba(0,0,0,.8);border-bottom:1px solid hsla(0,0%,100%,.1);display:flex;justify-content:space-between;padding:16px 20px 12px}.call-log-header .header-title .call-log-title[data-v-1ce1995a]{color:#fff;font-size:24px;font-weight:600;line-height:1.2;margin:0}.call-log-header .header-title .phone-info[data-v-1ce1995a]{color:#8e8e93;font-size:13px;margin-top:2px}.call-log-header .header-title .phone-info .phone-number[data-v-1ce1995a]{color:#007aff;font-weight:600}.call-log-header .menu-btn[data-v-1ce1995a]{background:hsla(0,0%,100%,.1)!important}.call-log-header .menu-btn[data-v-1ce1995a]:hover{background:hsla(0,0%,100%,.2)!important}.call-logs-list[data-v-1ce1995a]{flex:1;overflow-y:auto;padding:8px 0}.call-logs-list .call-item[data-v-1ce1995a]{border-radius:8px;margin:0 12px 8px;overflow:hidden}.call-logs-list[data-v-1ce1995a]::-webkit-scrollbar{width:4px}.call-logs-list[data-v-1ce1995a]::-webkit-scrollbar-track{background:transparent}.call-logs-list[data-v-1ce1995a]::-webkit-scrollbar-thumb{background:hsla(0,0%,100%,.2);border-radius:2px}.call-logs-list[data-v-1ce1995a]::-webkit-scrollbar-thumb:hover{background:hsla(0,0%,100%,.3)}.empty-state[data-v-1ce1995a]{align-items:center;display:flex;flex:1;flex-direction:column;justify-content:center;padding:40px 20px;text-align:center}.empty-state .empty-icon[data-v-1ce1995a]{margin-bottom:16px;opacity:.6}.empty-state .empty-title[data-v-1ce1995a]{color:#fff;font-size:20px;font-weight:600;margin:0 0 8px}.empty-state .empty-subtitle[data-v-1ce1995a]{color:#8e8e93;font-size:14px;line-height:1.4;margin:0}[data-v-1ce1995a] .menu-list{background:#1c1c1e!important;border:1px solid hsla(0,0%,100%,.1)!important}[data-v-1ce1995a] .menu-list .menu-item{color:#fff!important}[data-v-1ce1995a] .menu-list .menu-item:hover{background:hsla(0,0%,100%,.1)!important}[data-v-1ce1995a] .menu-list .menu-item .v-list-item__title{color:#fff!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2029:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VList.js
var VList = __webpack_require__(462);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItem.js
var VListItem = __webpack_require__(301);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/index.js + 2 modules
var components_VList = __webpack_require__(50);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItemIcon.js
var VListItemIcon = __webpack_require__(260);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VMenu/VMenu.js
var VMenu = __webpack_require__(461);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/call-log.vue?vue&type=template&id=1ce1995a&scoped=true









var call_logvue_type_template_id_1ce1995a_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "call-log-container"
  }, [_c('div', {
    staticClass: "call-log-header"
  }, [_c('div', {
    staticClass: "header-title"
  }, [_c('h2', {
    staticClass: "call-log-title"
  }, [_vm._v("Call Log")]), _vm._v(" "), _c('div', {
    staticClass: "phone-info"
  }, [_vm._v("\n        My Number: "), _c('span', {
    staticClass: "phone-number"
  }, [_vm._v(_vm._s(_vm.user.phone))])])]), _vm._v(" "), _c(VMenu["a" /* default */], {
    ref: "phoneMenu",
    attrs: {
      "offset-y": ""
    },
    scopedSlots: _vm._u([{
      key: "activator",
      fn: function fn(_ref) {
        var attrs = _ref.attrs;
        return [_c(VBtn["a" /* default */], _vm._b({
          staticClass: "menu-btn",
          attrs: {
            "text": "",
            "color": "primary",
            "fab": "",
            "icon": "",
            "small": ""
          },
          on: {
            "click": function click($event) {
              $event.stopPropagation();
              return _vm.togglePhoneMenu.apply(null, arguments);
            }
          }
        }, 'v-btn', attrs, false), [_c(VIcon["a" /* default */], {
          attrs: {
            "small": ""
          }
        }, [_vm._v("fa-solid fa-ellipsis-vertical")])], 1)];
      }
    }]),
    model: {
      value: _vm.phoneMenuOpen,
      callback: function callback($$v) {
        _vm.phoneMenuOpen = $$v;
      },
      expression: "phoneMenuOpen"
    }
  }, [_vm._v(" "), _c(VList["a" /* default */], {
    staticClass: "menu-list"
  }, [_c(VListItem["a" /* default */], {
    staticClass: "menu-item",
    on: {
      "click": _vm.shareNumberWithNearest
    }
  }, [_c(VListItemIcon["a" /* default */], [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "success",
      "small": ""
    }
  }, [_vm._v("fa-solid fa-share")])], 1), _vm._v(" "), _c(components_VList["a" /* VListItemContent */], [_c(components_VList["c" /* VListItemTitle */], [_vm._v("Share Number with Nearest")])], 1)], 1), _vm._v(" "), _c(VListItem["a" /* default */], {
    staticClass: "menu-item",
    on: {
      "click": _vm.toggleRingtone
    }
  }, [_c(VListItemIcon["a" /* default */], [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "info",
      "small": ""
    }
  }, [_vm._v(_vm._s(_vm.isPlaying ? 'fa-solid fa-stop' : 'fa-solid fa-play'))])], 1), _vm._v(" "), _c(components_VList["a" /* VListItemContent */], [_c(components_VList["c" /* VListItemTitle */], [_vm._v(_vm._s(_vm.isPlaying ? 'Stop Ringtone' : 'Preview Ringtone'))])], 1)], 1)], 1)], 1)], 1), _vm._v(" "), _c('div', {
    staticClass: "call-logs-list"
  }, [_c('app-infinite-scroll', {
    attrs: {
      "items": _vm.compiledCallLogs,
      "items-per-page": 10,
      "key-field": "id",
      "loading": _vm.callLogsLoading
    },
    on: {
      "load-more": _vm.onLoadMoreCallLogs
    },
    scopedSlots: _vm._u([{
      key: "item",
      fn: function fn(_ref2) {
        var item = _ref2.item,
          index = _ref2.index;
        return [_c('div', {
          staticClass: "call-item"
        }, [_c('text-call-row', {
          ref: "call-".concat(index),
          attrs: {
            "call": item
          }
        })], 1)];
      }
    }])
  })], 1), _vm._v(" "), _vm.compiledCallLogs.length === 0 ? _c('div', {
    staticClass: "empty-state"
  }, [_c('div', {
    staticClass: "empty-icon"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "size": "64",
      "color": "grey"
    }
  }, [_vm._v("fa-regular fa-phone")])], 1), _vm._v(" "), _c('h3', {
    staticClass: "empty-title"
  }, [_vm._v("No Call History")]), _vm._v(" "), _c('p', {
    staticClass: "empty-subtitle"
  }, [_vm._v("Your call history will appear here")])]) : _vm._e()]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/phone/call-log.vue?vue&type=template&id=1ce1995a&scoped=true

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find.js
var es_array_find = __webpack_require__(89);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// EXTERNAL MODULE: ./components/Pages/Text/text-contact.vue + 4 modules
var text_contact = __webpack_require__(1643);

// EXTERNAL MODULE: ./components/Pages/Text/text-new-contact.vue + 4 modules
var text_new_contact = __webpack_require__(1644);

// EXTERNAL MODULE: ./components/Pages/Text/text-call.vue + 4 modules
var text_call = __webpack_require__(1637);

// EXTERNAL MODULE: ./components/Common/app-card-items.vue + 9 modules
var app_card_items = __webpack_require__(1571);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-call-row.vue?vue&type=template&id=40c7c87a&scoped=true



var text_call_rowvue_type_template_id_40c7c87a_scoped_true_render = function render() {
  var _vm$currentContact;
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "call-row-card"
  }, [_c('div', {
    staticClass: "call-content"
  }, [_c('div', {
    staticClass: "call-avatar"
  }, [_c('text-avatar', {
    attrs: {
      "phone": _vm.call.num
    }
  })], 1), _vm._v(" "), _c('div', {
    staticClass: "call-direction mt-4"
  }, [_vm.call.incoming === 1 ? _c(VIcon["a" /* default */], {
    staticClass: "direction-icon",
    class: {
      'text-danger': _vm.call.accepts == 0,
      'text-success': _vm.call.accepts == 1
    },
    attrs: {
      "small": ""
    }
  }, [_vm._v("\n        fa-solid fa-phone-arrow-down-left\n      ")]) : _c(VIcon["a" /* default */], {
    staticClass: "direction-icon",
    class: {
      'text-danger': _vm.call.accepts == 0,
      'text-success': _vm.call.accepts == 1
    },
    attrs: {
      "small": ""
    }
  }, [_vm._v("\n        fa-solid fa-phone-arrow-up-right\n      ")])], 1), _vm._v(" "), _c('div', {
    staticClass: "call-info mt-2"
  }, [_c('div', {
    staticClass: "call-header"
  }, [_c('div', {
    staticClass: "call-details"
  }, [_c('div', {
    staticClass: "contact-name",
    style: "color: ".concat(((_vm$currentContact = _vm.currentContact) === null || _vm$currentContact === void 0 ? void 0 : _vm$currentContact.color) || '#ffffff')
  }, [_vm._v("\n            " + _vm._s(_vm.currentContact ? _vm.currentContact.display : _vm.call.num) + "\n          ")]), _vm._v(" "), _c('div', {
    staticClass: "call-time"
  }, [_c('app-timestamp', {
    attrs: {
      "stamp": _vm.call.time,
      "format": "short"
    }
  })], 1)]), _vm._v(" "), _c('div', {
    staticClass: "call-actions"
  }, [_c(VBtn["a" /* default */], {
    staticClass: "call-btn",
    attrs: {
      "icon": "",
      "small": "",
      "color": "success"
    },
    on: {
      "click": function click($event) {
        $event.stopPropagation();
        return _vm.$store.dispatch('nui/call', _vm.call.num);
      }
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-phone")])], 1), _vm._v(" "), _c('text-contact-actions', {
    attrs: {
      "phone": _vm.call.num,
      "contact": _vm.currentContact
    }
  })], 1)])])])]);
};
var text_call_rowvue_type_template_id_40c7c87a_scoped_true_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Text/text-call-row.vue?vue&type=template&id=40c7c87a&scoped=true

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// EXTERNAL MODULE: ./components/Common/app-avatar.vue + 4 modules
var app_avatar = __webpack_require__(189);

// EXTERNAL MODULE: ./components/Pages/Text/ContactFormMixin.js
var ContactFormMixin = __webpack_require__(1566);

// EXTERNAL MODULE: ./components/Pages/Text/text-contact-actions.vue + 4 modules
var text_contact_actions = __webpack_require__(1590);

// EXTERNAL MODULE: ./components/Common/app-btn.vue + 4 modules
var app_btn = __webpack_require__(495);

// EXTERNAL MODULE: ./components/Pages/Text/text-avatar.vue + 4 modules
var text_avatar = __webpack_require__(1636);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-call-row.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }







/* harmony default export */ var text_call_rowvue_type_script_lang_js = ({
  name: 'text-call-row',
  components: {
    TextAvatar: text_avatar["a" /* default */],
    AppBtn: app_btn["a" /* default */],
    TextContactActions: text_contact_actions["a" /* default */],
    AppAvatar: app_avatar["a" /* default */],
    AppTimestamp: app_timestamp["a" /* default */]
  },
  props: ['call'],
  mixins: [ContactFormMixin["a" /* default */]],
  data: function data() {
    return {};
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({
    currentContact: function currentContact() {
      return this.keyedContacts[this.call.num];
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    contacts: 'text/contacts',
    keyedContacts: 'text/keyedContacts'
  }))
});
// CONCATENATED MODULE: ./components/Pages/Text/text-call-row.vue?vue&type=script&lang=js
 /* harmony default export */ var Text_text_call_rowvue_type_script_lang_js = (text_call_rowvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Text/text-call-row.vue?vue&type=style&index=0&id=40c7c87a&prod&lang=scss&scoped=true
var text_call_rowvue_type_style_index_0_id_40c7c87a_prod_lang_scss_scoped_true = __webpack_require__(1922);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Text/text-call-row.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Text_text_call_rowvue_type_script_lang_js,
  text_call_rowvue_type_template_id_40c7c87a_scoped_true_render,
  text_call_rowvue_type_template_id_40c7c87a_scoped_true_staticRenderFns,
  false,
  null,
  "40c7c87a",
  null
  
)

/* harmony default export */ var text_call_row = (component.exports);
// EXTERNAL MODULE: ./components/Common/app-infinite-scroll.vue + 4 modules
var app_infinite_scroll = __webpack_require__(1638);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/call-log.vue?vue&type=script&lang=js













/* harmony default export */ var call_logvue_type_script_lang_js = ({
  data: function data() {
    return {
      isPlaying: false,
      phoneMenuOpen: false,
      callLogsLoading: false
    };
  },
  scrollToTop: true,
  components: {
    AppInfiniteScroll: app_infinite_scroll["a" /* default */],
    TextCallRow: text_call_row,
    AppCardItems: app_card_items["a" /* default */],
    TextCall: text_call["a" /* default */],
    TextNewContact: text_new_contact["a" /* default */],
    TextContact: text_contact["a" /* default */],
    AppCard: app_card["a" /* default */]
  },
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    compiledCallLogs: 'text/compiledCallLogs',
    phoneVibrate: 'system/phoneVibrate'
  }),
  methods: {
    togglePhoneMenu: function togglePhoneMenu() {
      // Event trigger method to toggle the phone menu
      this.phoneMenuOpen = !this.phoneMenuOpen;
    },
    openPhoneMenu: function openPhoneMenu() {
      // Public method to open menu via event trigger
      this.phoneMenuOpen = true;
    },
    closePhoneMenu: function closePhoneMenu() {
      // Public method to close menu via event trigger
      this.phoneMenuOpen = false;
    },
    shareNumberWithNearest: function shareNumberWithNearest() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return _this.$axios.$post('https://blrp_tablet/shareNumber', {
                phone: _this.user.phone
              });
            case 2:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    },
    // Event trigger method to open specific call menu
    openCallMenu: function openCallMenu(index) {
      var callRef = this.$refs["call-".concat(index)];
      if (callRef && callRef[0]) {
        var contactActionsComponent = callRef[0].$children.find(function (child) {
          return child.$options.name === 'text-contact-actions';
        });
        if (contactActionsComponent) {
          contactActionsComponent.openMenu();
        }
      }
    },
    // Handle infinite scroll load more
    onLoadMoreCallLogs: function onLoadMoreCallLogs(pagination) {
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              // Example of how to implement server-side pagination for call logs
              // Uncomment and modify for actual API integration

              /*
              this.callLogsLoading = true;
              try {
                const response = await this.$axios.$get('/api/call-logs', {
                  params: {
                    page: pagination.page,
                    limit: pagination.itemsPerPage,
                    offset: pagination.offset
                  }
                });
                 // Append new call logs to existing ones for server-side pagination
                this.$store.commit('text/APPEND_CALL_LOGS', response.callLogs);
              } catch (error) {
                console.error('Failed to load more call logs:', error);
              } finally {
                this.callLogsLoading = false;
              }
              */

              // For now, just log the pagination info
              console.log('Loading more call logs:', pagination);
            case 1:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    },
    toggleRingtone: function toggleRingtone() {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              if (_this2.isPlaying) {
                // Stop ringtone
                _this2.$axios.$post('https://blrp_phone/stopTestAudioPlayback');
              } else {
                // Play ringtone using user's set ringtone
                _this2.$axios.$post('https://blrp_phone/startTestAudioPlayback');
              }
              _this2.isPlaying = !_this2.isPlaying;
            case 2:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }))();
    }
  }
});
// CONCATENATED MODULE: ./pages/phone/call-log.vue?vue&type=script&lang=js
 /* harmony default export */ var phone_call_logvue_type_script_lang_js = (call_logvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/phone/call-log.vue?vue&type=style&index=0&id=1ce1995a&prod&lang=scss&scoped=true
var call_logvue_type_style_index_0_id_1ce1995a_prod_lang_scss_scoped_true = __webpack_require__(1924);

// CONCATENATED MODULE: ./pages/phone/call-log.vue






/* normalize component */

var call_log_component = Object(componentNormalizer["a" /* default */])(
  phone_call_logvue_type_script_lang_js,
  call_logvue_type_template_id_1ce1995a_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "1ce1995a",
  null
  
)

/* harmony default export */ var call_log = __webpack_exports__["default"] = (call_log_component.exports);

/***/ })

}]);