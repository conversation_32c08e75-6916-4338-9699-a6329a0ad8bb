(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[181],{

/***/ 1728:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1921);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("481ac054", content, true, {"sourceMap":false});

/***/ }),

/***/ 1920:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_active_vue_vue_type_style_index_0_id_14f6fcf3_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1728);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_active_vue_vue_type_style_index_0_id_14f6fcf3_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_active_vue_vue_type_style_index_0_id_14f6fcf3_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1921:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".active-call-container[data-v-14f6fcf3]{background:linear-gradient(180deg,#000,#111);display:flex;flex-direction:column;height:100%;padding:20px}.call-info[data-v-14f6fcf3]{align-items:center;display:flex;flex:1;flex-direction:column;justify-content:center}.contact-info h2[data-v-14f6fcf3]{color:#fff;font-size:24px;margin-bottom:10px}.call-duration[data-v-14f6fcf3]{color:#ccc;font-size:16px}.call-controls[data-v-14f6fcf3]{bottom:80px;left:50%;max-width:300px;position:absolute;transform:translateX(-50%);width:100%}.controls-grid[data-v-14f6fcf3]{align-items:center;display:flex;gap:40px;justify-content:space-around}.speaker-button-container[data-v-14f6fcf3]{align-items:center;cursor:pointer;display:flex;flex-direction:column;transition:transform .2s ease}.speaker-button-container[data-v-14f6fcf3]:hover{transform:scale(1.05)}.speaker-button[data-v-14f6fcf3]{align-items:center;background:hsla(0,0%,100%,.1);border:3px solid hsla(0,0%,100%,.3);border-radius:50%;box-shadow:0 4px 15px rgba(0,0,0,.3);display:flex;height:70px;justify-content:center;transition:all .3s ease;width:70px}.speaker-button.speaker-enabled[data-v-14f6fcf3]{animation:speakerPulse-14f6fcf3 2s infinite;background:rgba(76,175,80,.2);border:3px solid #4caf50;box-shadow:0 0 20px rgba(76,175,80,.4)}.speaker-label[data-v-14f6fcf3]{color:#fff;font-size:12px;font-weight:500;margin-top:8px;text-align:center}.end-call-button-container[data-v-14f6fcf3]{align-items:center;cursor:pointer;display:flex;flex-direction:column;transition:transform .2s ease}.end-call-button-container[data-v-14f6fcf3]:hover{transform:scale(1.05)}.end-call-button[data-v-14f6fcf3]{align-items:center;background:rgba(244,67,54,.8);border:3px solid #f44336;border-radius:50%;box-shadow:0 4px 15px rgba(244,67,54,.4);display:flex;height:70px;justify-content:center;transition:all .3s ease;width:70px}.end-call-button[data-v-14f6fcf3]:hover{background:#f44336;box-shadow:0 6px 20px rgba(244,67,54,.6)}.end-call-label[data-v-14f6fcf3]{color:#fff;font-size:12px;font-weight:500;margin-top:8px;text-align:center}@keyframes speakerPulse-14f6fcf3{0%{box-shadow:0 0 20px rgba(76,175,80,.4)}50%{box-shadow:0 0 30px rgba(76,175,80,.8)}to{box-shadow:0 0 20px rgba(76,175,80,.4)}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2169:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/call/active.vue?vue&type=template&id=14f6fcf3&scoped=true


var activevue_type_template_id_14f6fcf3_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "active-call-container"
  }, [_c('div', {
    staticClass: "call-info text-center"
  }, [_c('div', {
    staticClass: "contact-info mt-5"
  }, [_vm.activeCall && _vm.activeCall.contact ? _c('h2', [_vm._v("\n        " + _vm._s(_vm.activeCall.contact.display) + "\n      ")]) : _vm.activeCall ? _c('h2', [_vm._v("\n        " + _vm._s(_vm.activeCall.phone) + "\n      ")]) : _c('h2', [_vm._v("\n        No Active Call\n      ")]), _vm._v(" "), _vm.activeCall ? _c('div', {
    staticClass: "call-duration mt-3"
  }, [_c('i', {
    staticClass: "fa-solid fa-clock mr-2"
  }), _vm._v("\n        " + _vm._s(_vm.activeCall.elapsed_time || '00:00') + "\n      ")]) : _vm._e()])]), _vm._v(" "), _vm.activeCall ? _c('div', {
    staticClass: "call-controls"
  }, [_c('div', {
    staticClass: "controls-grid"
  }, [_c('div', {
    staticClass: "speaker-button-container",
    on: {
      "click": _vm.toggleSpeaker
    }
  }, [_c('div', {
    staticClass: "speaker-button",
    class: {
      'speaker-enabled': _vm.speakerEnabled
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "size": "28",
      "color": "white"
    }
  }, [_vm._v("\n            " + _vm._s(_vm.speakerEnabled ? 'fa-solid fa-volume-high' : 'fa-solid fa-volume-off') + "\n          ")])], 1), _vm._v(" "), _c('div', {
    staticClass: "speaker-label"
  }, [_vm._v("\n          " + _vm._s(_vm.speakerEnabled ? 'Speaker On' : 'Speaker Off') + "\n        ")])]), _vm._v(" "), _c('div', {
    staticClass: "end-call-button-container",
    on: {
      "click": _vm.endCall
    }
  }, [_c('div', {
    staticClass: "end-call-button"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "size": "28",
      "color": "white"
    }
  }, [_vm._v("fa-solid fa-phone-slash")])], 1), _vm._v(" "), _c('div', {
    staticClass: "end-call-label"
  }, [_vm._v("\n          End Call\n        ")])])])]) : _vm._e()]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/phone/call/active.vue?vue&type=template&id=14f6fcf3&scoped=true

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/call/active.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/* harmony default export */ var activevue_type_script_lang_js = ({
  name: 'active-call',
  components: {},
  props: [],
  data: function data() {
    return {};
  },
  created: function created() {
    var _this = this;
    window.onkeydown = function (e) {
      var code = e.keyCode ? e.keyCode : e.which;
      if (code === 13) {// enter
        // Could add functionality here
      }
      if (code === 8) {
        // backspace
        _this.endCall();
      }
    };
  },
  methods: {
    toggleSpeaker: function toggleSpeaker() {
      this.$store.dispatch('system/toggleSpeaker');
    },
    endCall: function endCall() {
      this.$store.dispatch('text/cancelCall');
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    activeCall: 'text/activeCall',
    speakerEnabled: 'system/speakerEnabled'
  }))
});
// CONCATENATED MODULE: ./pages/phone/call/active.vue?vue&type=script&lang=js
 /* harmony default export */ var call_activevue_type_script_lang_js = (activevue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/phone/call/active.vue?vue&type=style&index=0&id=14f6fcf3&prod&lang=scss&scoped=true
var activevue_type_style_index_0_id_14f6fcf3_prod_lang_scss_scoped_true = __webpack_require__(1920);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/phone/call/active.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  call_activevue_type_script_lang_js,
  activevue_type_template_id_14f6fcf3_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "14f6fcf3",
  null
  
)

/* harmony default export */ var active = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);