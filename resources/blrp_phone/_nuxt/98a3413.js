(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[85],{

/***/ 1563:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(7);
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(9);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(14);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(15);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(8);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(11);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(0);
/* harmony import */ var _mixins_measurable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(188);
/* harmony import */ var _mixins_toggleable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(100);
/* harmony import */ var _directives_intersect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(175);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(16);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(1);








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
// Mixins

 // Directives

 // Utilities



/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"])(_mixins_measurable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], _mixins_toggleable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"]).extend({
  name: 'VLazy',
  directives: {
    intersect: _directives_intersect__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"]
  },
  props: {
    options: {
      type: Object,
      // For more information on types, navigate to:
      // https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API
      default: function _default() {
        return {
          root: undefined,
          rootMargin: undefined,
          threshold: undefined
        };
      }
    },
    tag: {
      type: String,
      default: 'div'
    },
    transition: {
      type: String,
      default: 'fade-transition'
    }
  },
  computed: {
    styles: function styles() {
      return _objectSpread({}, this.measurableStyles);
    }
  },
  methods: {
    genContent: function genContent() {
      var children = this.isActive && Object(_util_helpers__WEBPACK_IMPORTED_MODULE_12__[/* getSlot */ "s"])(this);
      return this.transition ? this.$createElement('transition', {
        props: {
          name: this.transition
        }
      }, children) : children;
    },
    onObserve: function onObserve(entries, observer, isIntersecting) {
      if (this.isActive) return;
      this.isActive = isIntersecting;
    }
  },
  render: function render(h) {
    return h(this.tag, {
      staticClass: 'v-lazy',
      attrs: this.$attrs,
      directives: [{
        name: 'intersect',
        value: {
          handler: this.onObserve,
          options: this.options
        }
      }],
      on: this.$listeners,
      style: this.styles
    }, [this.genContent()]);
  }
}));

/***/ }),

/***/ 1689:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1783);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("a6cf4db6", content, true, {"sourceMap":false});

/***/ }),

/***/ 1690:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1785);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("b3086e78", content, true, {"sourceMap":false});

/***/ }),

/***/ 1691:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1787);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("06ce18ce", content, true, {"sourceMap":false});

/***/ }),

/***/ 1692:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1789);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("c5b7d5ae", content, true, {"sourceMap":false});

/***/ }),

/***/ 1782:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DatabaseResultPlate_vue_vue_type_style_index_0_id_13d65239_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1689);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DatabaseResultPlate_vue_vue_type_style_index_0_id_13d65239_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DatabaseResultPlate_vue_vue_type_style_index_0_id_13d65239_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1783:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".vehicle-return-field[data-v-13d65239]{display:inline-block;font-family:monospace;width:155px!important}.vehicle-return-data[data-v-13d65239]{font-family:monospace}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1784:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DatabaseResultPlate_vue_vue_type_style_index_1_id_13d65239_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1690);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DatabaseResultPlate_vue_vue_type_style_index_1_id_13d65239_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DatabaseResultPlate_vue_vue_type_style_index_1_id_13d65239_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1785:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".easy-charge-add:hover{color:#fff!important;cursor:pointer}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1786:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DatabaseResultDL_vue_vue_type_style_index_0_id_c151fbfc_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1691);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DatabaseResultDL_vue_vue_type_style_index_0_id_c151fbfc_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DatabaseResultDL_vue_vue_type_style_index_0_id_c151fbfc_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1787:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".vehicle-return-field[data-v-c151fbfc]{display:inline-block;font-family:monospace;width:150px!important}.vehicle-return-data[data-v-c151fbfc]{font-family:monospace}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1788:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DatabaseResultFirearm_vue_vue_type_style_index_0_id_3c5ce2c3_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1692);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DatabaseResultFirearm_vue_vue_type_style_index_0_id_3c5ce2c3_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DatabaseResultFirearm_vue_vue_type_style_index_0_id_3c5ce2c3_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1789:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".vehicle-return-field[data-v-3c5ce2c3]{display:inline-block;font-family:monospace;width:150px!important}.vehicle-return-data[data-v-3c5ce2c3]{font-family:monospace}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2024:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAppBar/VAppBar.js + 3 modules
var VAppBar = __webpack_require__(1548);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanel.js
var VExpansionPanel = __webpack_require__(1528);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelContent.js
var VExpansionPanelContent = __webpack_require__(1530);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelHeader.js
var VExpansionPanelHeader = __webpack_require__(1529);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanels.js
var VExpansionPanels = __webpack_require__(1527);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(1533);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VLazy/VLazy.js
var VLazy = __webpack_require__(1563);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VSpacer.js
var VSpacer = __webpack_require__(1523);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/database.vue?vue&type=template&id=59ab10f8














var databasevue_type_template_id_59ab10f8_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "p-3"
  }, [_c(VAppBar["a" /* default */], [_c(VForm["a" /* default */], {
    staticClass: "mr-3",
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm.runSearch.apply(null, arguments);
      }
    }
  }, [_c(VTextField["a" /* default */], {
    staticClass: "mr-4",
    staticStyle: {
      "width": "250px"
    },
    attrs: {
      "dense": "",
      "solo": "",
      "hide-details": "",
      "filled": "",
      "id": "searchTerm",
      "label": "  Search by license plate"
    },
    on: {
      "keyup": _vm.validateInputs
    },
    model: {
      value: _vm.searchTermLicense,
      callback: function callback($$v) {
        _vm.searchTermLicense = $$v;
      },
      expression: "searchTermLicense"
    }
  })], 1), _vm._v(" "), _c(VForm["a" /* default */], {
    staticClass: "mr-3",
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm.runSearch.apply(null, arguments);
      }
    }
  }, [_c(VTextField["a" /* default */], {
    staticClass: "mr-4",
    staticStyle: {
      "width": "250px"
    },
    attrs: {
      "dense": "",
      "solo": "",
      "hide-details": "",
      "filled": "",
      "id": "searchTermDL",
      "label": "  Search by DL #"
    },
    on: {
      "keyup": _vm.validateInputs
    },
    model: {
      value: _vm.searchTermDL,
      callback: function callback($$v) {
        _vm.searchTermDL = $$v;
      },
      expression: "searchTermDL"
    }
  })], 1), _vm._v(" "), _c(VForm["a" /* default */], {
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm.runSearch.apply(null, arguments);
      }
    }
  }, [_c(VTextField["a" /* default */], {
    staticClass: "mr-4",
    staticStyle: {
      "width": "250px"
    },
    attrs: {
      "dense": "",
      "solo": "",
      "hide-details": "",
      "filled": "",
      "id": "searchTermFirearm",
      "label": "  Search Firearm REG #"
    },
    on: {
      "keyup": _vm.validateInputs
    },
    model: {
      value: _vm.searchTermFirearm,
      callback: function callback($$v) {
        _vm.searchTermFirearm = $$v;
      },
      expression: "searchTermFirearm"
    }
  })], 1), _vm._v(" "), _c(VSpacer["a" /* default */]), _vm._v(" "), _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "mr-2",
    attrs: {
      "color": "primary"
    },
    on: {
      "click": _vm.runSearch
    }
  }, [_vm._v("Search")]), _vm._v(" "), _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    on: {
      "click": _vm.clearQuery
    }
  }, [_vm._v("Clear")])], 1), _vm._v(" "), _c(VExpansionPanels["a" /* default */], {
    model: {
      value: _vm.selected,
      callback: function callback($$v) {
        _vm.selected = $$v;
      },
      expression: "selected"
    }
  }, _vm._l(_vm.dbResults, function (result) {
    return _c(VExpansionPanel["a" /* default */], {
      key: result.id
    }, [_c(VExpansionPanelHeader["a" /* default */], [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
      attrs: {
        "cols": "3"
      }
    }, [_vm._v("\n            " + _vm._s(result.packet.title) + "\n\n            "), _c('div', {
      staticClass: "mt-2"
    }, [_vm._v("\n              " + _vm._s(result.packet.term) + "\n            ")])]), _vm._v(" "), result.person ? _c(VCol["a" /* default */], {
      staticClass: "text-muted"
    }, [result.person.id === _vm.user.id ? _c('span', [_vm._v("\n              " + _vm._s(result.person.name) + "\n              "), _c('b', {
      staticClass: "ml-1 mr-1 text-warning"
    }, [_vm._v("(YOU)")])]) : _c('span', [_vm._v("\n              --\n            ")]), _vm._v(" "), _c('app-timestamp', {
      attrs: {
        "stamp": result.created_at
      }
    }), _vm._v(" ago\n\n            "), _vm.location ? _c('div', {
      staticClass: "mt-2"
    }, [_c('i', {
      staticClass: "fa-regular fa-location-arrow mr-2"
    }), _vm._v("\n              " + _vm._s(result.location.name) + "\n            ")]) : _vm._e()], 1) : _vm._e()], 1)], 1), _vm._v(" "), _c(VLazy["a" /* default */], [_c(VExpansionPanelContent["a" /* default */], [result.packet.resultsFound && result.component ? _c('div', [_c(result.component, {
      tag: "component",
      attrs: {
        "data": result.packet.data
      }
    })], 1) : _c('div', [_vm._v("\n            No Results Found\n          ")])])], 1)], 1);
  }), 1), _vm._v(" "), _c('div', _vm._l(_vm.boloIncidents, function (incident) {
    return _c('crudy-table-items-component-police-incident', {
      key: incident.id,
      attrs: {
        "item": incident
      }
    });
  }), 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/database.vue?vue&type=template&id=59ab10f8

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTab.js
var VTab = __webpack_require__(1534);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabItem.js + 1 modules
var VTabItem = __webpack_require__(1554);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabs.js + 3 modules
var VTabs = __webpack_require__(1550);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabsItems.js + 1 modules
var VTabsItems = __webpack_require__(892);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/DatabaseResultPlate.vue?vue&type=template&id=13d65239&scoped=true






var DatabaseResultPlatevue_type_template_id_13d65239_scoped_true_render = function render() {
  var _vm$data$properties, _vm$data$properties2, _vm$data$properties3, _vm$data$properties4, _vm$data$properties5;
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('br'), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("License:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_vm._v(_vm._s(_vm.data.registration))])]), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Model:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_vm._v(_vm._s(_vm.data.vehicle.toUpperCase()))])]), _vm._v(" "), _vm.data.vinscratch == true ? _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Vehicle status:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data",
    staticStyle: {
      "font-weight": "bold",
      "color": "red"
    }
  }, [_vm._v("STOLEN VEHICLE")])]) : _vm._e(), _vm._v(" "), _vm.data.stolen == true ? _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Vehicle status:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data",
    staticStyle: {
      "font-weight": "bold",
      "color": "red"
    }
  }, [_vm._v("STOLEN VEHICLE")])]) : _vm._e(), _vm._v(" "), _vm.data.vinscratch != true ? _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Owner name:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_c('NuxtLink', {
    attrs: {
      "to": "/cad/persons/0/?character_number=".concat(_vm.data.character_id)
    }
  }, [_c('span', {
    staticClass: "vehicle-return-data text-primary"
  }, [_vm._v(_vm._s(_vm.data.firstname) + "\n          " + _vm._s(_vm.data.lastname))])])], 1)]) : _vm._e(), _vm._v(" "), _vm.data.vinscratch != true ? _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Owner phone:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_c('app-person-call', {
    attrs: {
      "phone": _vm.data.phone
    }
  })], 1)]) : _vm._e(), _vm._v(" "), _vm.data.vinscratch != true ? _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Owner DL:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_vm._v(_vm._s(_vm.data.dlnumber))])]) : _vm._e(), _vm._v(" "), _vm.data.vinscratch != true ? _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Vehicle Notes:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_vm._v(_vm._s((_vm$data$properties = _vm.data.properties) !== null && _vm$data$properties !== void 0 && _vm$data$properties.notes ? (_vm$data$properties2 = _vm.data.properties) === null || _vm$data$properties2 === void 0 ? void 0 : _vm$data$properties2.notes : 'None'))])]) : _vm._e(), _vm._v(" "), _vm.data.vinscratch != true ? _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Owner Wanted:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_vm._v(_vm._s((_vm$data$properties3 = _vm.data.properties) !== null && _vm$data$properties3 !== void 0 && _vm$data$properties3.wanted ? 'Yes' : 'No'))])]) : _vm._e(), _vm._v(" "), _vm.data.vinscratch != true ? _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Owner Parole:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_vm._v(_vm._s((_vm$data$properties4 = _vm.data.properties) !== null && _vm$data$properties4 !== void 0 && _vm$data$properties4.parole ? ((_vm$data$properties5 = _vm.data.properties) === null || _vm$data$properties5 === void 0 ? void 0 : _vm$data$properties5.parole) + ' months' : 'No'))])]) : _vm._e(), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Primary color:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data",
    style: _vm.primaryStyle
  })]), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Secondary color:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data",
    style: _vm.secondaryStyle
  })]), _vm._v(" "), _vm.data.vinscratch != true ? _c('div', [_c(VTabs["a" /* default */], {
    attrs: {
      "background-color": "transparent",
      "color": "primary"
    },
    model: {
      value: _vm.activeTab,
      callback: function callback($$v) {
        _vm.activeTab = $$v;
      },
      expression: "activeTab"
    }
  }, [_c(VTab["a" /* default */], [_vm._v("\n        🚀 Easy Traffic Stop\n      ")]), _vm._v(" "), _c(VTab["a" /* default */], [_vm._v("\n        🎫 Issue Ticket\n      ")])], 1), _vm._v(" "), _c(VTabsItems["a" /* default */], {
    model: {
      value: _vm.activeTab,
      callback: function callback($$v) {
        _vm.activeTab = $$v;
      },
      expression: "activeTab"
    }
  }, [_c(VTabItem["a" /* default */], [_c('div', {
    staticClass: "mt-4"
  }, [_c('easy-add-charges', {
    attrs: {
      "character-number": _vm.data.character_id,
      "plate": _vm.data.registration
    }
  })], 1)]), _vm._v(" "), _c(VTabItem["a" /* default */], [_c('div', {
    staticClass: "mt-4"
  }, [_c('easy-issue-ticket', {
    attrs: {
      "character-number": _vm.data.character_id,
      "plate": _vm.data.registration
    }
  })], 1)])], 1)], 1) : _vm._e()], 1);
};
var DatabaseResultPlatevue_type_template_id_13d65239_scoped_true_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/DatabaseResultPlate.vue?vue&type=template&id=13d65239&scoped=true

// EXTERNAL MODULE: ./components/Common/app-person-call.vue + 9 modules
var app_person_call = __webpack_require__(502);

// EXTERNAL MODULE: ./components/Pages/Police/easy-add-charges.vue + 4 modules
var easy_add_charges = __webpack_require__(521);

// EXTERNAL MODULE: ./components/Pages/Police/easy-issue-ticket.vue + 4 modules
var easy_issue_ticket = __webpack_require__(833);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/DatabaseResultPlate.vue?vue&type=script&lang=js




/* harmony default export */ var DatabaseResultPlatevue_type_script_lang_js = ({
  name: 'DatabaseResultPlate',
  components: {
    EasyIssueTicket: easy_issue_ticket["a" /* default */],
    EasyAddCharges: easy_add_charges["a" /* default */],
    AppPersonCall: app_person_call["a" /* default */]
  },
  props: ['data'],
  data: function data() {
    return {
      activeTab: 0,
      primaryStyle: {
        display: 'inline-block',
        width: '100px !important',
        height: '19px !important',
        border: '1px solid #FFF',
        backgroundColor: this.data.colour_code
      },
      secondaryStyle: {
        display: 'inline-block',
        width: '100px !important',
        height: '19px !important',
        border: '1px solid #FFF',
        backgroundColor: this.data.scolour_code
      }
    };
  },
  mounted: function mounted() {},
  destroyed: function destroyed() {},
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    locationName: 'location/name'
  })
});
// CONCATENATED MODULE: ./components/Pages/Police/DatabaseResultPlate.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_DatabaseResultPlatevue_type_script_lang_js = (DatabaseResultPlatevue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Police/DatabaseResultPlate.vue?vue&type=style&index=0&id=13d65239&prod&scoped=true&lang=css
var DatabaseResultPlatevue_type_style_index_0_id_13d65239_prod_scoped_true_lang_css = __webpack_require__(1782);

// EXTERNAL MODULE: ./components/Pages/Police/DatabaseResultPlate.vue?vue&type=style&index=1&id=13d65239&prod&lang=scss
var DatabaseResultPlatevue_type_style_index_1_id_13d65239_prod_lang_scss = __webpack_require__(1784);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Police/DatabaseResultPlate.vue







/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Police_DatabaseResultPlatevue_type_script_lang_js,
  DatabaseResultPlatevue_type_template_id_13d65239_scoped_true_render,
  DatabaseResultPlatevue_type_template_id_13d65239_scoped_true_staticRenderFns,
  false,
  null,
  "13d65239",
  null
  
)

/* harmony default export */ var DatabaseResultPlate = (component.exports);
// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/DatabaseResultDL.vue?vue&type=template&id=c151fbfc&scoped=true


var DatabaseResultDLvue_type_template_id_c151fbfc_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.data.characterData ? _c('div', [_c('br'), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("DL Number:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_vm._v(_vm._s(_vm.data.characterData.dlnumber))])]), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Name:")]), _vm._v(" "), _c('NuxtLink', {
    attrs: {
      "to": "/cad/persons/0/?character_number=".concat(_vm.data.characterData.id)
    }
  }, [_c('span', {
    staticClass: "vehicle-return-data text-primary"
  }, [_vm._v(_vm._s(_vm.data.characterData.firstname) + " " + _vm._s(_vm.data.characterData.lastname))])])], 1), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Phone:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_c('app-person-call', {
    attrs: {
      "phone": _vm.data.characterData.phone
    }
  })], 1)]), _vm._v(" "), _c('br'), _vm._v(" "), _vm._l(_vm.data.licensesData, function (licenseStatus, licenseType) {
    return _c(VCol["a" /* default */], {
      key: licenseType
    }, [_c('span', {
      staticClass: "vehicle-return-field"
    }, [_vm._v(_vm._s(licenseType))]), _vm._v(" "), _c('span', {
      staticClass: "vehicle-return-data"
    }, [_vm._v(_vm._s(licenseStatus))])]);
  }), _vm._v(" "), _c('br'), _vm._v(" "), _vm._l(_vm.data.addressesData, function (address, index) {
    return _c(VCol["a" /* default */], {
      key: index
    }, [_c('span', {
      staticClass: "vehicle-return-field"
    }, [_vm._v("Address:")]), _vm._v(" "), _c('span', {
      staticClass: "vehicle-return-data"
    }, [_vm._v(_vm._s(address))])]);
  }), _vm._v(" "), _c('br'), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Registration")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_vm._v("Model")])]), _vm._v(" "), _vm._l(_vm.data.vehiclesData, function (vehicle, index) {
    return _vm.data ? _c(VCol["a" /* default */], {
      key: index
    }, [_c('span', {
      staticClass: "vehicle-return-field"
    }, [_vm._v(_vm._s(vehicle.registration))]), _vm._v(" "), _c('span', {
      staticClass: "vehicle-return-data"
    }, [_vm._v(_vm._s(vehicle.vehicle.toUpperCase()))])]) : _vm._e();
  }), _vm._v(" "), _c('easy-add-charges', {
    attrs: {
      "character-number": _vm.data.characterData.id
    }
  })], 2) : _c('div', [_vm._v("\n  " + _vm._s(_vm.data) + "\n")]);
};
var DatabaseResultDLvue_type_template_id_c151fbfc_scoped_true_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/DatabaseResultDL.vue?vue&type=template&id=c151fbfc&scoped=true

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/DatabaseResultDL.vue?vue&type=script&lang=js



/* harmony default export */ var DatabaseResultDLvue_type_script_lang_js = ({
  name: 'DatabaseResultDL',
  components: {
    EasyAddCharges: easy_add_charges["a" /* default */],
    AppPersonCall: app_person_call["a" /* default */]
  },
  props: ['data'],
  data: function data() {
    return {};
  },
  mounted: function mounted() {},
  destroyed: function destroyed() {},
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    locationName: 'location/name'
  })
});
// CONCATENATED MODULE: ./components/Pages/Police/DatabaseResultDL.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_DatabaseResultDLvue_type_script_lang_js = (DatabaseResultDLvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Police/DatabaseResultDL.vue?vue&type=style&index=0&id=c151fbfc&prod&scoped=true&lang=css
var DatabaseResultDLvue_type_style_index_0_id_c151fbfc_prod_scoped_true_lang_css = __webpack_require__(1786);

// CONCATENATED MODULE: ./components/Pages/Police/DatabaseResultDL.vue






/* normalize component */

var DatabaseResultDL_component = Object(componentNormalizer["a" /* default */])(
  Police_DatabaseResultDLvue_type_script_lang_js,
  DatabaseResultDLvue_type_template_id_c151fbfc_scoped_true_render,
  DatabaseResultDLvue_type_template_id_c151fbfc_scoped_true_staticRenderFns,
  false,
  null,
  "c151fbfc",
  null
  
)

/* harmony default export */ var DatabaseResultDL = (DatabaseResultDL_component.exports);
// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/DatabaseResultFirearm.vue?vue&type=template&id=3c5ce2c3&scoped=true


var DatabaseResultFirearmvue_type_template_id_3c5ce2c3_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('br'), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Registration:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_vm._v(_vm._s(_vm.data.registration))])]), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("DL Number:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_vm._v(_vm._s(_vm.data.dlnumber))])]), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Name:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_vm._v(_vm._s(_vm.data.firstname) + " " + _vm._s(_vm.data.lastname))])]), _vm._v(" "), _c(VCol["a" /* default */], [_c('span', {
    staticClass: "vehicle-return-field"
  }, [_vm._v("Phone:")]), _vm._v(" "), _c('span', {
    staticClass: "vehicle-return-data"
  }, [_c('app-person-call', {
    attrs: {
      "phone": _vm.data.phone
    }
  })], 1)])], 1);
};
var DatabaseResultFirearmvue_type_template_id_3c5ce2c3_scoped_true_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/DatabaseResultFirearm.vue?vue&type=template&id=3c5ce2c3&scoped=true

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/DatabaseResultFirearm.vue?vue&type=script&lang=js


/* harmony default export */ var DatabaseResultFirearmvue_type_script_lang_js = ({
  name: 'DatabaseResultFirearm',
  components: {
    AppPersonCall: app_person_call["a" /* default */]
  },
  props: ['data'],
  data: function data() {
    return {};
  },
  mounted: function mounted() {},
  destroyed: function destroyed() {},
  methods: {},
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  })
});
// CONCATENATED MODULE: ./components/Pages/Police/DatabaseResultFirearm.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_DatabaseResultFirearmvue_type_script_lang_js = (DatabaseResultFirearmvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Police/DatabaseResultFirearm.vue?vue&type=style&index=0&id=3c5ce2c3&prod&scoped=true&lang=css
var DatabaseResultFirearmvue_type_style_index_0_id_3c5ce2c3_prod_scoped_true_lang_css = __webpack_require__(1788);

// CONCATENATED MODULE: ./components/Pages/Police/DatabaseResultFirearm.vue






/* normalize component */

var DatabaseResultFirearm_component = Object(componentNormalizer["a" /* default */])(
  Police_DatabaseResultFirearmvue_type_script_lang_js,
  DatabaseResultFirearmvue_type_template_id_3c5ce2c3_scoped_true_render,
  DatabaseResultFirearmvue_type_template_id_3c5ce2c3_scoped_true_staticRenderFns,
  false,
  null,
  "3c5ce2c3",
  null
  
)

/* harmony default export */ var DatabaseResultFirearm = (DatabaseResultFirearm_component.exports);
// EXTERNAL MODULE: ./config/colorTranslations.js
var colorTranslations = __webpack_require__(264);

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// EXTERNAL MODULE: ./utils/api.ts
var api = __webpack_require__(110);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/database.vue?vue&type=script&lang=ts


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }



























/* harmony default export */ var databasevue_type_script_lang_ts = ({
  name: 'DatabaseQuery',
  components: {
    AppTimestamp: app_timestamp["a" /* default */],
    CrudyTable: crudy_table["a" /* default */],
    AppPage: AppPage["a" /* default */],
    DatabaseResultPlate: DatabaseResultPlate,
    DatabaseResultDL: DatabaseResultDL,
    DatabaseResultFirearm: DatabaseResultFirearm
  },
  data: function data() {
    return {
      generalSearchTerm: null,
      searchTermLicense: null,
      searchTermDL: null,
      searchTermFirearm: null,
      selected: 0,
      boloIncidents: []
    };
  },
  mounted: function mounted() {
    var _this = this;
    if (this.$route.query.license) {
      this.searchTermDL = this.$route.query.license;
      this.runSearch();
    }
    window["$events"].$on('vehicle:data_callback_dl', function (data) {
      if (data.character === false) {
        _this.addResult({
          type: 'vehicle:data_callback_dl',
          // component: DatabaseResultDL,
          title: 'Driver License #',
          term: _this.searchTermDL,
          resultsFound: false,
          icon: 'fa-solid fa-id-card',
          data: null
        });
      } else {
        _this.$axios.$get("/police/characterAddresses/".concat(data.character.id)).then(function (response) {
          var realData = {
            addressesData: {},
            characterData: {},
            licensesData: {},
            vehiclesData: {}
          };
          realData.addressesData = response.data;
          realData.characterData = data.character;
          realData.licensesData = data.licenses;
          realData.vehiclesData = data.vehicles;
          _this.addResult({
            type: 'vehicle:data_callback_dl',
            // component: DatabaseResultDL,
            title: 'Driver License #',
            term: _this.searchTermDL,
            resultsFound: true,
            icon: 'fa-solid fa-id-card',
            data: realData
          });
        });
      }
    });
    window["$events"].$on('vehicle:data_callback', /*#__PURE__*/function () {
      var _ref = Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee(data) {
        var _a;
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              console.log('vehicle:data_callback', data);
              data = data === null || data === void 0 ? void 0 : data.vehicle;
              if (!(data === null || data === void 0 ? void 0 : data.vehicle)) {
                _context.next = 11;
                break;
              }
              data.colour_code = colorTranslations["a" /* default */][data.colour];
              data.scolour_code = colorTranslations["a" /* default */][data.scolour];
              if (data.colour_code === undefined) {
                data.colour_code = '#FFF';
              }
              if (data.scolour_code === undefined) {
                data.scolour_code = '#FFF';
              }
              if (!(!data.vinscratch && data.characterNumber)) {
                _context.next = 11;
                break;
              }
              _context.next = 10;
              return api["a" /* $axios */].$get("/police/db-query-search/".concat(data.id, "/").concat(data.characterNumber));
            case 10:
              data.properties = _context.sent;
            case 11:
              _this.addResult({
                type: 'vehicle:data_callback',
                // component: DatabaseResultPlate,
                title: 'Vehicle Plate',
                term: _this.searchTermLicense,
                resultsFound: (_a = data === null || data === void 0 ? void 0 : data.vehicle) !== null && _a !== void 0 ? _a : false,
                icon: 'fa-solid fa-car',
                data: data !== null && data !== void 0 ? data : {}
              });
            case 12:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function (_x) {
        return _ref.apply(this, arguments);
      };
    }());
    window["$events"].$on('vehicle:data_callback_weapon', function (data) {
      var _a;
      _this.addResult({
        type: 'vehicle:data_callback_weapon',
        // component: DatabaseResultFirearm,
        title: 'Firearm Registration #',
        term: _this.searchTermFirearm,
        resultsFound: (_a = data.weapon_data) !== null && _a !== void 0 ? _a : false,
        icon: 'fa-solid fa-scanner-gun',
        data: data.weapon_data
      });
    });
    // @ts-ignore
    window.$events.$on('blrp_tablet:client:doPlateSearchInternal', function (plate) {
      _this.searchTermLicense = plate;
      _this.runSearch();
    });
    if (this.pendingPlateSearch) {
      this.searchTermLicense = this.pendingPlateSearch;
      this.$store.commit('system/CLEAR_PENDING_SEARCH_PLATE');
      this.runSearch();
    }
    this.loadResults();
  },
  destroyed: function destroyed() {
    window["$events"].$off('vehicle:data_callback_dl');
    window["$events"].$off('vehicle:data_callback');
    window["$events"].$off('vehicle:data_callback_weapon');
    window["$events"].$off('blrp_tablet:client:doPlateSearchInternal');
  },
  methods: {
    runSearch: function runSearch() {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              if (!(_this2.searchTermDL && _this2.searchTermDL !== '')) {
                _context2.next = 6;
                break;
              }
              _this2.generalSearchTerm = _this2.searchTermLicense;
              _this2.$store.dispatch('nui/_api', {
                endpoint: 'getVehicleDataByDL',
                params: {
                  term: _this2.searchTermDL
                }
              });
              _context2.next = 5;
              return api["a" /* $axios */].$get("/police/bolo-search/".concat(_this2.searchTermDL));
            case 5:
              _this2.boloIncidents = _context2.sent;
            case 6:
              if (!(_this2.searchTermLicense && _this2.searchTermLicense !== '')) {
                _context2.next = 11;
                break;
              }
              _this2.$store.dispatch('nui/_api', {
                endpoint: 'getVehicleData',
                params: {
                  term: _this2.searchTermLicense
                }
              });
              _context2.next = 10;
              return api["a" /* $axios */].$get("/police/bolo-search/".concat(_this2.searchTermLicense));
            case 10:
              _this2.boloIncidents = _context2.sent;
            case 11:
              if (_this2.searchTermFirearm && _this2.searchTermFirearm !== '') {
                _this2.generalSearchTerm = _this2.searchTermLicense;
                _this2.$store.dispatch('nui/_api', {
                  endpoint: 'getWeaponInformation',
                  params: {
                    term: _this2.searchTermFirearm
                  }
                });
              }
              _this2.clearQuery();
            case 13:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    },
    clearQuery: function clearQuery() {
      if (this.$refs.queryResult) {
        this.$refs.queryResult.innerHTML = '';
      }
      this.searchTermLicense = '';
      this.searchTermDL = '';
      this.searchTermFirearm = '';
    },
    validateInputs: function validateInputs(event) {
      var _this3 = this;
      ['searchTerm', 'searchTermDL', 'searchTermFirearm'].forEach(function (id) {
        if (event.target.id != id) {
          _this3[id] = '';
        }
      });
    },
    addResult: function addResult(properties, syncResults) {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        var syncedProperties;
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              if (!properties.resultsFound) {
                _context3.next = 6;
                break;
              }
              _context3.next = 3;
              return _this4.$axios.$post('/police/query/add-log', {
                type: properties.type,
                packet: properties,
                location_name: _this4.location
              });
            case 3:
              syncedProperties = _context3.sent;
              _context3.next = 8;
              break;
            case 6:
              _this4.$toast.error('No results found for search');
              return _context3.abrupt("return");
            case 8:
              _this4.searchTermFirearm = null;
              _this4.searchTermLicense = null;
              _this4.searchTermDL = null;
              _context3.next = 13;
              return _this4.loadResults();
            case 13:
              _this4.$nextTick(function () {
                _this4.selected = _this4.dbResults.length - 1;
                _this4.$forceUpdate();
              });
            case 14:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }))();
    },
    loadResults: function loadResults() {
      var _this5 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee4() {
        var logs, _iterator, _step, log;
        return regeneratorRuntime.wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              _context4.next = 2;
              return _this5.$axios.$get('/police/query/logs');
            case 2:
              logs = _context4.sent;
              _iterator = _createForOfIteratorHelper(logs);
              try {
                for (_iterator.s(); !(_step = _iterator.n()).done;) {
                  log = _step.value;
                  if (log.type === 'vehicle:data_callback_dl') {
                    log.component = DatabaseResultDL;
                  }
                  if (log.type === 'vehicle:data_callback') {
                    log.component = DatabaseResultPlate;
                  }
                  if (log.type === 'vehicle:data_callback_weapon') {
                    log.component = DatabaseResultFirearm;
                  }
                }
              } catch (err) {
                _iterator.e(err);
              } finally {
                _iterator.f();
              }
              _this5.$store.commit('cad/SET_DATABASE_QUERY_RESULTS', logs);
            case 6:
            case "end":
              return _context4.stop();
          }
        }, _callee4);
      }))();
    }
  },
  watch: {},
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    pendingPlateSearch: 'system/pendingPlateSearch',
    dbResults: 'cad/dbResults',
    location: 'location/name'
  }))
});
// CONCATENATED MODULE: ./pages/cad/database.vue?vue&type=script&lang=ts
 /* harmony default export */ var cad_databasevue_type_script_lang_ts = (databasevue_type_script_lang_ts); 
// CONCATENATED MODULE: ./pages/cad/database.vue





/* normalize component */

var database_component = Object(componentNormalizer["a" /* default */])(
  cad_databasevue_type_script_lang_ts,
  databasevue_type_template_id_59ab10f8_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var database = __webpack_exports__["default"] = (database_component.exports);

/* nuxt-component-imports */
installComponents(database_component, {CrudyTableItemsComponentPoliceIncident: __webpack_require__(503).default})


/***/ })

}]);