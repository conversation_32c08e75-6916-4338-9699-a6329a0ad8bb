local CollectedRecordingData = {}
local suspendedCameras = {}
local RecordingStartedAt = {}

RegisterCommand('camcord', function(Source, args)
    EngageSecurityCamera(Source)
end, true)

for _, eventName in pairs(Config.CamTriggerEvents) do
    RegisterNetEvent(eventName, function()
        EngageSecurityCamera(source)
    end)
end

AddEventHandler('SonoranCAD::callcommands:SendCallApi', function(isEmergency, caller, callLocation, description, source, _, _, _)
    EngageSecurityCamera(source)
end)

function EngageSecurityCamera(serverId)
    local ped = GetPlayerPed(serverId)
    local coords = GetEntityCoords(ped)

    local closestCamId = nil
    local closestCamDist = nil

    for camId, cams in pairs(AllCameras) do
        local tDist = #(cams[1].centerPos - coords)

        if tDist < 80.0 then
            if not closestCamDist or tDist < closestCamDist then
                closestCamId = camId
                closestCamDist = tDist
            end
        end
    end

    if suspendedCameras[tonumber(closestCamId)] then
        for i = 1, 7 do
            if suspendedCameras[tonumber(closestCamId)] then
                Wait(1000)
            else
                break
            end
        end

        if suspendedCameras[tonumber(closestCamId)] then
            return
        end
    end

    if closestCamId then
        closestCamId = tostring(closestCamId)
        local activeCams = GlobalState.activeCams or {}

        if not activeCams[closestCamId] then
            activeCams[closestCamId] = true
            GlobalState.activeCams = activeCams
            RecordingStartedAt[tonumber(closestCamId)] = GetGameTimer()
        end
    end
end

Citizen.CreateThread(function()
    GlobalState.activeCams = {}

    while true do
        Wait(0)

        if GlobalState.activeCams then
            local playerCoords = {}

            -- collect players
            local timeout = 20
            for _, serverId in pairs(GetPlayers()) do
                timeout = timeout - 5
                if timeout <= 0 then Wait(0) timeout = 5 end

                local serverId = tonumber(serverId)
                local ped = GetPlayerPed(serverId)
                playerCoords[serverId] = GetEntityCoords(ped)
            end

            for camId, _ in pairs(GlobalState.activeCams) do
                camId = tonumber(camId)
                local shouldCameraStop = true

                if (GetGameTimer() - RecordingStartedAt[camId]) < Config.MaxRecordingLength and AllCameras[camId] then
                    for _, coords in pairs(playerCoords) do
                        if not shouldCameraStop then break end

                        for _, camData in pairs(AllCameras[camId]) do
                            if not shouldCameraStop then break end

                            if #(camData.pos - coords) < Config.MaxCamRecordingDistance then
                                shouldCameraStop = false
                                break
                            end
                        end
                    end
                end

                if shouldCameraStop then
                    HandleStopCameraRecording(camId)
                end
                Wait(0)
            end
        end
    end
end)

RegisterNetEvent('rcore_cam:stopRecording', function(camId)
    HandleStopCameraRecording(camId)
end)

function HandleStopCameraRecording(camId)
    camId = tonumber(camId)
    local activeCams = GlobalState.activeCams
    activeCams[tostring(camId)] = nil
    suspendedCameras[camId] = true
    GlobalState.activeCams = activeCams

    Wait(5000)

    local collectedVehicleData = {}
    local collectedNpcData = {}
    local playerRecordingKeyCache = {}

    if CollectedRecordingData[camId] then
        local lowestTime = FindLowestTime(CollectedRecordingData[camId])

        for playerServerId, data in pairs(CollectedRecordingData[camId]) do
            local combinedSingleVehicles = {}
            local combinedSingleNpcs = {}

            -- table.insert(collectedPlayerData, data.player)

            for i = 1, #data do
                if data[i].vehicles then
                    for ident, vData in pairs(data[i].vehicles) do
                        if not combinedSingleVehicles[ident] then
                            combinedSingleVehicles[ident] = {}
                        end

                        for j = 1, #vData do
                            vData[j].t = vData[j].t + (data.start - lowestTime)
                            table.insert(combinedSingleVehicles[ident], vData[j])
                        end
                    end
                end

                if data[i].npcs then
                    for ident, nData in pairs(data[i].npcs) do
                        if not combinedSingleNpcs[ident] then
                            combinedSingleNpcs[ident] = {}
                        end

                        for j = 1, #nData do
                            nData[j].t = nData[j].t + (data.start - lowestTime)
                            table.insert(combinedSingleNpcs[ident], nData[j])
                        end
                    end
                end

                if data[i].player then
                    if not playerRecordingKeyCache[playerServerId] then
                        playerRecordingKeyCache[playerServerId] = GetPlayerRecordingKey(playerServerId)
                    end

                    if not combinedSingleNpcs[playerRecordingKeyCache[playerServerId]] then
                        combinedSingleNpcs[playerRecordingKeyCache[playerServerId]] = {}
                    end

                    
                    for j = 1, #data[i].player do
                        data[i].player[j].t = data[i].player[j].t + (data.start - lowestTime)

                        if GetPlayerMetadata and #combinedSingleNpcs[playerRecordingKeyCache[playerServerId]] == 0 then
                            local meta = GetPlayerMetadata(playerServerId)

                            if meta then
                                data[i].player[j].metadata = GetPlayerMetadata(playerServerId)
                            end
                        end

                        table.insert(combinedSingleNpcs[playerRecordingKeyCache[playerServerId]], data[i].player[j])
                    end
                end
            end

            table.insert(collectedVehicleData, combinedSingleVehicles)
            table.insert(collectedNpcData, combinedSingleNpcs)

            Wait(0)
        end

        local vehicleRecordings = GetMergedVehicleData(collectedVehicleData)
        local npcRecordings = GetMergedVehicleData(collectedNpcData)

        Wait(0)

        local id = DbSaveRecording(tonumber(camId), 'cam_' .. camId)
        SaveResourceFile(GetCurrentResourceName(), './recordings/' .. id .. '.json', json.encode({
            peds = npcRecordings,
            vehicles = vehicleRecordings,
            destroyedCameras = FormatDestroyedCameras(camId),
            camGroupId = camId,
        }), -1)
    else
        -- print("ERROR: No collected data for", camId)
    end

    UndestroyCameraGroup(camId)
    suspendedCameras[camId] = nil
    CollectedRecordingData[camId] = nil
    RecordingStartedAt[camId] = nil
end

function FormatDestroyedCameras(camId)
    local dc = {}

    if not DestroyedCameras then
        print("ERROR: No destroyed cameras")
    end

    for _, camData in pairs(DestroyedCameras) do
        if camId == camData[1] then
            dc[camData[2]] = camData[3]
        end
    end

    return dc
end

RegisterNetEvent('rcore_cam:record', function(dataArray)
    local Source = source
    local camId = tonumber(dataArray[1])
    local recStartedAt = dataArray[2]
    local dataType = dataArray[3]
    local data = dataArray[4]

    if not CollectedRecordingData[camId] then
        CollectedRecordingData[camId] = {}
    end

    if not CollectedRecordingData[camId][Source] then
        CollectedRecordingData[camId][Source] = {}
    end

    if not CollectedRecordingData[camId][Source][dataType] then
        CollectedRecordingData[camId][Source][dataType] = {}
    end

    if not CollectedRecordingData[camId][Source].start then
        CollectedRecordingData[camId][Source].start = recStartedAt
    elseif CollectedRecordingData[camId][Source].start ~= recStartedAt then
        -- print("ERROR: Mismatched recording start!")
    end

    table.insert(CollectedRecordingData[camId][Source], {[dataType] = data})
end)


function FindLowestTime(data)
    local lowestTime = nil

    for playerServerId, plyData in pairs(data) do
        if not lowestTime or lowestTime > plyData.start then
            lowestTime = plyData.start
        end
    end

    return lowestTime
end