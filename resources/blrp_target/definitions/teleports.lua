--[[
AddBoxZone('CokeLabFrontOuter', vector3(-613.1924, 323.4548, 82.25697), 0.05, 1.3, {
  name = 'CokeLabFrontOuter',
  heading = 175.0,
  minZ = 81.0,
  maxZ = 83.5,
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'CokeLabFrontInner',
      icon = 'far fa-door-closed',
      label = 'Enter',
    },
  },
  context_disable = true,
  distance = 2.0
})

AddBoxZone('CokeLabFrontInner', vector3(-604.116, 295.0353, 56.2765), 0.05, 1.3, {
  name = 'CokeLabFrontInner',
  heading = 178.0,
  minZ = 55.0,
  maxZ = 57.5,
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'CokeLabFrontOuter',
      icon = 'far fa-door-closed',
      label = 'Exit',
    },
  },
  context_disable = true,
  distance = 2.0
})

AddBoxZone('CokeLabRearOuter', vector3(-617.1668, 314.4237, 82.33562), 0.05, 1.3, {
  name = 'CokeLabRearOuter',
  heading = 265.0,
  minZ = 81.0,
  maxZ = 83.5,
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'CokeLabRearInner',
      icon = 'far fa-door-closed',
      label = 'Enter',
    },
  },
  context_disable = true,
  distance = 2.0
})

AddBoxZone('CokeLabRearInner', vector3(-601.0247, 310.1843, 53.98789), 0.05, 3.3, {
  name = 'CokeLabRearInner',
  heading = 265.0,
  minZ = 52.0,
  maxZ = 56.0,
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'CokeLabRearOuter',
      icon = 'far fa-door-closed',
      label = 'Exit',
    },
  },
  context_disable = true,
  distance = 2.0
})
]]

-- AddBoxZone('VespucciPDLower', vector3(-1112.37, -848.1703, 13.79349), 3.5, 3.5, {
--   name = 'VespucciPDLower',
--   heading = -50.0,
--   minZ = 12.5,
--   maxZ = 15.8,
--   notifyText = '<i class="fa-regular fa-eye"></i> Vespucci PD',
-- }, {
--   options = {
--     {
--       event_client = 'core:client:teleports:requestFromTarget',
--       destination = 'VespucciPDUpper',
--       icon = 'far fa-door-open',
--       label = 'Roof Access',
--     },
--   },
--   distance = 2.0,
-- })

-- AddBoxZone('VespucciPDUpper', vector3(-1107.557, -831.9737, 37.83823), 2.8, 1.8, {
--   name = 'VespucciPDUpper',
--   heading = 38.0,
--   minZ = 36.8,
--   maxZ = 39.3,
--   notifyText = '<i class="fa-regular fa-eye"></i> Vespucci PD',
-- }, {
--   options = {
--     {
--       event_client = 'core:client:teleports:requestFromTarget',
--       destination = 'VespucciPDLower',
--       icon = 'far fa-door-open',
--       label = 'Parking Lot Access',
--     },
--   },
--   distance = 2.0,
-- })

AddBoxZone('MazeBankWestGround', vector3(-1381.17, -498.9531, 33.53347), 5.0, 7.0, {
  name = 'MazeBankWestGround',
  heading = 6.25,
  minZ = 32.0,
  maxZ = 35.0,
  notifyText = '<i class="fa-regular fa-eye"></i> Maze Bank West'
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'MazeBankWestRoof',
      icon = 'far fa-sort-circle-up',
      label = 'Maze Bank West: roof',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'MazeBankWestFloor10',
      icon = 'far fa-sort-circle-up',
      label = 'Maze Bank West: 10th floor',
    },
  },
  distance = 2.5,
})

AddBoxZone('MazeBankWestFloor10', vector3(-1394.387, -479.743, 72.042), 5.0, 5.0, {
  name = 'MazeBankWestFloor10',
  heading = -89.0,
  minZ = 71.0,
  maxZ = 74.0,
  notifyText = '<i class="fa-regular fa-eye"></i> Maze Bank West - 10th floor'
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'MazeBankWestRoof',
      icon = 'far fa-sort-circle-up',
      label = 'Maze Bank West: roof',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'MazeBankWestGround',
      icon = 'far fa-sort-circle-down',
      label = 'Maze Bank West: ground floor',
    },
  },
  distance = 2.5,
})

AddBoxZone('MazeBankWestRoof', vector3(-1369.799, -472.2043, 84.75718), 4.0, 3.0, {
  name = 'MazeBankWestRoof',
  heading = -55.0,
  minZ = 83.0,
  maxZ = 86.0,
  notifyText = '<i class="fa-regular fa-eye"></i> Maze Bank West - roof'
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'MazeBankWestFloor10',
      icon = 'far fa-sort-circle-down',
      label = 'Maze Bank West: 10th floor',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'MazeBankWestGround',
      icon = 'far fa-sort-circle-down',
      label = 'Maze Bank West: ground floor',
    },
  },
  distance = 2.5,
})

AddBoxZone('DownBadRoof', vector3(-296.515, -1061.869, 73.97872), 4.0, 3.0, {
  name = 'DownBadRoof',
  heading = 250.0,
  minZ = 73.0,
  maxZ = 75.5,
  notifyText = '<i class="fa-regular fa-eye"></i> DownBad Apartments - Roof'
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'DownBadGroundFloor',
      icon = 'far fa-sort-circle-down',
      label = 'Ground Floor',
    },
  },
  distance = 2.5
})

AddBoxZone('DownBadGroundFloor', vector3(-314.2141, -1035.798, 30.51732), 4.0, 6.0, {
  name = 'DownBadGroundFloor',
  heading = 160.0,
  minZ = 29.5,
  maxZ = 32.0,
  notifyText = '<i class="fa-regular fa-eye"></i> DownBad Apartments - Roof Access'
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'DownBadRoof',
      icon = 'far fa-sort-circle-up',
      label = 'Roof Access',
    },
  },
  distance = 2.5
})

AddBoxZone('305plazaGroundFloor', vector3(-1567.91, -403.34, 42.39), 1.6, 1.4, {
  name = '305plazaGroundFloor',
  heading = 120,
  minZ = 40.19,
  maxZ = 44.19,
  notifyText = '<i class="fa-regular fa-eye"></i> 305 Plaza Ground Floor - Staircase'
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      icon = 'far fa-sort-circle-up',
      label = '305 Plaza Roof',

      destination = '305Roof',
    },
  },
  distance = 2.5
})

AddBoxZone('305plazaRoof', vector3(-1572.46, -394.53, 59.15), 1.6, 1.0, {
  name = '305plazaRoof',
  heading = 50,
  minZ = 57.75,
  maxZ = 60.75,
  notifyText = '<i class="fa-regular fa-eye"></i> 305 Plaza Roof - Staircase'
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      icon = 'far fa-sort-circle-up',
      label = '305 Plaza Roof',

      destination = '305Ground',
    },
  },
  distance = 2.5
})

AddBoxZone('800VespucciBlvdGroundFloor', vector3(-567.85, -781.042, 30.667), 2.8, 3.0, {
  name = '800VespucciBlvdGroundFloor',
  heading = 180.0,
  minZ = 29.7,
  maxZ = 32.5,
  notifyText = '<i class="fa-regular fa-eye"></i> 800 Vespucci Blvd - Staircase'
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      icon = 'far fa-sort-circle-up',
      label = '800 Vespucci Blvd - Apt M',

      destination = '800VespucciAptM',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      icon = 'far fa-sort-circle-up',
      label = '800 Vespucci Blvd - Apt N',

      destination = '800VespucciAptN',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      icon = 'far fa-sort-circle-up',
      label = '800 Vespucci Blvd - Apt O',

      destination = '800VespucciAptO',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      icon = 'far fa-sort-circle-up',
      label = '800 Vespucci Blvd - Apt P',

      destination = '800VespucciAptP',
    },
  },
  distance = 2.5
})

for _, coords in pairs({
  vector3(-574.224, -768.295, 34.871),
  vector3(-574.226, -774.231, 34.871),
  vector3(-574.221, -786.114, 34.856),
  vector3(-574.226, -792.019, 34.856),
}) do
  AddBoxZone('800VespucciBlvdUpper' .. _, coords, 0.6, 6.0, {
    name = '800VespucciBlvdUpper' .. _,
    heading = 270.0,
    minZ = 34.0,
    maxZ = 37.0,
  }, {
    options = {
      {
        event_client = 'core:client:teleports:requestFromTarget',
        icon = 'far fa-sort-circle-down',
        label = '800 Vespucci Blvd - Ground Floor',

        destination = '800VespucciGround',
      },
    },
    distance = 2.5
  })
end

AddBoxZone('PlasmaKartElevatorUpper', vector3(-2257.1, 225.94, 168.735), 4.0, 4.0, {
  name = 'PlasmaKartElevatorUpper',
  heading = 90.0,
  minZ = 168.735,
  maxZ = 171.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PlasmaKartElevatorLower',
      icon = 'far fa-sort-circle-down',
      label = 'Elevator: Plasmakart Track',
    },
  },
  distance = 2.5
})

AddBoxZone('PlasmaKartElevatorLower', vector3(-2257.1, 225.94, 107.38), 4.0, 4.0, {
  name = 'PlasmaKartElevatorLower',
  heading = 90.0,
  minZ = 107.38,
  maxZ = 110.145,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PlasmaKartElevatorUpper',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Plasmakart Lobby',
    },
  },
  distance = 2.5
})

AddBoxZone('TinselTowersLobbyL', vector3(-621.0039, 46.91737, 42.59645), 0.5, 2.2, {
  name = 'TinselTowersLobbyL',
  heading = 0.0,
  minZ = 42.59,
  maxZ = 45.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'null',
      icon = 'far fa-times',
      label = 'Out Of Order',
    },
  },
  distance = 2.5
})

AddBoxZone('TinselTowersLobbyR', vector3(-614.5815, 46.90643, 42.59645), 0.5, 2.2, {
  name = 'TinselTowersLobbyR',
  heading = 0.0,
  minZ = 42.59,
  maxZ = 45.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'TinselTowersFloor7',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 7th Floor',
    },
  },
  distance = 2.5
})

AddBoxZone('TinselTowersFloor7', vector3(-604.9427, 49.8272324, 92.62619), 1.0, 2.0, {
  name = 'TinselTowersFloor7',
  heading = 0.0,
  minZ = 92.6,
  maxZ = 95.1,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'TinselTowersLobbyR',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Lobby',
    },
  },
  distance = 2.5
})

-- Governor office San Andreas Ave

AddBoxZone('7308SanAndreasAveFloor01', vector3(-589.4297, -708.634, 36.47742), 3.0, 3.2, {
  name = '7308SanAndreasAveFloor01',
  heading = 180.0,
  minZ = 35.3,
  maxZ = 37.8,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = '7308SanAndreasAveFloor10',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 10th Floor',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = '7308SanAndreasAveFloor11',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 11th Floor',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = '7308SanAndreasAveRoof',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Roof',
    },
  },
  distance = 3.0
})

AddBoxZone('7308SanAndreasAveFloor10', vector3(-576.1677, -714.2954, 113.1855), 1.5, 1.25, {
  name = '7308SanAndreasAveFloor10',
  heading = 270.0,
  minZ = 112.0,
  maxZ = 114.3,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = '7308SanAndreasAveFloor01',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = '7308SanAndreasAveFloor11',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 11th Floor',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = '7308SanAndreasAveRoof',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Roof',
    },
  },
  distance = 2.5
})

AddBoxZone('7308SanAndreasAveFloor11', vector3(-575.8998, -713.7172, 121.8024), 1.0, 2.0, {
  name = '7308SanAndreasAveFloor11',
  heading = 180.0,
  minZ = 120.7,
  maxZ = 123.0,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = '7308SanAndreasAveFloor01',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = '7308SanAndreasAveFloor10',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 10th Floor',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = '7308SanAndreasAveRoof',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Roof',
    },
  },
  distance = 2.5
})

AddBoxZone('7308SanAndreasAveRoof', vector3(-579.212, -716.8245, 130.074), 2.0, 1.5, {
  name = '7308SanAndreasAveRoof',
  heading = 270.0,
  minZ = 129.0,
  maxZ = 131.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = '7308SanAndreasAveFloor01',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = '7308SanAndreasAveFloor10',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 10th Floor',
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = '7308SanAndreasAveFloor11',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 11th Floor',
    },
  },
  distance = 2.5
})

-- Paleto Bay Sheriffs Office

AddBoxZone('PBSOBasement', vector3(-450.8417, 6010.143, 27.71025), 3.0, 3.2, {
  name = 'PBSOBasement',
  heading = 180.0,
  minZ = 27.7,
  maxZ = 27.9,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PBSOTopFloor',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',
      groups = {
        'LEO',
      },
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PBSOGroundFloor',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',
      groups = {
        'LEO',
        'DOJ',
      },
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PBSOBasement',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Basement',
      groups = {
        'LEO',
        'DOJ',
      },
    },
  },
  distance = 3.0,
})

AddBoxZone('PBSOGroundFloor', vector3(-450.8325, 6010.151, 32.40816), 1.5, 1.25, {
  name = 'PBSOGroundFloor',
  heading = 270.0,
  minZ = 32.4,
  maxZ = 32.7,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PBSOTopFloor',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',
      groups = {
        'LEO',
      },
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PBSOGroundFloor',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',
      groups = {
        'LEO',
        'DOJ',
      },
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PBSOBasement',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Basement',
      groups = {
        'LEO',
        'DOJ',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('PBSOTopFloor', vector3(-450.007, 6008.799, 37.09269), 1.0, 2.0, {
  name = 'PBSOTopFloor',
  heading = 180.0,
  minZ = 37.07,
  maxZ = 37.3,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PBSOTopFloor',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',
      groups = {
        'LEO',
      },
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PBSOGroundFloor',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',
      groups = {
        'LEO',
        'DOJ',
      },
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PBSOBasement',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Basement',
      groups = {
        'LEO',
        'DOJ',
      },
    },
  },
  distance = 2.5,
})

-- Cayo Lighthouse
AddBoxZone('CayoLightHouseLower', vector3(5611.578, -5654.933, 11.84094), 4.0, 2.0, {
  heading = 90.0,
  minZ = 10.0,
  maxZ = 13.0,
  notifyText = '<i class="fa-regular fa-eye"></i> Staircase',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'CayoLightHouseUpper',
      icon = 'far fa-sort-circle-up',
      label = 'Climb To Top',
    },
  },
  distance = 2.5
})

AddBoxZone('CayoLightHouseUpper', vector3(5614.561, -5656.649, 36.40007), 2.0, 2.0, {
  heading = 0.0,
  minZ = 35.8,
  maxZ = 37.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Staircase',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'CayoLightHouseLower',
      icon = 'far fa-sort-circle-down',
      label = 'Climb To Bottom',
    },
  },
  distance = 2.5
})

-- pillbox

AddBoxZone('PillboxHelipad', vector3(338.823, -583.965, 74.165), 3.0, 3.2, {
  name = 'PillboxHelipad',
  heading = 249.088,
  minZ = 73,
  maxZ = 75.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'Pillbox4thFloor1',
      icon = 'far fa-sort-circle-down',
      label = 'Elevator: 4th Floor',
      elevator_delay = true,
      ems_bypass = true,
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'Pillbox1stFloor1',
      icon = 'far fa-sort-circle-down',
      label = 'Elevator: 1st Floor',
      elevator_delay = true,
      ems_bypass = true,
    },
  },
  distance = 3.0,
})


AddBoxZone('Pillbox4thFloor1', vector3(335.684, -592.533, 43.268), 2.7, 2.8, {
  name = 'Pillbox4thFloor1',
  heading = 69.922,
  minZ = 43,
  maxZ = 44,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PillboxHelipad',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Helipad',
      elevator_delay = true,
      ems_bypass = true,
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'Pillbox1stFloor1',
      icon = 'far fa-sort-circle-down',
      label = 'Elevator: 1st Floor',
      elevator_delay = true,
      ems_bypass = true,
    },
  },
  distance = 1.5,
})

AddBoxZone('Pillbox4thFloor2', vector3(337.041, -589.272, 43.268), 2.95, 2.8, {
  name = 'Pillbox4thFloor2',
  heading = 69.922,
  minZ = 43,
  maxZ = 44,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PillboxHelipad',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Helipad',
      elevator_delay = true,
      ems_bypass = true,
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'Pillbox1stFloor2',
      icon = 'far fa-sort-circle-down',
      label = 'Elevator: 1st Floor',
      elevator_delay = true,
      ems_bypass = true,
    },
  },
  distance = 1.5,
})


AddBoxZone('Pillbox1stFloor1', vector3(323.446, -583.362, 28.848), 2.95, 2.8, {
  name = 'Pillbox1stFloor1',
  heading = 249.535,
  minZ = 28,
  maxZ = 30,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PillboxHelipad',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Helipad',
      elevator_delay = true,
      ems_bypass = true,
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'Pillbox4thFloor1',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 4th Floor',
      elevator_delay = true,
      ems_bypass = true,
    },
  },
  distance = 1.5,
})

AddBoxZone('Pillbox1stFloor2', vector3(322.274, -586.527, 28.848), 2.95, 2.8, {
  name = 'Pillbox1stFloor2',
  heading = 249.535,
  minZ = 28,
  maxZ = 30,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'PillboxHelipad',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Helipad',
      elevator_delay = true,
      ems_bypass = true,
    },
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'Pillbox4thFloor2',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 4th Floor',
      elevator_delay = true,
      ems_bypass = true,
    },
  },
  distance = 1.5,
})

-- eastborn way Apt

AddBoxZone('EastbornWayElevatorUpper', vector3(-329.38, 52.2, 58.75), 2.5, 2.5, {
  name = 'EastbornWayElevatorUpper',
  heading = 350.0,
  minZ = 57.75,
  maxZ = 60.15,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'EastbornWayElevatorLower',
      icon = 'far fa-sort-circle-down',
      label = 'Elevator: Eastborn Way Lower',
    },
  },
  distance = 2.5,
  context_disable = true,
})

AddBoxZone('EastbornWayElevatorLower', vector3(-329.67, 48.41, 54.43), 2.5, 2.5, {
  name = 'EastbornWayElevatorLower',
  heading = 350.0,
  minZ = 51.83,
  maxZ = 55.83,
  notifyText = '<i class="fa-regular fa-eye"></i> Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'EastbornWayElevatorUpper',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: Eastborn Way Upper',
    },
  },
  distance = 2.5,
  context_disable = true,
})
