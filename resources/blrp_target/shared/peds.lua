-- When setting timeflag (param 7), high bits are hours this ped should be VISIBLE
static_store_peds = {
  -- <PERSON><PERSON>o <PERSON> 2024
  --[[
  {
    model = `mp_f_freemode_01`,
    coords = vector4(4913.085, -4929.109, 3.384, 90.0),
    store_id = 'cp24-bf24',
    anim = {
      dict = 'anim@mp_corona_idles@female_b@idle_a',
      clip = 'idle_a',
    },
    clothing = {
      [3] = {4, 0},
      [4] = {274, 14},
      [5] = {0, 0},
      [6] = {16, 4},
      [7] = {0, 0},
      [8] = {14, 0},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {917, 0},
      ['p0'] = {-1, 0},
      ['p1'] = {-1, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },

    blend = json.decode('{"pants_2":0,"makeup_2":57,"bodyb_1":11,"nose_4":0,"neck_thickness":0,"sun_2":100,"chin_4":-17,"chin_1":20,"torso_1":15,"bproof_2":0,"eyebrows_3":0,"makeup_4":255,"righthand_2":0,"hair_color_2":3,"shoes_1":34,"blush_2":80,"moles_1":3,"eyebrows_1":1,"mom":26,"chest_1":255,"bodyb_2":29,"jaw_2":-19,"jaw_1":10,"lipstick_2":100,"beard_2":100,"beard_4":0,"helmet_1":-1,"lefthand_2":0,"complexion_2":100,"ears_2":0,"blush_3":5,"lip_thickness":-100,"sun_1":255,"dad":5,"makeup_1":9,"torso_2":0,"lipstick_1":0,"bags_1":0,"bproof_1":0,"cheeks_2":0,"eyebrows_5":-8,"bodyb_3":255,"pants_1":61,"shoes_2":0,"blush_1":1,"sex":0,"hair_1":87,"chin_3":-46,"neckarm_2":0,"nose_3":53,"complexion_1":255,"makeup_3":255,"helmet_2":0,"ears_1":-1,"chest_4":0,"mask_2":0,"age_2":23,"skin_md_weight":0,"hair_color_1":0,"nose_5":19,"neckarm_1":0,"face_md_weight":22,"lipstick_4":0,"eyebrows_6":-64,"eyebrows_4":0,"bodyb_4":4,"hair_2":0,"eyebrows_2":100,"chest_2":100,"eye_color":5,"moles_2":29,"makeup_type":1,"blemishes_2":4,"blemishes_1":255,"arms":15,"nose_2":25,"arms_2":0,"nose_1":-12,"glasses_2":0,"righthand_1":-1,"nose_6":0,"bags_2":0,"beard_1":255,"glasses_1":-1,"lefthand_1":-1,"tshirt_1":15,"chin_2":-7,"age_1":0,"lipstick_3":6,"eye_squint":0,"decals_2":0,"cheeks_1":0,"mask_1":0,"chest_3":0,"cheeks_3":-6,"beard_3":0,"tshirt_2":0,"decals_1":0}')
  },

  {
    model = `mp_f_freemode_01`,
    coords = vector4(5155.221, -5102.295, 2.356, 255.695),
    store_id = 'cp24-general',
    anim = {
      dict = 'anim@mp_corona_idles@female_b@idle_a',
      clip = 'idle_a',
    },
    clothing = {
      [2] = {10, 0},
      [3] = {15, 0},
      [4] = {274, 8},
      [5] = {0, 0},
      [6] = {16, 0},
      [7] = {0, 0},
      [8] = {14, 0},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {16, 2},
      ['p0'] = {-1, 0},
      ['p1'] = {-1, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },

    blend = json.decode('{"complexion_1":255,"blush_3":13,"makeup_1":11,"nose_1":100,"nose_2":0,"cheeks_3":11,"glasses_1":-1,"decals_1":0,"nose_3":0,"jaw_2":100,"eyebrows_5":-100,"age_1":255,"hair_color_1":0,"blemishes_1":255,"righthand_2":0,"bodyb_2":100,"torso_2":0,"pants_2":0,"eyebrows_6":12,"hair_2":0,"torso_1":15,"makeup_2":100,"nose_4":-17,"blush_1":3,"complexion_2":100,"mask_2":0,"lefthand_2":0,"tshirt_1":15,"neckarm_2":0,"bags_2":0,"chest_3":0,"helmet_2":0,"neckarm_1":0,"helmet_1":-1,"mask_1":0,"lipstick_2":53,"glasses_2":0,"lipstick_3":6,"bproof_2":0,"jaw_1":-86,"ears_1":-1,"cheeks_2":35,"bags_1":0,"mom":40,"beard_4":0,"chin_4":-100,"bodyb_3":255,"nose_5":0,"chest_2":100,"shoes_1":34,"blemishes_2":100,"eyebrows_2":100,"arms":15,"moles_2":100,"hair_1":128,"bodyb_1":3,"chest_4":0,"hair_color_2":60,"blush_2":54,"chin_3":-41,"makeup_type":1,"lipstick_1":0,"chin_1":-2,"makeup_3":255,"makeup_4":255,"ears_2":0,"lip_thickness":-100,"moles_1":11,"sun_2":100,"neck_thickness":0,"eyebrows_3":0,"eyebrows_4":0,"nose_6":0,"chest_1":255,"lefthand_1":-1,"beard_1":255,"face_md_weight":0,"bproof_1":0,"sex":0,"eye_squint":100,"beard_3":0,"beard_2":100,"tshirt_2":0,"arms_2":0,"skin_md_weight":80,"shoes_2":0,"bodyb_4":100,"eyebrows_1":1,"cheeks_1":-100,"decals_2":0,"eye_color":5,"dad":3,"chin_2":49,"age_2":100,"pants_1":61,"lipstick_4":0,"righthand_1":-1,"sun_1":255}'),
  },

  {
    model = `cs_omega`,
    coords = vector4(5330.382, -5272.351, 33.186, 1.751),
    store_id = 'cp24-kush',
    anim = {
      dict = 'anim@mp_celebration@idles@female',
      clip = 'celebration_idle_f_a',
    },
    clothing = {
      [3] = {0, 0},
      [4] = {0, 0},
      [5] = {0, 0},
      [6] = {0, 0},
      [7] = {0, 0},
      [8] = {1, 0},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {0, 0},
      ['p0'] = {-1, 0},
      ['p1'] = {0, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },
  },

  {
    model = `a_f_y_epsilon_01`,
    coords = vector4(4876.633, -4471.428, 8.978, 356.385),
    store_id = 'cp24-hydration',
    anim = {
      dict = 'friends@fra@ig_1',
      clip = 'base_idle',
    },
    clothing = {
      [3] = {1, 0},
      [4] = {0, 0},
      [5] = {0, 0},
      [6] = {0, 0},
      [7] = {0, 0},
      [8] = {0, 0},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {0, 0},
      ['p0'] = {-1, 0},
      ['p1'] = {-1, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },
  },

  {
    model = `a_m_y_epsilon_02`,
    coords = vector4(5387.896, -5550.729, 52.521, 103.634),
    store_id = 'cp24-hydration',
    anim = {
      dict = 'anim@heists@heist_corona@team_idles@male_a',
      clip = 'idle',
    },
    clothing = {
      [3] = {0, 0},
      [4] = {0, 0},
      [5] = {0, 0},
      [6] = {0, 0},
      [7] = {0, 0},
      [8] = {0, 0},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {0, 0},
      ['p0'] = {-1, 0},
      ['p1'] = {-1, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },
  },

  {
    model = `a_m_y_epsilon_01`,
    coords = vector4(5052.698, -5181.651, 3.777, 99.097),
    store_id = 'cp24-hydration',
    anim = {
      dict = 'amb@world_human_hang_out_street@Female_arm_side@idle_a',
      clip = 'idle_a',
    },
    clothing = {
      [3] = {1, 2},
      [4] = {0, 0},
      [5] = {0, 0},
      [6] = {0, 0},
      [7] = {0, 0},
      [8] = {0, 0},
      [9] = {0, 0},
      [10] = {2, 0},
      [11] = {0, 0},
      ['p0'] = {-1, 0},
      ['p1'] = {0, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },
  },
  ]]
  -- /Cayo Perico 2024

  -- Yankton 2024
  --[[
    {
    model = `u_m_y_proldriver_01`,
    coords = vector4(5970.433, -5140.410, 85.141, 174.427),
    store_id = 'xm24-gen',
    anim = {
    dict = 'amb@prop_human_bum_shopping_cart@male@idle_a',
    clip = 'idle_c',
    },
    clothing = {
      [2] = {0, 0},
      [3] = {0, 0},
      [4] = {0, 0},
      [5] = {0, 0},
      [6] = {0, 0},
      [7] = {0, 0},
      [8] = {0, 0},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {0, 0},
      ['p0'] = {-1, 0},
      ['p1'] = {-1, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },
  },

  {
    model = `A_M_Y_Vinewood_02`,
    coords = vector4(6044.525, -5244.286, 85.854, 173.072),
    store_id = 'xm24-liq',
    anim = {
    dict = 'rcmnigel1cnmt_1c',
    clip = 'base',
    },
    clothing = {
      [3] = {0, 2},
      [4] = {0, 1},
      [5] = {0, 0},
      [6] = {0, 0},
      [7] = {0, 0},
      [8] = {0, 0},
      [9] = {1, 0},
      [10] = {0, 0},
      [11] = {0, 0},
      ['p0'] = {-1, 0},
      ['p1'] = {-1, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },
  },

  {
    model = `S_M_M_StrVend_01`,
    coords = vector4(6042.624, -5273.706, 86.118, 273.221),
    store_id = 'xm24vend1',
    anim = {
    dict = 'anim@heists@humane_labs@finale@strip_club',
    clip = 'ped_b_celebrate_loop',
    },
    clothing = {
      [2] = {0, 2},
      [3] = {0, 1},
      [4] = {0, 1},
      [5] = {0, 0},
      [6] = {0, 0},
      [7] = {0, 0},
      [8] = {0, 1},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {0, 0},
      ['p0'] = {1, 1},
      ['p1'] = {-1, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },
  },

  {
    model = `a_f_m_prolhost_01`,
    coords = vector4(6045.057, -5267.035, 86.067, 223.041),
    store_id = 'xm24vend3',
    anim = {
    dict = 'anim@amb@nightclub@lazlow@ig1_vip@',
    clip = 'clubvip_base_laz',
    },
    clothing = {
      [2] = {0, 0},
      [3] = {0, 0},
      [4] = {0, 0},
      [5] = {0, 0},
      [6] = {0, 0},
      [7] = {0, 0},
      [8] = {0, 0},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {0, 0},
      ['p0'] = {-1, 0},
      ['p1'] = {-1, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },
  },

  {
    model = `S_M_M_Linecook`,
    coords = vector4(6048.856, -5264.946, 86.121, 187.876),
    store_id = 'xm24vend2',
    anim = {
    dict = 'friends@fra@ig_1',
    clip = 'base_idle',
    },
    clothing = {
      [1] = {0, 0},
      [2] = {0, 0},
      [3] = {0, 3},
      [4] = {0, 1},
      [5] = {0, 0},
      [6] = {0, 0},
      [7] = {0, 0},
      [8] = {0, 2},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {0, 0},
      ['p0'] = {-1, 0},
      ['p1'] = {0, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },
  },
  ]]
  -- \Yankton 2024

  -- Camp Morningwood 2025
  {
  model = `a_m_m_indian_01`,
  coords = vector4(-890.091, 2837.090, 23.581, 197.819),
  store_id = 'camp-247',
  anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
    clothing = { [0] = { 0, 1 }, [1] = { 0, 0 }, [2] = { 0, 0 }, [3] = { 0, 0 }, [4] = { 0, 1 } },
  },
-- \Camp Morningwood 2025

  -- Mask store Vespucci Beach
  {
    model = `a_f_y_hipster_03`,
    coords = vector4(-1201.979, -1510.565, 4.374, 124.698),
    store_id = 'masquerade',

    extra_options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Buy Mask',

        uid = 'masquerade-open-mask',

        filter = function(entity, hit_coords)
          return IsEntityVisible(entity) and NetworkGetEntityIsLocal(entity) and Entity(entity).state.store_uid == 'masquerade'
        end
      },
    },

    anim = {
      dict = 'anim@heists@heist_corona@team_idles@male_a',
      clip = 'idle',
    },
    clothing = { [0] = { 1, 0 }, [2] = { 1, 0 }, [3] = { 0, 0 }, [4] = { 0, 1 } },
  },

  -- Little Seoul Digital Den
  {
    model = `a_f_y_hipster_02`,
    coords = vector4(-660.028, -856.768, 24.491, 271.603),
    store_id = 'digi-all',
    anim = {
      dict = 'anim@heists@heist_corona@team_idles@male_a',
      clip = 'idle',
    },
    clothing = { [0] = { 1, 0 }, [2] = { 1, 0 }, [3] = { 0, 0 }, [4] = { 0, 1 } },
    timeflag = 17300992,
  },

  -- Alta Street Digital Den
  {
    model = `a_f_y_hipster_02`,
    coords = vector4(83.265, -223.970, 54.641, 251.309),
    store_id = 'digi-all',
    anim = {
      dict = 'anim@heists@heist_corona@team_idles@male_a',
      clip = 'idle',
    },
    clothing = { [0] = { 1, 0 }, [2] = { 1, 0 }, [3] = { 0, 0 }, [4] = { 0, 1 } },
    timeflag = 16907776,
  },

  -- Textile City Digital Den
  {
    model = `a_f_y_hipster_02`,
    coords = vector4(391.029, -824.114, 29.308, 178.264),
    store_id = 'digi-all',
    anim = {
      dict = 'anim@heists@heist_corona@team_idles@male_a',
      clip = 'idle',
    },
    clothing = { [0] = { 1, 0 }, [2] = { 1, 0 }, [3] = { 0, 0 }, [4] = { 0, 1 } },
    timeflag = 17301376,
  },

  -- Mirror Park Digital Den
  {
    model = `a_f_y_hipster_02`,
    coords = vector4(1134.988, -468.036, 66.636, 162.567),
    store_id = 'digi-all',
    anim = {
      dict = 'anim@heists@heist_corona@team_idles@male_a',
      clip = 'idle',
    },
    clothing = { [0] = { 1, 0 }, [2] = { 1, 0 }, [3] = { 0, 0 }, [4] = { 0, 1 } },
    timeflag = 16842496,
  },

  -- Paleto Bay Digital Den
  {
    model = `a_f_y_hipster_02`,
    coords = vector4(-24.442, 6476.720, 31.492, 41.471),
    store_id = 'digi-paleto',
    anim = {
      dict = 'anim@heists@heist_corona@team_idles@male_a',
      clip = 'idle',
    },
    clothing = { [0] = { 1, 0 }, [2] = { 1, 0 }, [3] = { 0, 0 }, [4] = { 0, 1 } },
    timeflag = 16842496,
  },

  -- Vespucci Beach Digital Den
  {
    model = `a_f_y_hipster_02`,
    coords = vector4(-1242.518, -1452.792, 4.374, 128.116),
    store_id = 'digi-vb',
    anim = {
      dict = 'anim@heists@heist_corona@team_idles@male_a',
      clip = 'idle',
    },
    clothing = { [0] = { 1, 0 }, [2] = { 1, 0 }, [3] = { 0, 0 }, [4] = { 0, 1 } },
    timeflag = 16907776,
  },

  -- LD Organics
  {
    model = `ig_entourage_a`,
    coords = vector4(-1247.906, -1448.695, 4.374, 33.244),
    store_id = 'ldorganics',
    anim = {
      dict = 'anim@heists@heist_corona@team_idles@male_a',
      clip = 'idle',
    },
    timeflag = 16907776,
  },

  -- Legion Coffee Guy
  {
    model = `s_m_m_strvend_01`,
    coords = vector4(202.244, -962.205, 28.499, 217.370),
    store_id = 'legion-coffee',
    anim = {
      dict = 'anim@heists@heist_corona@team_idles@male_a',
      clip = 'idle',
    },
    clothing = {
    [0] = {0, 2},
    [1] = {0, 0},
    [2] = {0, 2},
    [3] = {0, 0},
    [4] = {0, 1},
    [5] = {0, 0},
    [6] = {0, 0},
    [7] = {0, 0},
    [8] = {0, 1},
    [9] = {0, 0},
    [10] = {0, 0},
    [11] = {0, 0},
    ['p0'] = {-1, 0},
    ['p1'] = {-1, 0},
    ['p2'] = {-1, 0},
    ['p6'] = {-1, 0},
    ['p7'] = {-1, 0},
    },
  },

  -- DOL Los Santos
  {
    model = `a_f_y_business_04`,
    coords = vector4(-542.467, -197.858, 37.74, 66.947),
    store_id = '',
    anim = {
      dict = 'amb@lo_res_idles@',
      clip = 'generic_seating_lo_res_base',
    },
  },

  {
    model = `a_f_y_business_04`,
    coords = vector4(-551.843, -203.294, 37.74, 348.104),
    store_id = '',
    anim = {
      dict = 'amb@lo_res_idles@',
      clip = 'generic_seating_lo_res_base',
    },
  },

  -- Grapeseed Market
  {
    model = `a_m_m_farmer_01`,
    coords = vector4(1792.683, 4594.454, 37.683, 185.519),
    store_id = 'grapeseed-market',
    anim = {
      dict = 'tigerle@custom@jobs@handsonback',
      clip = 'tigerle_custom_handsonback',
    },
    clothing = { [0] = { 0, 2 }, [1] = { 0, 0 }, [2] = { 0, 0 }, [3] = { 4, 0 }, [4] = { 1, 0 }, [5] = { 0, 1 }, [6] = { 0, 0 }, [7] = { 0, 0 }, [8] = { 0, 0 }, [9] = { 0, 0 }, [10] = { 0, 0 }, [11] = { 0, 0 } },
  },

  -- Ballas Liquor
  {
    model = `g_f_y_ballas_01`,
    coords = vector4(-6.266, -1822.757, 25.336, 140.780),
    store_id = 'liquor-gsl',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- YouTool
  {
    model = `s_m_y_construct_01`,
    coords = vector4(2749.257, 3472.292, 55.679, 240.0),
    store_id = 'youtool-supply',
    anim = {
      dict = 'tigerle@custom@jobs@handsonback',
      clip = 'tigerle_custom_handsonback',
    },
  },

  -- Metal Merchant
  {
    model = `s_m_y_construct_02`,
    coords = vector4(33.077, -2673.565, 6.009, 360.0),
    store_id = 'metal-merchant',
    anim = {
      dict = 'tigerle@custom@jobs@handsonback',
      clip = 'tigerle_custom_handsonback',
    },
  },

  -- Fence
  {
    model = `s_m_m_migrant_01`,
    coords = vector4(1585.600, 6464.229, 25.317, 30.864),
    store_id = 'fence',
    anim = {
      dict = 'anim@heists@heist_corona@team_idles@male_a',
      clip = 'idle',
    },
    clothing = { [0] = { 0, 0 }, [2] = { 0, 0 }, [3] = { 1, 1 }, [4] = { 0, 0 }, [8] =  { 0, 0 } },
  },

  -- Pillbox Gift Shop
  {
    model = `u_f_y_princess`,
    coords = vector4(315.693,-589.612,43.446, 159.418),
    store_id = 'pillbox-gifts',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Bay Hardware
  {
    model = `s_m_m_cntrybar_01`,
    coords = vector4(-11.241, 6499.509, 31.501, 44.281),
    store_id = 'bayhardware',
    anim = {
      dict = 'tigerle@custom@jobs@handsonback',
      clip = 'tigerle_custom_handsonback',
    },
  },

  -- Calico Jacks
  {
    model = `a_m_y_surfer_01`,
    coords = vector4(-2963.700, 454.930, 15.316, 92.754),
    store_id = 'calicojacks',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Tannery
  {
    model = `a_f_y_indian_01`,
    coords = vector4(465.477, -735.555, 27.364, 93.698),
    store_id = 'tannery',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Meat Sales
  {
    model = `s_f_y_factory_01`,
    coords = vector4(975.019, -2169.379, 29.466, 351.935),
    store_id = 'meat-sales',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Illegal Hunting
  {
    model = `cs_hunter`,
    coords = vector4(1707.696, 4924.610, 42.078, 322.835),
    store_id = 'illegal-hunting-sell',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  {
    model = `cs_hunter`,
    coords = vector4(-403.931, 6363.428, 13.211, 200.246),
    store_id = 'illegal-hunting-buy',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Illegal Weapons
  {
    model = `s_m_y_dealer_01`,
    coords = vector4(-1556.806, -232.577, 49.480, 135.269),
    store_id = 'illegal-weapons',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Illegal Items
--   {
--     model = `csb_cletus`,
--     coords = vector4(1388.384, 3603.909, 38.942, 287.880),
--     store_id = 'illegal-items',
--     anim = {
--       dict = 'mini@strip_club@idles@bouncer@base',
--       clip = 'base',
--     },
--   },

  -- Pawn Fence
  {
    model = `mp_m_freemode_01`,
    coords = vector4(417.626, 332.148, 103.126, 165.534),
    store_id = 'fence-items',
    anim = {
      dict = 'timetable@maid@couch@',
      clip = 'base',
    },
    clothing = {
      [1] = {0, 0},
      [3] = {0, 0},
      [4] = {311, 17},
      [5] = {228, 0},
      [6] = {161, 1},
      [7] = {123, 1},
      [8] = {318, 0},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {888, 0},
      ['p0'] = {-1, 0},
      ['p1'] = {4, 6},
      ['p2'] = {32, 2},
      ['p6'] = {37, 0},
      ['p7'] = {12, 0},
    },
    blend = json.decode('{"nose_5":6,"jaw_2":-100,"eyebrows_1":33,"ears_1":-1,"glasses_2":0,"shoes_2":0,"blush_1":3,"sun_2":100,"complexion_1":255,"bproof_1":0,"lefthand_2":0,"eyebrows_3":0,"neckarm_2":0,"nose_6":0,"bodyb_4":100,"skin_md_weight":35,"chest_1":1,"blemishes_2":100,"lipstick_2":15,"dad":12,"righthand_2":0,"shoes_1":34,"lipstick_4":0,"makeup_3":255,"age_1":1,"moles_2":100,"bodyb_2":100,"chin_3":0,"makeup_4":255,"bodyb_3":255,"eyebrows_4":0,"neckarm_1":0,"bags_2":0,"chest_4":0,"torso_1":15,"moles_1":255,"nose_4":3,"decals_2":0,"tshirt_2":0,"nose_2":17,"bags_1":0,"hair_color_1":0,"lipstick_1":0,"beard_1":9,"mask_2":0,"ears_2":0,"helmet_1":-1,"cheeks_3":25,"lefthand_1":-1,"glasses_1":-1,"hair_1":194,"makeup_1":255,"lipstick_3":62,"beard_2":100,"lip_thickness":1,"helmet_2":0,"righthand_1":-1,"face_md_weight":100,"neck_thickness":0,"sun_1":255,"makeup_2":100,"pants_2":0,"sex":0,"bodyb_1":255,"blemishes_1":255,"mask_1":0,"eyebrows_5":38,"chin_2":0,"chin_1":0,"bproof_2":0,"chest_3":1,"arms_2":0,"tshirt_1":15,"torso_2":0,"blush_2":30,"hair_2":0,"beard_4":0,"jaw_1":-52,"decals_1":0,"nose_1":-67,"cheeks_1":-23,"pants_1":61,"makeup_type":0,"cheeks_2":-3,"age_2":100,"arms":15,"blush_3":6,"eye_squint":52,"mom":29,"chest_2":68,"beard_3":1,"eyebrows_2":100,"chin_4":0,"eyebrows_6":68,"complexion_2":100,"hair_color_2":2,"nose_3":42,"eye_color":5}')
  },

  --[[ B1 Gift Shop
  {
    model = `mp_m_freemode_01`,
    coords = vector4(-3067.365, 7.052, 7.836, 269.891),
    store_id = 'b1lsia-store',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
    clothing = {
      [1] = {239, 13},
      [3] = {0, 0},
      [4] = {271, 3},
      [5] = {0, 0},
      [6] = {175, 0},
      [7] = {232, 2},
      [8] = {15, 0},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {710, 5},
      ['p0'] = {-1, 0},
      ['p1'] = {56, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },
    blend = json.decode('{"complexion_2":100,"ears_2":0,"blush_2":100,"bodyb_1":255,"blush_1":255,"eyebrows_2":100,"chin_2":0,"chest_3":2,"makeup_4":255,"eyebrows_3":2,"lip_thickness":0,"beard_4":0,"torso_2":0,"lipstick_4":0,"eyebrows_5":0,"nose_4":0,"chest_1":0,"moles_2":100,"lefthand_1":-1,"shoes_1":34,"nose_6":0,"sun_2":100,"tshirt_1":15,"makeup_3":255,"neck_thickness":0,"bproof_1":0,"age_1":255,"face_md_weight":57,"moles_1":255,"eye_color":0,"beard_3":3,"bodyb_2":100,"lefthand_2":0,"eyebrows_4":0,"makeup_type":0,"eyebrows_6":0,"bags_1":0,"blemishes_1":255,"nose_3":0,"shoes_2":0,"age_2":100,"sun_1":255,"beard_1":11,"righthand_2":0,"blemishes_2":100,"complexion_1":255,"lipstick_3":0,"decals_2":0,"sex":0,"cheeks_2":0,"dad":0,"lipstick_1":255,"beard_2":100,"decals_1":0,"bags_2":0,"bproof_2":0,"cheeks_3":0,"glasses_1":-1,"hair_color_2":0,"hair_color_1":3,"eyebrows_1":0,"skin_md_weight":0,"righthand_1":-1,"chin_3":0,"makeup_2":100,"mask_1":0,"lipstick_2":100,"ears_1":-1,"helmet_1":-1,"glasses_2":0,"jaw_1":0,"pants_2":0,"jaw_2":0,"hair_1":116,"torso_1":15,"eye_squint":0,"chest_4":0,"nose_5":0,"blush_3":0,"hair_2":0,"neckarm_1":0,"mask_2":0,"mom":38,"chest_2":100,"tshirt_2":0,"cheeks_1":0,"nose_1":0,"bodyb_3":255,"bodyb_4":100,"pants_1":61,"makeup_1":255,"helmet_2":0,"neckarm_2":0,"chin_1":0,"chin_4":0,"arms":15,"nose_2":0,"arms_2":0}')
  }, ]]

  --[[ Cinco De Mayo
  {
    model = `g_f_y_vagos_01`,
    coords = vector4(376.097, -347.231, 46.669, 244.146),
    store_id = 'lilmec-supply',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
    clothing = {
      [1] = {1, 0},
      [2] = {0, 2},
      [3] = {0, 1},
      [4] = {0, 2},
      [5] = {0, 0},
      [6] = {0, 0},
      [7] = {0, 0},
      [8] = {1, 1},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {-1, 0},
      ['p0'] = {-1, 0},
      ['p1'] = {-1, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },
  }, --]]

  -- Vineyard
  {
    model = `cs_dreyfuss`,
    coords = vector4(-1892.311, 2076.792, 140.998, 317.320),
    store_id = 'vineyard',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Repair Parts
  {
    model = `s_m_m_autoshop_02`,
    coords = vector4(1154.355, -785.455, 57.599, 39.347),
    store_id = 'mechanic-parts',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  {
    model = `s_m_m_autoshop_02`,
    coords = vector4(-69.115, 6423.695, 31.532, 279.626),
    store_id = 'mechanic-parts',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Locksmith
  {
    model = `s_m_y_ammucity_01`,
    coords = vector4(165.149, -1804.185, 29.321, 319.339),
    store_id = 'locksmith',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  {
    model = `s_m_y_ammucity_01`,
    coords = vector4(114.777, 6631.229, 31.973, 307.306),
    store_id = 'locksmith',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Vangelico
  {
    model = `cs_movpremmale`,
    coords = vector4(-622.372, -229.972, 38.057, 304.477),
    store_id = 'vangelico',

    extra_options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Buy Gang Jewelry',

        uid = 'vangelico-chains',

        filter = function(entity, hit_coords)
          return IsEntityVisible(entity) and NetworkGetEntityIsLocal(entity) and Entity(entity).state.store_uid == 'vangelico'
        end
      },
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Buy Jewelry',

        uid = 'vangelico-jewelry',

        filter = function(entity, hit_coords)
          return IsEntityVisible(entity) and NetworkGetEntityIsLocal(entity) and Entity(entity).state.store_uid == 'vangelico'
        end
      },
    },

    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Fireworks
  {
    model = `a_m_y_juggalo_01`,
    coords = vector4(-1631.522, -1099.764, 13.023, 70.957),
    store_id = 'fireworks',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Mugs
  {
    model = `s_f_y_shop_low`,
    coords = vector4(273.621, -833.145, 29.414, 203.628),
    store_id = 'mugs',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Tubes
  -- {
  --   model = `s_f_y_shop_low`,
  --   coords = vector4(-1109.280, -1694.562, 4.565, 304.447),
  --   store_id = 'tubes',
  --   anim = {
  --     dict = 'mini@strip_club@idles@bouncer@base',
  --     clip = 'base',
  --   },
  -- },

  --Arcde
  {
    model = `s_m_y_valet_01`,
    coords = vector4(-1292.981, -301.713, 36.051, 296.274),
    store_id = 'arcade-exchange',
    extra_options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Ticket Shop',

        uid = 'arcade-shop',

        filter = function(entity, hit_coords)
          return IsEntityVisible(entity) and NetworkGetEntityIsLocal(entity) and Entity(entity).state.store_uid == 'arcade-exchange'
        end
      },
    },
    anim = {
      dict = 'anim@amb@nightclub@lazlow@ig1_vip@',
      clip = 'clubvip_base_laz',
    },
  },

  -- Prison electronics
  {
    model = `s_m_m_prisguard_02`,
    coords = vector4(1840.409, 2577.767, 46.024, 357.789),
    store_id = 'prison-electronics',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Badpix
  {
    model = `a_f_y_bevhills_02`,
    coords = vector4(169.667389, -960.9898, 29.777, 165.392),
    store_id = 'badpix',
    anim = {
      dict = 'anim@amb@nightclub@lazlow@ig1_vip@',
      clip = 'clubvip_base_laz',
    },
    clothing = { [0] = { 0, 0 }, [2] = { 3, 0 }, [3] = { 1, 1 }, [4] = { 0, 2 }, [8] =  { 0, 0 } },
  },

  -- Light and Love
  {
    model = `mp_f_freemode_01`,
    coords = vector4(-889.253, 6038.575, 48.813, 114.218),
    store_id = 'lightandlove',
    anim = {
      dict = 'anim@amb@nightclub@lazlow@ig1_vip@',
      clip = 'clubvip_base_laz',
    },
    clothing = exports.blrp_clothingstore:BackwardsCompatibleOutfit(2944, { [1] = {0, 0}, [3] = {15, 0}, [4] = {246, 6}, [5] = {175, 3}, [6] = {33, 1}, [7] = {203, 0}, [8] = {393, 21}, [9] = {0, 0}, [10] = {247, 4}, [11] = {775, 1}, ['p0'] = {-1, 0}, ['p1'] = {61, 0}, ['p2'] = {-1, 0}, ['p6'] = {-1, 0}, ['p7'] = {18, 1}, }, 'female'),
    blend = json.decode('{"ears_1":-1,"helmet_1":-1,"beard_4":0,"cheeks_3":0,"mom":40,"decals_1":0,"age_2":100,"hair_color_2":12,"lipstick_3":15,"pants_1":61,"bodyb_4":100,"age_1":255,"blush_1":1,"moles_2":57,"eyebrows_1":0,"bags_2":0,"sun_1":255,"lefthand_2":0,"nose_1":-25,"bodyb_1":6,"nose_5":0,"eye_color":0,"lefthand_1":-1,"chin_3":-57,"chest_4":0,"chest_2":100,"sex":0,"bproof_2":0,"makeup_2":80,"hair_2":0,"arms":15,"nose_3":-59,"shoes_1":34,"neckarm_2":0,"pants_2":0,"arms_2":0,"righthand_1":-1,"righthand_2":0,"eyebrows_4":0,"cheeks_1":0,"shoes_2":0,"jaw_1":21,"eyebrows_6":0,"sun_2":100,"neckarm_1":0,"glasses_2":0,"lipstick_1":1,"chest_1":255,"chin_4":0,"ears_2":0,"blush_2":36,"lip_thickness":-60,"torso_1":15,"jaw_2":-33,"makeup_4":255,"makeup_1":1,"bodyb_3":255,"torso_2":0,"glasses_1":-1,"makeup_3":255,"tshirt_2":0,"tshirt_1":15,"bags_1":0,"hair_color_1":8,"helmet_2":0,"makeup_type":1,"nose_6":0,"nose_2":28,"blush_3":2,"lipstick_4":0,"moles_1":8,"eyebrows_3":7,"bodyb_2":57,"dad":0,"beard_3":0,"chin_1":0,"lipstick_2":14,"nose_4":37,"decals_2":0,"eye_squint":-79,"chin_2":0,"complexion_1":255,"skin_md_weight":78,"hair_1":189,"eyebrows_5":0,"blemishes_2":100,"face_md_weight":58,"beard_1":255,"neck_thickness":0,"chest_3":0,"cheeks_2":0,"bproof_1":0,"beard_2":100,"complexion_2":100,"eyebrows_2":81,"mask_2":0,"blemishes_1":255,"mask_1":0}')
  },

  -- Weed Seeds
  {
    model = `u_m_y_caleb`,
    coords = vector4(2220.668, 5578.226, 53.717, 174.082),
    store_id = 'weedseeds',
    scenario = 'WORLD_HUMAN_GARDENER_PLANT',
  },

  -- Drug run pharmacies
  {
    model = `s_m_m_doctor_01`,
    coords = vector4(-3163.604, 1097.378, 20.927, 249.090),
    store_id = 'pharmacy-odeas',
    anim = {
      dict = 'anim@amb@nightclub@lazlow@ig1_vip@',
      clip = 'clubvip_base_laz',
    },
    quest_id = 'DrugRunPickupChumash',
  },

  {
    model = `s_m_m_doctor_01`,
    coords = vector4(1414.939, 3636.654, 34.973, 202.347),
    store_id = 'pharmacy-odeas',
    anim = {
      dict = 'anim@amb@nightclub@lazlow@ig1_vip@',
      clip = 'clubvip_base_laz',
    },
    quest_id = 'DrugRunPickupSandy',
  },

  {
    model = `s_m_m_doctor_01`,
    coords = vector4(160.071, 6651.885, 31.657, 133.219),
    store_id = 'pharmacy-all',
    anim = {
      dict = 'anim@amb@nightclub@lazlow@ig1_vip@',
      clip = 'clubvip_base_laz',
    },
    quest_id = 'DrugRunPickupPaleto',
  },

  -- fishing
  {
    model = `S_M_Y_Chef_01`,
    coords = vector4(-3416.548, 966.175, 8.347, 359.810),
    store_id = 'fs-fish-sales',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  {
    model = `S_M_M_Linecook`,
    coords = vector4(-1487.010, -909.751, 10.024, 321.720),
    store_id = 'fs-illegal-fish-sales',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  },

  -- Redwood
  {
    model = `mp_M_freemode_01`,
    coords = vector4(2901.737, 4410.314, 50.286, 227.649),
    store_id = 'redwoods-supply',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
    clothing = {
      [2] = {10, 0},
      [3] = {0, 0},
      [4] = {6, 1},
      [5] = {0, 0},
      [6] = {12, 0},
      [7] = {0, 0},
      [8] = {289, 2},
      [9] = {0, 0},
      [10] = {0, 0},
      [11] = {282, 7},
      ['p0'] = {76, 15},
      ['p1'] = {65, 2},
      ['p2'] = {-1, 0},
      ['p6'] = {-1, 0},
      ['p7'] = {-1, 0},
    },
    blend = json.decode('{"ears_1":-1,"helmet_1":-1,"beard_4":0,"cheeks_3":0,"mom":38,"decals_1":0,"age_2":3,"hair_color_2":12,"lipstick_3":0,"pants_1":61,"bodyb_4":100,"age_1":255,"blush_1":0,"moles_2":3,"eyebrows_1":0,"bags_2":0,"sun_1":255,"lefthand_2":0,"nose_1":-25,"bodyb_1":6,"nose_5":0,"eye_color":0,"lefthand_1":-1,"chin_3":-57,"chest_4":0,"chest_2":100,"sex":0,"bproof_2":0,"makeup_2":0,"hair_2":0,"arms":15,"nose_3":-59,"shoes_1":34,"neckarm_2":0,"pants_2":0,"arms_2":0,"righthand_1":-1,"righthand_2":0,"eyebrows_4":0,"cheeks_1":0,"shoes_2":0,"jaw_1":21,"eyebrows_6":0,"sun_2":100,"neckarm_1":0,"glasses_2":0,"lipstick_1":0,"chest_1":255,"chin_4":0,"ears_2":0,"blush_2":0,"lip_thickness":-60,"torso_1":15,"jaw_2":-33,"makeup_4":0,"makeup_1":0,"bodyb_3":255,"torso_2":0,"glasses_1":-1,"makeup_3":0,"tshirt_2":0,"tshirt_1":15,"bags_1":0,"hair_color_1":8,"helmet_2":0,"makeup_type":0,"nose_6":0,"nose_2":28,"blush_3":2,"lipstick_4":0,"moles_1":8,"eyebrows_3":7,"bodyb_2":57,"dad":10,"beard_3":0,"chin_1":0,"lipstick_2":0,"nose_4":37,"decals_2":0,"eye_squint":-79,"chin_2":0,"complexion_1":255,"skin_md_weight":78,"hair_1":78,"eyebrows_5":0,"blemishes_2":100,"face_md_weight":58,"beard_1":15,"neck_thickness":0,"chest_3":0,"cheeks_2":0,"bproof_1":0,"beard_2":100,"complexion_2":100,"eyebrows_2":81,"mask_2":0,"blemishes_1":255,"mask_1":0}')
  },

-- Trojan Armor
  {
    model = `mp_m_freemode_01`,
    coords = vector4(-303.675, -1293.344, 31.246, 250.0),
    store_id = 'trojan-storefront',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
    clothing = {
      [1] = {237, 12},
      [3] = {0, 0},
      [4] = {262, 0},
      [5] = {228, 0},
      [6] = {14, 15},
      [7] = {0, 0},
      [8] = {0, 240},
      [9] = {135, 0},
      [10] = {0, 0},
      [11] = {722, 0},
      ['p0'] = {-1, 0},
      ['p1'] = {38, 0},
      ['p2'] = {-1, 0},
      ['p6'] = {20, 0},
      ['p7'] = {-1, 0},
    },
    blend = json.decode('{"nose_3":49,"eye_squint":100,"mom":25,"skin_md_weight":80,"makeup_2":100,"lipstick_4":0,"moles_2":100,"makeup_3":255,"chest_3":0,"arms":15,"eyebrows_4":0,"bodyb_1":10,"beard_1":10,"decals_1":0,"ears_2":0,"shoes_2":0,"makeup_type":0,"ears_1":-1,"makeup_1":255,"beard_4":0,"bodyb_3":255,"hair_2":0,"glasses_1":-1,"eyebrows_3":0,"bproof_2":0,"beard_2":100,"tshirt_1":15,"blush_3":0,"chin_3":0,"glasses_2":0,"lipstick_2":43,"dad":4,"blemishes_2":100,"lipstick_3":6,"jaw_2":16,"cheeks_1":-70,"torso_2":0,"righthand_2":0,"helmet_1":-1,"chest_2":100,"chest_4":0,"lip_thickness":-59,"neckarm_1":0,"sun_2":100,"shoes_1":34,"eye_color":2,"chest_1":255,"lefthand_1":-1,"nose_4":0,"nose_5":0,"bodyb_4":100,"mask_2":0,"blemishes_1":255,"sex":0,"complexion_2":100,"neck_thickness":0,"eyebrows_1":30,"bodyb_2":100,"age_1":255,"eyebrows_6":-30,"chin_4":-56,"lipstick_1":3,"decals_2":0,"hair_color_2":55,"eyebrows_2":100,"complexion_1":0,"mask_1":0,"age_2":100,"nose_6":0,"pants_2":0,"sun_1":255,"bags_1":0,"hair_color_1":3,"bproof_1":0,"beard_3":0,"chin_2":5,"nose_1":2,"blush_2":100,"face_md_weight":60,"torso_1":15,"moles_1":4,"tshirt_2":0,"lefthand_2":0,"arms_2":0,"neckarm_2":0,"bags_2":0,"hair_1":135,"chin_1":-20,"jaw_1":-7,"makeup_4":255,"helmet_2":0,"pants_1":61,"cheeks_2":50,"righthand_1":-1,"nose_2":0,"blush_1":255,"cheeks_3":45,"eyebrows_5":29}')
  },
}

-------------------------------------
---------- 24/7 Store Peds ----------
-------------------------------------
for _, v in pairs({
  vector4(-46.486, -1757.987, 29.411, 48.803), -- Grove St LTD
  vector4(-706.032, -913.416, 19.206, 90.430), -- Little Seoul LTD
  vector4(1164.904, -322.544, 69.195, 101.214), -- Mirror Park LTD
  vector4(-1820.037, 794.373, 138.076, 133.086), -- Richman LTD
  vector4(1698.144, 4922.904, 42.054, 324.111), -- Grapeseed LTD
  vector4(161.950, 6642.423, 31.689, 225.404), -- Paleto 24/7
  vector4(1728.915, 6416.366, 35.027, 238.767), -- Chiliad 24/7
  vector4(2676.856, 3280.503, 55.231, 332.153), -- Senora Fwy 24/7
  vector4(548.840, 2669.711, 42.146, 95.076), -- Harmony 24/7
  vector4(-3243.671, 1000.546, 12.821, 355.346), -- Chumash North 24/7
  vector4(-3040.507, 584.412, 7.899, 19.268), -- Chumash South 24/7
  vector4(373.351, 327.771, 103.556, 256.141), -- Vinewood 24/7
  vector4(2555.681, 381.241, 108.613, 358.839), -- Palomino 24/7
  vector4(24.871, -1345.801, 29.487, 269.770), -- Strawberry 24/7
  vector4(239.563, -897.737, 29.588, 161.962), -- Legion 24/7
  { vector4(-674.451, 5835.154, 17.331, 47.479), 'bayview-all' }, -- Bayview Lodge
  { vector4(1821.907, 3755.654, 33.477, 37.185), '247-market' , true }, -- 24/7 Market
}) do
  local coords = nil
  local store_id = nil
  local bulk_order = false

  if type(v) == 'vector4' then
    coords = v
    store_id = '247-all'
  else
    coords, store_id, bulk_order = table.unpack(v)
  end

  if coords and store_id then
    table.insert(static_store_peds, {
      model = `a_m_m_indian_01`,
      coords = coords,
      store_id = store_id,
      bulk_order = bulk_order,
      anim = {
        dict = 'mini@strip_club@idles@bouncer@base',
        clip = 'base',
      },
      clothing = { [0] = { 0, 2 }, [1] = { 0, 0 }, [2] = { 0, 0 }, [3] = { 0, 1 }, [4] = { 0, 2 } },
    })
  end
end

-------------------------------------
--------- Liquor Store Peds ---------
-------------------------------------
for _, v in pairs({
  vector4(1165.808, 2710.778, 38.148, 178.382), -- Route 68
  vector4(-2966.404, 390.896, 15.033, 82.433), -- Chumash
  vector4(-1486.258, -378.049, 40.153, 137.935), -- Morningwood
  vector4(1134.081, -982.575, 46.406, 276.404), -- Murrieta Heights
  vector4(1392.481, 3606.246, 34.971, 200.370), -- Ace Liquor
  vector4(-160.889, 6321.209, 31.576, 314.818), -- Paleto Liquor
  vector4(-1224.771, -1484.211, 4.374, 33.148), -- Vespucci Beach
}) do
  table.insert(static_store_peds, {
    model = `u_m_o_taphillbilly`,
    coords = v,
    store_id = 'liquor-all',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  })
end

-------------------------------------
---------- Ammunation Peds ----------
-------------------------------------
for _, v in pairs({
  vector4(1697.865, 3757.745, 34.705, 150.755),
  vector4(247.190, -51.598, 69.941, 5.898),
  vector4(840.904, -1028.801, 28.195, 304.951),
  vector4(-326.018, 6081.476, 31.455, 175.430),
  vector4(-659.076, -939.718, 21.829, 130.190),
  vector4(-1310.812, -394.470, 36.696, 28.604),
  vector4(-1112.447, 2697.494, 18.555, 169.720),
  vector4(2564.697, 298.663, 108.735, 310.549),
  vector4(-3167.219, 1087.241, 20.839, 199.995),
  vector4(813.191, -2155.324, 29.619, 7.114),
  vector4(18.564, -1108.023, 29.797, 154.466),
}) do
  table.insert(static_store_peds, {
    model = `s_m_m_ammucountry`,
    coords = v,
    store_id = 'ammunation-all',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  })
end

-------------------------------------
----------- Pharmacy Peds -----------
-------------------------------------
for _, v in pairs({
  vector4(68.770, -1569.807, 29.587, 46.257),
  vector4(591.159, 2744.385, 42.033, 183.984),
  vector4(-1227.174, -1474.724, 4.364, 125.918),
}) do
  table.insert(static_store_peds, {
    model = `s_m_m_doctor_01`,
    coords = v,
    store_id = 'pharmacy-all',
    anim = {
      dict = 'mini@strip_club@idles@bouncer@base',
      clip = 'base',
    },
  })
end

Citizen.CreateThread(function()
  Citizen.Wait(1000)

  local peds_grouped = {}

  -- Group peds by model
  for _, ped in pairs(static_store_peds) do
    if not peds_grouped[ped.model] then
      peds_grouped[ped.model] = {}
    end

    table.insert(peds_grouped[ped.model], ped)
  end

  -- Create target options (client & server)
  for model, group in pairs(peds_grouped) do
    local uids_touched = {}
    local options = {}
    local quest_option = false

    for _, ped in pairs(group) do
      local uid = ped.store_id

      if not uids_touched[uid] then
        uids_touched[uid] = true

        -- Quest dialogue option
        if ped.quest_id and not quest_option then
          quest_option = true,

          table.insert(options, {
            event_server = 'blrp_quests:server:talkToPed',
            icon = 'fa-regular fa-comment-dots',
            label = 'Talk',

            filter_pass_value = true,
            filter_pass_value_as = 'quest_id',
            filter = function(entity, _, _, state)
              return IsEntityVisible(entity) and state.target_quest_id
            end
          })
        end

        -- Shop option
        table.insert(options, {
          event_server = 'blrp_core:server:item-store:resolveTargetConfig',
          icon = 'fa-solid fa-dollar-sign',
          label = 'Shop',

          uid = uid,

          filter = function(entity, hit_coords)
            return IsEntityVisible(entity) and NetworkGetEntityIsLocal(entity) and Entity(entity).state.store_uid == uid
          end
        })

        if ped.bulk_order then
          table.insert(options, {
            event_server = 'blrp_core:server:auto-order:resolveTargetConfig',
            icon = 'fa-solid fa-dollar-sign',
            label = 'Pickup Stock',

            uid = uid,

            filter = function(entity, hit_coords)
              return IsEntityVisible(entity) and NetworkGetEntityIsLocal(entity) and Entity(entity).state.store_uid == uid
            end
          })
        end

        if ped.extra_options then
          for _, extra_option in pairs(ped.extra_options) do
            table.insert(options, extra_option)
          end
        end
      end
    end

    AddTargetModel({ model }, {
      options = options,
      distance = 4.0
    })
  end

  -- Create peds on client
  if not IsDuplicityVersion() then
    local loaded_models = {}

    for idx, ped in pairs(static_store_peds) do
      local uid = ped.store_id
      local quest_id = ped.quest_id

      local spawned_ped = exports.blrp_core:CreateStaticPed({
        id = quest_id or ('StorePed-' .. idx .. '-' .. uid),
        model = ped.model,
        coords = ped.coords,
        dict = ped.anim and ped.anim.dict or false,
        clip = ped.anim and ped.anim.clip or false,
        scenario = ped.scenario or false,
        outfit = ped.clothing,
        skin = ped.blend,
        store_uid = uid,
        timeflag = ped.timeflag,
      })

      if quest_id then
        Entity(spawned_ped).state.target_quest_id = quest_id
        exports.blrp_quests:RegisterPed(quest_id, spawned_ped)
      end
    end
  end
end)

