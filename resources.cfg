start yarn
start mapmanager
start chat
start blrp_ui
ensure clothing_restream
ensure clothing_addon_1
ensure clothing_addon_2
ensure clothing_addon_3
ensure clothing_addon_4
start webpack
start spawnmanager
start oxmysql
start ox_lib
start blrp_rpc
start vrp
start mythic_notify
start mythic_progbar
start customscripts
start RageUI
start lvc
start lscustoms
start license_shop
start vrp_basic_mission
start mythic_hospital

#meth cooking
start kq_link
start kq_meth

# Cars
ensure [cars]
ensure [weapons]

start weazelnews
start addons
start Sidecar
start blrp_weapons
start weapon_stuff
start pQueue
start cd_easytime
start wk_wars2x
start InteractSound
start loadingscreen
start sit
start helicam
start vrp_taxi
stop webadmin
start emote_pack_1
start PazeFortniteDancePackV3
start PazeeeFortniteDancePackV4
start dpemotes
start RichPresence
start map-atlas
start loaf_basketball
start rcore_pool
start rcore_bowling
# start rcore_cam
# start rcore_cam_assets
start rcore_tennis
start rcore_guidebook
ensure [rcore_whiteboard]
start PolyZone
start gtr
start gizmo
start shark
start rcore_lunapark
start robot_dog
start Capybara

start utk_hackdependency
start utk_fingerprint
start ultra-fingerprint

#start blrp_dev
start blrp_ui
start blrp_core
start bad_menu
start blrp_menu
start blrp_tablet
start blrp_phone
# start blrp_restart
start blrp_api
start blrp_clothingstore
start blrp_wardrobe
start blrp_furniturestore
start blrp_keybinds
start blrp_hud
start blrp_quests
start blrp_characterselect
start blrp_inventory
start blrp_banking
start blrp_fuel
start blrp_documents
start blrp_vehicles
start blrp_paper
start blrp_doors
start blrp_npctesting
start blrp_character
start blrp_tattoos
start blrp_balls
#start gcphone
start screenshot-basic
start blrp_scenes
start housing
start blrp_radio
start blrp_postoffice
start blrp_sounds
start blrp_stuff
start blrp_poker
start blrp_roulette
# start blrp_slots
start blrp_casinowall
start blrp_phonehack
start blrp_projectors
start blrp_boosting
start blrp_nitro
start blrp_racing
start blrp_hack1
start blrp_hack2
start blrp_hack3
start blrp_hack4
start blrp_hack5
start blrp_hack6
start blrp_hack7
start blrp_hack8
start blrp_hack9
start blrp_hack10
start blrp_hack11
start blrp_hack12
start blrp_hack13
start blrp_hack14
start blrp_lockpick
start blrp_hotwire
start glow_minigames
start glitch_minigames
start SN-Hacking
start untangle
start blrp_hack15
start blrp_voting
start blrp_mugshot
#start blrp_atm_robbery
# start blrp_yankton
start blrp_movement
start blrp_pix
start blrp_lotto
start blrp_medal
#start blrp_lasers
start blrp_tcg

start DiamondBlackjack

start tg-uwucats
start ls_bolt_minigame

#start blrp_campmorningwood

# Cayo Perico
#start blrp_cayo
#start blrp_cayo_assets

# in development
# start blrp_char_select

start pma-voice

start SmartHose
start SmartFires
start dev_resource
start cd_keymaster
start cb_vaulthack
start blrp_zones
start blrp_target
start [PlasmaGame]
start securitycams
start pata-plasmakart
start PlasmaKart
ensure xsound
#start mx-surround
start xdiskjockey_beta
start rcore_spray
start rcore_gunrange
start rcore_spray_assets
start rcore_radiocar
#start mx-audioplayer
start rcore_golf
start animpack_1
start Radargun
start darts-scaleform
start kossek-darts
start rcore_arcade
start dashcam
start pw_midget_carter
start pw_midget_andreas
start fat_american_cop
# start kc-pickitback
start scully_bodycam
start cw-performance
start cw-racingapp
start Snow-Features

#RTX scripts
# start [rtx]
start arcade_ipl
#start rtx_arcades
#start rtx_arcades_objects
start rtx_cablecar

#script to play movies in the theatre
# exec @hypnonema/permissions.cfg
# start hypnonema

ensure dirk-core
start dirk-fishing

# Adjusted manifest files & resources - Chain
# Rework/Fleshedout Resources that are Completed

# Completed Resources
#start radar # Need a little more UI issues.
start vrp_teleports



# Custom Maps


start map_Core
start custom-mapping
start audio-occlusions
start brofx_audio


# Escrowed Maps

start cfx-gabz-mapdata
start cfx-gabz-247
start cfx-gabz-altruists
start cfx-gabz-ammunation
start cfx-gabz-arcade
start cfx-gabz-bahama
start cfx-gabz-barber
start cfx-gabz-bowling
start cfx-gabz-carmeet
start cfx-gabz-catcafe
#start cfx-gabz-davispd
start cfx-gabz-esbltd
start cfx-gabz-fleeca
start cfx-gabz-impound
#start cfx-gabz-lamesapd
start cfx-gabz-mba
start cfx-gabz-mrpd-props
start cfx-gabz-mirrorpark1
start cfx-gabz-paletobank
start cfx-gabz-paletopd
start cfx-gabz-parkranger
start cfx-gabz-pdm
start cfx-gabz-pdprops
start cfx-gabz-pearls
start cfx-gabz-tattoo
start cfx-gabz-townhall
start cfx-gabz-vagos
start cfx-gabz-vbmarket
start cfx-gabz-burgershot
start cfx-gabz-triads
# start cfx-nteam-tunnel
start map4all-pillbox
start map4all-ss-hospital-compatible
start map4all-ss-sheriff-compatible
start map4all-impound
start hane_coolbeans_ves
start hane_cul_de_sac
start brofx_farmhouse_02
start artex_cardealer
start diables_tattoos_city
start himen_prentiss
start j17_officeinterior
start jr_vbmafia
start jr_vesp_gang
start jr_audio_occlusions
start map_sandy-motel
start rsd_mlo_church
start map_AceCustoms
start pata-plasma-game
start pata-plasma-game-dlc
start pata-shells1
start pata-shells2
start pata-shells3
start avp_house_shell_01_props
start avp_house_shell_03_props
start k4mb1-post-trojan
start rcore_lunapark_assets
start rjz_treehouse
start doiidaum_vespucci_beach_park
start kiiya_vwpd
# start richards_offstore_halloweendecoration
start strawberry_gardens_verpi
start cfx_prompt_custom_Truck_Repairs
start alphamail_v1_by_NE_NOORE
start artex_mexicanmafia
start lev_cellar
start Hotbox
start brofx_vspd
start hn_police_la_mesa_med
start gcom_vag633_int
start cfx-mxc-pitstop

start map_bobcat
start map_casino
start map_digitaldens
start map_prison
start map_hackerspace-hideouts
start map_vremastered
start map_rooms   # labs and stuff v
start map_rooms2

# start server_scenarios

start map_odeas
start map_cypress-gangunit
# start map_cypress-vagogarage
start map_davis-firestation
start map_downbadapt-instance
# start map_downtown-chihuahua
start map_downtown-downbad
start map_downtown-gym
# start map_hills-cartelranch
start map_littleseoul-weazlenews
start map_mazebank-offices
start map_mirrorpark-aod
start map_mirrorpark-laundry
start map_mirrorpark-downtowncab
start map_mirrorpark-aodrepair
start map_oilfields-hawkhideout
start map_paleto_sandy-forest
start map_paleto-mechanic
start map_paleto-lostmc
start map_paleto-liquor
start map_paleto-license
start map_paleto-convenience
start map_paleto-cru
start map_paleto-rangerhut
start map_paleto-hospital
start map_paleto-lodge
start map_rockford-305mechanic
start map_rockford-church
start map_rockford-dynastyrealtor
start map_rockford-lscustoms
start map_rockford-radiolossantos
start map_sandy-yellowjack
start map_sandy-boathouse
start map_sandy-cityhall
start map_sandy-market
start map_sandy-flywheels
start map_sandy-clothing
start map_sandy-bank
start map_sandy-condemned
start map_sandy-mineshaft
start map_sandy-sportinggoods
start map_sandy-redwood
start map_sandy_oilfields-csprunk  # this interior was condensed from map_Room2 resource since both used the same interior
start map_sandy-scorpions
start map_sandy-pumpndump
start map_sandy-outer-church
start map_southside-ballas
start map_southside-ballaspanic
start map_southside-familiescompound
start map_southside-locksmith
start map_southside-tunershop
start map_southside-vanillaunicorn
start map_southside-forum
start map_vespucci-therapyroomthing
# start map_vinewood-galaxyclub
start map_ReapersSalvage
start map_autopia_reapers
start map_dnxnewcalafiaroads
start map_BeachClubHouse
start map_clintonpawn
start map_chihuahuas_mp
start map_mirrorpark_fence


start map_housingshells


# seasonal

# start map_Christmas2023
# start map_Yankton
# start blrp_egghunt
# start map_pride
# start map_HalloweenA
# start map_HalloweenC_Nve
# start nve_xmas
# start camp-morningwood
# start zancudo-mil
# start sci-bunker

# Custom Pro

start props_Addon
start props_Halloween
start pata-bathroom-props-2
start pata-bedroom-props
start pata-christmas-props
start pata-dressing-props
start pata-kitchen-props-2
start pata-laundry-props
start pata-livingroom-props
start pata-office-props
start em_wallah_uwu_props
start alca_medic_pack



# Unsure if Needed
#start CustomMedical #Sandy Clinic Medical + Paleto Fire Medical

#I know this is not related to resources, but I also know that server file is updated indepdent of git
sets banner_connecting "https://i.gyazo.com/c62525d4adfe3b48c6c6044135559b52.jpg"
